
# Software Requirements Specification (Concise)

## 1. Purpose & Brand Alignment
Ringerike Landskap AS (Anleggsgartner & Maskinentreprenør) requires a website that:
- Reflects their identity as **nature-driven**, **quality-focused**, and **trustworthy** landscapers in Røyse, Norway.
- Seamlessly guides visitors from an introductory overview of services and projects to direct inquiries.
- Delivers a professional, polished user experience consistent with the company’s emphasis on craftmanship and natural harmony.

## 2. Primary Routes & Page Structure
Use short, memorable URLs in Norwegian. For example:

1. **Hjem (Home)**
   - **Path:** `/`
   - **Key Content:**
     - Hero banner with seasonal imagery (reflecting “i samspill med naturen”).
     - Brief brand statement emphasizing Ringerike Landskap’s ethos (quality, nature, trust).
     - Teasers linking to Services (`/tjenester`) and Projects (`/prosjekter`).
     - Prominent CTA button leading to “Kontakt” (`/kontakt`).

2. **<PERSON>va vi gjø<PERSON> (Services)**
   - **Path:** `/tjenester`
   - **Key Content:**
     - List of services (e.g., Belegningsstein, Støttemur, Cortenstål installations, etc.) with brief descriptions.
     - Optionally, sub-routes if needed: `"/tjenester/[specific-service]"`.
     - CTA to `/kontakt` or link to `"/prosjekter"` to view examples.

3. **Prosjekter (Projects)**
   - **Path:** `/prosjekter`
   - **Key Content:**
     - Visual gallery of completed works showcasing craftsmanship.
     - Optional filters by service type or category.
     - Lightbox or detail view for each project image, highlighting brand’s emphasis on quality.
     - Sub-route for deeper project case studies if needed.

4. **Hvem er vi (About)**
   - **Path:** `/om-oss` or `/hvem-er-vi`
   - **Key Content:**
     - Company story, team introduction, brand values (quality, sustainability, local expertise).
     - Reinforces brand identity in a personal tone (why Ringerike Landskap invests in “grønne løsninger og fornøyde kunder”).
     - Subtle CTA linking to `/kontakt`.

5. **Kontakt (Contact)**
   - **Path:** `/kontakt`
   - **Key Content:**
     - Minimalistic contact form (Name, Email, Phone (optional), Message, GDPR consent checkbox).
     - Direct contact info (phone, email) and map embed (optional).
     - Emphasizes Ringerike Landskap’s responsiveness and reliability (promptly handling inquiries).

---

## 3. Core Functional Requirements
1. **Mobile-Friendly & Seasonal Hero Sections**
   - A dynamic hero on Home that updates seasonally (e.g., “Vintervedlikehold nå!”).
   - Responsive layout that adapts gracefully to different screen sizes.

2. **Services Overview & Linking**
   - Organized list of landscaping services, each with a short descriptor.
   - Ability to link relevant completed projects from each service detail if desired.

3. **Projects Gallery**
   - Display high-quality images with an intuitive lightbox or modal for zoomed-in views.
   - Optionally tag/filter images (e.g., “Belegningsstein,” “Hekk/Beplantning”) for user exploration.

4. **Contact Form & GDPR Compliance**
   - Required fields: Name, Email, Message; optional Phone.
   - Clear confirmation upon successful send and minimal spam measures (honeypot or reCAPTCHA).
   - Explicit checkbox for GDPR consent referencing the site’s privacy policy.

5. **Brand Consistency**
   - Use the brand’s **signature green** (e.g., `#3E8544`) and the **Inter** font family throughout headings and body text for uniformity.
   - Embed short brand statements in key sections (hero, about, contact) to maintain a consistent voice reflecting expertise and reliability.

---

## 4. Data Flow & Minimal Technical Notes
- **Contact Submission Flow:**
  1. User fills form at `/kontakt`.
  2. Server validates input (empty fields, email format, GDPR checkbox).
  3. On success, site emails inquiry to designated staff (e.g., `<EMAIL>`).
  4. Display a success message (brand-appropriate tone: “Takk for at du kontaktet oss!”).

- **Projects & Services Data:**
  - Content can be statically coded or managed in a simple CMS.
  - Link the relevant images and text to each service or project per design.

- **Performance & Security (Brief Highlights):**
  - Use short, compressed images for the Projects gallery to maintain fast load times.
  - Enforce HTTPS for user confidence and brand trust.
  - Validate contact form server-side to protect from malicious inputs.

---

## 5. Developer-Focused Guidelines
- **Project Structure**
  - Keep routes and navigation consistent with the five main sections.
  - Avoid duplicative content; ensure a single, definitive location for services data.

- **Design Implementation**
  - Follow brand color usage (`#3E8544` for primary buttons/accents) and Inter font.
  - Maintain the minimal, clean style that conveys reliability and top-tier craftsmanship.

- **Quality & Testing**
  - **Cross-check** each route for accessibility (e.g., alt text for all images).
  - Test the contact form thoroughly with invalid/missing data to ensure robust error handling.
  - Ensure seasonal hero updates remain easy to switch (simple text override or small script).

- **Reinforce Branding in Microcopy**
  - Contact form placeholders, success messages, and hover texts must reflect an approachable, professional brand tone. E.g., “Vi hører gjerne fra deg!”

- **Post-Launch Maintenance**
  - Occasionally refresh the gallery with new photos to showcase recent work.
  - Keep seasonal hero text current (spring planting, fall yard cleanup, winter brøyting, etc.).

---