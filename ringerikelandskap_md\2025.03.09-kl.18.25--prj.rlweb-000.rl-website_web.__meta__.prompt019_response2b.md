# 'https://chatgpt.com/c/67cda770-dc8c-8008-9cbc-7eca3b375202'

```
ringerike-landskap/
├── app/
│   ├── (home)/
│   │   └── page.tsx
│   ├── hva/
│   │   └── page.tsx
│   ├── prosjekter/
│   │   ├── [id]/
│   │   │   └── page.tsx
│   │   └── page.tsx
│   ├── hvem/
│   │   └── page.tsx
│   └── kontakt/
│       └── page.tsx
│
├── components/
│   ├── ui/
│   │   ├── accordion/
│   │   │   └── accordion.tsx
│   │   ├── carousel/
│   │   │   └── carousel.tsx
│   │   ├── dialog/
│   │   │   └── dialog.tsx
│   │   └── ...
│   ├── features/
│   │   ├── seasonal-banner/
│   │   │   └── seasonal-banner.tsx
│   │   ├── service-filter/
│   │   │   └── service-filter.tsx
│   │   ├── project-grid/
│   │   │   └── project-grid.tsx
│   │   └── ...
│   ├── layout/
│   │   ├── main-nav.tsx
│   │   ├── footer.tsx
│   │   └── ...
│   └── sections/
│       ├── hero/
│       │   └── hero.tsx
│       ├── testimonials/
│       │   └── testimonials.tsx
│       └── ...
│
├── data/
│   ├── services.ts
│   ├── projects.ts
│   ├── testimonials.ts
│   └── seasonal-content.ts
│
├── hooks/
│   ├── use-seasonal.ts
│   ├── use-service-filter.ts
│   └── ...
│
├── lib/
│   ├── seo/
│   │   ├── metadata.ts
│   │   └── schema-generator.ts
│   └── seasonal/
│       ├── detector.ts
│       └── mapper.ts
│
├── public/
│   ├── images/
│   │   ├── projects/
│   │   ├── services/
│   │   └── team/
│   └── locales/
│       └── en.json
│
├── styles/
│   ├── globals.css
│   └── tokens.css
│
├── types/
│   └── models.ts
│
├── context/
│   ├── seasonal-context.tsx
│   └── location-context.tsx
│
└── utils/
    ├── helpers.ts
    └── api/
        └── mock.ts
```
