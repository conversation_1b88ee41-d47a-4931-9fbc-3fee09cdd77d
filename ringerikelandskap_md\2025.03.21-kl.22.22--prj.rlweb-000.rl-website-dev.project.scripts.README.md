# Scripts Directory

This directory contains utility scripts for the Ringerike Landskap website project.

## Organization

The scripts are organized into the following categories:

### 🖼️ `screenshots/`
Scripts for capturing and managing website screenshots.

- `screenshots/capture/` - Scripts for capturing website screenshots
- `screenshots/manage/` - Scripts for managing, organizing, and cleaning screenshots

### 🔄 `dependencies/`
Scripts for analyzing and visualizing code dependencies.

- `dependencies/visualize/` - <PERSON>rip<PERSON> for creating different visualization formats
- `dependencies/analyze/` - Scripts for analyzing dependencies without visualization
- `dependencies/utils/` - Helper utilities for dependency operations

### 💻 `dev/`
Development workflow enhancement scripts.

### 🛠️ `utils/`
General utility scripts that don't fit into other categories.

## Usage

Most scripts can be executed via npm scripts defined in package.json. See the README in each subdirectory for specific usage instructions.
