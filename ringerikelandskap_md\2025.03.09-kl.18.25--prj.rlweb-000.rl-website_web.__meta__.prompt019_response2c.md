# 'https://chatgpt.com/c/67cda770-dc8c-8008-9cbc-7eca3b375202'

```
ringerike-landskap/
├── app/
│   ├── (home)/
│   │   └── page.tsx
│   ├── hva/
│   │   └── page.tsx
│   ├── prosjekter/
│   │   ├── [id]/
│   │   │   └── page.tsx
│   │   └── page.tsx
│   ├── hvem/
│   │   └── page.tsx
│   └── kontakt/
│       └── page.tsx
│
├── components/
│   ├── ui/            # Low-level, reusable UI (e.g., Button, Dialog, Carousel)
│   │   ├── button/
│   │   │   └── button.tsx
│   │   ├── dialog/
│   │   │   └── dialog.tsx
│   │   └── ...
│   ├── layout/        # Layout-level components (Header, Footer, Layout)
│   │   ├── header.tsx
│   │   ├── footer.tsx
│   │   └── main-layout.tsx
│   ├── features/      # Feature-focused components
│   │   ├── seasonal-banner/
│   │   ├── service-filter/
│   │   ├── project-grid/
│   │   └── ...
│   └── sections/      # Page sections (hero, testimonials, etc.)
│       ├── hero/
│       │   └── hero.tsx
│       └── testimonials/
│           └── testimonials.tsx
│
├── context/
│   ├── seasonal-context.tsx
│   └── location-context.tsx
│
├── data/
│   ├── services.ts
│   ├── projects.ts
│   ├── testimonials.ts
│   └── seasonal-content.ts
│
├── docs/
│   ├── 01-project-state/
│   │   ├── 01-current-codebase-reality.md
│   │   ├── 02-technical-debt-inventory.md
│   │   └── 03-company-information.md
│   ├── 02-codebase-map/
│   │   ├── 01-directory-structure.md
│   │   ├── 02-architecture-overview.md
│   │   └── 03-component-hierarchy.md
│   ├── 03-implementation-patterns/
│   │   ├── 01-current-patterns.md
│   │   ├── 02-component-patterns.md
│   │   └── 03-target-patterns.md
│   ├── 04-visual-systems/
│   │   ├── 01-screenshot-infrastructure.md
│   │   ├── 02-design-guidelines.md
│   │   └── 03-styling-approach.md
│   ├── 05-development-workflow/
│   │   ├── 01-environment-setup.md
│   │   ├── 02-optimization-guide.md
│   │   └── 03-accessibility-guide.md
│   ├── AI-README.md
│   └── README.md
│
├── hooks/
│   ├── use-seasonal.ts
│   ├── use-service-filter.ts
│   └── ...
│
├── lib/
│   ├── seo/
│   │   ├── metadata.ts
│   │   └── schema-generator.ts
│   └── seasonal/
│       ├── detector.ts
│       └── mapper.ts
│
├── public/
│   ├── images/
│   │   ├── projects/
│   │   ├── services/
│   │   └── team/
│   └── locales/
│       └── en.json
│
├── scripts/
│   ├── screenshot-manager.js
│   ├── dev-with-snapshots.js
│   ├── auto-snapshot.js
│   └── ...
│
├── screenshots/
│   ├── latest/
│   └── 2025-03-09_19-30-00/   # Example timestamped directory
│
├── .ai-analysis/
│   ├── latest/
│   ├── snapshot-[timestamp]/
│   └── metadata.json
│
├── styles/
│   ├── globals.css
│   └── tokens.css
│
├── types/
│   ├── models.ts
│   ├── service.interface.ts
│   ├── project.interface.ts
│   └── ...
│
├── utils/
│   ├── helpers.ts
│   └── api/
│       └── mock.ts
│
├── package.json
├── tsconfig.json
├── tailwind.config.js
├── postcss.config.js
└── vite.config.ts    # or next.config.js

```
