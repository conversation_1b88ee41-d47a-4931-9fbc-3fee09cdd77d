Remember, it should be less focused on the technical and more focused on the essential. the goal is to clearly reflect the company’s unique and authentic identity in the context of seo. <PERSON>lor each message specifically for a Norwegian landscaping firm based in Hole, Buskerud, targeting local prospective customers within a 20–50 km radius. Use crisp, compelling, and unambiguous language. Avoid overused clichés, overly casual expressions, and saccharine sweetness. Highlight the company's distinctive personal touch and genuine character, ensuring every piece of content aligns with a website designed to attract local landscaping clientele.

Process:
- Imitating the structure of the provided markdown template.
- Leveraging all the context about Ringerike Landskap.
- Presenting the final output (the elevator pitch) in a similar format as the initial example.
- Providing concise and highly informative response.

Now interpret the initial markdown-template to such degree that you're able to imitate it's inherent structure. Remember that to **imitate**, you need to adapt to the existing structure:

Example:

    1. Product Requirements Document: Ringerike Landskap AS Website
    - Work with an expert product manager to create your requirements. This outlines a PRD based on existing website data.
    ```
    # Context
    I am acting as an expert product manager. My role is to synthesize existing information about the Ringerike Landskap AS website into a Product Requirements Document. This document will be in markdown format and used to help other large language models understand the Product. I will be concise, but comprehensive.

    # Instructions
    1.  **(Implicitly Done)** Analyze all provided documentation (website images, file contents of `prj-web` directory).
    2.  **(Implicitly Done)** Identify all essential information about the company, its services, and the website's functionality.
    3.  Output a markdown file based on the synthesized context, using the Sample PRD headings as a guide, but adapted for a website rather than a new application.

    # Sample PRD Headings (Adapted for Website)

    1.  Elevator Pitch – Pitch this product (the website) in one paragraph.
    2.  This is for
    3.  Functional Requirements – What does the website do?
    4.  How it Works – How will the user interact with the website?
    5.  User Interface – How will the website look?
    ```

    1.1 Response to LLM Questions after First Prompt
    ```
    # 1. Elevator Pitch

    Ringerike Landskap AS's website is a digital platform showcasing a Norwegian landscaping and groundwork contractor (Anleggsgartner & maskinentreprenør) serving Røyse, Hønefoss, and the broader Ringerike region (Hole, Jevnaker, Sundvollen, Vik) in Norway.  The site presents the company's comprehensive services (including design, construction, and maintenance of outdoor spaces, specializing in stonework (Belegningsstein, Støttemurer, Kantstein, Trapper og Repoer), Corten steel, patios (Platting), lawns (Ferdigplen), and planting (Hekk og Beplantning)), a portfolio of completed projects, customer testimonials, and detailed contact information.  The website's core functionality includes project and service filtering (by category, location, tag, and season), detailed project specifications, a local service area map, and dynamically generated content based on the current season (vår, sommer, høst, vinter).  Built with React 18+, TypeScript, Vite, and Tailwind CSS, the site emphasizes responsive design, accessibility, and SEO best practices.

    ...

    ...

    ...

    ```
