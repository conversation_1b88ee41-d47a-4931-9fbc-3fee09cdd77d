# Current Implementation Patterns

This document outlines the current implementation patterns found in the Ringerike Landskap website codebase. Understanding these patterns is essential for maintaining consistency during development and planning improvements.

## Component Patterns

The codebase currently uses several different component implementation patterns:

### Functional Components with Props

Most components use the functional component pattern with explicit props:

```tsx
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  onClick?: () => void;
}

function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  return (
    <button 
      className={`btn ${variant === 'primary' ? 'btn-primary' : variant === 'secondary' ? 'btn-secondary' : 'btn-outline'}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}

export default Button;
```

### Context Usage

Some components use React Context for state management:

```tsx
// Context definition
export const ServiceContext = createContext<{
  services: ServiceType[];
  loading: boolean;
} | undefined>(undefined);

// Provider component
export function ServiceProvider({ children }: { children: React.ReactNode }) {
  const [services] = useState<ServiceType[]>(servicesData);
  const [loading] = useState(false);
  
  return (
    <ServiceContext.Provider value={{ services, loading }}>
      {children}
    </ServiceContext.Provider>
  );
}

// Usage in components
function ServiceList() {
  const context = useContext(ServiceContext);
  
  if (!context) {
    throw new Error('ServiceList must be used within ServiceProvider');
  }
  
  const { services, loading } = context;
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      {services.map(service => (
        <ServiceCard key={service.id} service={service} />
      ))}
    </div>
  );
}
```

### Prop Drilling

Deeply nested components often use prop drilling:

```tsx
function ProjectsPage() {
  const [filter, setFilter] = useState('all');
  
  return (
    <div>
      <ProjectFilter filter={filter} setFilter={setFilter} />
      <ProjectList filter={filter} />
    </div>
  );
}

function ProjectList({ filter }: { filter: string }) {
  return (
    <div>
      {projects
        .filter(project => filter === 'all' || project.category === filter)
        .map(project => (
          <ProjectItem key={project.id} project={project} />
        ))}
    </div>
  );
}
```

## Styling Patterns

### Tailwind Classes

The primary styling approach uses Tailwind CSS utility classes:

```tsx
function Card({ title, content }) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
      <p className="text-gray-600">{content}</p>
    </div>
  );
}
```

### Conditional Classes

Conditional styling is implemented using template literals or conditional expressions:

```tsx
function Alert({ type, message }) {
  return (
    <div className={`p-4 rounded ${
      type === 'error' ? 'bg-red-100 text-red-800' :
      type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
      'bg-green-100 text-green-800'
    }`}>
      {message}
    </div>
  );
}
```

## Data Management Patterns

### Static Data Files

Data is primarily managed through static TypeScript files:

```tsx
// src/data/services.ts
import { ServiceType } from '@/types';

export const services: ServiceType[] = [
  {
    id: "belegningsstein",
    title: "Belegningsstein",
    description: "Profesjonell legging av belegningsstein for innkjørsler og gangveier.",
    image: "/images/categorized/belegg/IMG_3037.webp",
    features: [
      "Solid grunnarbeid",
      "Presis legging",
      "Flere designmuligheter"
    ]
  },
  // More services...
];
```

### Helper Functions

Helper functions for data filtering and transformation:

```tsx
// Helper function to get a service by ID
export function getServiceById(id: string): ServiceType | undefined {
  return services.find(service => service.id === id);
}

// Helper function to filter services by season
export function getSeasonalServices(): ServiceType[] {
  const currentMonth = new Date().getMonth();
  const isSummer = currentMonth >= 4 && currentMonth <= 8;
  
  return services.filter(service => 
    isSummer ? !service.winterOnly : !service.summerOnly
  );
}
```

## Routing Pattern

The routing structure uses React Router with nested routes:

```tsx
function App() {
  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/hvem-er-vi" element={<AboutPage />} />
            <Route path="/hva-vi-gjor" element={<ServicesPage />} />
            <Route path="/tjenester/:id" element={<ServiceDetailPage />} />
            <Route path="/prosjekter" element={<ProjectsPage />} />
            <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />
            <Route path="/kontakt" element={<ContactPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}
```

## State Management Patterns

The codebase uses several approaches to state management:

1. **Component Local State**: Using `useState` for component-specific state
2. **React Context**: For sharing state between related components
3. **URL Parameters**: For page-specific state reflected in the URL
4. **Form State**: Managed within form components

No central state management library is currently in use.

## TypeScript Usage

TypeScript is used throughout the codebase with varying levels of type safety:

### Interface Definitions

```tsx
// src/types/services.ts
export interface ServiceType {
  id: string;
  title: string;
  description: string;
  image: string;
  features: string[];
  summerOnly?: boolean;
  winterOnly?: boolean;
}
```

### Type Assertions

```tsx
function getProjectImage(project: any) {
  // Using type assertion
  return (project as ProjectType).image || '/images/placeholder.webp';
}
```

### Generic Types

```tsx
function useFetch<T>(url: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Implementation...
  
  return { data, loading, error };
}
```

## Areas for Pattern Improvement

Based on the current implementation patterns, several areas could benefit from standardization:

1. **Component Structure**: Standardize on functional components with explicit prop interfaces
2. **State Management**: Adopt a more consistent approach (either Context API or a state management library)
3. **TypeScript Usage**: Improve type safety by reducing `any` usage and adding proper generics
4. **Styling Approach**: Standardize conditional styling with utility functions
5. **Data Fetching**: Implement consistent data fetching patterns

These improvements would enhance code quality while maintaining the existing functionality. 