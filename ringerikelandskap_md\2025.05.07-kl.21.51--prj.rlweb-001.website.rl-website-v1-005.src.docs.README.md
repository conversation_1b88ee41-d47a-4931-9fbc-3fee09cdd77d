# Source Code Documentation

This directory contains documentation specific to the source code implementation of the Ringerike Landskap website.

## Documentation Structure

The documentation in this directory focuses on code-level implementation details, while the main `/docs` directory contains higher-level feature documentation.

## Related Documentation

For comprehensive feature documentation, please refer to:

1. [**Contact Form Analytics**](../../docs/CONTACT_FORM_ANALYTICS.md) - Documentation of the enhanced contact form with advanced SEO and analytics metrics

## Implementation Notes

### Contact Form Analytics

The contact form in `src/sections/60-contact/index.tsx` has been enhanced with comprehensive client information tracking capabilities. This implementation:

1. Collects detailed user information including:
   - Device and browser information
   - Performance metrics
   - User behavior data
   - Engagement metrics
   - Conversion data
   - Marketing data

2. Uses a combination of:
   - Browser APIs for data collection
   - localStorage/sessionStorage for persistent tracking
   - Event listeners for user interaction tracking

3. Formats dates in Norwegian format (YYYY.MM.DD, Kl.HH:MM)

4. Sends the collected data along with the form submission to Formspree.io

For detailed implementation information, see the [Contact Form README](../sections/60-contact/README.md) and the [Contact Form Analytics Documentation](../../docs/CONTACT_FORM_ANALYTICS.md).
