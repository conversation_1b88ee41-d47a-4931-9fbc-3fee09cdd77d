# CONTEXT

You are writing system_message instruction templates specifically designed for AUTONOMOUS CONCISTENCY when used as input for autonomous coding assistants such as `vscode+cline` or `cursor ai`.

# OBJECTIVE

Please extract a LLM-OPTIMIZED `system_message` based on the MAXIMALLY HIGH-VALUE INSIGHTS from it - use it as a base to create a system instruction specifically designed to perform **safe and structured code consolidation for codebases such as the one below**:

    # `rl-website_web-001.md`

    ### Abstract-Thought-Chain-As-File-Structure

    ```
    ├── essential-context.md
    ├── 01-abstraction-root
    │   ├── 00-distilledContext.md
    │   └── 01-projectbrief.md
    ├── 02-context
    │   ├── 02-productContext.md
    │   └── 04-techContext.md
    ├── 03-structure-design
    │   ├── 03-systemPatterns.md
    │   ├── 05-structureMap.md
    │   └── 10-simplification-candidates.md
    ├── 04-process-tracking
    │   ├── 06-activeContext.md
    │   ├── 07-progress.md
    │   ├── 08-tasks.md
    │   └── 09-drift-monitor.md
    └── 05-evolution
        ├── 09-lineage-template.md
        └── 10-feature-first-organization.md
    ```

    ---

    #### `essential-context.md`

    ```markdown
        # RLWeb: Essential Context

        ## 1. Root Purpose & Mission


        ### Distilled Essence
        - **Purpose**: Digital showcase for Ringerike Landskap, connecting customers in the Ringerike region with authentic, personalized landscaping services through hyperlocal SEO focus.
        - **Identity**: Owner-driven landscaping with specialized skills (welding, corten steel) and personal investment reflected through clear component architecture in React/TypeScript/Tailwind.
        - **Value Proposition**: Expert local terrain knowledge combined with authentic customer relationships, delivering exceptional outdoor spaces through a modern, accessible, mobile-first website.

        ### Core Mission
        RLWeb serves as the digital representation of Ringerike Landskap AS (`https://ringerikelandskap.no`), connecting local customers in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik) with personalized, high-quality landscaping services. The website's primary purpose is to showcase the authentic, owner-driven approach to landscaping while implementing hyperlocal SEO strategies to ensure visibility to customers actively seeking these services.

        ### Critical Constraints
        1. **Authenticity**: Must genuinely reflect the personal investment and craftsmanship of the two owners who take pride in their work.
        2. **Technical Requirements**: Built with React 18, TypeScript, Vite, and Tailwind CSS.
        3. **UX Requirements**: Mobile-first responsive design, accessibility compliance (WCAG AA), optimized performance.
        4. **SEO Focus**: Hyperlocal SEO strategy targeting specified locations in the Ringerike region.
        5. **Content Management**: Content managed via structured data files without CMS integration.

        ### Key Features & Requirements
        1. **Eight Core Services**: Presentation of services (Kantstein, Ferdigplen, Støttemur, Hekk/beplantning, Cortenstål, Belegningsstein, Platting, Trapp/repo) with filtering capabilities.
        2. **Conversion Focus**: Prominent "Book Gratis Befaring" CTAs optimized for conversion.
        3. **Trust Building**: Testimonials and project showcases demonstrating past work and customer satisfaction.
        4. **Seasonal Adaptation**: Content that adapts based on current season to highlight relevant services.
        5. **Location Emphasis**: Clear indication of service areas and local expertise.

        ## 2. Value Context & User Needs

        ### Primary Users: Homeowners in Ringerike Region
        - **Need**: Find reliable local landscaping services with expertise in regional terrain and conditions
        - **Problem**: Difficulty identifying quality contractors with proven local experience
        - **Solution**: Showcase authentic, locally-focused portfolio with testimonials and region-specific content

        ### Secondary Users: Commercial Property Owners
        - **Need**: Professional landscaping services that enhance property value while ensuring durability in local climate
        - **Problem**: Finding service providers who can handle larger-scale projects with the same attention to detail as residential work
        - **Solution**: Demonstrate range of project scales and commercial applications through categorized portfolio

        ### User Journeys
        - **Discovery**: SEO-optimized content targeting hyperlocal search terms → Visual showcase of craftsmanship → Clear CTAs
        - **Consideration**: Service filtering by need/location/season → Testimonials and project examples → Detailed service information
        - **Return**: Seasonal content updates → Personal connection through owner profiles and direct contact

        ### Company Differentiation
        1. **Owner-Driven Service**: Direct involvement of owners in projects, providing personal accountability and investment
        2. **Specialized Skills**: Professional welding and corten steel expertise as unique service offerings
        3. **Local Knowledge**: Deep understanding of Ringerike region terrain, soil conditions, and climate challenges
        4. **Authentic Relationships**: Focus on exceeding customer expectations through thoughtful implementation

        ### Geographic & Seasonal Focus
        - **Core Service Area**: Ringerike region (Røyse, Hønefoss, Hole kommune, Jevnaker, Sundvollen, Vik)
        - **Seasonal Services**:
          - **Spring**: Focus on Hekk/Beplantning, Ferdigplen
          - **Summer**: Focus on Platting, Cortenstål, Belegningsstein
          - **Fall**: Focus on Støttemurer, Kantstein, Trapper/Repoer
          - **Winter**: Focus on Planning and Design services

        ## 3. Technical Context

        ### Core Stack
        - **Frontend**: React 18, TypeScript 5.5.3
        - **Build**: Vite 5.4.2
        - **Styling**: Tailwind CSS 3.4.1
        - **Routing**: React Router DOM 6.22.3
        - **Animation**: Framer Motion 12.5.0
        - **Icons**: Lucide React 0.344.0
        - **Utilities**: clsx 2.1.0, tailwind-merge 2.2.1
        - **SEO**: React Helmet Async 2.0.5

        ### Performance Targets
        - **First Contentful Paint**: < 1.2s
        - **Largest Contentful Paint**: < 2.5s
        - **Time to Interactive**: < 3.5s
        - **Cumulative Layout Shift**: < 0.1
        - **First Input Delay**: < 100ms

        ### Accessibility Requirements
        - **WCAG 2.1 AA** compliance
        - Keyboard navigation, screen reader support, proper color contrast
        - Visible focus states, responsive text, accessible forms

        ### Responsive Design
        - **Mobile**: < 640px (base styles)
        - **Tablet**: ≥ 640px (sm: prefix)
        - **Laptop**: ≥ 1024px (lg: prefix)
        - **Desktop**: ≥ 1280px (xl: prefix)

        ### Content Management
        - Static data files (TypeScript) for structured content
        - Type-safe data interfaces for all content types
        - Organized by domain (services, projects, testimonials)

        ## 4. System Architecture & Patterns

        ### Core Architectural Principles

        #### 1. Feature-First Organization
        - **Pattern**: Group code by domain feature rather than technical function
        - **Implementation**: Top-level directories organized around business features (services, projects, testimonials)
        - **Rationale**: Enhances maintainability by keeping related code together regardless of technical type

        #### 2. Composition Over Inheritance
        - **Pattern**: Build complex components by combining simpler ones rather than extending base classes
        - **Implementation**: Small, focused components with clear responsibilities combined to create feature components
        - **Rationale**: Improves flexibility, reusability, and testing while reducing coupling

        #### 3. Single Responsibility Components
        - **Pattern**: Each component should have one reason to change
        - **Implementation**: Small, focused components that do one thing well
        - **Rationale**: Enhances maintainability, readability, and testability

        #### 4. Unified Data Flow
        - **Pattern**: Unidirectional data flow using props-down, events-up model
        - **Implementation**: State lifted to appropriate level, passed down through props
        - **Rationale**: Predictable state management, easier debugging

        ### Component Architecture

        #### Component Hierarchy
        ```
        UI (Presentational) → Feature (Business Logic) → Page (Route)
        ```

        #### Layer Responsibilities
        1. **UI Components** (`/components/ui/`)
           - Generic, reusable UI elements with no business logic
           - Highly reusable, stateless when possible, styled with Tailwind

        2. **Feature Components** (`/features/{feature}/`)
           - Business-domain specific components combining UI components
           - Application-specific, may contain business logic, combine multiple UI components

        3. **Page Components** (`/pages/`)
           - Route-level components that compose features into complete pages
           - Connected to routing, handle data fetching, compose multiple features

        #### Key Component Patterns
        - **Variant Pattern**: Components accept `variant` prop to modify appearance/behavior
        - **Composition Pattern**: Components accept `children` prop for flexible content composition
        - **Conditional Rendering Pattern**: Components adapt display based on prop conditions
        - **Layout Components Pattern**: Components that handle pure layout concerns

        ### Data & State Management

        #### Data Sources
        - Static data files (`/src/data/`)
        - Custom hooks for data fetching (`/src/hooks/`)
        - Context for global state (`/src/lib/context/`)

        #### Data Management Patterns
        - **Data Fetching**: Custom hooks abstract data fetching logic
        - **Data Transformation**: Transform data at the boundaries close to API/data source
        - **Prop Drilling Minimization**: Content-specific React context for widely needed data

        #### State Management Patterns
        - **Local Component State**: React's `useState` for component-specific state
        - **Lifted State**: State lifted to common ancestor when shared between siblings
        - **Custom Hooks for Complex State**: Extract complex state logic into custom hooks

        ### Styling Architecture
        - **Utility-First Approach**: Compose styles using Tailwind utility classes directly in markup
        - **Component-Specific Abstractions**: Extract common patterns using composition
        - **Responsive Design Pattern**: Mobile-first with progressive enhancement
        - **Style Composition with cn/clsx**: Dynamic class composition based on props/state
        - **Theme Consistency**: Consistent color system, typography system, and spacing system

        ## 5. Current vs. Target Structure

        ### Current Structure Analysis
        The current RLWeb structure combines elements of both feature-based and technical separation with several issues:

        ```
        src/
        ├── _consolidated/        # Experimental consolidated structure
        ├── components/          # Generic and shared components
        ├── content/             # Content structure definitions
        ├── data/                # Static data files
        ├── features/            # Feature-based component organization
        ├── hooks/               # Custom React hooks
        ├── lib/                 # Shared utilities and configurations
        ├── pages/               # Page components
        ├── styles/              # Global styles
        ├── types/               # Type definitions (duplication issue)
        └── utils/               # Utility functions (duplication issue)
        ```

        ### Key Structure Issues
        1. **Duplication and Inconsistency**:
           - Hook placement across `/hooks` and `/lib/hooks`
           - Type definitions duplicated across `/lib/types` and `/types`
           - Utility functions split between `/lib/utils` and `/utils`
           - Component organization overlapping between `/components` and `/features`

        2. **Incomplete Feature-First Structure**:
           - Some features properly organized in `/features` directory
           - Others spread across `/components` with mixed organization
           - No clear distinction between UI components and feature components

        3. **Inconsistent Naming and Organization**:
           - Mix of singular and plural directory names
           - Unclear boundaries between shared and feature-specific components
           - Data layer organization follows different patterns for different domains

        ### Target Structure Design
        ```
        src/
        ├── App.tsx              # Root application component
        ├── main.tsx             # Application entry point
        ├── assets/              # Static assets
        ├── components/          # Shared, reusable UI components
        │   ├── ui/              # Primitive UI components
        │   └── layout/          # Layout components
        ├── features/            # Feature-specific code
        │   ├── common/          # Components, hooks, etc. shared across features
        │   ├── home/            # Homepage feature
        │   ├── projects/        # Projects feature
        │   ├── services/        # Services feature
        │   └── testimonials/    # Testimonials feature
        ├── hooks/               # Application-wide custom hooks
        ├── lib/                 # Application-wide utilities and configurations
        │   ├── api/             # API layer
        │   ├── config/          # Application configuration
        │   ├── context/         # React contexts
        │   ├── types/           # Core type definitions
        │   └── utils/           # Utility functions
        ├── data/                # Static data sources
        └── styles/              # Global styles
        ```

        ### Identified Bottlenecks
        1. **Technical Bottlenecks**:
           - Import path complexity
           - Duplicate hook implementation
           - Inconsistent component patterns

        2. **Structural Bottlenecks**:
           - Mixed organization models
           - Unclear component boundaries
           - Experimental structure fragments

        3. **Process Bottlenecks**:
           - Migration coordination

        ### High-Impact Technical Debt Items
        1. **Duplicate Type Definitions**: Types defined in multiple locations
        2. **Component Duplication**: Similar components with different implementations
        3. **Utility Function Fragmentation**: Utilities spread across different directories

        ## 7. Prioritized Tasks

        ### High Priority Tasks

        #### 1. Feature-First Structure Migration Planning
        **Goal**: Plan detailed migration path from current mixed structure to feature-first organization
        **Root Connection**: Structure directly impacts maintainability and ability to efficiently implement authentic representation of landscaping services
        **Effort**: Medium (3-5 hours)

        #### 2. Type System Consolidation
        **Goal**: Centralize and standardize type definitions to improve type safety and developer experience
        **Root Connection**: Strong typing ensures consistency in service and project representations
        **Effort**: Medium (2-4 hours)

        #### 3. Component Pattern Standardization
        **Goal**: Establish consistent component patterns to improve maintainability and developer experience
        **Root Connection**: Standardized components ensure consistent presentation of services and projects
        **Effort**: Medium (3-5 hours)

        ### Medium Priority Tasks

        #### 4. Path Alias Implementation
        **Goal**: Implement path aliases to simplify import paths and improve maintainability
        **Root Connection**: Improves developer efficiency, enabling faster feature implementation
        **Effort**: Low (1-2 hours)

        #### 5. Hook Consolidation
        **Goal**: Consolidate hooks into single location with consistent patterns
        **Root Connection**: Consistent data fetching and state management ensure reliable presentation
        **Effort**: Medium (2-3 hours)

        #### 6. Service/Project Feature Component Refactoring
        **Goal**: Refactor service and project components to follow feature-first organization
        **Root Connection**: Direct implementation of core service showcase functionality
        **Effort**: High (4-7 hours)


        ### Implementation Strategy
        1. Start with high-value, low-effort simplifications, e.g:
           - Path Alias Implementation
           - Hook Consolidation
           - Type Definition Consolidation

        2. Mid-term focus on pattern standardization, e.g:
           - Style Utility Standardization
           - Component Pattern Standardization
           - Data Management Standardization

        3. Long-term, tackle large-scale restructuring, e.g:
           - Feature Directory Restructuring
           - UI Component Library Consolidation

        ## 9. Evolution & Transformation Decisions

        ### Feature-First Organization Transformation

        #### Context
        The RLWeb codebase currently uses a mixed organizational approach with scattered code, unclear boundaries, navigation complexity, inconsistent patterns, and experimental structures.

        #### Insight
        The codebase naturally divides along business domain lines rather than technical function lines. Co-locating related code creates natural boundaries that align with how developers conceptualize the application and better reflects the company's service-oriented business model.

        #### Transformation
        1. Establish clear component hierarchy (UI → Feature → Page)
        2. Create dedicated feature directories for each core domain
        3. Implement consistent internal feature organization
        4. Consolidate shared resources (hooks, types, utilities)
        5. Execute phased migration strategy

        #### Impact
        - Improved developer experience (easier navigation, clearer mental model)
        - Enhanced maintainability (co-located code, clearer responsibilities)
        - Better scalability (well-defined feature boundaries)
        - Reduced cognitive load (consistent patterns, natural organization)

        #### Root Connection
        This transformation supports the project's root purpose by enhancing content presentation, supporting authenticity, improving SEO implementation, enabling faster evolution, and reflecting company values.

        ## 10. Structural Integrity Monitoring

        ### Drift Detection Criteria
        - **File Location Drift**: Components in incorrect directories, feature-specific code outside feature directories
        - **Pattern Drift**: Diverging component patterns, inconsistent interfaces, mixed styling approaches
        - **API Boundary Drift**: Direct imports across feature boundaries, inconsistent export patterns

        ### Current High-Drift Areas
        1. **Type Definitions**: Types scattered across multiple locations
        2. **Component Organization**: Mixed organization with overlapping responsibilities
        3. **Hook Placement**: Hooks in multiple locations with inconsistent patterns

        ### Drift Prevention Strategies
        1. **Immediate Actions**: Document current structure, establish refactoring priorities
        2. **Short-term Actions**: Implement automated validation, add structure documentation
        3. **Long-term Actions**: Consider linting rules, regular architecture reviews, maintain Memory Bank
    ```

    ---

    #### `01-abstraction-root\00-distilledContext.md`

    ```markdown
        # Distilled Context - RLWeb Essence

        - **Purpose**: Digital showcase for Ringerike Landskap, connecting customers in the Ringerike region with authentic, personalized landscaping services through hyperlocal SEO focus.
        - **Identity**: Owner-driven landscaping with specialized skills (welding, corten steel) and personal investment reflected through clear component architecture in React/TypeScript/Tailwind.
        - **Value Proposition**: Expert local terrain knowledge combined with authentic customer relationships, delivering exceptional outdoor spaces through a modern, accessible, mobile-first website.
    ```

    ---

    #### `01-abstraction-root\01-projectbrief.md`

    ```markdown
        # Project Brief - Ringerike Landskap Website (RLWeb)

        ## Core Mission
        RLWeb serves as the digital representation of Ringerike Landskap AS, connecting local customers in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik) with personalized, high-quality landscaping services. The website's primary purpose is to showcase the authentic, owner-driven approach to landscaping while implementing hyperlocal SEO strategies to ensure visibility to customers actively seeking these services.

        ## Critical Constraints
        1. **Authenticity**: The website must genuinely reflect the personal investment and craftsmanship of the two owners who take pride in their work and relationships with customers.
        2. **Technical Requirements**: Built with React 18, TypeScript, Vite, and Tailwind CSS.
        3. **UX Requirements**: Mobile-first responsive design, accessibility compliance (WCAG AA), optimized performance.
        4. **SEO Focus**: Hyperlocal SEO strategy targeting specified locations in the Ringerike region.
        5. **Content Management**: Content managed via structured data files without CMS integration.

        ## Core Value Proposition
        Ringerike Landskap AS offers exceptional landscaping services by combining:
        - Deep knowledge of local terrain and conditions in the Ringerike region
        - Personalized customer relationships with direct owner involvement
        - Specialized skills including professional welding and corten steel implementations
        - Eight core service offerings tailored to local needs and conditions

        ## Key Features & Requirements
        1. **Eight Core Services**: Presentation of services (Kantstein, Ferdigplen, Støttemur, Hekk/beplantning, Cortenstål, Belegningsstein, Platting, Trapp/repo) with filtering capabilities.
        2. **Conversion Focus**: Prominent "Book Gratis Befaring" CTAs optimized for conversion.
        3. **Trust Building**: Testimonials and project showcases demonstrating past work and customer satisfaction.
        4. **Seasonal Adaptation**: Content that adapts based on current season to highlight relevant services.
        5. **Location Emphasis**: Clear indication of service areas and local expertise.

        ## Target Audience
        - Primary: Homeowners, businesses, and property developers in the Ringerike region seeking professional landscaping services.
        - Secondary: Existing customers seeking additional services or reviewing projects.

        ## Success Metrics
        1. Increased qualified leads through contact forms and consultation requests
        2. Improved search engine visibility for local landscaping search terms
        3. Higher engagement with portfolio and services content
        4. Positive user feedback on website usability and content relevance
    ```

    ---

    #### `02-context\02-productContext.md`

    ```markdown
        # Product Context - RLWeb Value Alignment

        ## User Needs & Problems Solved

        ### Primary Users: Homeowners in Ringerike Region
        - **Need**: Find reliable local landscaping services with expertise in regional terrain and conditions
        - **Problem**: Difficulty identifying quality contractors with proven local experience
        - **Solution**: Showcase authentic, locally-focused portfolio with testimonials and region-specific content

        ### Secondary Users: Commercial Property Owners
        - **Need**: Professional landscaping services that enhance property value while ensuring durability in local climate
        - **Problem**: Finding service providers who can handle larger-scale projects with the same attention to detail as residential work
        - **Solution**: Demonstrate range of project scales and commercial applications through categorized portfolio

        ### Tertiary Users: Existing Customers
        - **Need**: Access to additional services and ability to share positive experiences
        - **Problem**: Maintaining ongoing relationship with service provider
        - **Solution**: Easy navigation to full service range and integrated testimonial system

        ## User Journeys Mapped to Root Value

        ### Discovery Journey
        - **Entry Point**: SEO-optimized content targeting hyperlocal search terms (aligns with _local focus_ value)
        - **Engagement**: Visual showcase of craftsmanship with emphasis on personalized approach (aligns with _authenticity_ value)
        - **Conversion**: Clear "Book Gratis Befaring" CTAs throughout the site (aligns with _customer relationship_ value)

        ### Consideration Journey
        - **Exploration**: Service filtering by need, location, or season (aligns with _tailored solutions_ value)
        - **Validation**: Testimonials and project examples showing actual results (aligns with _transparency_ value)
        - **Decision**: Detailed service information with evidence of local expertise (aligns with _regional expertise_ value)

        ### Return Journey
        - **Re-engagement**: Seasonal content updates promoting timely services (aligns with _relevant solutions_ value)
        - **Relationship**: Personal connection through owner profiles and direct contact options (aligns with _personal investment_ value)

        ## Operational Context

        ### Company Differentiation Points
        1. **Owner-Driven Service**: Direct involvement of owners in projects, providing personal accountability and investment
        2. **Specialized Skills**: Professional welding and corten steel expertise as unique service offerings
        3. **Local Knowledge**: Deep understanding of Ringerike region terrain, soil conditions, and climate challenges
        4. **Authentic Relationships**: Focus on exceeding customer expectations through thoughtful implementation

        ### Geographic Focus
        - **Core Service Area**: Ringerike region, specifically targeting:
          - Røyse (company base)
          - Hønefoss
          - Hole kommune
          - Jevnaker
          - Sundvollen
          - Vik
        - **Radius**: Approximately 20-50 km from base in Røyse

        ### Service Seasonality
        - **Spring**: Focus on Hekk/Beplantning, Ferdigplen
        - **Summer**: Focus on Platting, Cortenstål, Belegningsstein
        - **Fall**: Focus on Støttemurer, Kantstein, Trapper/Repoer
        - **Winter**: Focus on Planning and Design services

        ## Content Strategy Alignment

        ### Informational Architecture
        - Service categorization reflects the eight core service areas
        - Project showcasing emphasizes local relevance and seasonal appropriateness
        - Testimonials connect customer experiences back to specific service types and locations

        ### Conversion Strategy
        - Primary: Generate consultation requests through "Book Gratis Befaring" CTAs
        - Secondary: Build trust through authentic representation of work quality and customer relationships
        - Tertiary: Educate visitors on landscaping best practices for the Ringerike region

        ### Trust Mechanisms
        - Before/after project comparisons
        - Customer testimonials with location specificity
        - Owner profiles highlighting expertise and personal investment
        - Transparent communication of process and approach

        ## Root Goal Connection
        Every aspect of the website is designed to communicate the owners' personal investment in and passion for creating exceptional outdoor spaces specifically tailored to the Ringerike region, directly supporting the core mission of connecting local customers with authentic, personalized landscaping services.
    ```

    ---

    #### `02-context\04-techContext.md`

    ```markdown
        # Technical Context - RLWeb Implementation Constraints

        ## Core Technology Stack

        ### Frontend Framework
        - **React 18**
          - Leverages new concurrent features for improved performance
          - Functional components with hooks-based approach exclusively
          - Component composition pattern for UI building
          - Strict TypeScript integration

        ### Type System
        - **TypeScript 5.5.3**
          - Strict type checking enabled
          - Interface-based type definitions
          - Comprehensive type definitions for component props
          - Type safety throughout the application

        ### Build Tools
        - **Vite 5.4.2**
          - Modern build tooling for fast development experience
          - ESM-native with optimized production builds
          - Built-in code splitting and asset optimization
          - Environment variable support for configuration

        ### Styling
        - **Tailwind CSS 3.4.1**
          - Utility-first approach for styling
          - Custom design system extension through theme configuration
          - Integration with component patterns via composition
          - JIT compilation for optimized production builds

        ## Supporting Libraries

        ### Routing
        - **React Router DOM 6.22.3**
          - Component-based routing
          - URL parameters for dynamic pages
          - Nested routing support

        ### UI Enhancement
        - **Framer Motion 12.5.0**
          - Animation library for enhanced UI interactions
          - Performance-optimized animations
          - Motion components for transitions

        ### Icons & UI Components
        - **Lucide React 0.344.0**
          - Icon library with React components
          - Tree-shakable for production optimization

        ### Utility Libraries
        - **clsx 2.1.0 / tailwind-merge 2.2.1**
          - Conditional class composition
          - Tailwind class conflict resolution

        ### SEO & Document Head
        - **React Helmet Async 2.0.5**
          - Asynchronous head management
          - SEO optimization through metadata

        ## Development Tools

        ### Linting & Code Quality
        - **ESLint 9.9.1**
          - TypeScript-specific linting rules
          - React best practices enforcement
          - Code consistency rules

        ### Testing
        - Currently limited implementation
        - Plan for component testing with framework TBD

        ## Performance Constraints

        ### Target Metrics
        - **First Contentful Paint**: < 1.2s
        - **Largest Contentful Paint**: < 2.5s
        - **Time to Interactive**: < 3.5s
        - **Cumulative Layout Shift**: < 0.1
        - **First Input Delay**: < 100ms

        ### Mobile Performance
        - Critical focus due to high mobile traffic expectations
        - Optimization for low-end devices and potentially unstable connections
        - Responsive design implementation with performance in mind

        ### Asset Optimization
        - WebP format for images
        - Responsive image loading
        - Lazy loading implementation for below-the-fold content
        - SVG optimization for icons and simple illustrations

        ## Accessibility Requirements

        ### Compliance Target
        - **WCAG 2.1 AA** compliance
        - Focus on core accessibility principles: perceivable, operable, understandable, robust

        ### Specific Requirements
        - **Keyboard Navigation**: All interactive elements must be keyboard accessible
        - **Screen Reader Support**: Semantic HTML with appropriate ARIA attributes
        - **Color Contrast**: Meet AA requirements (4.5:1 for normal text, 3:1 for large text)
        - **Focus Indicators**: Visible focus states for all interactive elements
        - **Responsive Text**: Scalable text that remains readable at different sizes
        - **Form Accessibility**: Labels, error states, and instructions accessible to all users

        ## SEO Implementation

        ### Technical SEO
        - Semantic HTML structure
        - Schema.org structured data (LocalBusiness schema)
        - Meta tags with dynamic content
        - XML sitemap generation
        - Canonical URL implementation

        ### Hyperlocal SEO
        - Location-specific content targeted at Ringerike region
        - Local service area content optimization
        - Service-specific keywords aligned with business offerings

        ## Content Management

        ### Data Structure
        - Static data files (TypeScript) for structured content
        - Type-safe data interfaces for all content types
        - Organized by domain (services, projects, testimonials)

        ### Media Management
        - Organized image directories by category
        - Metadata for images stored in structured format
        - Standard sizes and formats for consistency

        ## Responsive Design Requirements

        ### Breakpoints
        - **Mobile**: < 640px (base styles)
        - **Tablet**: ≥ 640px (sm: prefix)
        - **Laptop**: ≥ 1024px (lg: prefix)
        - **Desktop**: ≥ 1280px (xl: prefix)

        ### Mobile-First Approach
        - Base styles target mobile devices
        - Progressive enhancement for larger screens
        - Touch-friendly UI elements (appropriate sizing, spacing)

        ### Layout Shifts
        - Fixed aspect ratios for images
        - Pre-allocated space for dynamic content
        - Careful management of asynchronously loaded content

        ## Browser Support

        ### Supported Browsers
        - Chrome (last 2 versions)
        - Firefox (last 2 versions)
        - Safari (last 2 versions)
        - Edge (last 2 versions)
        - iOS Safari (last 2 versions)
        - Android Chrome (last 2 versions)

        ### Progressive Enhancement
        - Core functionality works in all supported browsers
        - Enhanced functionality where supported
        - Graceful degradation for older browsers

        ## Integration Points

        ### External Services
        - No current external API integrations
        - Potential future integration with contact form submission service

        ### Analytics
        - Basic analytics implementation planned
        - GDPR-compliant tracking
        - Performance and user journey tracking

        ## Deployment & Environment

        ### Hosting Target
        - Static site hosting
        - CDN distribution
        - HTTPS required

        ### Build Process
        - Vite-based build pipeline
        - Environment-specific configurations
        - Optimized production assets

        ### CI/CD
        - Simple deployment workflow
        - Build verification before deployment

        ## Security Considerations

        ### Form Security
        - Input validation
        - CSRF protection
        - Rate limiting for form submissions

        ### Content Security
        - Content Security Policy implementation
        - Secure external resource loading

        ## Technical Debt Areas

        ### Identified Improvement Needs
        - Component organization: Unused component duplication
        - Code splitting optimization
        - Testing implementation
        - File structure consistency
        - Image optimization pipeline
        - Hook consolidation in lib/hooks directory
    ```

    ---

    #### `03-structure-design\03-systemPatterns.md`

    ```markdown
        # System Patterns - RLWeb Architectural Form

        ## Core Architectural Principles

        ### 1. Feature-First Organization
        - **Pattern**: Group code by domain feature rather than technical function
        - **Implementation**: Top-level directories organized around business features (services, projects, testimonials)
        - **Rationale**: Enhances maintainability by keeping related code together regardless of technical type
        - **Example**: `/src/features/services/` contains all service-related components, hooks, and utilities

        ### 2. Composition Over Inheritance
        - **Pattern**: Build complex components by combining simpler ones rather than extending base classes
        - **Implementation**: Small, focused components with clear responsibilities combined to create feature components
        - **Rationale**: Improves flexibility, reusability, and testing while reducing coupling
        - **Example**: `ServiceCard` combines with `ServiceGrid` to create a complete service listing feature

        ### 3. Single Responsibility Components
        - **Pattern**: Each component should have one reason to change
        - **Implementation**: Small, focused components that do one thing well
        - **Rationale**: Enhances maintainability, readability, and testability
        - **Example**: `Testimonial` component handles rendering a single testimonial, while `TestimonialSlider` handles navigation between testimonials

        ### 4. Unified Data Flow
        - **Pattern**: Unidirectional data flow using props-down, events-up model
        - **Implementation**: State lifted to appropriate level, passed down through props
        - **Rationale**: Predictable state management, easier debugging
        - **Example**: Page components fetch data and pass it down to feature components

        ## Component Architecture

        ### Component Hierarchy
        ```
        UI (Presentational) → Feature (Business Logic) → Page (Route)
        ```

        ### Layer Responsibilities

        1. **UI Components** (`/src/components/ui/`)
           - Purpose: Generic, reusable UI elements with no business logic
           - Examples: Button, Container, Hero, Card
           - Characteristics: Highly reusable, stateless when possible, styled with Tailwind

        2. **Feature Components** (`/src/features/{feature}/`)
           - Purpose: Business-domain specific components combining UI components
           - Examples: ServiceGrid, ProjectFilter, TestimonialSlider
           - Characteristics: Application-specific, may contain business logic, combine multiple UI components

        3. **Page Components** (`/src/pages/`)
           - Purpose: Route-level components that compose features into complete pages
           - Examples: HomePage, ServiceDetailPage, ProjectsPage
           - Characteristics: Connected to routing, handle data fetching, compose multiple features

        ### Shared Component Patterns

        1. **Variant Pattern**
           - **Pattern**: Components accept a `variant` prop to modify appearance/behavior
           - **Implementation**: `variant = 'default' | 'compact' | 'featured'` with conditional rendering
           - **Example**: `ServiceCard` renders differently based on variant

        2. **Composition Pattern**
           - **Pattern**: Components accept `children` prop for flexible content composition
           - **Implementation**: Component provides structure, consumer provides content
           - **Example**: `Container` component wraps any content with consistent spacing

        3. **Conditional Rendering Pattern**
           - **Pattern**: Components adapt display based on prop conditions
           - **Implementation**: Ternary operators or logical operators for simple conditions
           - **Example**: `{loading ? <Skeleton /> : <Content data={data} />}`

        4. **Layout Components Pattern**
           - **Pattern**: Components that handle pure layout concerns
           - **Implementation**: Flexible containers with consistent spacing/arrangement
           - **Example**: Grid layouts with responsive breakpoints

        ## Data Flow Architecture

        ### Data Sources
        - Static data files (`/src/data/`)
        - Custom hooks for data fetching (`/src/hooks/`)
        - Context for global state (`/src/lib/context/`)

        ### Data Management Patterns

        1. **Data Fetching Pattern**
           - **Pattern**: Custom hooks abstract data fetching logic
           - **Implementation**: `useData` hook for consistent loading, error, and data states
           - **Example**: `const { data, loading, error } = useData(getServiceAreas, [])`

        2. **Data Transformation Pattern**
           - **Pattern**: Transform data at the boundaries close to API/data source
           - **Implementation**: Utility functions normalize and prepare data for components
           - **Example**: Mapping raw service data to properly typed ServiceType objects

        3. **Prop Drilling Minimization**
           - **Pattern**: Content-specific React context for widely needed data
           - **Implementation**: Context providers at appropriate level of component tree
           - **Example**: Theme information provided through context

        ### State Management Patterns

        1. **Local Component State**
           - **Pattern**: Use React's `useState` for component-specific state
           - **Implementation**: State contained within components that need it
           - **Example**: Active tab in a TabPanel component

        2. **Lifted State**
           - **Pattern**: State lifted to common ancestor when shared between siblings
           - **Implementation**: State and state setters passed down as props
           - **Example**: Filter state managed by parent and passed to filter and results components

        3. **Custom Hooks for Complex State**
           - **Pattern**: Extract complex state logic into custom hooks
           - **Implementation**: Hooks combine multiple useState/useEffect calls into cohesive API
           - **Example**: `useMediaQuery` for responsive design decisions

        ## Styling Architecture

        ### Tailwind CSS Strategy

        1. **Utility-First Approach**
           - **Pattern**: Compose styles using utility classes directly in markup
           - **Implementation**: Tailwind CSS classes applied directly to elements
           - **Example**: `className="flex flex-col md:flex-row gap-4 p-4"`

        2. **Component-Specific Abstractions**
           - **Pattern**: Extract common patterns using composition
           - **Implementation**: Reusable components encapsulate common style patterns
           - **Example**: Card component that provides consistent styling

        3. **Responsive Design Pattern**
           - **Pattern**: Mobile-first with progressive enhancement
           - **Implementation**: Base styles for mobile, override at breakpoints
           - **Example**: `className="text-sm md:text-base lg:text-lg"`

        4. **Style Composition with cn/clsx**
           - **Pattern**: Dynamic class composition based on props/state
           - **Implementation**: `cn` utility combines classes conditionally
           - **Example**: `cn('base-class', variant === 'primary' ? 'bg-green-500' : 'bg-gray-500')`

        ### Theme Consistency

        1. **Color System**
           - Primary: green-500 (brand green)
           - Secondary: gray-600
           - Accent: Contextual based on service type
           - Background: white/gray-50

        2. **Typography System**
           - Headings: font-semibold, text-xl through text-4xl
           - Body: text-base/text-lg, text-gray-600
           - CTAs: font-medium

        3. **Spacing System**
           - Based on Tailwind's default spacing scale
           - Consistent container padding of px-4 sm:px-6 lg:px-8

        ## Responsive Design Architecture

        1. **Container Pattern**
           - **Pattern**: Centered content with max-width and responsive padding
           - **Implementation**: `Container` component with configurable max-width
           - **Example**: `<Container size="lg">content</Container>`

        2. **Grid System Pattern**
           - **Pattern**: Responsive grid layouts using Tailwind's grid utilities
           - **Implementation**: Column counts increase at breakpoints
           - **Example**: `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6`

        3. **Stack Pattern**
           - **Pattern**: Vertical spacing between elements
           - **Implementation**: Flex column with gap
           - **Example**: `flex flex-col gap-4`

        4. **Responsive Component Variants**
           - **Pattern**: Components adjust layout based on screen size
           - **Implementation**: Media query tailwind classes or useMediaQuery hook
           - **Example**: Cards stacked on mobile, side-by-side on desktop

        ## SEO & Accessibility Patterns

        1. **Semantic HTML Pattern**
           - **Pattern**: Proper HTML element usage for semantics and accessibility
           - **Implementation**: Appropriate heading levels, landmark regions, ARIA where needed
           - **Example**: Using `<section>`, `<article>`, proper heading hierarchy

        2. **Metadata Pattern**
           - **Pattern**: Consistent page metadata using Helmet
           - **Implementation**: Meta component for each page
           - **Example**: `<Meta title="Services" description="..." />`

        3. **Structured Data Pattern**
           - **Pattern**: Schema.org markup for enhanced search results
           - **Implementation**: JSON-LD structured data
           - **Example**: LocalBusiness schema for company info

        4. **Progressive Enhancement Pattern**
           - **Pattern**: Core functionality works without JS, enhanced with it
           - **Implementation**: Server-rendered content or static export as base
           - **Example**: Navigation accessible without JS, enhanced with transitions when available

        ## Performance Patterns

        1. **Code Splitting Pattern**
           - **Pattern**: Split code by route for faster initial load
           - **Implementation**: Vite's dynamic import for route-based code splitting
           - **Example**: Lazy loading page components

        2. **Asset Optimization Pattern**
           - **Pattern**: Optimize images and other assets
           - **Implementation**: WebP format, responsive images, lazy loading
           - **Example**: `<img loading="lazy" srcset="..." sizes="..." />`

        3. **Component Lazy Loading Pattern**
           - **Pattern**: Defer loading of off-screen components
           - **Implementation**: Intersection component for viewport detection
           - **Example**: `<Intersection onIntersect={() => setVisible(true)}>`

        ## Error Handling Patterns

        1. **Graceful Degradation Pattern**
           - **Pattern**: Provide fallbacks when ideal content isn't available
           - **Implementation**: Default props, error states, loading states
           - **Example**: Skeleton components during loading

        2. **Error Boundary Pattern**
           - **Pattern**: Catch and handle errors at component boundaries
           - **Implementation**: React Error Boundaries
           - **Example**: Error boundary around feature components

        ## Seasonal Context Pattern

        1. **Contextual Content Adaptation**
           - **Pattern**: Content adapts based on current season
           - **Implementation**: Season detection in context, conditional content rendering
           - **Example**: Highlighting season-appropriate services on homepage
    ```

    ---

    #### `03-structure-design\05-structureMap.md`

    ```markdown
        # Structure Map - RLWeb Current vs. Target Structure

        ## Current File Structure Analysis

        The current RLWeb structure combines elements of both feature-based and technical separation. While there are promising organizational patterns in place, several areas require refinement to achieve better alignment with core architectural principles.

        ### Current High-Level Structure

        ```
        src/
        ├── _consolidated/        # Experimental consolidated structure
        ├── components/          # Generic and shared components
        │   ├── layout/          # Layout components (Header, Footer, etc.)
        │   ├── projects/        # Project-specific components
        │   ├── seo/             # SEO-related components
        │   ├── shared/          # Shared utility components
        │   ├── testimonials/    # Testimonial-specific components
        │   └── ui/              # General UI components
        ├── content/             # Content structure definitions
        ├── data/                # Static data files (services, projects, testimonials)
        ├── docs/                # Documentation
        ├── features/            # Feature-based component organization
        │   ├── home/            # Homepage-specific features
        │   ├── projects/        # Projects feature components
        │   ├── services/        # Services feature components
        │   └── testimonials/    # Testimonials feature components
        ├── hooks/               # Custom React hooks
        ├── lib/                 # Shared utilities and configurations
        │   ├── api/             # API interfaces
        │   ├── config/          # Configuration files
        │   ├── context/         # React contexts
        │   ├── hooks/           # More hooks (duplication issue)
        │   ├── types/           # Type definitions
        │   └── utils/           # Utility functions
        ├── pages/               # Page components
        ├── styles/              # Global styles
        ├── types/               # More type definitions (duplication issue)
        └── utils/               # More utility functions (duplication issue)
        ```

        ## Key Structure Issues

        ### 1. Duplication and Inconsistency

        - **Hook Placement**: Hooks scattered across `/hooks` and `/lib/hooks`
        - **Type Definitions**: Type definitions duplicated across `/lib/types` and `/types`
        - **Utility Functions**: Utilities split between `/lib/utils` and `/utils`
        - **Component Organization**: Overlapping responsibilities between `/components` and `/features`

        ### 2. Incomplete Feature-First Structure

        - Some features properly organized in `/features` directory
        - Others spread across `/components` with mixed technical/domain organization
        - No clear distinction between UI components and feature components

        ### 3. Inconsistent Naming and Organization

        - Mix of singular and plural directory names
        - Unclear boundaries between shared and feature-specific components
        - Data layer organization follows different patterns for different domains

        ### 4. Experimental Directories

        - `_consolidated` directory indicates an attempt at reorganization without full implementation
        - Multiple patterns existing simultaneously creates cognitive overhead

        ## Target Structure Design

        The target structure aims to establish a clear, feature-first organization while maintaining clean separation of concerns. The goal is to make the codebase more intuitive, reduce duplication, and improve maintainability.

        ### Target High-Level Structure

        ```
        src/
        ├── App.tsx              # Root application component
        ├── main.tsx             # Application entry point
        ├── assets/              # Static assets (might include some current /public contents)
        │   ├── icons/           # SVG icons and graphical elements
        │   └── images/          # Static images not tied to content
        ├── components/          # Shared, reusable UI components
        │   ├── ui/              # Primitive UI components
        │   │   ├── Button.tsx
        │   │   ├── Card.tsx
        │   │   ├── Container.tsx
        │   │   ├── Hero.tsx
        │   │   └── ...
        │   └── layout/          # Layout components
        │       ├── Header.tsx
        │       ├── Footer.tsx
        │       ├── Meta.tsx
        │       └── ...
        ├── features/            # Feature-specific code
        │   ├── common/          # Components, hooks, etc. shared across features
        │   ├── home/            # Homepage feature
        │   │   ├── components/  # Components specific to homepage
        │   │   ├── hooks/       # Hooks specific to homepage
        │   │   └── index.tsx    # Main page component
        │   ├── projects/        # Projects feature
        │   │   ├── components/  # Project-specific components
        │   │   ├── hooks/       # Project-specific hooks
        │   │   ├── types.ts     # Project-specific type extensions
        │   │   ├── utils.ts     # Project-specific utilities
        │   │   └── index.tsx    # Projects page component
        │   ├── services/        # Services feature
        │   └── testimonials/    # Testimonials feature
        ├── hooks/               # Application-wide custom hooks
        ├── lib/                 # Application-wide utilities and configurations
        │   ├── api/             # API layer
        │   ├── config/          # Application configuration
        │   ├── context/         # React contexts
        │   ├── types/           # Core type definitions
        │   └── utils/           # Utility functions
        ├── data/                # Static data sources
        └── styles/              # Global styles
        ```

        ## Migration Path

        ### Phase 1: Consolidation of Duplicated Resources

        1. **Consolidate Hook Definitions**:
           - Move all hooks to `/hooks` directory
           - Organize by domain where appropriate
           - Update imports throughout the application

        2. **Consolidate Type Definitions**:
           - Move all shared types to `/lib/types`
           - Feature-specific types belong with their features
           - Ensure consistent naming and organization

        3. **Consolidate Utility Functions**:
           - Move all shared utilities to `/lib/utils`
           - Feature-specific utilities belong with their features

        ### Phase 2: Feature-First Reorganization

        1. **Establish Feature Directory Structure**:
           - Identify all core features: home, services, projects, testimonials
           - Create consistent subdirectory structure within each feature
           - Map existing components to their appropriate feature

        2. **Move Components to Feature Directories**:
           - Relocate domain-specific components from `/components` to appropriate feature directories
           - Update imports throughout the application

        3. **Clean Up Common Components**:
           - Review remaining components in `/components`
           - Ensure all are truly shared/reusable across features
           - Organize into logical categories

        ### Phase 3: Content and Data Organization

        1. **Content Data Consolidation**:
           - Consolidate `/content` and `/data` directories
           - Organize data files to align with features
           - Ensure consistent naming and structure

        ### Phase 4: Structure Optimization

        1. **Remove Experimental Directories**:
           - Once migration is complete, remove `_consolidated` directory
           - Ensure no references remain

        2. **Documentation Updates**:
           - Update code documentation to reflect new structure
           - Create architecture documentation for onboarding

        ## Structure Justification

        ### Why Feature-First Organization?

        - **Benefit 1: Locality of Code**: Related code lives together, making it easier to understand and modify features
        - **Benefit 2: Scalability**: New features can be added without affecting existing ones
        - **Benefit 3: Encapsulation**: Features can manage their own internal structure while exposing a clean API
        - **Benefit 4: Team Collaboration**: Teams can work on different features with minimal conflicts

        ### Why Consolidate Shared Resources?

        - **Benefit 1: DRY Principle**: Removes duplication of similar functionality
        - **Benefit 2: Consistent Patterns**: Enforces consistent approaches to common problems
        - **Benefit 3: Discoverability**: Makes it easier to find existing solutions rather than creating new ones
        - **Benefit 4: Maintenance**: Central location for updating shared patterns

        ## Impact Assessment

        ### Positive Impacts

        - **Improved Developer Experience**: Clear structure makes the codebase easier to navigate and understand
        - **Reduced Cognitive Load**: Consistent patterns reduce the need to context-switch between different organizational models
        - **Better Scalability**: Clear boundaries make it easier to add new features without refactoring
        - **Easier Onboarding**: New developers can understand the codebase more quickly

        ### Potential Challenges

        - **Migration Effort**: Significant refactoring required to implement the new structure
        - **Import Updates**: Many import paths will need to be updated
        - **Testing Impact**: Tests will need to be updated to reflect new structure
        - **Learning Curve**: Team members will need to adapt to the new structure

        ## Structure Evolution Tracking

        This section will be updated as the structure evolves. Key milestones and decisions in the migration process will be documented here.

        ### Current Status
        - Structure analysis complete
        - Migration plan established
        - Initial reorganization of _consolidated directory as experimental proof of concept

        ### Next Steps
        - Begin Phase 1: Consolidation of duplicated resources
        - Document detailed component mapping for feature reorganization
        - Create automated tests to ensure functionality remains intact during reorganization
    ```

    ---

    #### `03-structure-design\10-simplification-candidates.md`

    ```markdown
        # Simplification Candidates - RLWeb Structure Optimization

        ## Simplification Scoring Framework

        Each simplification candidate is evaluated based on the following criteria:

        - **Impact Score** (1-5): How much the simplification improves maintainability, readability, or performance
          - 1: Minimal improvement, mostly aesthetic
          - 3: Moderate improvement to developer experience or performance
          - 5: Transformative improvement with major maintainability benefits

        - **Implementation Effort** (1-5): How difficult the simplification is to implement
          - 1: Simple, low-risk change with minimal testing needed
          - 3: Moderate complexity requiring careful testing
          - 5: Complex, high-risk change requiring extensive testing

        - **Structural Alignment** (1-5): How well the simplification aligns with target architecture
          - 1: Tangential to architectural goals
          - 3: Supports architectural goals
          - 5: Directly implements key architectural principles

        - **Disruption Risk** (1-5): Potential for introducing bugs or breaking existing functionality
          - 1: High risk, many dependencies or complex logic
          - 3: Moderate risk, some dependencies
          - 5: Low risk, isolated change

        - **Value Score**: (Impact + Structural Alignment) / (Implementation Effort / Disruption Risk)
          - Higher value indicates better return on investment

        ## High-Value Simplification Candidates

        ### 1. Path Alias Implementation

        **Description**: Implement path aliases to simplify import paths and reduce complexity

        **Current Complexity**:
        ```typescript
        import { ServiceType } from '../../../lib/types';
        import { useData } from '../../../hooks/useData';
        ```

        **Target Simplification**:
        ```typescript
        import { ServiceType } from '@/lib/types';
        import { useData } from '@/hooks/useData';
        ```

        **Scoring**:
        - **Impact**: 4 - Significantly improves readability and maintainability
        - **Implementation Effort**: 2 - Relatively straightforward with TypeScript and Vite support
        - **Structural Alignment**: 3 - Supports cleaner imports but doesn't alter structure
        - **Disruption Risk**: 4 - Low risk with comprehensive search/replace
        - **Value Score**: (4 + 3) / (2 / 4) = 14 - Very high value

        **Implementation Steps**:
        1. Update `tsconfig.json` with path aliases
        2. Update Vite configuration to support aliases
        3. Systematically replace imports throughout codebase
        4. Verify functionality with comprehensive testing

        ---

        ### 2. Type Definition Consolidation

        **Description**: Consolidate duplicate type definitions into a single source of truth

        **Current Complexity**:
        - Types defined in both `/types` and `/lib/types`
        - Duplicated definitions for common entities
        - Inconsistent naming and structure

        **Target Simplification**:
        - Single source of truth in `/lib/types`
        - Clear naming conventions
        - Feature-specific extensions where needed

        **Scoring**:
        - **Impact**: 5 - Critical for type safety and consistency
        - **Implementation Effort**: 3 - Requires careful analysis and migration
        - **Structural Alignment**: 5 - Core architectural principle
        - **Disruption Risk**: 3 - Moderate risk due to widespread usage
        - **Value Score**: (5 + 5) / (3 / 3) = 10 - High value

        **Implementation Steps**:
        1. Map all type definitions and usages
        2. Design consolidated type hierarchy
        3. Implement consolidated types
        4. Update imports throughout codebase
        5. Verify type correctness across application

        ---

        ### 3. Component Pattern Standardization

        **Description**: Standardize component patterns, especially for variant handling and props

        **Current Complexity**:
        - Inconsistent handling of component variants
        - Mixed approaches to props typing
        - Varying style application methods

        **Target Simplification**:
        - Consistent pattern for variant handling
        - Standardized props interface patterns
        - Uniform styling approach

        **Scoring**:
        - **Impact**: 4 - Improves maintainability and consistency
        - **Implementation Effort**: 4 - Significant refactoring required
        - **Structural Alignment**: 4 - Supports architectural principles
        - **Disruption Risk**: 3 - Moderate risk due to component dependencies
        - **Value Score**: (4 + 4) / (4 / 3) = 6 - Moderate value

        **Implementation Steps**:
        1. Document target patterns
        2. Create exemplar components
        3. Incrementally refactor components
        4. Update documentation
        5. Monitor for regressions

        ---

        ### 4. Feature Directory Restructuring

        **Description**: Migrate to consistent feature-first organization with clear component boundaries

        **Current Complexity**:
        - Mixed organization between `/components` and `/features`
        - Unclear boundaries between UI and feature components
        - Inconsistent internal structure within features

        **Target Simplification**:
        - Clear feature-first organization
        - Consistent internal structure within features
        - Clean separation of UI components

        **Scoring**:
        - **Impact**: 5 - Transformative improvement to codebase organization
        - **Implementation Effort**: 5 - Major restructuring effort
        - **Structural Alignment**: 5 - Core architectural principle
        - **Disruption Risk**: 2 - High risk due to extensive changes
        - **Value Score**: (5 + 5) / (5 / 2) = 4 - Moderate value but high impact

        **Implementation Steps**:
        1. Create detailed migration plan
        2. Implement feature by feature
        3. Update imports throughout codebase
        4. Comprehensive testing
        5. Update documentation

        ---

        ### 5. Hook Consolidation

        **Description**: Consolidate hooks into single location with consistent patterns

        **Current Complexity**:
        - Hooks spread across `/hooks` and `/lib/hooks`
        - Inconsistent naming and implementation patterns
        - Potential duplication of functionality

        **Target Simplification**:
        - Single location for shared hooks
        - Consistent naming and implementation patterns
        - Clear documentation of hook purposes

        **Scoring**:
        - **Impact**: 3 - Improves hook discoverability and consistency
        - **Implementation Effort**: 2 - Relatively straightforward refactoring
        - **Structural Alignment**: 4 - Supports architectural principles
        - **Disruption Risk**: 3 - Moderate risk due to component dependencies
        - **Value Score**: (3 + 4) / (2 / 3) = 10.5 - High value

        **Implementation Steps**:
        1. Analyze all hooks and their usages
        2. Design consolidated hook APIs
        3. Implement in target location
        4. Update imports
        5. Add documentation

        ---

        ## Medium-Value Simplification Candidates

        ### 6. UI Component Library Consolidation

        **Description**: Consolidate duplicate UI components and establish clear component library

        **Current Complexity**:
        - Similar UI components implemented multiple times
        - Inconsistent component interfaces
        - No clear component library structure

        **Target Simplification**:
        - Single implementation of common UI patterns
        - Consistent component interfaces
        - Well-organized component library

        **Scoring**:
        - **Impact**: 4 - Improves consistency and reduces duplication
        - **Implementation Effort**: 4 - Significant refactoring required
        - **Structural Alignment**: 3 - Supports architectural principles
        - **Disruption Risk**: 2 - High risk due to widespread usage
        - **Value Score**: (4 + 3) / (4 / 2) = 3.5 - Medium value

        **Implementation Steps**:
        1. Audit existing components
        2. Identify patterns for consolidation
        3. Design unified component interfaces
        4. Implement consolidated components
        5. Replace throughout codebase

        ---

        ### 7. Style Utility Standardization

        **Description**: Standardize Tailwind usage patterns and extract common style compositions

        **Current Complexity**:
        - Inconsistent Tailwind usage patterns
        - Duplicate style combinations
        - Mixed approaches to style conditional logic

        **Target Simplification**:
        - Consistent utility usage patterns
        - Common style combinations extracted
        - Standard approach to conditional styling

        **Scoring**:
        - **Impact**: 3 - Improves visual consistency and maintainability
        - **Implementation Effort**: 3 - Moderate refactoring effort
        - **Structural Alignment**: 3 - Supports architectural principles
        - **Disruption Risk**: 4 - Low risk, mostly isolated changes
        - **Value Score**: (3 + 3) / (3 / 4) = 8 - Medium-high value

        **Implementation Steps**:
        1. Document standard patterns
        2. Extract common style combinations
        3. Refactor components incrementally
        4. Update style documentation

        ---

        ### 8. Data Management Standardization

        **Description**: Standardize data management patterns across features

        **Current Complexity**:
        - Inconsistent data fetching patterns
        - Mixed approaches to data transformation
        - Variable error handling

        **Target Simplification**:
        - Consistent data fetching patterns
        - Standard approach to data transformation
        - Uniform error handling

        **Scoring**:
        - **Impact**: 3 - Improves predictability and maintainability
        - **Implementation Effort**: 3 - Moderate refactoring effort
        - **Structural Alignment**: 3 - Supports architectural principles
        - **Disruption Risk**: 3 - Moderate risk due to data dependencies
        - **Value Score**: (3 + 3) / (3 / 3) = 6 - Medium value

        **Implementation Steps**:
        1. Document standard patterns
        2. Refactor data management in high-priority features
        3. Extend to other features
        4. Verify data consistency

        ---

        ## Low-Value Simplification Candidates

        ### 9. Experimental Directory Cleanup

        **Description**: Clean up experimental `_consolidated` directory

        **Current Complexity**:
        - Experimental directory with partial migration attempts
        - Potential duplication of functionality
        - Mixed approach confuses developers

        **Target Simplification**:
        - Remove or fully integrate experimental code
        - Eliminate redundant implementations
        - Clear guidance on approved patterns

        **Scoring**:
        - **Impact**: 2 - Mostly removes confusion
        - **Implementation Effort**: 2 - Relatively straightforward
        - **Structural Alignment**: 2 - Supporting task rather than core architecture
        - **Disruption Risk**: 4 - Low risk as experimental code
        - **Value Score**: (2 + 2) / (2 / 4) = 8 - Medium value

        **Implementation Steps**:
        1. Audit experimental directory
        2. Identify valuable patterns
        3. Integrate or remove
        4. Update documentation

        ---

        ### 10. Documentation Consolidation

        **Description**: Consolidate and standardize documentation

        **Current Complexity**:
        - Documentation spread across multiple locations
        - Inconsistent formats and detail levels
        - Some outdated information

        **Target Simplification**:
        - Centralized documentation strategy
        - Consistent format and detail level
        - Up-to-date information

        **Scoring**:
        - **Impact**: 2 - Improves developer onboarding
        - **Implementation Effort**: 2 - Relatively straightforward
        - **Structural Alignment**: 1 - Supporting task rather than core architecture
        - **Disruption Risk**: 5 - Very low risk
        - **Value Score**: (2 + 1) / (2 / 5) = 7.5 - Medium value

        **Implementation Steps**:
        1. Audit existing documentation
        2. Define documentation standards
        3. Consolidate and update
        4. Implement in central location

        ---

        ## Simplification Value Summary

        | Simplification | Impact | Effort | Alignment | Risk | Value Score | Priority |
        |----------------|--------|--------|-----------|------|-------------|----------|
        | 1. Path Alias Implementation | 4 | 2 | 3 | 4 | 14.0 | 1 |
        | 5. Hook Consolidation | 3 | 2 | 4 | 3 | 10.5 | 2 |
        | 2. Type Definition Consolidation | 5 | 3 | 5 | 3 | 10.0 | 3 |
        | 7. Style Utility Standardization | 3 | 3 | 3 | 4 | 8.0 | 4 |
        | 9. Experimental Directory Cleanup | 2 | 2 | 2 | 4 | 8.0 | 5 |
        | 10. Documentation Consolidation | 2 | 2 | 1 | 5 | 7.5 | 6 |
        | 3. Component Pattern Standardization | 4 | 4 | 4 | 3 | 6.0 | 7 |
        | 8. Data Management Standardization | 3 | 3 | 3 | 3 | 6.0 | 8 |
        | 4. Feature Directory Restructuring | 5 | 5 | 5 | 2 | 4.0 | 9 |
        | 6. UI Component Library Consolidation | 4 | 4 | 3 | 2 | 3.5 | 10 |

        ## Implementation Strategy

        The optimal approach is to prioritize high-value, low-effort simplifications first:

        1. Path Alias Implementation - Immediate quick win
        2. Hook Consolidation - High-value, relatively straightforward
        3. Type Definition Consolidation - Critical architectural foundation

        These three simplifications provide the foundation for larger restructuring efforts and create immediate improvements to developer experience and code quality.

        Mid-term focus should be on pattern standardization:
        4. Style Utility Standardization
        5. Component Pattern Standardization
        6. Data Management Standardization

        Long-term, large-scale restructuring can be approached:
        7. Feature Directory Restructuring
        8. UI Component Library Consolidation

        Supporting tasks can be addressed opportunistically:
        9. Experimental Directory Cleanup
        10. Documentation Consolidation
    ```

    ---

    #### `04-process-tracking\06-activeContext.md`

    ```markdown
        # Active Context - RLWeb Current Focus

        ## Current Focus Areas

        ### 1. Codebase Analysis & Structure Design
        - Completed initial analysis of project structure and patterns
        - Identified key issues with current organization: duplication, inconsistent naming, mixed organization approaches
        - Designed target structure with feature-first organization and clear separation of concerns
        - Created migration path with phased approach to minimize disruption

        ### 2. Memory Bank Establishment
        - Created initial memory bank structure with hierarchical organization
        - Populated core files to capture project essence, patterns, and structure
        - Established foundation for ongoing project management and knowledge capture

        ### 3. Component Consolidation Planning
        - Identified pattern duplication across the codebase
        - Located structural inconsistencies requiring consolidation
        - Mapped initial component relationships and dependencies
        - Identified opportunities for simplification

        ### 4. Root Purpose Alignment
        - Verified that structure maps back to core purpose: authentic representation of owner-driven landscaping company
        - Confirmed technical implementation supports hyperlocal SEO focus on Ringerike region
        - Validated that component architecture supports showcasing personalized approach and craftsmanship

        ## Current Decisions

        ### Structure Reorganization Strategy
        - **Decision**: Adopt feature-first organization with clean separation of shared components
        - **Rationale**: Enhances maintainability, scalability, and team collaboration
        - **Constraints**: Must maintain current functionality during migration
        - **Implementation Approach**: Phased migration to minimize disruption

        ### Component Hierarchy Definition
        - **Decision**: Establish clear three-tier component hierarchy: UI → Feature → Page
        - **Rationale**: Supports composition pattern and separation of concerns
        - **Implementation**: Document patterns and gradually refactor components to match

        ### Type System Consolidation
        - **Decision**: Consolidate duplicate type definitions into central location with feature-specific extensions
        - **Rationale**: Improves maintainability while preserving domain-specific organization
        - **Implementation**: Map all type usage and consolidate with careful import updates

        ### Styling Architecture Standardization
        - **Decision**: Standardize Tailwind usage patterns across components
        - **Rationale**: Creates visual consistency and reduces CSS complexity
        - **Implementation**: Document patterns and refactor component styles incrementally

        ## Identified Bottlenecks

        ### 1. Technical Bottlenecks
        - **Import Path Complexity**: Current structure creates convoluted import paths
          - **Impact**: Reduces developer efficiency, increases risk of errors
          - **Solution Strategy**: Implement path aliases in TypeScript configuration

        - **Duplicate Hook Implementation**: Hooks defined in multiple locations
          - **Impact**: Risk of inconsistent behavior, maintenance challenges
          - **Solution Strategy**: Consolidate hooks with consistent API design

        - **Inconsistent Component Patterns**: Multiple approaches to similar UI problems
          - **Impact**: Increases cognitive load, reduces maintainability
          - **Solution Strategy**: Document standard patterns and refactor gradually

        ### 2. Structural Bottlenecks
        - **Mixed Organization Models**: Combination of feature-based and technical separation
          - **Impact**: Confusing navigation, difficult to find related code
          - **Solution Strategy**: Migrate to consistent feature-first organization

        - **Unclear Component Boundaries**: Overlapping responsibilities between components
          - **Impact**: Redundant code, difficulty understanding component purposes
          - **Solution Strategy**: Define clear component responsibilities and boundaries

        - **Experimental Structure Fragments**: Partial migration attempts (`_consolidated` directory)
          - **Impact**: Multiple patterns create confusion
          - **Solution Strategy**: Complete migration or remove experimental directories

        ### 3. Process Bottlenecks
        - **Migration Coordination**: Ensuring consistent approach during restructuring
          - **Impact**: Risk of inconsistent implementation or regression
          - **Solution Strategy**: Detailed migration plan with validation steps

        ## Technical Debt Analysis

        ### High-Impact Debt Items
        1. **Duplicate Type Definitions**: Types defined in multiple locations
           - **Risk**: Type inconsistencies leading to runtime errors
           - **Effort**: Medium - requires careful mapping and consolidation
           - **Priority**: High - foundation for other improvements

        2. **Component Duplication**: Similar components with different implementations
           - **Risk**: Maintenance burden, inconsistent UI
           - **Effort**: High - requires interface standardization
           - **Priority**: High - critical for maintainability

        3. **Utility Function Fragmentation**: Utilities spread across different directories
           - **Risk**: Duplicate implementations, inconsistent APIs
           - **Effort**: Medium - requires careful consolidation and testing
           - **Effort**: Medium - isolated with clear boundaries for migration

        ### Medium-Impact Debt Items
        1. **Inconsistent File Naming**: Mix of naming conventions
           - **Risk**: Confusion and difficulty finding files
           - **Effort**: Low - mechanical changes with tooling support
           - **Priority**: Medium - improves developer experience

        2. **Data Management Inconsistency**: Different approaches to data handling
           - **Risk**: Unpredictable data flow, harder to debug
           - **Effort**: Medium - requires pattern standardization
           - **Priority**: Medium - improves predictability

        ### Low-Impact Debt Items
        1. **Documentation Gaps**: Missing or outdated documentation
           - **Risk**: Knowledge loss, onboarding friction
           - **Effort**: Low to Medium - can be addressed incrementally
           - **Priority**: Low - can be improved gradually

        ## Compression Check Performed

        This active context file resists compression because it captures the current focus areas, decisions, and issues that require attention for the RLWeb project. It serves as operational guidance for the immediate next steps in the project's evolution and cannot be abstracted further without losing critical context needed for day-to-day work. The bottlenecks and technical debt items are specific instances that need monitoring rather than patterns that can be elevated to system patterns.
    ```

    ---

    #### `04-process-tracking\07-progress.md`

    ```markdown
        # Progress Tracking - RLWeb Status & Entropy Check

        ## Project Milestones

        ### Completed Milestones

        #### 1. Project Analysis & Setup (April 28, 2025)
        - ‚úÖ Analyzed existing codebase structure and patterns
        - ‚úÖ Identified key architectural issues and improvement opportunities
        - ‚úÖ Established Memory Bank system for structural intelligence
        - ‚úÖ Created core pattern documentation and architectural direction
        - ‚úÖ Designed target structure with migration plan

        ### In-Progress Milestones

        #### 1. Structure Reorganization Foundation (Phase 1)
        - ‚è≥ Hook consolidation (0%)
        - ‚è≥ Type definition consolidation (0%)
        - ‚è≥ Utility function consolidation (0%)
        - ‚è≥ Path alias configuration (0%)

        #### 2. Feature-First Migration (Phase 2)
        - ‚è≥ Feature directory structure establishment (0%)
        - ‚è≥ Component migration mapping (0%)
        - ‚è≥ Component relocation implementation (0%)

        #### 3. Component Pattern Standardization
        - ‚è≥ UI component pattern documentation (0%)
        - ‚è≥ Feature component pattern documentation (0%)
        - ‚è≥ Styling pattern standardization (0%)

        ### Planned Milestones

        #### 1. Structure Optimization (Phase 3 & 4)
        - üìä Content data consolidation
        - üìä Experimental directory cleanup
        - üìä Documentation updates

        #### 2. Performance & Accessibility Enhancement
        - üìä Performance optimization implementation
        - üìä Accessibility compliance improvements
        - üìä SEO implementation verification

        ## Metrics Tracking

        ### Code Quality Metrics

        | Metric | Initial (Apr 28) | Current | Target | Status |
        |--------|-----------------|---------|--------|--------|
        | Component Duplication | High | High | Low | Not Started |
        | Type Consistency | Low | Low | High | Not Started |
        | Import Path Complexity | High | High | Low | Not Started |
        | Pattern Consistency | Low | Low | High | Not Started |
        | Code Organization Score | Low | Low | High | Not Started |

        ### Structure Metrics

        | Metric | Initial (Apr 28) | Current | Target | Status |
        |--------|-----------------|---------|--------|--------|
        | Feature Encapsulation | Partial | Partial | Complete | Not Started |
        | Component Reusability | Medium | Medium | High | Not Started |
        | Directory Depth | Inconsistent | Inconsistent | Standardized | Not Started |
        | Naming Consistency | Low | Low | High | Not Started |

        ## Technical Debt Ledger

        ### Added Debt

        * None added in current session

        ### Paid Debt

        * None paid off in current session

        ### Current Debt Items

        | Item | Impact | Effort | Priority | Status |
        |------|--------|--------|----------|--------|
        | Duplicate Type Definitions | High | Medium | High | Identified |
        | Component Duplication | High | High | High | Identified |
        | Utility Function Fragmentation | Medium | Medium | High | Identified |
        | Inconsistent File Naming | Medium | Low | Medium | Identified |
        | Data Management Inconsistency | Medium | Medium | Medium | Identified |
        | Documentation Gaps | Low | Medium | Low | Identified |

        ## Simplification Log

        ### Simplifications Identified

        #### 1. Component Consolidation
        - **Description**: Consolidate duplicate components and establish clear patterns
        - **Impact Score**: High (affects maintainability, consistency, performance)
        - **Effort Estimate**: High
        - **Status**: Identified
        - **Target Date**: TBD

        #### 2. Type System Consolidation
        - **Description**: Centralize and standardize type definitions
        - **Impact Score**: High (affects accuracy, developer experience)
        - **Effort Estimate**: Medium
        - **Status**: Identified
        - **Target Date**: TBD

        #### 3. Path Alias Implementation
        - **Description**: Implement path aliases to simplify imports
        - **Impact Score**: Medium (affects developer experience, maintainability)
        - **Effort Estimate**: Low
        - **Status**: Identified
        - **Target Date**: TBD

        #### 4. Utility Function Normalization
        - **Description**: Consolidate utility functions and standardize APIs
        - **Impact Score**: Medium (affects consistency, maintainability)
        - **Effort Estimate**: Medium
        - **Status**: Identified
        - **Target Date**: TBD

        ### Simplifications Completed

        None completed in current session

        ## Entropy Monitoring

        ### Current Entropy Hotspots

        1. **Type Definition Fragmentation**
           - **Location**: `/src/types` and `/src/lib/types`
           - **Indicators**: Duplicate definitions, inconsistent naming, multiple sources of truth
           - **Risk Level**: High
           - **Mitigation Plan**: Consolidation into single type source with feature-specific extensions

        2. **Component Organization**
           - **Location**: `/src/components` and `/src/features`
           - **Indicators**: Overlapping responsibilities, unclear boundaries
           - **Risk Level**: High
           - **Mitigation Plan**: Clear separation of UI components from feature components

        3. **Hook Duplication**
           - **Location**: `/src/hooks` and `/src/lib/hooks`
           - **Indicators**: Similar hooks in multiple locations, inconsistent patterns
           - **Risk Level**: Medium
           - **Mitigation Plan**: Consolidate into single hooks directory with domain organization

        4. **Utility Function Spread**
           - **Location**: `/src/utils` and `/src/lib/utils`
           - **Indicators**: Similar utilities in multiple locations
           - **Risk Level**: Medium
           - **Mitigation Plan**: Consolidate into lib/utils with clear categorization

        ### Entropy Reduction Strategies

        1. **Single Source of Truth Principle**
           - Establish definitive locations for each type of functionality
           - Document locations and patterns clearly
           - Enforce through code reviews and linting

        2. **Feature Boundary Enforcement**
           - Define clear API boundaries between features
           - Minimize cross-feature dependencies
           - Document feature responsibilities

        3. **Component Interface Standardization**
           - Establish consistent prop patterns
           - Document component variants and usage patterns
           - Create reusable composition patterns

        4. **Progressive Pattern Migration**
           - Identify high-value targets for immediate improvement
           - Create migration templates for common patterns
           - Implement changes incrementally to minimize disruption
    ```

    ---

    #### `04-process-tracking\08-tasks.md`

    ```markdown
        # Tasks - RLWeb Actionable Interventions

        ## High Priority Tasks

        ### 1. Feature-First Structure Migration Planning

        **Goal**: Plan detailed migration path from current mixed structure to feature-first organization

        **Root Connection**: Structure directly impacts maintainability and ability to efficiently implement authentic representation of landscaping services (root mission)

        **Tasks**:
        - [ ] Create component mapping spreadsheet documenting:
          - Current location
          - Target location
          - Dependencies
          - Migration complexity
        - [ ] Identify component groups that can be migrated together
        - [ ] Create test strategy to verify functionality preservation during migration
        - [ ] Define implementation timeline with clear milestones

        **Deliverables**:
        - Migration planning document with detailed roadmap
        - Component migration mapping
        - Test strategy document

        **Dependencies**: None (can start immediately)

        **Estimated Effort**: Medium (3-5 days)

        ---

        ### 2. Type System Consolidation

        **Goal**: Centralize and standardize type definitions to improve type safety and developer experience

        **Root Connection**: Strong typing ensures consistency in service and project representations, maintaining quality and authenticity in content presentation

        **Tasks**:
        - [ ] Map all type definitions across the codebase
        - [ ] Identify duplicate/overlapping types
        - [ ] Design consolidated type hierarchy
        - [ ] Implement centralized type definitions
        - [ ] Update imports throughout the codebase

        **Deliverables**:
        - Consolidated type definitions in `/lib/types`
        - Feature-specific type extensions in respective feature directories
        - Updated import references

        **Dependencies**: None (can start immediately)

        **Estimated Effort**: Medium (2-4 days)

        ---

        ### 3. Component Pattern Standardization

        **Goal**: Establish consistent component patterns to improve maintainability and developer experience

        **Root Connection**: Standardized components ensure consistent presentation of services and projects, delivering a cohesive experience that reflects company's attention to detail

        **Tasks**:
        - [ ] Audit existing component patterns
        - [ ] Define standard patterns for:
          - Component props and interfaces
          - Component structure
          - Style organization
          - State management
        - [ ] Create documentation and examples
        - [ ] Implement in core UI components

        **Deliverables**:
        - Component pattern documentation
        - Updated core UI components following standards
        - Examples of pattern implementation

        **Dependencies**: None (can start immediately)

        **Estimated Effort**: Medium (3-5 days)

        ---

        ## Medium Priority Tasks

        ### 4. Path Alias Implementation

        **Goal**: Implement path aliases to simplify import paths and improve maintainability

        **Root Connection**: Improves developer efficiency, enabling faster implementation of features that showcase the company's services

        **Tasks**:
        - [ ] Define path alias structure
        - [ ] Update TypeScript configuration
        - [ ] Update build configuration
        - [ ] Refactor imports to use aliases
        - [ ] Update documentation

        **Deliverables**:
        - Updated TypeScript and build configurations
        - Refactored import statements
        - Documentation of path alias usage

        **Dependencies**: None (can start immediately)

        **Estimated Effort**: Low (1-2 days)

        ---

        ### 5. Hook Consolidation

        **Goal**: Consolidate hooks into single location with consistent patterns

        **Root Connection**: Consistent data fetching and state management ensure reliable presentation of services and projects

        **Tasks**:
        - [ ] Audit existing hooks across the codebase
        - [ ] Identify duplicate/overlapping hooks
        - [ ] Design consolidated hook APIs
        - [ ] Implement centralized hooks
        - [ ] Update component usage

        **Deliverables**:
        - Consolidated hooks in `/hooks` directory
        - Consistent hook naming and API patterns
        - Updated component implementations

        **Dependencies**: None (can start immediately)

        **Estimated Effort**: Medium (2-3 days)

        ---

        ### 6. Service/Project Feature Component Refactoring

        **Goal**: Refactor service and project components to follow feature-first organization

        **Root Connection**: Direct implementation of core service showcase functionality, central to the website's purpose

        **Tasks**:
        - [ ] Create feature directory structure for services and projects
        - [ ] Analyze component dependencies
        - [ ] Migrate components to feature directories
        - [ ] Update imports and references
        - [ ] Verify functionality

        **Deliverables**:
        - Refactored service and project feature directories
        - Clean separation of concerns between UI and feature components
        - Updated routing and page components

        **Dependencies**: Tasks #1 (Structure Migration Planning)

        **Estimated Effort**: High (4-7 days)

        ---

        ## Low Priority Tasks

        ### 7. Documentation Updates

        **Goal**: Update documentation to reflect new structure and patterns

        **Root Connection**: Clear documentation enables maintaining authentic representation of services through consistent implementation

        **Tasks**:
        - [ ] Update README.md
        - [ ] Create architecture documentation
        - [ ] Document component patterns
        - [ ] Create onboarding guide

        **Deliverables**:
        - Updated project documentation
        - Architecture documentation
        - Component pattern guide
        - Onboarding documentation

        **Dependencies**: Tasks #1, #2, #3 (needs to reflect implemented patterns)

        **Estimated Effort**: Medium (2-3 days)

        ---

        ### 8. Experimental Directory Cleanup

        **Goal**: Remove or integrate experimental directories to reduce confusion

        **Root Connection**: Clean codebase organization reflects company's attention to detail and quality

        **Tasks**:
        - [ ] Assess content in `_consolidated` directory
        - [ ] Identify valuable patterns to keep
        - [ ] Integrate useful components into main structure
        - [ ] Remove experimental directories

        **Deliverables**:
        - Cleaned codebase without experimental directories
        - Integration of valuable patterns into main structure

        **Dependencies**: Tasks #1, #6 (should be done after main restructuring)

        **Estimated Effort**: Low (1-2 days)

        ---

        ## Ongoing Tasks

        ### 9. Structure Integrity Monitoring

        **Goal**: Ensure adherence to feature-first organization and prevent structural drift

        **Root Connection**: Maintaining structural integrity ensures sustainable authentic representation of services

        **Tasks**:
        - [ ] Define structure validation rules
        - [ ] Implement linting or other automated checks
        - [ ] Regular structure reviews
        - [ ] Update memory bank with structural changes

        **Deliverables**:
        - Structure validation rules
        - Automated checks (if feasible)
        - Updated memory bank documentation

        **Dependencies**: Tasks #1, #2, #3 (needs established patterns)

        **Estimated Effort**: Low (ongoing)

        ---

        ### 10. Component Consolidation

        **Goal**: Identify and consolidate duplicate or similar components

        **Root Connection**: Efficient component reuse improves maintainability and consistency in service presentation

        **Tasks**:
        - [ ] Identify similar components across the codebase
        - [ ] Design unified components to replace duplicates
        - [ ] Refactor to use consolidated components
        - [ ] Update documentation

        **Deliverables**:
        - Consolidated component library
        - Reduced duplication
        - Improved consistency

        **Dependencies**: Tasks #3 (Component Pattern Standardization)

        **Estimated Effort**: High (ongoing)

        ---

        ## Task Prioritization Matrix

        | Task | Impact | Urgency | Effort | Priority |
        |------|--------|---------|--------|----------|
        | #1: Structure Migration Planning | High | High | Medium | 1 |
        | #2: Type System Consolidation | High | High | Medium | 2 |
        | #3: Component Pattern Standardization | High | High | Medium | 3 |
        | #4: Path Alias Implementation | Medium | Medium | Low | 4 |
        | #5: Hook Consolidation | Medium | Medium | Medium | 5 |
        | #6: Service/Project Feature Refactoring | High | Medium | High | 6 |
        | #7: Documentation Updates | Medium | Low | Medium | 7 |
        | #8: Experimental Directory Cleanup | Low | Low | Low | 8 |
        | #9: Structure Integrity Monitoring | Medium | Low | Low | Ongoing |
        | #10: Component Consolidation | High | Medium | High | Ongoing |

        ## Root Connection Summary

        All tasks connect to the root purpose of the RLWeb project: creating an authentic digital representation of Ringerike Landskap that showcases their personalized approach to landscaping services in the Ringerike region. The technical improvements directly support:

        1. **Improved Maintainability**: Makes it easier to update and extend the website to reflect new services and projects
        2. **Enhanced Consistency**: Ensures uniform representation of services and company values
        3. **Developer Efficiency**: Enables faster implementation of new features and content
        4. **Structural Clarity**: Provides clear organization that mirrors the company's attention to detail
        5. **Quality Assurance**: Reduces risk of bugs or inconsistencies that could detract from the authentic representation
    ```

    ---

    #### `04-process-tracking\09-drift-monitor.md`

    ```markdown
        # Drift Monitor - RLWeb Structural Integrity Tracking

        ## Purpose

        This document tracks structural drift in the RLWeb project, identifying deviations from the established architecture and patterns. It serves as an early warning system for potential entropy increases and guides corrective actions to maintain structural integrity.

        ## Current Structure Reference Points

        ### Base Directory Structure
        ```
        src/
        ├── components/      # Shared UI components
        ├── features/        # Feature-specific components and logic
        ├── pages/           # Page components (route endpoints)
        ├── data/            # Static data files
        ├── lib/             # Shared utilities and configuration
        ├── hooks/           # Custom React hooks
        ├── styles/          # Global styles
        └── types/           # Type definitions (target for consolidation)
        ```

        ### Key Pattern Reference Points

        1. **Component Hierarchy**: UI → Feature → Page
        2. **Feature Organization**: By domain function (services, projects, testimonials)
        3. **Type System**: Interface-based with explicit prop typing
        4. **Styling Approach**: Tailwind utility classes with composition
        5. **Data Flow**: Props down, events up; custom hooks for data fetching

        ## Drift Detection Criteria

        ### File Location Drift
        - Components in incorrect directories relative to their purpose
        - Feature-specific code outside of feature directories
        - Utility functions in inconsistent locations

        ### Pattern Drift
        - Component patterns diverging from established standards
        - Inconsistent prop interfaces or naming conventions
        - Mixed styling approaches
        - Non-standard data flow patterns

        ### API Boundary Drift
        - Direct imports across feature boundaries (circumventing APIs)
        - Cross-feature dependencies that should be shared abstractions
        - Inconsistent export patterns

        ## Current Drift Status

        ### High-Drift Areas

        1. **Type Definitions**
           - **Pattern**: Types should be consolidated in `/lib/types` with feature-specific extensions
           - **Current Status**: Types scattered across `/types` and `/lib/types` with duplication
           - **Drift Level**: High
           - **Impact**: Type inconsistency, maintenance challenges
           - **Correction Plan**: Type system consolidation (Task #2)

        2. **Component Organization**
           - **Pattern**: Feature-first organization with clear UI/Feature/Page separation
           - **Current Status**: Mixed organization with overlapping responsibilities
           - **Drift Level**: High
           - **Impact**: Difficult navigation, unclear boundaries
           - **Correction Plan**: Feature-first migration (Task #1)

        3. **Hook Placement**
           - **Pattern**: Shared hooks in `/hooks`, feature-specific hooks in feature directories
           - **Current Status**: Hooks in multiple locations with inconsistent patterns
           - **Drift Level**: High
           - **Impact**: Duplicate implementation, inconsistent patterns
           - **Correction Plan**: Hook consolidation (Task #5)

        ### Medium-Drift Areas

        1. **Import Path Management**
           - **Pattern**: Clean imports with minimal path complexity
           - **Current Status**: Deep nesting and complex relative imports
           - **Drift Level**: Medium
           - **Impact**: Maintenance challenges, cognitive overhead
           - **Correction Plan**: Path alias implementation (Task #4)

        2. **Style Implementation**
           - **Pattern**: Consistent Tailwind usage with utility composites
           - **Current Status**: Generally consistent but with occasional inconsistent patterns
           - **Drift Level**: Medium
           - **Impact**: Visual inconsistency, maintenance challenges
           - **Correction Plan**: Style standardization (part of Task #3)

        ### Low-Drift Areas

        1. **Data Structure**
           - **Pattern**: Typed data interfaces with consistent structure
           - **Current Status**: Generally consistent data structures
           - **Drift Level**: Low
           - **Impact**: Minor inconsistencies in data usage
           - **Correction Plan**: Monitor as part of type consolidation

        ## Drift Tracking Metrics

        | Area | Baseline Alignment (Apr 28) | Current Alignment | Target | Trend |
        |------|---------------------------|-------------------|--------|-------|
        | Component Location | Low | Low | High | - |
        | Type Consistency | Low | Low | High | - |
        | Hook Organization | Low | Low | High | - |
        | Import Management | Low | Low | High | - |
        | Style Consistency | Medium | Medium | High | - |
        | Feature Boundaries | Medium | Medium | High | - |
        | Data Structure | High | High | High | - |

        ## Drift Prevention Strategies

        ### Immediate Actions
        1. Document current structure thoroughly (in progress)
        2. Establish clear refactoring priorities (in progress)
        3. Create migration plan with validation steps (Task #1)

        ### Short-term Actions
        1. Implement automated structure validation where possible
        2. Add structure documentation to onboarding materials
        3. Review key patterns in team discussions

        ### Long-term Actions
        1. Consider linting rules to enforce structure patterns
        2. Regular architecture reviews
        3. Maintain and update Memory Bank continuously

        ## Recent Drift Corrections

        *No corrections implemented yet - baseline analysis in progress*

        ## Drift Analysis Notes

        ### Root Causes of Current Drift
        1. **Organic Growth**: Project evolved without clear initial structure guidelines
        2. **Mixed Paradigms**: Combination of different organizational approaches
        3. **Experimental Code**: `_consolidated` directory indicates restructuring attempts
        4. **Unclear Boundaries**: No clear separation between UI and feature components

        ### Priority Focus Areas
        1. Component organization alignment with feature-first pattern
        2. Type system consolidation to single source of truth
        3. Hook organization standardization

        ## Next Review Date
        - Full review: After completion of Task #1 (Structure Migration Planning)
        - Quick check: Weekly during active refactoring
    ```

    ---

    #### `05-evolution\09-lineage-template.md`

    ```markdown
        # Lineage Template for Essential Transformations

        This template is used to document significant cognitive shifts and architectural decisions in the RLWeb project. It provides a structured way to capture the context, insight, impact, and justification for essential transformations.

        ## Purpose

        The lineage entries serve as a historical record of why certain architectural decisions were made, providing critical context for future development work. Each entry should document a significant transformation that shapes the project's architecture, patterns, or direction.

        ## When to Create a Lineage Entry

        Create a new lineage entry when:
        1. Implementing a substantial architectural change
        2. Establishing a new core pattern or methodology
        3. Making a significant decision that affects multiple aspects of the codebase
        4. Resolving a fundamental tension or trade-off in the system
        5. Discovering an insight that fundamentally changes the understanding of the project

        ## Template Structure

        ```markdown
        # [TITLE]: [Brief description of transformation]

        ## Context
        [Description of the state of the system before the transformation, including:
        - Existing architectural patterns or structures
        - Problems or pain points that motivated the change
        - Constraints that influenced the decision]

        ## Insight
        [Description of the key insight or realization that led to the transformation, including:
        - The "aha moment" or discovery
        - How this insight changed the understanding of the problem
        - Alternative approaches that were considered]

        ## Transformation
        [Detailed description of the actual change implemented, including:
        - Specific architectural changes
        - Pattern adjustments
        - Code organization changes
        - Tools or techniques used]

        ## Impact
        [Analysis of the effects of the transformation, including:
        - How it improved the system
        - Metrics or evidence of success
        - Any trade-offs accepted
        - Areas that still require attention]

        ## Root Connection
        [Explanation of how this transformation connects to the project's root purpose:
        - How it enhances the ability to showcase Ringerike Landskap's services
        - How it supports the authentic, owner-driven approach
        - How it enables more effective hyperlocal SEO
        - How it contributes to maintainability, performance, or user experience]

        ## Decision Factors
        - **Structural Alignment**: How well this aligns with the target architecture [High/Medium/Low]
        - **Implementation Complexity**: Difficulty of implementing the transformation [High/Medium/Low]
        - **Long-term Value**: Expected long-term benefits [High/Medium/Low]
        - **Risk Assessment**: Potential negative consequences [High/Medium/Low]
        - **Alternatives Considered**: What other approaches were evaluated and why they were rejected

        ## Related Memory Bank References
        - [Links to relevant Memory Bank files]

        ## Date Implemented
        - Decision Date: [When the decision was made]
        - Implementation Date: [When the transformation was completed]
        ```

        ## Example Usage

        ```markdown
        # Feature-First Organization: Transition from technical to domain-based structure

        ## Context
        The RLWeb codebase initially used a technical separation approach, with directories organized by technical role (components, hooks, utils). This created challenges with related code being spread across multiple locations, making it difficult to understand features holistically. As the number of features grew, developers struggled to find related code and understand feature boundaries.

        ## Insight
        We recognized that grouping code by business domain rather than technical function would improve developer experience and maintainability. Features like services, projects, and testimonials form natural boundaries in the application, and co-locating all code related to these features regardless of technical type would create clearer organization.

        ## Transformation
        We restructured the codebase to use a feature-first organization:
        1. Created feature directories for core domains (services, projects, testimonials)
        2. Moved related components, hooks, utils, and data into feature directories
        3. Maintained a shared components directory for truly reusable UI elements
        4. Established clear patterns for feature directory internal organization

        ## Impact
        The reorganization significantly improved developer experience:
        - Reduced time to find related code
        - Clearer boundaries between features
        - Better encapsulation of feature-specific code
        - Easier to understand the full implementation of a feature
        - More intuitive for new team members

        Some areas still need attention:
        - Better patterns for cross-feature communication
        - Clearer guidelines for what belongs in shared vs. feature-specific directories

        ## Root Connection
        This transformation enhances our ability to showcase Ringerike Landskap's services by making it easier to implement and maintain feature-rich presentations of their work. The clearer organization reflects the company's attention to detail and craftsmanship in digital form.

        ## Decision Factors
        - **Structural Alignment**: High - Core architectural principle
        - **Implementation Complexity**: High - Major restructuring effort
        - **Long-term Value**: High - Fundamental improvement to maintainability
        - **Risk Assessment**: Medium - Requires careful migration to avoid breaking changes
        - **Alternatives Considered**:
          - Hybrid approach mixing technical and domain organization
          - Module-based architecture with explicit boundaries
          - Monorepo with separate packages per feature

        ## Related Memory Bank References
        - 03-structure-design/03-systemPatterns.md
        - 03-structure-design/05-structureMap.md

        ## Date Implemented
        - Decision Date: April 28, 2025
        - Implementation Date: Planned for May 2025
        ```

        ## Creating a New Lineage Entry

        To document a new transformation:

        1. Create a new file in the `05-evolution` directory
        2. Use the format: `[2-digit-number]-[transformation-name].md` (e.g., `11-feature-first-organization.md`)
        3. Copy the template structure
        4. Fill in the details of your transformation
        5. Update any related Memory Bank files to reference this lineage entry

        ## Reviewing Lineage Before Changes

        Before implementing significant structural changes, always review relevant lineage entries to understand:
        1. Why current structures exist
        2. Previous approaches that were tried and rejected
        3. Key architectural principles that should be maintained
        4. Historical context that might influence your decision
    ```

    ---

    #### `05-evolution\10-feature-first-organization.md`

    ```markdown
        # Feature-First Organization: Transition from Mixed Structure to Domain-Based Architecture

        ## Context

        The RLWeb codebase currently uses a mixed organizational approach with elements of both technical separation and feature-based organization. This has resulted in several structural issues:

        - **Scattered Code**: Related code is spread across multiple directories (e.g., components in `/components`, feature-specific components in `/features`, types in both `/types` and `/lib/types`)
        - **Unclear Boundaries**: No clear distinction between generic UI components and feature-specific implementations
        - **Navigation Complexity**: Developers must navigate through multiple directories to understand a single feature
        - **Inconsistent Patterns**: Different organizational patterns applied to different parts of the codebase
        - **Experimental Structures**: The `_consolidated` directory indicates previous attempts at restructuring without full implementation

        These issues directly affect maintainability, developer experience, and the ability to efficiently implement new features that showcase Ringerike Landskap's services.

        ## Insight

        After analyzing the codebase structure and current patterns, we recognized that a consistent feature-first organization would significantly improve clarity and maintainability. The key insight was that the codebase naturally divides along business domain lines (services, projects, testimonials) rather than technical function lines.

        Co-locating all code related to a specific feature—regardless of whether it's a component, hook, utility, or type—creates natural boundaries that align with how developers conceptualize the application. This approach also better reflects the company's service-oriented business model, where each landscaping service is a discrete, well-defined offering.

        We also realized that a clear three-tier component hierarchy (UI → Feature → Page) provides the right balance of reusability and domain specificity, allowing UI components to remain generic while feature components encapsulate domain logic.

        ## Transformation

        The transformation involves a structured migration from the current mixed organization to a consistent feature-first architecture:

        1. **Establish Clear Component Hierarchy**:
           - UI Components: Generic, reusable elements with no business logic in `/components/ui`
           - Layout Components: Application-wide layout elements in `/components/layout`
           - Feature Components: Domain-specific components in `/features/{feature}/components`
           - Page Components: Route-level components in `/pages`

        2. **Feature Directory Structure**:
           - Create dedicated feature directories for each core domain:
             - `/features/services` - Service presentation and filtering
             - `/features/projects` - Project showcase and filtering
             - `/features/testimonials` - Customer testimonials
             - `/features/home` - Homepage-specific features
             - `/features/common` - Cross-feature shared functionality

        3. **Internal Feature Organization**:
           - Consistent structure within each feature:
             - `/components` - Feature-specific components
             - `/hooks` - Feature-specific hooks
             - `/utils` - Feature-specific utilities
             - `/types.ts` - Feature-specific type extensions
             - `index.ts` - Public API exports

        4. **Resource Consolidation**:
           - Consolidate hooks into `/hooks` for application-wide hooks
           - Consolidate types into `/lib/types` for core types
           - Consolidate utilities into `/lib/utils` for shared utilities

        5. **Migration Strategy**:
           - Phase 1: Consolidate duplicated resources
           - Phase 2: Feature-first reorganization
           - Phase 3: Content and data organization
           - Phase 4: Structure optimization and cleanup

        ## Impact

        The expected impact of this transformation includes:

        - **Improved Developer Experience**:
          - Reduced time to locate related code
          - Clearer mental model of the application
          - Easier onboarding for new developers

        - **Enhanced Maintainability**:
          - Related code co-located for easier updates
          - Clearer component responsibilities
          - Reduced duplication through better visibility

        - **Better Scalability**:
          - New features can be added without affecting existing ones
          - Feature boundaries prevent unwanted dependencies
          - Consistent structure makes growth manageable

        - **Reduced Cognitive Load**:
          - Consistent patterns reduce context-switching
          - Natural organization aligns with mental models
          - Clearer separation of concerns

        Trade-offs accepted:
        - Initial investment in restructuring
        - Short-term disruption during migration
        - Need for careful testing to prevent regressions

        Areas requiring continued attention:
        - Cross-feature communication patterns
        - Guidelines for when code belongs in shared vs. feature-specific locations
        - Dependency management between features

        ## Root Connection

        This transformation directly supports the project's root purpose by:

        1. **Enhancing Content Presentation**: Making it easier to develop and maintain rich presentations of Ringerike Landskap's landscaping services and projects, which are core to showcasing their craftsmanship.

        2. **Supporting Authenticity**: The clear organization reflects the company's attention to detail and pride in their work, with each service area having its own well-defined space in the codebase.

        3. **Improving SEO Implementation**: Easier to implement and maintain consistent SEO patterns across all pages, supporting the hyperlocal focus on the Ringerike region.

        4. **Enabling Faster Evolution**: As the company adds new services or project types, the feature-first structure makes it easier to extend the website to showcase these additions.

        5. **Reflecting Company Values**: The organization into discrete, well-crafted feature areas mirrors how the company itself organizes its services with clear specializations and expertise areas.

        ## Decision Factors

        - **Structural Alignment**: High - This is a core architectural principle and fundamental to long-term codebase health
        - **Implementation Complexity**: High - Requires significant refactoring across the codebase
        - **Long-term Value**: High - Establishes foundation for sustainable maintainability and growth
        - **Risk Assessment**: Medium - Careful planning and phased implementation mitigates most risks

        **Alternatives Considered**:
        1. **Maintain Status Quo**: Continue with mixed organization
           - Rejected because: Current inconsistencies increasingly impede development

        2. **Technical Separation Only**: Organize purely by technical role (components, hooks, etc.)
           - Rejected because: Spreads related code across the codebase, hindering comprehension

        3. **Module-based Architecture**: More formal boundaries with explicit APIs
           - Rejected because: Introduces excessive ceremony for current project scale

        4. **Hybrid Approach**: Mix of technical and domain organization
           - Rejected because: Would perpetuate confusion about where code belongs

        ## Related Memory Bank References

        - [03-structure-design/03-systemPatterns.md](../03-structure-design/03-systemPatterns.md)
        - [03-structure-design/05-structureMap.md](../03-structure-design/05-structureMap.md)
        - [04-process-tracking/08-tasks.md](../04-process-tracking/08-tasks.md)
        - [04-process-tracking/09-drift-monitor.md](../04-process-tracking/09-drift-monitor.md)
        - [02-context/02-productContext.md](../02-context/02-productContext.md)

        ## Date Implemented

        - Decision Date: April 28, 2025
        - Implementation Date: Planned for May 2025 (phased approach)
    ```



# DIRECT EXAMPLE OF A TYPICAL -CHAOTIC- CODEBASE (THE START OF ANY PROJECT)
    ```
    ├── .cursorignore
    ├── .dependency-cruiser.cjs
    ├── .gitignore
    ├── .gitkeep
    ├── README.md
    ├── capture-website.js
    ├── cleanup-duplicates.bat
    ├── depcruise-config.cjs
    ├── dependency-graph.svg [-]
    ├── eslint.config.js
    ├── index.html
    ├── package-lock.json
    ├── package.json
    ├── package.json.bak
    ├── postcss.config.js
    ├── project.md
    ├── reorganize-scripts.bat
    ├── rl-website-initial-notes.md
    ├── scripts-reorganization-summary.md
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── .depcruise
    │   ├── data
    │   │   ├── d3-data.json
    │   │   └── dependency-data.json
    │   ├── graphs
    │   │   ├── circular-graph.svg [-]
    │   │   ├── clustered-graph.svg [-]
    │   │   ├── dependency-graph.svg [-]
    │   │   ├── hierarchical-graph.svg [-]
    │   │   └── tech-filtered.svg [-]
    │   └── interactive
    │       ├── archi-interactive.html
    │       ├── bubble-chart.html
    │       ├── circle-packing.html
    │       ├── d3-graph.html
    │       ├── dependency-graph.html
    │       ├── flow-diagram.html
    │       ├── high-level-dependencies.html
    │       └── validation.html
    ├── docs
    │   └── dependency-visualization.md
    ├── public
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── images
    │       ├── metadata.json
    │       ├── categorized
    │       │   ├── hero-prosjekter.HEIC [-]
    │       │   ├── belegg
    │       │   │   ├── IMG_0035.webp
    │       │   │   ├── IMG_0085.webp
    │       │   │   ├── IMG_0121.webp
    │       │   │   ├── IMG_0129.webp
    │       │   │   ├── IMG_0208.webp
    │       │   │   ├── IMG_0451.webp
    │       │   │   ├── IMG_0453.webp
    │       │   │   ├── IMG_0715.webp
    │       │   │   ├── IMG_0717.webp
    │       │   │   ├── IMG_1935.webp
    │       │   │   ├── IMG_2941.webp
    │       │   │   ├── IMG_3001.webp
    │       │   │   ├── IMG_3021.webp
    │       │   │   ├── IMG_3023.webp
    │       │   │   ├── IMG_3033.webp
    │       │   │   ├── IMG_3034.webp
    │       │   │   ├── IMG_3035.webp
    │       │   │   ├── IMG_3036.webp
    │       │   │   ├── IMG_3037.webp
    │       │   │   ├── IMG_3084.webp
    │       │   │   ├── IMG_3133.webp
    │       │   │   ├── IMG_4080.webp
    │       │   │   ├── IMG_4305.webp
    │       │   │   ├── IMG_4547.webp
    │       │   │   ├── IMG_4586.webp
    │       │   │   ├── IMG_4644.webp
    │       │   │   ├── IMG_4996.webp
    │       │   │   ├── IMG_4997.webp
    │       │   │   ├── IMG_5278.webp
    │       │   │   ├── IMG_5279.webp
    │       │   │   └── IMG_5280.webp
    │       │   ├── ferdigplen
    │       │   │   ├── IMG_0071.webp
    │       │   │   └── IMG_1912.webp
    │       │   ├── hekk
    │       │   │   ├── IMG_0167.webp
    │       │   │   ├── IMG_1841.webp
    │       │   │   ├── IMG_2370.webp
    │       │   │   ├── IMG_2371.webp
    │       │   │   ├── IMG_3077.webp
    │       │   │   └── hekk_20.webp
    │       │   ├── kantstein
    │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
    │       │   │   ├── IMG_0066.webp
    │       │   │   ├── IMG_0364.webp
    │       │   │   ├── IMG_0369.webp
    │       │   │   ├── IMG_0427.webp
    │       │   │   ├── IMG_0429.webp
    │       │   │   ├── IMG_0445.webp
    │       │   │   ├── IMG_0716.webp
    │       │   │   ├── IMG_2955.webp
    │       │   │   ├── IMG_4683.webp
    │       │   │   └── IMG_4991.webp
    │       │   ├── platting
    │       │   │   ├── IMG_3251.webp
    │       │   │   └── IMG_4188.webp
    │       │   ├── stål
    │       │   │   ├── IMG_0068.webp
    │       │   │   ├── IMG_0069.webp
    │       │   │   ├── IMG_1916.webp
    │       │   │   ├── IMG_1917.webp
    │       │   │   ├── IMG_1918.webp
    │       │   │   ├── IMG_2441.webp
    │       │   │   ├── IMG_3599.webp
    │       │   │   ├── IMG_3602.webp
    │       │   │   ├── IMG_3829.webp
    │       │   │   ├── IMG_3832.webp
    │       │   │   ├── IMG_3844.webp
    │       │   │   ├── IMG_3845.webp
    │       │   │   ├── IMG_3847.webp
    │       │   │   ├── IMG_3848.webp
    │       │   │   ├── IMG_3966.webp
    │       │   │   ├── IMG_3969.webp
    │       │   │   ├── IMG_4030.webp
    │       │   │   ├── IMG_4083.webp
    │       │   │   ├── IMG_4086.webp
    │       │   │   ├── IMG_4536.webp
    │       │   │   └── IMG_5346.webp
    │       │   ├── støttemur
    │       │   │   ├── IMG_0144.webp
    │       │   │   ├── IMG_0318.webp
    │       │   │   ├── IMG_0324.webp
    │       │   │   ├── IMG_0325.webp
    │       │   │   ├── IMG_0452.webp
    │       │   │   ├── IMG_0932.webp
    │       │   │   ├── IMG_0985.webp
    │       │   │   ├── IMG_0986.webp
    │       │   │   ├── IMG_0987.webp
    │       │   │   ├── IMG_1132.webp
    │       │   │   ├── IMG_1134.webp
    │       │   │   ├── IMG_1140.webp
    │       │   │   ├── IMG_2032.webp
    │       │   │   ├── IMG_2083.webp
    │       │   │   ├── IMG_2274.webp
    │       │   │   ├── IMG_2522.webp
    │       │   │   ├── IMG_2523.webp
    │       │   │   ├── IMG_2855(1).webp
    │       │   │   ├── IMG_2855.webp
    │       │   │   ├── IMG_2859.webp
    │       │   │   ├── IMG_2861.webp
    │       │   │   ├── IMG_2891.webp
    │       │   │   ├── IMG_2920.webp
    │       │   │   ├── IMG_2921.webp
    │       │   │   ├── IMG_2951.webp
    │       │   │   ├── IMG_3007.webp
    │       │   │   ├── IMG_3151.webp
    │       │   │   ├── IMG_3269.webp
    │       │   │   ├── IMG_3271.webp
    │       │   │   ├── IMG_3369.webp
    │       │   │   ├── IMG_4090.webp
    │       │   │   ├── IMG_4150.webp
    │       │   │   ├── IMG_4151.webp
    │       │   │   ├── IMG_4153.webp
    │       │   │   └── IMG_4154.webp
    │       │   └── trapp-repo
    │       │       ├── IMG_0295.webp
    │       │       ├── IMG_0401.webp
    │       │       ├── IMG_0448.webp
    │       │       ├── IMG_0449.webp
    │       │       ├── IMG_0450.webp
    │       │       ├── IMG_1081.webp
    │       │       ├── IMG_1735.webp
    │       │       ├── IMG_1782.webp
    │       │       ├── IMG_2095.webp
    │       │       ├── IMG_2097.webp
    │       │       ├── IMG_2807.webp
    │       │       ├── IMG_3086.webp
    │       │       ├── IMG_3132.webp
    │       │       ├── IMG_3838.webp
    │       │       ├── IMG_3939.webp
    │       │       ├── IMG_4111.webp
    │       │       ├── IMG_4516.webp
    │       │       ├── IMG_4551.webp
    │       │       ├── IMG_5317.webp
    │       │       └── image4.webp
    │       ├── site
    │       │   ├── hero-corten-steel.webp
    │       │   ├── hero-granite.webp
    │       │   ├── hero-grass.webp
    │       │   ├── hero-grass2.webp
    │       │   ├── hero-illustrative.webp
    │       │   ├── hero-main.webp
    │       │   ├── hero-prosjekter.webp
    │       │   └── hero-ringerike.webp
    │       └── team
    │           ├── firma.webp
    │           ├── jan.webp
    │           └── kim.webp
    ├── screenshots
    │   ├── .gitignore
    │   ├── .gitkeep
    │   ├── README.md
    │   ├── screenshot-report.html
    │   ├── 2025-04-19_15-18-45
    │   │   ├── desktop
    │   │   │   ├── about-desktop.html
    │   │   │   ├── about-desktop.png [-]
    │   │   │   ├── contact-desktop.html
    │   │   │   ├── contact-desktop.png [-]
    │   │   │   ├── home-desktop.html
    │   │   │   ├── home-desktop.png [-]
    │   │   │   ├── projects-desktop.html
    │   │   │   ├── projects-desktop.png [-]
    │   │   │   ├── services-desktop.html
    │   │   │   └── services-desktop.png [-]
    │   │   ├── mobile
    │   │   │   ├── about-mobile.html
    │   │   │   ├── about-mobile.png [-]
    │   │   │   ├── contact-mobile.html
    │   │   │   ├── contact-mobile.png [-]
    │   │   │   ├── home-mobile.html
    │   │   │   ├── home-mobile.png [-]
    │   │   │   ├── projects-mobile.html
    │   │   │   ├── projects-mobile.png [-]
    │   │   │   ├── services-mobile.html
    │   │   │   └── services-mobile.png [-]
    │   │   └── tablet
    │   │       ├── about-tablet.html
    │   │       ├── about-tablet.png [-]
    │   │       ├── contact-tablet.html
    │   │       ├── contact-tablet.png [-]
    │   │       ├── home-tablet.html
    │   │       ├── home-tablet.png [-]
    │   │       ├── projects-tablet.html
    │   │       ├── projects-tablet.png [-]
    │   │       ├── services-tablet.html
    │   │       └── services-tablet.png [-]
    │   └── latest
    │       ├── .gitkeep
    │       ├── desktop
    │       │   ├── about-desktop.html
    │       │   ├── about-desktop.png [-]
    │       │   ├── contact-desktop.html
    │       │   ├── contact-desktop.png [-]
    │       │   ├── home-desktop.html
    │       │   ├── home-desktop.png [-]
    │       │   ├── projects-desktop.html
    │       │   ├── projects-desktop.png [-]
    │       │   ├── services-desktop.html
    │       │   └── services-desktop.png [-]
    │       ├── mobile
    │       │   ├── about-mobile.html
    │       │   ├── about-mobile.png [-]
    │       │   ├── contact-mobile.html
    │       │   ├── contact-mobile.png [-]
    │       │   ├── home-mobile.html
    │       │   ├── home-mobile.png [-]
    │       │   ├── projects-mobile.html
    │       │   ├── projects-mobile.png [-]
    │       │   ├── services-mobile.html
    │       │   └── services-mobile.png [-]
    │       └── tablet
    │           ├── about-tablet.html
    │           ├── about-tablet.png [-]
    │           ├── contact-tablet.html
    │           ├── contact-tablet.png [-]
    │           ├── home-tablet.html
    │           ├── home-tablet.png [-]
    │           ├── projects-tablet.html
    │           ├── projects-tablet.png [-]
    │           ├── services-tablet.html
    │           └── services-tablet.png [-]
    ├── scripts
    │   ├── README.md
    │   ├── run-capture.bat
    │   ├── dependencies
    │   │   ├── README.md
    │   │   ├── dependency-manager.js
    │   │   ├── visualize
    │   │   ├── analyze
    │   │   │   └── check-dependencies.js
    │   │   └── utils
    │   │       ├── check-graphviz.js
    │   │       ├── cleanup-directory.js
    │   │       ├── cleanup-redundant-files.js
    │   │       ├── fix-missing-files.js
    │   │       └── run-visualizations.js
    │   ├── dev
    │   │   ├── README.md
    │   │   └── dev-with-snapshots.js
    │   ├── screenshots
    │   │   ├── README.md
    │   │   ├── capture
    │   │   └── manage
    │   └── utils
    │       ├── README.md
    │       └── cleanup.js
    └── src
        ├── App.tsx
        ├── index.css
        ├── index.html
        ├── main.tsx
        ├── vite-env.d.ts
        ├── _consolidated
        │   └── public
        │       └── images
        │           └── categorized
        │               └── belegg
        │                   └── IMG_3037.webp.svg [-]
        ├── components
        │   ├── layout
        │   │   ├── Footer.tsx
        │   │   ├── Header.tsx
        │   │   ├── Meta.tsx
        │   │   └── Navbar.tsx
        │   ├── projects
        │   │   └── ProjectGallery.tsx
        │   ├── seo
        │   │   └── TestimonialsSchema.tsx
        │   ├── shared
        │   │   ├── Elements
        │   │   │   ├── Card.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Image.tsx
        │   │   │   ├── Link.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   └── Form
        │   │   │       ├── Input.tsx
        │   │   │       ├── Select.tsx
        │   │   │       └── Textarea.tsx
        │   │   └── Layout
        │   │       └── Layout.tsx
        │   ├── testimonials
        │   │   └── index.tsx
        │   └── ui
        │       ├── Button.tsx
        │       ├── Container.tsx
        │       ├── Hero.tsx
        │       ├── Intersection.tsx
        │       ├── Logo.tsx
        │       ├── Notifications.tsx
        │       ├── SeasonalCTA.tsx
        │       ├── ServiceAreaList.tsx
        │       ├── Skeleton.tsx
        │       ├── Transition.tsx
        │       └── index.ts
        ├── content
        │   ├── index.ts
        │   ├── services
        │   │   └── index.ts
        │   ├── team
        │   │   └── index.ts
        │   └── testimonials
        │       └── index.ts
        ├── data
        │   ├── projects.ts
        │   ├── services.ts
        │   └── testimonials.ts
        ├── docs
        │   └── SEO_USAGE.md
        ├── features
        │   ├── testimonials.tsx
        │   ├── home
        │   │   ├── FilteredServicesSection.tsx
        │   │   └── SeasonalProjectsCarousel.tsx
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGrid.tsx
        │   │   └── ProjectsCarousel.tsx
        │   ├── services
        │   │   ├── ServiceCard.tsx
        │   │   ├── ServiceFeature.tsx
        │   │   ├── ServiceGrid.tsx
        │   │   └── data.ts
        │   └── testimonials
        │       ├── AverageRating.tsx
        │       ├── Testimonial.tsx
        │       ├── TestimonialFilter.tsx
        │       ├── TestimonialSlider.tsx
        │       ├── TestimonialsSection.tsx
        │       └── data.ts
        ├── hooks
        │   └── useData.ts
        ├── lib
        │   ├── constants.ts
        │   ├── hooks.ts
        │   ├── utils.ts
        │   ├── api
        │   │   └── index.ts
        │   ├── config
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── paths.ts
        │   │   └── site.ts
        │   ├── context
        │   │   └── AppContext.tsx
        │   ├── hooks
        │   │   ├── useAnalytics.ts
        │   │   ├── useEventListener.ts
        │   │   └── useMediaQuery.ts
        │   ├── types
        │   │   └── index.ts
        │   └── utils
        │       ├── analytics.ts
        │       ├── dom.ts
        │       ├── formatting.ts
        │       ├── images.ts
        │       ├── index.ts
        │       ├── seasonal.ts
        │       ├── seo.ts
        │       └── validation.ts
        ├── pages
        │   ├── ProjectDetail.tsx
        │   ├── Projects.tsx
        │   ├── ServiceDetail.tsx
        │   ├── Services.tsx
        │   ├── TestimonialsPage.tsx
        │   ├── about
        │   │   └── index.tsx
        │   ├── contact
        │   │   └── index.tsx
        │   ├── home
        │   │   └── index.tsx
        │   ├── services
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   └── testimonials
        │       └── index.tsx
        ├── styles
        │   ├── animations.css
        │   ├── base.css
        │   └── utilities.css
        ├── types
        │   ├── content.ts
        │   └── index.ts
        └── utils
            └── imageLoader.ts
    ```
