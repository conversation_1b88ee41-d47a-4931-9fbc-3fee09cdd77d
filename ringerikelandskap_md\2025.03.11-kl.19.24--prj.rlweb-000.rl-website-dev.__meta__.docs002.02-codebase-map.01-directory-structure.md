# Codebase Directory Structure

This document provides a comprehensive map of the Ringerike Landskap website codebase organization, focusing on the actual structure rather than an idealized version.

## Root Directory Structure

```
project/
├── .ai-analysis/       # AI-specific screenshots and analysis data
├── content/            # Structured content organization
│   ├── projects/
│   ├── services/
│   ├── team/
│   └── testimonials/
├── docs/               # Project documentation
├── node_modules/       # Dependencies
├── public/             # Static assets
│   └── images/         # Image assets organized by category
├── screenshots/        # Screenshot captures for visual reference
│   ├── latest/         # Always contains most recent screenshots
│   └── [timestamped]/  # Historical screenshot directories
├── scripts/            # Automation scripts
│   ├── screenshot-manager.js
│   ├── dev-with-snapshots.js
│   └── auto-snapshot.js
├── src/                # Source code
└── various config files (package.json, tsconfig.json, etc.)
```

## Source Directory Structure

```
src/
├── app/                # Core application setup
├── components/         # Reusable UI components
│   ├── layout/         # Layout components
│   │   ├── Header.tsx
│   │   └── Footer.tsx
│   └── ui/             # UI components
│       ├── Button.tsx
│       ├── Card.tsx
│       └── etc.
├── contexts/           # React context providers
├── data/               # Mock data and content
│   ├── services.ts
│   ├── projects.ts
│   ├── testimonials.ts
│   ├── team.ts
│   └── navigation.ts
├── features/           # Feature-specific components
│   ├── home/
│   ├── services/
│   └── projects/
├── hooks/              # Custom React hooks
├── lib/                # Third-party integrations
├── pages/              # Route components
│   ├── home/
│   │   └── index.tsx
│   ├── about/
│   │   └── index.tsx
│   ├── services/
│   │   ├── index.tsx
│   │   └── detail.tsx
│   ├── projects/
│   │   ├── index.tsx
│   │   └── detail.tsx
│   └── contact/
│       └── index.tsx
├── styles/             # Global styles
├── types/              # TypeScript type definitions
│   ├── services.ts
│   ├── projects.ts
│   └── common.ts
├── utils/              # Utility functions
├── App.tsx             # Main application component
├── main.tsx            # Application entry point
└── index.css           # Global CSS
```

## Key Relations Between Directories

### Content Flow

```
content/ → src/data/ → components/ → pages/
```

The content directory provides structured content which is imported into data files, then consumed by components and finally rendered in pages.

### Visual Documentation System

```
src/ → scripts/ → screenshots/ and .ai-analysis/
```

The source code is captured through screenshot scripts, which store visual output in both screenshots and .ai-analysis directories.

## Critical Directories

These directories contain essential functionality that must be handled carefully:

1. **scripts/**: Contains screenshot and development automation
2. **src/data/**: Contains all content definitions
3. **src/pages/**: Contains the routing structure
4. **.ai-analysis/**: Contains AI-specific captures and metadata

## Dependency Management

The project uses npm with these key dependencies:

- React ecosystem: react, react-dom, react-router-dom
- UI utilities: clsx, lucide-react, tailwind-merge
- Development tools: vite, typescript, eslint
- Build tools: postcss, autoprefixer, tailwindcss

## Configuration Files

- **vite.config.ts**: Configures the Vite build tool
- **tsconfig.json**: Main TypeScript configuration
- **tsconfig.app.json**: App-specific TypeScript configuration
- **tsconfig.node.json**: Node-specific TypeScript configuration
- **tailwind.config.js**: Tailwind CSS configuration
- **postcss.config.js**: PostCSS configuration
- **eslint.config.js**: ESLint configuration

## Notes on Actual Usage

The structure described above represents the actual organization, with some observations:

1. Not all directories are fully populated (some are placeholders)
2. Some components don't follow the ideal organization pattern
3. The boundaries between features/ and components/ are sometimes blurred
4. Context usage is inconsistent across the application

This map serves as a guide for navigating the codebase while acknowledging its current organizational state. 