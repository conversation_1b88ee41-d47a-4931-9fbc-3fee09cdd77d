# CONTEXT

You are writing system_message instruction templates specifically designed for AUTONOMOUS CONCISTENCY when used as input for autonomous coding assistants such as `vscode+cline` or `cursor ai`.

# OBJECTIVE

Please extract a LLM-OPTIMIZED `system_message` based on the MAXIMALLY HIGH-VALUE INSIGHTS from it - use it as a base to create a system instruction specifically designed to perform **safe and structured code consolidation for codebases such as the one below**:

    # `rl-website_web-001.md`

    ### Abstract-Thought-Chain-As-File-Structure

    ```
    ├── 00-README.md
    ├── 01-abstraction-root
    │   ├── 00-distilledContext-template.md
    │   └── 01-projectbrief-template.md
    ├── 02-context
    │   ├── 02-productContext-template.md
    │   ├── 04-techContext-template.md
    │   └── 09-framework-specific-adaptations-template.md
    ├── 03-structure-design
    │   ├── 03-systemPatterns-template.md
    │   ├── 05-structureMap-template.md
    │   ├── 09-abstract-patterns-glossary-template.md
    │   └── 10-simplification-candidates-template.md
    ├── 04-process-tracking
    │   ├── 06-activeContext-template.md
    │   ├── 07-progress-template.md
    │   ├── 08-tasks-template.md
    │   └── 09-drift-monitor-template.md
    ├── 05-evolution
    │   └── 09-lineage-template.md
    ├── 06-tools
    │   ├── 01-mb-quick-setup.sh
    │   ├── 02-mb-assistant.sh
    │   └── 09-mb-tools.sh
    └── 07-guides
        ├── 00-README.md
        ├── 01-quick-start-guide.md
        ├── 02-visual-guide.md
        ├── 03-processing-order-guide.md
        ├── 09-memory-bank-core-template.md
        ├── 10-memory-bank-enhancement-module.md
        ├── 11-memory-bank-implementation-guide.md
        ├── 12-memory-bank-learning-path.md
        ├── 13-memory-bank-system-instruction.md
        └── 14-memory-bank-web-project-template.md
    ```

    ---

    #### `00-README.md`

    ```markdown

        # Memory Bank Templates

        > **[Structural Role Reminder]**: This document provides an overview of the Memory Bank templates organized by abstraction layers.

        ## Layered Abstraction Structure

        The Memory Bank’s progressive abstraction-structuring is not merely a tool for managing complexity; it enacts the universal tension between entropy (disorder, proliferation, drift) and negentropy (order, focus, essence). Each recursive act of distillation, compression, and structure in the Memory Bank echoes the core principle that understanding—true, actionable clarity—arises not by accumulating more data, but by continually stripping away the non-essential and recentering purpose. Paradoxically, this means that what is preserved is determined by what is continually dissolved, filtered, and abstracted. Structure isn’t a static map but an ongoing act of resistance against sprawl. The deeper insight: every bit of lasting knowledge in the system is defined as much by the structure it excludes as by what it encodes. Thus, the Memory Bank becomes a living demonstration that meaningful digital architecture is not an archive of detail, but a disciplined, evolving negotiation with entropy—where each simplification increases, rather than depletes, the system’s core intelligence.

        The Memory Bank templates are organized according to their level of abstraction, making the system self-describing. Each directory encapsulates one layer of structural responsibility, supporting system cohesion and clarity.

        ```
        memory-bank/
        ├── 00-README.md                # Project overview and navigation
        ├── 01-abstraction-root/        # Irreducible project essence and mission
        │   ├── 00-distilledContext.md  # Ultra-compressed project essence in 2-3 bullets
        │   └── 01-projectbrief.md      # Root purpose definition and critical constraints
        ├── 02-context/                 # External reality and constraints
        │   ├── 02-productContext.md    # User needs, problems solved, and external context
        │   ├── 04-techContext.md       # Technology stack, constraints, and integration points
        │   └── 09-framework-specific-adaptations.md  # Framework-specific patterns and practices
        ├── 03-structure-design/        # Architectural patterns and structure
        │   ├── 03-systemPatterns.md    # System architecture, component hierarchy, and data flows
        │   ├── 05-structureMap.md      # Current and target file structure with migration path
        │   ├── 09-abstract-patterns-glossary.md      # Reusable patterns and anti-patterns
        │   └── 10-simplification-candidates.md       # High-impact simplification tracking
        ├── 04-process-tracking/        # Active development and status
        │   ├── 06-activeContext.md     # Current focus, bottlenecks, and in-progress simplifications
        │   ├── 07-progress.md          # Milestones, metrics, and simplifications achieved
        │   ├── 08-tasks.md             # Structure-anchored tasks supporting project mission
        │   └── 09-drift-monitor.md     # Structural integrity monitoring
        ├── 05-evolution/               # Cognitive evolution
        │   └── 09-lineage.md           # Cognitive evolution snapshots
        ├── 06-tools/                   # Automation tools
        │   ├── 01-mb-quick-setup.sh    # 5-minute setup script
        │   ├── 02-mb-assistant.sh      # Interactive guidance tool
        │   └── 09-mb-tools.sh          # CLI utilities for Memory Bank maintenance
        └── 07-guides/                  # Implementation guidance
            ├── 00-README.md            # Guide directory navigation
            ├── 01-quick-start-guide.md # Fast onboarding guide
            ├── 02-visual-guide.md      # Visual diagrams and workflows
            ├── 03-processing-order-guide.md # Processing order clarification
            ├── 09-memory-bank-core-template.md       # Core system principles
            ├── 10-memory-bank-enhancement-module.md  # Advanced enhancement modules
            ├── 11-memory-bank-implementation-guide.md # Implementation instructions
            ├── 12-memory-bank-learning-path.md       # Graduated learning approach
            ├── 13-memory-bank-system-instruction.md  # AI system instructions
            └── 14-memory-bank-web-project-template.md # Web-specific adaptation
        ```

        ## Numeric Ordering System

        The Memory Bank uses a dual-level numeric ordering system:

        1. **Directory Level (01-07)**: Folders are numbered to reflect their position in the abstraction hierarchy, from most abstract (01) to implementation guides (07).

        2. **File Level (00-14)**: Files within each directory are also numbered:
           - **Core Files (00-08)**: The primary Memory Bank files that form the backbone of the system
           - **Extension Files (09+)**: Additional files that enhance and support the core structure

        This systematic numbering ensures that:
        - Files are naturally sorted in the intended sequence when viewed in file browsers
        - The abstraction hierarchy is reinforced through the numeric prefixes
        - Related files are grouped together conceptually

        ## Implementation Flow

        The exact sequence for processing Memory Bank files is:

        1. Start with this README (`00-README.md`)
        2. Create and fill in core files in numerical sequence across directories (00-08)
        3. Add enhancement files (09+) as needed for your project
        4. Use tools and guides for reference and assistance

        For detailed guidance on the exact processing order, see:
        `07-guides/03-processing-order-guide.md`

        ## Directory Purposes

        | Directory | Purpose |
        |-----------|---------|
        | `01-abstraction-root/` | Contains the most abstract files that define the project's irreducible purpose and essence. |
        | `02-context/` | Holds files that define the external reality, user needs, and technical constraints. |
        | `03-structure-design/` | Contains the architectural patterns, structure mapping, and simplification strategies. |
        | `04-process-tracking/` | Tracks active development work, progress, tasks, and structural integrity. |
        | `05-evolution/` | Captures the cognitive evolution of the project through lineage entries. |
        | `06-tools/` | Provides automation tools for Memory Bank maintenance and validation. |
        | `07-guides/` | Offers comprehensive guidance on implementing and using the Memory Bank system. |

        ## Self-Describing Structure

        This layered organization reinforces the Memory Bank's core principles:

        1. **File-Structure-First**: The directory structure itself communicates the conceptual hierarchy.
        2. **Root-First Thinking**: The abstraction layers descend from the root purpose outward.
        3. **Persistent Simplification**: Each layer compresses information at its appropriate abstraction level.
        4. **Value Extraction Bias**: The structure maximizes clarity by grouping related concepts.
        5. **Outward Mapping**: Moving from abstract (`01-abstraction-root/`) to concrete (`04-process-tracking/`).

        ## Using This Repository

        1. For quick setup, use the script: `06-tools/01-mb-quick-setup.sh`
        2. For interactive guidance, run: `06-tools/02-mb-assistant.sh`
        3. For AI assistant integration, copy `07-guides/13-memory-bank-system-instruction.md`
        4. For visual guides, see `07-guides/02-visual-guide.md`
        5. For processing order clarity, see `07-guides/03-processing-order-guide.md`

        ## Special File: AI System Instruction

        For AI coding assistants like Cline or Cursor AI, use the dedicated system instruction file:

        `07-guides/13-memory-bank-system-instruction.md`

        This file provides a consolidated instruction that can be copied directly into your AI assistant's system instruction settings.

        ---

        *Note: This layered structure represents the ideal organization for a Memory Bank system. For simpler projects, you may start with just the numbered files (00-08) and add the other layers as your project evolves.*
    ```

    ---

    #### `01-abstraction-root\00-distilledContext-template.md`

    ```markdown
        # Distilled Context

        > **[Structural Role Reminder]**: This file provides the ultra-compressed project essence in 2-3 bullets. It is the fastest path back to the project's core purpose.

        ## Project Irreducible Essence

        - **Purpose**: [One sentence stating why this project must exist]
        - **Value**: [One sentence describing the unique value it provides]
        - **Core Constraint**: [One sentence on the primary constraint that shapes everything else]

        ## Current Focus

        - [Brief description of the current highest-priority focus area]
        - [How this focus directly reinforces the project's root purpose]

        ---

        *Note: This document must be revisited and revalidated before starting any significant development work. All actions must trace back to this distilled essence.*
    ```

    ---

    #### `01-abstraction-root\01-projectbrief-template.md`

    ```markdown
        # Project Brief

        > **[Structural Role Reminder]**: This file defines the root abstraction - the project's irreducible mission, value proposition, and critical constraints. All other aspects of the project must trace back to this document.

        ## Mission Statement

        [One paragraph clearly defining why this project must exist, who it serves, and what core problem it solves]

        ## Core Value Proposition

        - **For**: [Target users/audience]
        - **Who**: [Description of the key problem/need]
        - **This Project**: [How it uniquely addresses that need]
        - **Unlike**: [Alternatives or current solutions]
        - **Our Solution**: [Key differentiator or unique approach]

        ## Critical Constraints

        ### Technical Constraints

        - [List technical boundaries that cannot be crossed]
        - [Performance requirements]
        - [Compatibility requirements]
        - [Security requirements]

        ### Business Constraints

        - [Timeline constraints]
        - [Resource constraints]
        - [Stakeholder requirements]
        - [Market/competitive constraints]

        ### User Experience Constraints

        - [Core user expectations that must be met]
        - [Accessibility requirements]
        - [Usability standards]
        - [Brand alignment requirements]

        ## Success Definition

        [Clear, measurable criteria that define when this project has succeeded in its mission]

        ---

        *Note: This is the root document against which all project decisions must be validated. Any deviation must be explicitly justified and documented in the Memory Bank.*
    ```

    ---

    #### `02-context\02-productContext-template.md`

    ```markdown
        # Product Context

        > **[Structural Role Reminder]**: This file maps external reality (users, needs, outcomes) and justifies features against the root purpose defined in `1-projectbrief.md`.

        ## User Archetypes

        ### Primary User: [Name]

        - **Demographics**: [Age, location, technical proficiency, etc.]
        - **Goals**: [What they want to accomplish]
        - **Pain Points**: [Current challenges they face]
        - **Interaction Patterns**: [How they typically use similar products/services]
        - **Success Metrics**: [How they measure their own success]

        ### Secondary User: [Name]

        - [Same structure as above]

        ## Problems Solved

        | Problem | Current Reality | Our Solution | Value Created |
        |---------|----------------|--------------|--------------|
        | [Problem 1] | [How it's handled now] | [Our approach] | [Specific value] |
        | [Problem 2] | [How it's handled now] | [Our approach] | [Specific value] |
        | [Problem 3] | [How it's handled now] | [Our approach] | [Specific value] |

        ## User Journey

        1. **Discovery**: [How users find and decide to use our product]
        2. **Onboarding**: [Initial experience and key first interactions]
        3. **Core Usage**: [Primary value-delivering interactions]
        4. **Mastery**: [Advanced features and long-term engagement]
        5. **Advocacy**: [How users share and promote our product]

        ## External Context Factors

        ### Seasonal Considerations

        - [Any seasonal patterns that affect usage or needs]
        - [How the product needs to adapt to these patterns]

        ### Geographic/Regional Factors

        - [Location-specific considerations]
        - [Any regional adaptations needed]

        ### Market/Competitive Context

        - [Key competitors and their approaches]
        - [Market trends affecting user needs or expectations]
        - [Our differentiators in this context]

        ## Value Connections

        [Explicit connections showing how addressing user needs directly supports the project mission from `1-projectbrief.md`]

        ---

        *Note: All features and design decisions must be traceable to the user needs and problems documented here, which in turn must support the core mission in `1-projectbrief.md`.*
    ```

    ---

    #### `02-context\04-techContext-template.md`

    ```markdown
        # Technical Context

        > **[Structural Role Reminder]**: This file defines the technical constraints and stack choices that form the material boundaries of the project. It documents what tools are used and why.

        ## Technology Stack

        ### Core Technologies

        | Technology | Version | Purpose | Justification |
        |------------|---------|---------|--------------|
        | [Framework] | [Version] | [Purpose] | [Why this choice supports the project mission] |
        | [Language] | [Version] | [Purpose] | [Why this choice supports the project mission] |
        | [Database] | [Version] | [Purpose] | [Why this choice supports the project mission] |
        | [State Management] | [Version] | [Purpose] | [Why this choice supports the project mission] |
        | [CSS Solution] | [Version] | [Purpose] | [Why this choice supports the project mission] |

        ### Build & Development Tools

        | Tool | Version | Purpose | Configuration |
        |------|---------|---------|--------------|
        | [Bundler] | [Version] | [Purpose] | [Key configuration details] |
        | [Linter] | [Version] | [Purpose] | [Key configuration details] |
        | [Type Checker] | [Version] | [Purpose] | [Key configuration details] |
        | [Test Runner] | [Version] | [Purpose] | [Key configuration details] |
        | [Package Manager] | [Version] | [Purpose] | [Key configuration details] |

        ## Key Technical Constraints

        ### Performance Requirements

        - **Load Time Target**: [Specific target, e.g., "< 2 seconds on 4G"]
        - **Rendering Performance**: [Specific target, e.g., "60fps for animations"]
        - **Bundle Size Limit**: [Specific target, e.g., "< 200KB initial load"]
        - **Memory Usage**: [Specific target if applicable]

        ### Compatibility Requirements

        - **Browser Support**: [List of supported browsers and minimum versions]
        - **Device Support**: [Types of devices: desktop, tablet, mobile, etc.]
        - **Screen Size Range**: [Min/max supported screen dimensions]
        - **Offline Capabilities**: [Requirements for offline operation if applicable]

        ### Security Requirements

        - **Authentication Method**: [Details of the authentication approach]
        - **Data Protection**: [Requirements for data encryption, storage]
        - **API Security**: [How API calls are secured]
        - **Compliance Requirements**: [Any legal/regulatory requirements]

        ## Integration Points

        ### External APIs

        | API | Purpose | Authentication Method | Rate Limits | Data Format |
        |-----|---------|----------------------|------------|-------------|
        | [API Name] | [Purpose] | [Auth Method] | [Rate Limits] | [Format] |
        | [API Name] | [Purpose] | [Auth Method] | [Rate Limits] | [Format] |

        ### Third-Party Services

        | Service | Purpose | Integration Method | Fallback Strategy |
        |---------|---------|-------------------|------------------|
        | [Service] | [Purpose] | [Integration Method] | [Fallback if service fails] |
        | [Service] | [Purpose] | [Integration Method] | [Fallback if service fails] |

        ## Technical Debt & Known Limitations

        | Area | Description | Impact | Mitigation Strategy |
        |------|-------------|--------|---------------------|
        | [Area] | [Description of limitation] | [Impact on project] | [How we're managing this] |
        | [Area] | [Description of limitation] | [Impact on project] | [How we're managing this] |

        ## Development Environment

        ```plaintext
        [Detailed steps for setting up the development environment]
        ```

        ## Build & Deployment Process

        ```mermaid
        flowchart TD
            Local[Local Development] --> Build[Build Process]
            Build --> Test[Automated Tests]
            Test --> Deploy[Deployment]
            Deploy --> Verify[Verification]
        ```

        [Description of the build and deployment process, including CI/CD if applicable]

        ---

        *Note: All technical decisions must be evaluated against these constraints and stack choices, which in turn must support the architectural patterns in `3-systemPatterns.md`, the user needs in `2-productContext.md`, and ultimately the mission in `1-projectbrief.md`.*
    ```

    ---

    #### `02-context\09-framework-specific-adaptations-template.md`

    ```markdown
        # Framework-Specific Memory Bank Adaptations

        > **[Structural Role Reminder]**: This file provides specialized adaptation guidelines for using the Memory Bank system with specific frontend frameworks, ensuring framework-appropriate patterns and optimizations.

        ## React/TypeScript Adaptation

        ### Component Architecture Patterns

        ```typescript
        // Preferred component pattern
        const Component: React.FC<Props> = ({ prop1, prop2 }) => {
          // Hooks at the top
          const [state, setState] = useState<StateType>(initialState);

          // Side effects grouped by concern
          useEffect(() => {
            // Side effect implementation
          }, [dependencies]);

          // Event handlers with explicit naming
          const handleEvent = () => {
            // Implementation
          };

          // Return statement at the bottom
          return (
            <div>
              {/* JSX structure */}
            </div>
          );
        };
        ```

        ### Memory Bank Enhancements for React Projects

        | File | React-Specific Focus |
        |------|---------------------|
        | `3-systemPatterns.md` | Document component hierarchy, prop drilling limits, state management approach, React memoization strategy |
        | `4-techContext.md` | React version, key libraries (React Router, state management, UI kit), build optimizations |
        | `5-structureMap.md` | Feature-based vs. component-based organization, shared components strategy |
        | `simplification-candidates.md` | Focus on component consolidation, custom hook extraction, render optimization |

        ### React-Specific Forbidden Patterns Checklist

        - [ ] Class components (unless absolutely necessary for lifecycle methods)
        - [ ] Deeply nested component trees (>3 levels without composition)
        - [ ] Prop drilling beyond 3 levels (use context or state management)
        - [ ] Inline event handlers in JSX (`onClick={() => handleClick()}`)
        - [ ] Large components (>250 lines)
        - [ ] Overuse of `useEffect` for business logic
        - [ ] Direct DOM manipulation (use refs properly instead)
        - [ ] Non-memoized callbacks in dependency arrays

        ### React-Specific Complexity Reduction Paths

        1. **Component Atomization**:
           - Break monolithic components into smaller, focused ones
           - Extract reusable UI primitives to `05_ui/` directory
           - Implement compound component patterns for flexibility

        2. **State Management Consolidation**:
           - Map all state management approaches and consolidate
           - Move business logic out of components into hooks
           - Establish clear data flow patterns (unidirectional preferred)

        3. **Custom Hooks Library**:
           - Extract repeated logic into custom hooks
           - Standardize hook naming and parameter patterns
           - Document hook usage with TypeScript interface examples

        ## Vue.js Adaptation

        ### Component Architecture Patterns

        ```vue
        <!-- Preferred Vue component pattern -->
        <template>
          <!-- Template structure -->
        </template>

        <script setup lang="ts">
        // Imports
        import { ref, computed, onMounted } from 'vue';
        import type { PropType } from 'vue';

        // Props definition
        const props = defineProps({
          prop1: { type: String as PropType<string>, required: true },
          prop2: { type: Number as PropType<number>, default: 0 },
        });

        // Emits definition
        const emit = defineEmits(['update', 'submit']);

        // Reactive state
        const state = ref<StateType>(initialState);

        // Computed properties
        const derivedValue = computed(() => {
          // Computation
        });

        // Lifecycle hooks
        onMounted(() => {
          // Initialization
        });

        // Methods
        function handleEvent() {
          // Implementation
          emit('update', state.value);
        }
        </script>

        <style scoped>
        /* Component styles */
        </style>
        ```

        ### Memory Bank Enhancements for Vue Projects

        | File | Vue-Specific Focus |
        |------|---------------------|
        | `3-systemPatterns.md` | Component options vs. composition API usage, prop validation, emit contracts |
        | `4-techContext.md` | Vue version, Pinia/Vuex, Vue Router, build optimizations |
        | `5-structureMap.md` | Feature-based organization, single-file component boundaries |
        | `simplification-candidates.md` | Focus on composables extraction, template simplification |

        ### Vue-Specific Complexity Reduction Paths

        1. **Composition API Migration**:
           - Convert options API components to composition API
           - Extract reusable composables for shared behavior
           - Implement provide/inject for cross-component state

        2. **Component Library Organization**:
           - Establish clear boundaries between presentational and container components
           - Extract form controls into reusable pattern library
           - Standardize prop validation patterns

        ## Angular Adaptation

        ### Component Architecture Patterns

        ```typescript
        // Preferred Angular component pattern
        @Component({
          selector: 'app-component',
          templateUrl: './component.component.html',
          styleUrls: ['./component.component.scss'],
          changeDetection: ChangeDetectionStrategy.OnPush
        })
        export class ComponentComponent implements OnInit, OnDestroy {
          // Inputs/Outputs
          @Input() inputProp!: string;
          @Output() outputEvent = new EventEmitter<EventType>();

          // Private properties
          private destroy$ = new Subject<void>();

          // Public properties for template binding
          public viewModel: ViewModel;

          // Constructor for dependency injection
          constructor(private service: Service) {}

          // Lifecycle hooks
          ngOnInit(): void {
            this.service.getData()
              .pipe(takeUntil(this.destroy$))
              .subscribe(data => {
                // Handle data
              });
          }

          ngOnDestroy(): void {
            this.destroy$.next();
            this.destroy$.complete();
          }

          // Public methods
          handleEvent(): void {
            // Implementation
            this.outputEvent.emit(value);
          }
        }
        ```

        ### Memory Bank Enhancements for Angular Projects

        | File | Angular-Specific Focus |
        |------|---------------------|
        | `3-systemPatterns.md` | NgModule structure, lazy loading patterns, change detection strategy |
        | `4-techContext.md` | Angular version, state management (NgRx/Akita), compilation optimizations |
        | `5-structureMap.md` | Feature module organization, shared module strategies |
        | `simplification-candidates.md` | Focus on smart/dumb component separation, directive extraction |

        ### Angular-Specific Complexity Reduction Paths

        1. **Module Optimization**:
           - Review and optimize NgModule structure
           - Implement proper lazy loading for feature modules
           - Extract shared functionality to proper shared modules

        2. **Change Detection Optimization**:
           - Apply OnPush change detection strategy consistently
           - Implement proper immutability patterns
           - Use async pipe instead of manual subscription management

        3. **Service Layer Refinement**:
           - Consolidate service responsibilities
           - Implement proper service inheritance patterns where beneficial
           - Establish clear injection hierarchy

        ## Framework Agnostic Best Practices

        Regardless of the frontend framework used, these principles should be applied:

        1. **Naming Consistency**:
           - Establish and document naming conventions in `3-systemPatterns.md`
           - Enforce consistent patterns via linting rules documented in `4-techContext.md`

        2. **Type Safety**:
           - Document type hierarchy and shared interfaces in `3-systemPatterns.md`
           - Establish type guard patterns for runtime validation

        3. **Performance Patterns**:
           - Document rendering optimization strategies specific to the framework
           - Establish standardized performance measurement and budgets

        4. **Testing Strategy**:
           - Document component testing approach in `4-techContext.md`
           - Establish clear boundaries for unit vs. integration tests

        ## Implementation Steps

        To adapt the Memory Bank to a specific framework:

        1. Identify framework-specific patterns and anti-patterns
        2. Document these in a framework-specific section of `3-systemPatterns.md`
        3. Create framework-specific complexity reduction paths in `simplification-candidates.md`
        4. Update the drift monitor to include framework-specific drift patterns
        5. Modify CLI tools to check for framework-specific best practices

        > **Framework-Specific Root Connection**: Remember that framework choices must serve the project's root purpose, not dictate it. Always validate framework-specific decisions against `1-projectbrief.md` to ensure technology serves mission.

        ---

        *Note: This document should be used alongside the core Memory Bank templates to add framework-specific structure and guidance. When starting a new project, choose the appropriate framework section and incorporate its patterns into your Memory Bank.*
    ```

    ---

    #### `03-structure-design\03-systemPatterns-template.md`

    ```markdown
        # System Patterns

        > **[Structural Role Reminder]**: This file defines the architectural form of the project - how components and systems are organized, how data flows, and how state is managed. It serves as the blueprint for structural order.

        ## Architecture Overview

        ```mermaid
        flowchart TD
            User([User]) --> UI[UI Layer]
            UI --> Features[Feature Modules]
            Features --> Domain[Domain Logic]
            Domain --> Data[Data Layer]
            Data --> External[External Services]
        ```

        [Brief explanation of the overall architecture and how it supports the project mission]

        ## Current Structure

        ```plaintext
        [Current project file structure - should be detailed enough to understand the organization]
        ```

        ## Target Structure

        ```plaintext
        [Target/ideal project file structure - often more organized and simplified]
        ```

        [Explanation of why this target structure better supports the project mission and addresses issues in the current structure]

        ## Component Hierarchy

        ```mermaid
        flowchart TD
            App --> Layout
            Layout --> Pages
            Pages --> FeatureModules
            FeatureModules --> Components
            Components --> UIElements
        ```

        ### Key Component Types

        | Type | Responsibility | Examples | Rules |
        |------|----------------|----------|-------|
        | Layout | [Responsibility] | [Examples] | [Rules] |
        | Page | [Responsibility] | [Examples] | [Rules] |
        | Feature Module | [Responsibility] | [Examples] | [Rules] |
        | Component | [Responsibility] | [Examples] | [Rules] |
        | UI Element | [Responsibility] | [Examples] | [Rules] |

        ## Data Flow Patterns

        ```mermaid
        flowchart LR
            DataSource[Data Source] --> Fetch[Data Fetching]
            Fetch --> StateManagement[State Management]
            StateManagement --> UI[UI Rendering]
            UI --> UserInteraction[User Interaction]
            UserInteraction --> StateUpdates[State Updates]
            StateUpdates --> DataPersistence[Data Persistence]
            DataPersistence --> DataSource
        ```

        ### State Management

        - **Global State**: [Description of global state approach]
        - **Feature State**: [Description of feature-scoped state approach]
        - **Component State**: [Description of local component state approach]
        - **Data Persistence**: [Approach to persisting state]

        ### Data Fetching Strategy

        - **Primary Method**: [Main data fetching approach]
        - **Caching Strategy**: [How data is cached]
        - **Error Handling**: [Standard approach to handling fetch errors]
        - **Loading States**: [How loading states are managed]

        ## Routing Architecture

        - **Route Structure**: [Description of route organization]
        - **Route Parameters**: [How route parameters are defined and used]
        - **Navigation Guards**: [Access control and navigation protection]
        - **Lazy Loading**: [Strategy for code splitting and lazy loading]

        ## Cross-Cutting Concerns

        - **Authentication**: [Authentication approach and flow]
        - **Error Handling**: [Global error handling strategy]
        - **Logging**: [Logging approach and tools]
        - **Internationalization**: [i18n approach if applicable]
        - **Accessibility**: [a11y standards and implementation]
        - **Performance**: [Performance monitoring and optimization]

        ## Implementation Patterns

        ### Component Implementation Pattern

        ```typescript
        // Example component pattern
        function ExampleComponent(props: Props) {
          // State declarations

          // Effects/lifecycle

          // Event handlers

          // Helper functions

          // Render
          return (
            // JSX or template
          );
        }
        ```

        ### Feature Module Pattern

        ```plaintext
        feature/
        ├── components/         # Feature-specific components
        ├── hooks/              # Feature-specific hooks
        ├── utils/              # Feature-specific utilities
        ├── types.ts            # Type definitions
        ├── constants.ts        # Constants
        └── index.ts            # Public API exports
        ```

        ---

        *Note: All implementation decisions must trace back to these patterns, which in turn support the user needs in `2-productContext.md` and the mission in `1-projectbrief.md`.*
    ```

    ---

    #### `03-structure-design\05-structureMap-template.md`

    ```markdown
        # Structure Map

        > **[Structural Role Reminder]**: This file maps the current file structure against the target structure, providing a clear synthesis of where we are, where we're going, and the path between them.

        ## Current Structure Reality

        ```plaintext
        [Full current project file structure, with indentation to show hierarchy]
        ```

        ### Structure Analysis

        | Area | Current State | Issues | Root Cause |
        |------|--------------|--------|------------|
        | [Area 1] | [Current organization] | [Problems identified] | [What caused this structure] |
        | [Area 2] | [Current organization] | [Problems identified] | [What caused this structure] |
        | [Area 3] | [Current organization] | [Problems identified] | [What caused this structure] |

        ## Target Structure

        ```plaintext
        [Target project file structure, with indentation to show hierarchy]
        ```

        ### Target Structure Justification

        | Area | Target Organization | Benefits | Alignment to Project Goals |
        |------|---------------------|----------|----------------------------|
        | [Area 1] | [Target organization] | [Expected benefits] | [How this supports project mission] |
        | [Area 2] | [Target organization] | [Expected benefits] | [How this supports project mission] |
        | [Area 3] | [Target organization] | [Expected benefits] | [How this supports project mission] |

        ## Migration Path

        ```mermaid
        gantt
            title Structure Migration Plan
            dateFormat  YYYY-MM-DD
            section Area 1
            Refactor X           :a1, 2023-01-01, 7d
            Move Y               :a2, after a1, 5d
            section Area 2
            Consolidate Z        :a3, 2023-01-10, 10d
        ```

        ### Migration Steps

        1. **[Step 1]**
           - Actions: [Specific actions to take]
           - Dependencies: [What must be done first]
           - Risk Factors: [Potential issues to watch for]
           - Verification: [How to confirm success]

        2. **[Step 2]**
           - Actions: [Specific actions to take]
           - Dependencies: [What must be done first]
           - Risk Factors: [Potential issues to watch for]
           - Verification: [How to confirm success]

        3. **[Step 3]**
           - Actions: [Specific actions to take]
           - Dependencies: [What must be done first]
           - Risk Factors: [Potential issues to watch for]
           - Verification: [How to confirm success]

        ## Dependency Management

        | Module | Current Dependencies | Target Dependencies | Migration Strategy |
        |--------|---------------------|---------------------|-------------------|
        | [Module 1] | [Current dependencies] | [Target dependencies] | [How to transition] |
        | [Module 2] | [Current dependencies] | [Target dependencies] | [How to transition] |
        | [Module 3] | [Current dependencies] | [Target dependencies] | [How to transition] |

        ## Impact Analysis

        | Area | Refactoring Impact | User Impact | Testing Requirements |
        |------|-------------------|------------|---------------------|
        | [Area 1] | [Development impact] | [Impact on users] | [Testing needs] |
        | [Area 2] | [Development impact] | [Impact on users] | [Testing needs] |
        | [Area 3] | [Development impact] | [Impact on users] | [Testing needs] |

        ## Structure Evolution History

        | Date | Change | Rationale | Outcome |
        |------|--------|-----------|---------|
        | [Date] | [Structure change] | [Why it was done] | [Results of change] |
        | [Date] | [Structure change] | [Why it was done] | [Results of change] |

        ---

        *Note: This structure map must be kept in sync with both the current reality and the architectural principles in `3-systemPatterns.md`. Any deviation from the target structure must be explicitly justified.*
    ```

    ---

    #### `03-structure-design\09-abstract-patterns-glossary-template.md`

    ```markdown
        # Abstract Patterns Glossary

        > **[Structural Role Reminder]**: This file catalogs fundamental structural patterns, anti-patterns, and complexity reduction strategies to serve as a reusable reference across the Memory Bank system.

        ## Core Structural Patterns

        ### Root Anchoring Pattern
        **Definition**: Every element in the system traces its existence and purpose directly to the project's irreducible mission.
        **Implementation**: Explicit references to `1-projectbrief.md` or `0-distilledContext.md` in all files.
        **Violation Example**: Content that cannot justify its connection to the root purpose.
        **Complexity Reduction**: Eliminates scope creep and purpose drift by forcing alignment to core mission.

        ### Progressive Abstraction Pattern
        **Definition**: Information is organized in descending levels of abstraction, from most abstract (root) to most concrete (tasks).
        **Implementation**: Numbered file hierarchy (0-8) with clear abstraction boundaries.
        **Violation Example**: Implementation details appearing in high-level abstraction files.
        **Complexity Reduction**: Makes information retrieval predictable; readers can find appropriate abstraction level.

        ### Compression-First Pattern
        **Definition**: New information is first attempted to be compressed into existing patterns before being added independently.
        **Implementation**: The compression check protocol documented before any substantial addition.
        **Violation Example**: Adding new lists or sections without attempting pattern extraction.
        **Complexity Reduction**: Forces continuous abstraction and pattern recognition, preventing documentation bloat.

        ### Single Responsibility Pattern
        **Definition**: Each file has one clearly defined cognitive role with non-overlapping scope.
        **Implementation**: Structural role declarations at the top of every file clearly defining its purpose.
        **Violation Example**: A file that serves multiple purposes or contains information that belongs elsewhere.
        **Complexity Reduction**: Prevents cognitive overload; readers know exactly what to expect from each file.

        ### Metabolic Pruning Pattern
        **Definition**: The system actively removes redundant, obsolete, or non-aligned information rather than passively accumulating it.
        **Implementation**: Regular audits, drift monitoring, and explicit pruning activities.
        **Violation Example**: Growing files without proportional value increase.
        **Complexity Reduction**: Prevents entropy accumulation by forcing active maintenance.

        ### High-Impact Simplification Pattern
        **Definition**: Focus on interventions that provide maximum clarity gain for minimum effort.
        **Implementation**: Scoring candidate simplifications by impact, effort, clarity, and root alignment.
        **Violation Example**: Making complex changes with minimal clarity improvement.
        **Complexity Reduction**: Ensures effort is directed where it will produce maximum value.

        ### Cognitive Evolution Pattern
        **Definition**: Documenting significant shifts in understanding to maintain institutional knowledge.
        **Implementation**: Lineage entries capturing context, catalysts, and structural impact of realizations.
        **Violation Example**: Making structural changes without documenting rationale.
        **Complexity Reduction**: Prevents repeated discovery of the same insights; preserves reasoning.

        ## Anti-Patterns to Avoid

        ### Passive Documentation
        **Anti-Pattern**: Accumulating information without processing it into structure.
        **Detection Signs**: Growing file size without corresponding clarity improvement.
        **Correction**: Apply compression-first principle; extract patterns from instances.

        ### Detail Sprawl
        **Anti-Pattern**: Expanding documentation with implementation details rather than patterns.
        **Detection Signs**: Lengthy how-to sections, long lists of steps, code fragments.
        **Correction**: Elevate to patterns; move implementation details to code comments or specialized guides.

        ### Abstraction Leakage
        **Anti-Pattern**: Information appearing at the wrong abstraction level.
        **Detection Signs**: Technical details in root files; purpose statements in task files.
        **Correction**: Relocate to appropriate file; ensure each file maintains abstraction purity.

        ### Orphaned Content
        **Anti-Pattern**: Information not clearly connected to project root purpose.
        **Detection Signs**: Sections that cannot justify their existence in terms of project mission.
        **Correction**: Either explicitly connect to root or remove; no floating information allowed.

        ### Root Drift
        **Anti-Pattern**: Gradual disconnection from the project's core purpose.
        **Detection Signs**: Files that no longer reference or support the mission in `1-projectbrief.md`.
        **Correction**: Re-anchor all content to root purpose; prune what cannot be justified.

        ### Temporal Buildup
        **Anti-Pattern**: Organizing information chronologically rather than structurally.
        **Detection Signs**: Files organized by date rather than concept; historical narrative.
        **Correction**: Extract patterns and restructure by abstraction, not timeline.

        ### Structural Duplication
        **Anti-Pattern**: Same concept appearing in multiple files or locations.
        **Detection Signs**: Repeated explanations, copy-pasted sections.
        **Correction**: Consolidate to single location, reference from other places.

        ## Complexity Reduction Strategies

        ### Pattern Extraction
        **Strategy**: Identify common elements across multiple items and create a pattern that encompasses them.
        **When to Use**: When you have 3+ similar items, concepts, or sections.
        **Example**:
        ```
        # Instead of:
        - Feature A handles input validation with regex
        - Feature B handles input validation with regex
        - Feature C handles input validation with regex

        # Use:
        Pattern: Features employ regex for input validation
        Features using this pattern: A, B, C
        ```

        ### Abstraction Elevation
        **Strategy**: Move detailed concepts to a higher abstraction level by identifying their shared principles.
        **When to Use**: When implementation details obscure the underlying pattern.
        **Example**:
        ```
        # Instead of:
        The button uses flex-box with centered text and has hover states.

        # Use:
        UI elements follow responsive design principles with consistent interaction states.
        ```

        ### Reference Instead of Duplication
        **Strategy**: Refer to information in its canonical location rather than repeating it.
        **When to Use**: When the same information would otherwise be duplicated.
        **Example**:
        ```
        # Instead of:
        This component handles user authentication with JWT tokens and validates credentials against the database.

        # Use:
        This component implements the Authentication Pattern (see 3-systemPatterns.md).
        ```

        ### Tabular Compression
        **Strategy**: Convert narrative text to tables when presenting structured information.
        **When to Use**: For comparisons, feature matrices, or structured descriptions.
        **Example**:
        ```
        # Instead of:
        Feature A is high priority. It will take about 2 days. It supports the project mission by enhancing user experience.
        Feature B is medium priority. It will take about 1 day. It supports the project mission by fixing bugs.

        # Use:
        | Feature | Priority | Effort | Mission Alignment |
        |---------|----------|--------|-------------------|
        | A       | High     | 2 days | UX enhancement    |
        | B       | Medium   | 1 day  | Bug fixing        |
        ```

        ### Visualize Relationships
        **Strategy**: Use diagrams (Mermaid) to express relationships instead of text descriptions.
        **When to Use**: For workflows, hierarchies, and dependency relationships.
        **Example**:
        ```
        # Instead of:
        Component A depends on Component B, which depends on Component C.

        # Use:
        ```mermaid
        graph TD
            A --> B --> C
        ```
        ```

        ### Hierarchical Structuring
        **Strategy**: Organize information in clear hierarchies with consistent header levels.
        **When to Use**: For all documentation to enforce clear relationships.
        **Example**:
        ```
        # H1: Major Section
        ## H2: Subsection
        ### H3: Specific Topic

        # Not mixed levels like:
        ### Specific Topic
        ## Subsection
        #### Detailed point
        ```

        ### Prune Obsolete Information
        **Strategy**: Actively remove information that is no longer relevant or accurate.
        **When to Use**: During regular audits or when new information supersedes old.
        **Example**: Delete sections that describe deprecated approaches, or move to lineage for historical reference.

        ## Applying Patterns in Memory Bank Files

        | File | Primary Patterns to Apply | Anti-Patterns to Avoid |
        |------|---------------------------|------------------------|
        | `0-distilledContext.md` | Root Anchoring, Extreme Compression | Detail Sprawl, Abstraction Leakage |
        | `1-projectbrief.md` | Root Anchoring, Progressive Abstraction | Root Drift, Detail Sprawl |
        | `2-productContext.md` | Root Anchoring, Tabular Compression | Temporal Buildup, Orphaned Content |
        | `3-systemPatterns.md` | Pattern Extraction, Visualize Relationships | Detail Sprawl, Abstraction Leakage |
        | `4-techContext.md` | Tabular Compression, Reference Instead of Duplication | Detail Sprawl, Structural Duplication |
        | `5-structureMap.md` | Visualize Relationships, Hierarchical Structuring | Abstraction Leakage, Temporal Buildup |
        | `6-activeContext.md` | Tabular Compression, Single Responsibility | Temporal Buildup, Passive Documentation |
        | `7-progress.md` | Tabular Compression, Metabolic Pruning | Passive Documentation, Temporal Buildup |
        | `8-tasks.md` | Root Anchoring, Tabular Compression | Orphaned Content, Root Drift |
        | `drift-monitor.md` | Tabular Compression, Pattern Extraction | Passive Documentation, Structural Duplication |
        | `simplification-candidates.md` | High-Impact Simplification, Tabular Compression | Detail Sprawl, Passive Documentation |
        | `/lineage/` | Cognitive Evolution, Single Responsibility | Temporal Buildup, Detail Sprawl |

        ---

        *Note: This glossary serves as a reference for consistent pattern application across the Memory Bank. It should be updated when new patterns are identified or existing patterns are refined.*
    ```

    ---

    #### `03-structure-design\10-simplification-candidates-template.md`

    ```markdown
        # High-Impact Simplification Candidates

        > **[Structural Role Reminder]**: This file formalizes the process of identifying and implementing simplifications that provide maximum impact for minimum intervention, focusing attention on strategic improvements.

        ## Evaluation Criteria

        1. **Minimal Implementation Effort** (1-10): How easy is this to implement?
           - *1*: Requires months of work, multiple developers
           - *5*: Requires days of work by one developer
           - *10*: Can be implemented in hours by one developer

        2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?
           - *1*: Marginal improvement in structure clarity
           - *5*: Noticeable improvement in one area of the project
           - *10*: Transformative clarity enhancement across multiple areas

        3. **Widespread Impact** (1-10): How many areas will benefit?
           - *1*: Benefits limited to a small, isolated area
           - *5*: Benefits several interconnected components
           - *10*: Benefits the entire project ecosystem

        4. **Root Reinforcement** (1-10): How strongly does this support the project mission?
           - *1*: Tangential connection to project mission
           - *5*: Clear connection to project mission
           - *10*: Directly strengthens the core project purpose

        5. **Impact Score** = (Clarity × Impact × Root) ÷ Effort
           - Higher scores indicate more "bang for your buck" simplifications

        ## Current Candidates

        | ID | Simplification | Effort (1-10) | Clarity (1-10) | Impact (1-10) | Root (1-10) | Score | Status |
        |----|----------------|--------------|---------------|--------------|------------|-------|--------|
        | S1 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |
        | S2 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |
        | S3 | [Description] | [Score] | [Score] | [Score] | [Score] | [Calculated] | [Pending/In Progress/Completed] |

        ## Candidate Details

        ### [S1]: [Simplification Name]

        - **Description**: [Detailed description of the simplification opportunity]
        - **Current Complexity**: [Description of the current complex state]
        - **Proposed Solution**: [Specific approach to simplify]
        - **Implementation Path**:
          1. [Step 1]
          2. [Step 2]
          3. [Step 3]
        - **Expected Benefits**:
          - [Benefit 1]
          - [Benefit 2]
          - [Benefit 3]
        - **Potential Risks**:
          - [Risk 1]: [Mitigation]
          - [Risk 2]: [Mitigation]
        - **Files Affected**:
          - [File Path 1]: [Change description]
          - [File Path 2]: [Change description]
        - **Root Connection**: [Explicit connection to the project mission in `1-projectbrief.md`]

        ### [S2]: [Simplification Name]

        - [Same structure as above]

        ## Implemented Simplifications

        | ID | Simplification | Implementation Date | Actual Effort | Actual Impact | Lessons Learned |
        |----|----------------|---------------------|--------------|--------------|----------------|
        | [ID] | [Name] | [Date] | [Description] | [Description] | [Lessons] |
        | [ID] | [Name] | [Date] | [Description] | [Description] | [Lessons] |

        ## Simplification Ideas Parking Lot

        These are raw ideas that need further evaluation before becoming formal candidates:

        1. [Idea 1]
        2. [Idea 2]
        3. [Idea 3]

        ## Compression Check

        **[Date]**: Before adding the above simplification candidates, attempts were made to:

        1. [ ] Merge overlapping simplification ideas
        2. [ ] Elevate common patterns into higher-level architectural improvements
        3. [ ] Discard candidates with insufficient impact-to-effort ratio
        4. [ ] Prioritize candidates that directly reinforce project root purpose

        ---

        *Note: Each development cycle should identify and implement at least one high-impact simplification. After implementation, document the results in `7-progress.md` and update the "Implemented Simplifications" section here.*
    ```

    ---

    #### `04-process-tracking\06-activeContext-template.md`

    ```markdown
        # Active Context

        > **[Structural Role Reminder]**: This file tracks the current focus, in-progress work, bottlenecks, and active simplifications. It serves as an integration point for ongoing development efforts.

        ## Current Focus Areas

        ### Primary Focus: [Name]

        - **What**: [Brief description of the focus area]
        - **Why**: [How this focus area directly connects to project mission]
        - **Status**: [Current state - Active/Paused/Blocked]
        - **Key Decisions**: [Major decisions made in this area]
        - **Open Questions**: [Unresolved questions]

        ### Secondary Focus: [Name]

        - [Same structure as above]

        ## In-Progress Work

        | Work Item | Description | Owner | Status | Blockers | Root Connection |
        |-----------|-------------|-------|--------|----------|----------------|
        | [Item 1] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |
        | [Item 2] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |
        | [Item 3] | [Description] | [Owner] | [Status] | [Blockers] | [How this connects to project mission] |

        ## Current Bottlenecks

        | Bottleneck | Impact | Root Cause | Mitigation Strategy | Resolution Path |
        |------------|--------|------------|---------------------|----------------|
        | [Bottleneck 1] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |
        | [Bottleneck 2] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |
        | [Bottleneck 3] | [Impact] | [Root Cause] | [Mitigation] | [Resolution] |

        ## Active Simplifications

        ### Simplification 1: [Name]

        - **Target**: [What's being simplified]
        - **Current Complexity**: [Current state description]
        - **Approach**: [How we're simplifying]
        - **Expected Impact**: [Anticipated benefits]
        - **Status**: [Current state of simplification effort]
        - **Root Alignment**: [How this simplification supports project mission]

        ### Simplification 2: [Name]

        - [Same structure as above]

        ## Component Analysis

        | Component | Duplication | Complexity | Usage Pattern | Consolidation Opportunity |
        |-----------|-------------|------------|--------------|---------------------------|
        | [Component 1] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |
        | [Component 2] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |
        | [Component 3] | [Duplication] | [Complexity] | [Usage] | [Opportunity] |

        ## Recent Design Decisions

        | Decision | Rationale | Alternatives Considered | Impact | Date |
        |----------|-----------|-------------------------|--------|------|
        | [Decision 1] | [Rationale] | [Alternatives] | [Impact] | [Date] |
        | [Decision 2] | [Rationale] | [Alternatives] | [Impact] | [Date] |
        | [Decision 3] | [Rationale] | [Alternatives] | [Impact] | [Date] |

        ## Implementation Findings

        | Area | Finding | Implication | Action Needed |
        |------|---------|-------------|---------------|
        | [Area 1] | [Finding] | [Implication] | [Action] |
        | [Area 2] | [Finding] | [Implication] | [Action] |
        | [Area 3] | [Finding] | [Implication] | [Action] |

        ## Next Integration Points

        | What | When | Prerequisites | Complexity | Value |
        |------|------|---------------|------------|-------|
        | [Item 1] | [When] | [Prerequisites] | [Complexity] | [Value] |
        | [Item 2] | [When] | [Prerequisites] | [Complexity] | [Value] |
        | [Item 3] | [When] | [Prerequisites] | [Complexity] | [Value] |

        ---

        *Note: This file should be updated at the beginning and end of each significant work session. It serves as the primary integration point for active development knowledge.*
    ```

    ---

    #### `04-process-tracking\07-progress-template.md`

    ```markdown
        # Progress

        > **[Structural Role Reminder]**: This file tracks assimilation milestones, simplifications achieved, and measures progress versus entropy. It serves as the status log and structural integrity validator.

        ## Project Status Overview

        | Metric | Status | Trend | Target | Notes |
        |--------|--------|-------|--------|-------|
        | Overall Completion | [Percentage] | [↑/↓/→] | [Target] | [Notes] |
        | Technical Debt | [Rating] | [↑/↓/→] | [Target] | [Notes] |
        | Component Consolidation | [Progress] | [↑/↓/→] | [Target] | [Notes] |
        | Performance Metrics | [Metrics] | [↑/↓/→] | [Target] | [Notes] |
        | Test Coverage | [Percentage] | [↑/↓/→] | [Target] | [Notes] |

        ## Completed Milestones

        | Milestone | Completion Date | Description | Impact | Root Connection |
        |-----------|----------------|-------------|--------|----------------|
        | [Milestone 1] | [Date] | [Description] | [Impact] | [Connection to project mission] |
        | [Milestone 2] | [Date] | [Description] | [Impact] | [Connection to project mission] |
        | [Milestone 3] | [Date] | [Description] | [Impact] | [Connection to project mission] |

        ## Major Simplifications Achieved

        ### Simplification 1: [Name]

        - **Before**: [Description of prior complexity]
        - **After**: [Description of simplified state]
        - **Complexity Reduction**: [Quantifiable metrics if available]
        - **Value Added**: [Specific benefits gained]
        - **Date Completed**: [Date]
        - **Root Connection**: [How this reinforces project mission]

        ### Simplification 2: [Name]

        - [Same structure as above]

        ## Technical Debt Ledger

        | Debt Item | Area | Severity | Introduction Date | Mitigation Plan | Status |
        |-----------|------|----------|-------------------|----------------|--------|
        | [Debt 1] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |
        | [Debt 2] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |
        | [Debt 3] | [Area] | [High/Medium/Low] | [Date] | [Plan] | [Status] |

        ## Metrics Tracking

        ### Core Metrics

        | Metric | Initial | Current | Target | Trend |
        |--------|---------|---------|--------|-------|
        | [Metric 1] | [Initial] | [Current] | [Target] | [↑/↓/→] |
        | [Metric 2] | [Initial] | [Current] | [Target] | [↑/↓/→] |
        | [Metric 3] | [Initial] | [Current] | [Target] | [↑/↓/→] |

        ### Component Metrics

        | Component | Duplication | Complexity | Usage | Reusability Score |
        |-----------|-------------|------------|-------|------------------|
        | [Component 1] | [Measure] | [Measure] | [Measure] | [Score] |
        | [Component 2] | [Measure] | [Measure] | [Measure] | [Score] |
        | [Component 3] | [Measure] | [Measure] | [Measure] | [Score] |

        ## Known Issues

        | Issue | Impact | Root Cause | Workaround | Resolution Plan | Priority |
        |-------|--------|------------|------------|----------------|----------|
        | [Issue 1] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |
        | [Issue 2] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |
        | [Issue 3] | [Impact] | [Cause] | [Workaround] | [Plan] | [Priority] |

        ## Retrospective Insights

        | Insight | Area | Source | Application | Date |
        |---------|------|--------|-------------|------|
        | [Insight 1] | [Area] | [Source] | [How applied] | [Date] |
        | [Insight 2] | [Area] | [Source] | [How applied] | [Date] |
        | [Insight 3] | [Area] | [Source] | [How applied] | [Date] |

        ## High-Impact Simplifications Log

        | Date | Simplification | Before Complexity | After Complexity | Value Impact |
        |------|----------------|-------------------|------------------|--------------|
        | [Date] | [Description] | [Before] | [After] | [Impact] |
        | [Date] | [Description] | [Before] | [After] | [Impact] |
        | [Date] | [Description] | [Before] | [After] | [Impact] |

        ---

        *Note: This progress log should be updated after every significant milestone or simplification. It should focus on measuring value delivered and complexity reduced, always connecting back to the project's root mission.*
    ```

    ---

    #### `04-process-tracking\08-tasks-template.md`

    ```markdown
        # Tasks

        > **[Structural Role Reminder]**: This file contains concrete, structure-anchored tasks that directly support the project mission. Each task must trace its lineage to the root purpose.

        ## Active Tasks

        ### High Priority

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |
        |----|------|-------------|-------|--------------|-------------|--------|----------------|
        | [H1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [H2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [H3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        ### Medium Priority

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |
        |----|------|-------------|-------|--------------|-------------|--------|----------------|
        | [M1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [M2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [M3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        ### Low Priority

        | ID | Task | Description | Value | Dependencies | Assigned To | Status | Root Connection |
        |----|------|-------------|-------|--------------|-------------|--------|----------------|
        | [L1] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [L2] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |
        | [L3] | [Task Name] | [Description] | [Value] | [Dependencies] | [Assignee] | [Status] | [Connection to project mission] |

        ## Task Details

        ### [Task ID]: [Task Name]

        - **Description**: [Detailed description of the task]
        - **Value Proposition**: [Specific value this task delivers]
        - **Acceptance Criteria**:
          - [Criterion 1]
          - [Criterion 2]
          - [Criterion 3]
        - **Implementation Approach**: [Recommended approach]
        - **Dependencies**:
          - [Dependency 1]
          - [Dependency 2]
        - **Risks**:
          - [Risk 1]: [Mitigation strategy]
          - [Risk 2]: [Mitigation strategy]
        - **Structure Connection**: [How this task aligns with system patterns in `3-systemPatterns.md`]
        - **Root Connection**: [How this task supports project mission in `1-projectbrief.md`]

        ### [Task ID]: [Task Name]

        - [Same structure as above]

        ## Completed Tasks

        | ID | Task | Completion Date | Outcome | Impact |
        |----|------|----------------|---------|--------|
        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |
        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |
        | [ID] | [Task Name] | [Date] | [Outcome] | [Impact] |

        ## Blocked Tasks

        | ID | Task | Blocker | Impact | Resolution Path |
        |----|------|---------|--------|----------------|
        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |
        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |
        | [ID] | [Task Name] | [Blocker] | [Impact] | [Path to resolution] |

        ## Task Themes

        ### Theme 1: [Name]

        - **Purpose**: [What this set of tasks aims to achieve]
        - **Related Tasks**: [IDs of related tasks]
        - **Key Dependencies**: [Critical dependencies for this theme]
        - **Success Metrics**: [How success will be measured]
        - **Root Connection**: [How this theme supports project mission]

        ### Theme 2: [Name]

        - [Same structure as above]

        ---

        *Note: All tasks must be structure-anchored and root-aligned. Never create floating tasks disconnected from the project structure and mission. Each task must add value through complexity reduction, clarity improvement, or direct mission advancement.*
    ```

    ---

    #### `04-process-tracking\09-drift-monitor-template.md`

    ```markdown
        # Drift Monitor

        > **[Structural Role Reminder]**: This file provides a proactive monitoring mechanism for Memory Bank integrity, serving as an early warning system for structural decay.

        ## Last Structure Validation: [Date]

        | File | Original Purpose | Current Usage | Drift Status | Action Needed |
        |------|-----------------|---------------|-------------|---------------|
        | `0-distilledContext.md` | Ultra-compressed project essence in 2-3 bullets. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `1-projectbrief.md` | Root purpose definition and critical constraints. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `2-productContext.md` | User needs, problems solved, and external context. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `3-systemPatterns.md` | System architecture, component hierarchy, and data flows. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `4-techContext.md` | Technology stack, constraints, and integration points. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `5-structureMap.md` | Current and target file structure with migration path. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `6-activeContext.md` | Current focus, bottlenecks, and in-progress simplifications. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `7-progress.md` | Milestones, metrics, and simplifications achieved. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |
        | `8-tasks.md` | Structure-anchored tasks supporting project mission. | [Current usage description] | ✅ or ⚠️ | [Action if needed] |

        ## Common Drift Patterns

        | Pattern | Warning Signs | Correction Strategy |
        |---------|--------------|---------------------|
        | Purpose Dilution | File no longer clearly connects to root purpose | Re-anchor to `1-projectbrief.md`, remove unrelated content |
        | Scope Expansion | File growing beyond single responsibility | Move content to appropriate files, elevate patterns |
        | Abstraction Leakage | Details appearing in higher abstraction levels | Push details down to appropriate level |
        | Content Duplication | Same concepts appearing in multiple files | Consolidate to most appropriate file, reference as needed |
        | Temporal Buildup | Content accumulating chronologically without structure | Reorganize by abstraction, not timeline |
        | Orphaned Information | Insights without clear structural connection | Connect to structure or remove |

        ## Drift Correction Log

        | Date | File | Issue Detected | Correction Applied |
        |------|------|----------------|-------------------|
        | [Date] | [File] | [Issue] | [Correction] |
        | [Date] | [File] | [Issue] | [Correction] |
        | [Date] | [File] | [Issue] | [Correction] |

        ## Drift Prevention Checklist

        Before adding new content to any Memory Bank file, verify:

        - [ ] **Root Connection**: Does this clearly support the project mission in `1-projectbrief.md`?
        - [ ] **Correct Abstraction Level**: Is this at the appropriate level of abstraction for this file?
        - [ ] **Single Responsibility**: Does this respect the file's single structural role?
        - [ ] **No Duplication**: Is this information not already captured elsewhere?
        - [ ] **Compression Attempted**: Have I tried to merge/elevate/dissolve this into existing patterns?
        - [ ] **Structural Clarity**: Does this enhance rather than obscure the project's structure?

        ---

        *Note: Update this drift monitor after every significant Memory Bank update or at least monthly. Use the CLI tool `./mb-tools.sh mb_audit` to automatically check for potential drift.*
    ```

    ---

    #### `05-evolution\09-lineage-template.md`

    ```markdown
        # Lineage Entry: [Title of Cognitive Shift]

        > **[Structural Role Reminder]**: This file captures a significant cognitive shift in project understanding, serving as a checkpoint in the Memory Bank's evolution.

        ## Context

        **Date**: [Date of Entry]
        **Memory Bank Version**: [Version Number or Identifier]
        **Current Project Phase**: [Development Phase]

        ## Prior Understanding

        [Description of how the project was understood before this insight]

        ## Cognitive Shift

        ### Key Insight

        [The core realization or understanding that changed]

        ### Catalyzing Factors

        - [What triggered this shift in understanding]
        - [Specific events, observations, or analyses]
        - [External or internal factors that prompted reconsideration]

        ## Structural Impact

        ### Memory Bank Changes

        | File | Changes | Justification |
        |------|---------|---------------|
        | [File Path] | [Description of Changes] | [Why the change was necessary] |
        | [File Path] | [Description of Changes] | [Why the change was necessary] |
        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        ### Structure Evolution

        **Before**:
        ```plaintext
        [Relevant structure representation before the change]
        ```

        **After**:
        ```plaintext
        [Relevant structure representation after the change]
        ```

        ### Abstraction Evolution

        [How this shift affects the project's abstraction layers]

        ## Root Connection

        [How this cognitive shift reinforces or refines connection to the project's root purpose]

        ## Practical Implications

        ### Development Impact

        [How this shift affects the development approach]

        ### Verification Method

        [How we can verify this new understanding is more accurate or valuable]

        ### Next Steps

        [Immediate actions prompted by this cognitive shift]

        ---

        *Note: Lineage entries serve as cognitive milestones, capturing key moments where understanding fundamentally shifted. They provide context for why the Memory Bank evolved as it did and preserve the reasoning behind structural changes.*
    ```

    ---

    #### `06-tools\01-mb-quick-setup.sh`

    ```bash
        #!/bin/bash
        # Memory Bank Quick Setup Script
        #
        # Usage:
        #   chmod +x mb-quick-setup.sh
        #   ./mb-quick-setup.sh [target-directory]
        #
        # Creates a lightweight Memory Bank structure in the specified directory
        # or in the current directory if no target is specified.

        # Set target directory
        TARGET_DIR=${1:-.}
        MB_DIR="$TARGET_DIR/memory-bank"

        # Display header
        echo "Memory Bank Quick Setup"
        echo "======================="
        echo

        # Create Memory Bank directory
        mkdir -p "$MB_DIR"
        echo "📁 Created Memory Bank directory: $MB_DIR"

        # Function to create file with content
        create_file() {
            local file="$1"
            local title="$2"
            local description="$3"

            cat > "$file" << EOL
        # $title

        > $description

        EOL

            echo "📝 Created $file"
        }

        # Create essential structure
        create_file "$MB_DIR/0-distilledContext.md" "Project Distilled Context" "This file contains the absolute essence of the project in 2-3 bullets."
        cat >> "$MB_DIR/0-distilledContext.md" << 'EOL'

        -
        -
        -

        EOL

        create_file "$MB_DIR/1-projectbrief.md" "Project Brief" "This file defines why this project must exist."
        cat >> "$MB_DIR/1-projectbrief.md" << 'EOL'

        ## Core Mission

        [Define the irreducible purpose of this project in 1-2 sentences]

        ## Value Proposition

        [Describe the unique value this project provides]

        ## Critical Constraints

        [List 2-3 non-negotiable constraints that shape the project]

        EOL

        create_file "$MB_DIR/2-productContext.md" "Product Context" "This file maps the external reality: users, needs, and outcomes."
        cat >> "$MB_DIR/2-productContext.md" << 'EOL'

        ## User Archetypes

        [Identify core user types]

        ## Problems Solved

        [List the key problems this project solves]

        ## Operating Environment

        [Describe where and how this product will be used]

        EOL

        create_file "$MB_DIR/6-activeContext.md" "Active Context" "This file tracks current focus, bottlenecks, and in-progress simplifications."
        cat >> "$MB_DIR/6-activeContext.md" << 'EOL'

        ## Current Focus

        [What are you working on right now?]

        ## Bottlenecks

        [What's blocking progress?]

        ## In-Progress Simplifications

        [What complexity are you trying to reduce?]

        EOL

        create_file "$MB_DIR/8-tasks.md" "Tasks" "This file contains concrete, structure-anchored tasks that trace to the root goal."
        cat >> "$MB_DIR/8-tasks.md" << 'EOL'

        ## Active Tasks

        - [ ] Task 1: [Description]
        - [ ] Task 2: [Description]

        ## Completed Tasks

        - [x] Initial Memory Bank setup

        EOL

        # Create README
        cat > "$MB_DIR/README.md" << 'EOL'
        # Project Memory Bank

        This directory contains the cognitive architecture for this project.

        ## Core Files

        - **0-distilledContext.md**: Ultra-compressed project essence
        - **1-projectbrief.md**: Root purpose definition
        - **2-productContext.md**: User needs and external context
        - **6-activeContext.md**: Current focus and bottlenecks
        - **8-tasks.md**: Structure-anchored tasks

        ## Memory Bank Principles

        - **File-Structure-First**: Cognitive architecture expressed through file structure
        - **Root-First Thinking**: Every insight relates upward to the project's mission
        - **Persistent Simplification**: Actions aim to reduce net complexity
        - **Value Extraction Bias**: Focus on actionable insight over detail
        - **Outward Mapping**: Think from root → abstraction layers → implementation

        ## Daily Usage

        1. Begin each work session by reading `0-distilledContext.md`
        2. Update your current focus in `6-activeContext.md`
        3. Work on tasks from `8-tasks.md`
        4. Extract patterns to create more structure as needed

        EOL

        # Completion message
        echo
        echo "✅ Memory Bank setup complete!"
        echo
        echo "Next steps:"
        echo "1. Define your project's essence in 0-distilledContext.md"
        echo "2. Establish your project's purpose in 1-projectbrief.md"
        echo "3. Begin each work session by reading 0-distilledContext.md"
        echo
        echo "For more information, visit: https://github.com/yourname/memory-bank-templates"
    ```

    ---

    #### `06-tools\02-mb-assistant.sh`

    ```bash
        #!/bin/bash
        # Memory Bank Assistant - Interactive guidance for Memory Bank usage
        #
        # Usage:
        #   chmod +x mb-assistant.sh
        #   ./mb-assistant.sh
        #
        # Provides interactive guidance for Memory Bank usage based on common scenarios

        # Colors
        GREEN='\033[0;32m'
        BLUE='\033[0;34m'
        YELLOW='\033[1;33m'
        RED='\033[0;31m'
        NC='\033[0m' # No Color

        # Display header
        clear
        echo -e "${BLUE}=================================${NC}"
        echo -e "${BLUE}    Memory Bank Assistant    ${NC}"
        echo -e "${BLUE}=================================${NC}"
        echo

        # Function to show main menu
        show_main_menu() {
          echo -e "\n${YELLOW}What would you like help with?${NC}"
          echo "1. I'm new to Memory Bank - how do I start?"
          echo "2. Which files should I update today?"
          echo "3. How do I use Memory Bank with my AI assistant?"
          echo "4. I'm feeling overwhelmed - how can I simplify?"
          echo "5. Memory Bank troubleshooting"
          echo "6. Memory Bank best practices"
          echo "7. Exit"
          echo
          echo -n "Enter your choice (1-7): "
        }

        # Function to handle "new to Memory Bank" scenario
        new_to_memory_bank() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    Getting Started Guide    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo
          echo -e "${YELLOW}Welcome to Memory Bank!${NC}"
          echo
          echo "Memory Bank is a system for maintaining structural clarity in your projects."
          echo "It helps you focus on what matters most and avoid complexity creep."
          echo
          echo -e "${GREEN}Here's how to get started in 5 minutes:${NC}"
          echo
          echo "1. Create the minimal Memory Bank structure:"
          echo "   - Run: ./06-tools/1-mb-quick-setup.sh"
          echo "   - Or create these files manually:"
          echo "     * memory-bank/0-distilledContext.md (project essence)"
          echo "     * memory-bank/1-projectbrief.md (project purpose)"
          echo
          echo "2. Define your project's essence in 0-distilledContext.md"
          echo "   - What is the absolute core of your project in 2-3 bullets?"
          echo "   - Keep it ultra-compressed and focused on the essentials"
          echo
          echo "3. Define your project's purpose in 1-projectbrief.md"
          echo "   - Why must this project exist?"
          echo "   - What unique value does it provide?"
          echo "   - What critical constraints shape it?"
          echo
          echo "4. Begin each work session by reading 0-distilledContext.md"
          echo
          echo -e "${YELLOW}Ready for more advanced usage?${NC}"
          echo
          echo "5. Create additional files as needed:"
          echo "   - 2-productContext.md (users and problems solved)"
          echo "   - 6-activeContext.md (current focus and bottlenecks)"
          echo "   - 8-tasks.md (structure-anchored tasks)"
          echo
          echo -e "${GREEN}That's it! You're now using Memory Bank.${NC}"
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Function to handle "which files to update" scenario
        which_files_to_update() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    Daily/Weekly Updates    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo

          # Get current day
          DAY=$(date +%A)

          echo -e "${YELLOW}Today is $DAY. Here's what to update:${NC}"
          echo
          echo -e "${GREEN}Daily Updates:${NC}"
          echo "- 0-distilledContext.md (READ ONLY - 30 seconds)"
          echo "- 6-activeContext.md (UPDATE - what you're focusing on today)"
          echo "- 8-tasks.md (UPDATE - tasks for today)"
          echo
          if [[ "$DAY" == "Friday" ]]; then
            echo -e "${GREEN}Weekly Updates (Friday):${NC}"
            echo "- 7-progress.md (UPDATE - milestones achieved this week)"
            echo "- Identify at least one simplification opportunity"
            echo
          fi

          echo -e "${YELLOW}Other periodic updates:${NC}"
          echo
          echo "After major milestones:"
          echo "- 3-systemPatterns.md (architecture patterns)"
          echo "- 5-structureMap.md (file structure)"
          echo
          echo "When project scope/direction changes:"
          echo "- 0-distilledContext.md (project essence)"
          echo "- 1-projectbrief.md (project purpose)"
          echo "- 2-productContext.md (users and needs)"
          echo "- 4-techContext.md (technology stack)"
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Function to handle "AI assistant usage" scenario
        ai_assistant_usage() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    AI Assistant Integration    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo
          echo -e "${YELLOW}Using Memory Bank with AI coding assistants:${NC}"
          echo
          echo "Memory Bank works exceptionally well with AI coding assistants like Cline, Cursor, or GitHub Copilot."
          echo
          echo -e "${GREEN}Setup instructions:${NC}"
          echo
          echo "1. Find the system instruction file:"
          echo "   - 07-guides/13-memory-bank-system-instruction.md"
          echo
          echo "2. Copy the content of the file (everything between the code block markers)"
          echo
          echo "3. Paste it into your AI assistant's system instruction settings"
          echo "   - For Cline in VS Code: Settings > System Instructions"
          echo "   - For Cursor: Settings > AI > Instructions"
          echo "   - For GitHub Copilot Chat: Create a YAML file in .github/"
          echo
          echo "4. Tell your AI assistant to help you with Memory Bank"
          echo "   Example: \"Set up a Memory Bank for this project and help me define the core files.\""
          echo
          echo -e "${YELLOW}Daily usage with AI:${NC}"
          echo
          echo "5. Ask your AI to help maintain the Memory Bank files:"
          echo "   - \"Update our Memory Bank files based on today's work\""
          echo "   - \"Extract patterns from our recent code into 3-systemPatterns.md\""
          echo "   - \"Identify potential simplifications for our project\""
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Function to handle "simplify Memory Bank" scenario
        simplify_memory_bank() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    Simplifying Memory Bank    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo
          echo -e "${YELLOW}Feeling overwhelmed by Memory Bank?${NC}"
          echo
          echo "Memory Bank should reduce complexity, not add to it. Here's how to simplify:"
          echo
          echo -e "${GREEN}The absolute minimal approach:${NC}"
          echo
          echo "1. Just maintain these two files:"
          echo "   - 0-distilledContext.md (project essence)"
          echo "   - 1-projectbrief.md (project purpose)"
          echo
          echo "2. Read them at the start of each work session"
          echo
          echo "That's it! Everything else is optional."
          echo
          echo -e "${YELLOW}If you have a few more minutes:${NC}"
          echo
          echo "Add just these three more files for a balanced approach:"
          echo "- 2-productContext.md (users and problems solved)"
          echo "- 6-activeContext.md (current focus)"
          echo "- 8-tasks.md (structure-anchored tasks)"
          echo
          echo -e "${GREEN}Remember:${NC}"
          echo "- Don't treat Memory Bank as documentation that needs to be complete"
          echo "- It's a living system that helps you maintain clarity"
          echo "- Quality over quantity - a focused 0-distilledContext.md is worth more"
          echo "  than dozens of detailed but unused files"
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Function to handle "troubleshooting" scenario
        troubleshooting() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    Memory Bank Troubleshooting    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo
          echo -e "${YELLOW}Common Memory Bank issues:${NC}"
          echo
          echo -e "${RED}Problem:${NC} \"My Memory Bank feels like passive documentation\""
          echo -e "${GREEN}Solution:${NC}"
          echo "- Revisit 0-distilledContext.md and ensure it's ultra-compressed"
          echo "- Extract patterns in 3-systemPatterns.md instead of listing instances"
          echo "- Focus on active files (6-activeContext.md, 8-tasks.md)"
          echo "- Delete any content that doesn't connect to your root purpose"
          echo
          echo -e "${RED}Problem:${NC} \"I don't know which files to update\""
          echo -e "${GREEN}Solution:${NC}"
          echo "- Daily: Just update 6-activeContext.md and 8-tasks.md"
          echo "- Weekly: Update 7-progress.md"
          echo "- Only update others when there's a significant change"
          echo
          echo -e "${RED}Problem:${NC} \"Memory Bank isn't helping me manage complexity\""
          echo -e "${GREEN}Solution:${NC}"
          echo "- Ensure your 0-distilledContext.md is truly essential"
          echo "- Work through 3-systemPatterns.md to identify repeated patterns"
          echo "- Use 6-activeContext.md to focus on bottlenecks"
          echo "- Explicitly identify simplification opportunities"
          echo
          echo -e "${RED}Problem:${NC} \"My team isn't using Memory Bank consistently\""
          echo -e "${GREEN}Solution:${NC}"
          echo "- Start team meetings by reviewing 0-distilledContext.md"
          echo "- Simplify to just the core files (0, 1, 6, 8)"
          echo "- Integrate with existing team workflows"
          echo "- Assign a Memory Bank steward to maintain it"
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Function to handle "best practices" scenario
        best_practices() {
          clear
          echo -e "${BLUE}=================================${NC}"
          echo -e "${BLUE}    Memory Bank Best Practices    ${NC}"
          echo -e "${BLUE}=================================${NC}"
          echo
          echo -e "${YELLOW}Memory Bank best practices:${NC}"
          echo
          echo -e "${GREEN}1. Start with essence, not details${NC}"
          echo "   - Define 0-distilledContext.md first"
          echo "   - Be ruthless about what goes in it"
          echo "   - Limit to 2-3 bullets total"
          echo
          echo -e "${GREEN}2. Make it part of your workflow${NC}"
          echo "   - Begin each day by reading 0-distilledContext.md"
          echo "   - Update 6-activeContext.md before and after work"
          echo "   - Link your tasks in 8-tasks.md to the root purpose"
          echo
          echo -e "${GREEN}3. Compress, don't expand${NC}"
          echo "   - Elevate details to patterns"
          echo "   - Merge similar concepts"
          echo "   - Delete anything not connected to the root"
          echo
          echo -e "${GREEN}4. Focus on high-impact areas${NC}"
          echo "   - 0-distilledContext.md (clarity)"
          echo "   - 3-systemPatterns.md (architecture)"
          echo "   - 6-activeContext.md (daily focus)"
          echo "   - 8-tasks.md (action items)"
          echo
          echo -e "${GREEN}5. Don't Let Perfect Be The Enemy of Good${NC}"
          echo "   - An imperfect, used Memory Bank is better than a perfect, unused one"
          echo "   - Start minimal and add only what delivers value"
          echo "   - It's OK to have incomplete files – they'll evolve over time"
          echo
          echo -n "Press Enter to return to the main menu..."
          read
        }

        # Main program loop
        while true; do
          clear
          show_main_menu
          read CHOICE

          case $CHOICE in
            1) new_to_memory_bank ;;
            2) which_files_to_update ;;
            3) ai_assistant_usage ;;
            4) simplify_memory_bank ;;
            5) troubleshooting ;;
            6) best_practices ;;
            7)
              echo -e "\n${GREEN}Thank you for using Memory Bank Assistant!${NC}"
              echo "Remember: Memory Bank is not what you know about the project;"
              echo "it is what the project knows about itself."
              echo
              exit 0
              ;;
            *)
              echo -e "\n${RED}Invalid option. Please enter a number between 1 and 7.${NC}"
              sleep 2
              ;;
          esac
        done
    ```

    ---

    #### `06-tools\09-mb-tools.sh`

    ```bash
        #!/bin/bash
        # Memory Bank Tools - CLI utilities for Memory Bank maintenance
        # This script provides automation for Memory Bank system maintenance and health checks

        # Constants
        MB_DIR="memory-bank"
        LINEAGE_DIR="$MB_DIR/lineage"
        EPOCHS_DIR="$MB_DIR/epochs"
        REQUIRED_FILES=("0-distilledContext.md" "1-projectbrief.md" "2-productContext.md" "3-systemPatterns.md" "4-techContext.md" "5-structureMap.md" "6-activeContext.md" "7-progress.md" "8-tasks.md")
        OPTIONAL_FILES=("drift-monitor.md" "simplification-candidates.md")

        # Colors for terminal output
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[0;33m'
        BLUE='\033[0;34m'
        NC='\033[0m' # No Color

        # Print colored message
        function print_message() {
          local color=$1
          local message=$2
          echo -e "${color}${message}${NC}"
        }

        # Check if Memory Bank exists
        function check_mb_exists() {
          if [ ! -d "$MB_DIR" ]; then
            print_message "$RED" "‚ùå Memory Bank directory not found. Run 'mb_init' to create it."
            return 1
          fi
          return 0
        }

        # Initialize a new Memory Bank
        function mb_init() {
          print_message "$BLUE" "üèóÔ∏è  Initializing Memory Bank structure..."

          # Create directories
          mkdir -p "$MB_DIR"
          mkdir -p "$LINEAGE_DIR"
          mkdir -p "$EPOCHS_DIR"

          # Placeholder logic for copying template files
          # In a real implementation, this would copy from a templates directory
          print_message "$YELLOW" "üìã Would create the following files:"
          for file in "${REQUIRED_FILES[@]}"; do
            echo "  - $MB_DIR/$file"
          done

          # Create an initial drift monitor
          echo -e "# Drift Monitor\n\n## Last Structure Validation: $(date +%Y-%m-%d)\n\n| File | Original Purpose | Current Usage | Drift Status | Action Needed |\n|------|-----------------|---------------|-------------|---------------|\n" > "$MB_DIR/drift-monitor.md"

          for file in "${REQUIRED_FILES[@]}"; do
            echo "| \`$file\` | | | ‚úÖ | |" >> "$MB_DIR/drift-monitor.md"
          done

          echo -e "\n## Drift Correction Log\n\n| Date | File | Issue Detected | Correction Applied |\n|------|------|----------------|-------------------|\n" >> "$MB_DIR/drift-monitor.md"

          # Create initial simplification candidates file
          echo -e "# High-Impact Simplification Candidates\n\n## Evaluation Criteria:\n1. **Minimal Implementation Effort** (1-10): How easy is this to implement?\n2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?\n3. **Widespread Impact** (1-10): How many areas will benefit?\n4. **Root Reinforcement** (1-10): How strongly does this support the project mission?\n5. **Impact Score** = Clarity √ó Impact √ó Root √∑ Effort\n\n## Current Candidates:\n\n| ID | Simplification | Effort | Clarity | Impact | Root | Score | Status |\n|----|----------------|--------|---------|--------|------|-------|--------|\n" > "$MB_DIR/simplification-candidates.md"

          print_message "$GREEN" "‚úÖ Memory Bank initialized successfully."
          print_message "$YELLOW" "‚ö†Ô∏è  Next steps:"
          print_message "$YELLOW" "  1. Fill in the template files with your project information"
          print_message "$YELLOW" "  2. Run 'mb_validate' to check your Memory Bank structure"
        }

        # Validate Memory Bank structure
        function mb_validate() {
          if ! check_mb_exists; then
            return 1
          fi

          print_message "$BLUE" "üîç Validating Memory Bank structure..."

          local errors=0
          local warnings=0

          # Check for required files
          for file in "${REQUIRED_FILES[@]}"; do
            if [ ! -f "$MB_DIR/$file" ]; then
              print_message "$RED" "‚ùå Missing required file: $MB_DIR/$file"
              errors=$((errors+1))
            else
              # Check for structural role reminder
              if ! grep -q "\[Structural Role" "$MB_DIR/$file"; then
                print_message "$YELLOW" "‚ö†Ô∏è  File $file is missing a Structural Role Reminder"
                warnings=$((warnings+1))
              fi

              # Check file size (too small likely means incomplete)
              local size=$(wc -c < "$MB_DIR/$file")
              if [ $size -lt 100 ]; then
                print_message "$YELLOW" "‚ö†Ô∏è  File $file seems too small ($size bytes). Is it complete?"
                warnings=$((warnings+1))
              fi
            fi
          done

          # Check for cross-references between files
          print_message "$BLUE" "üîÑ Checking cross-references..."

          # Check if projectbrief is referenced in other files
          for file in "${REQUIRED_FILES[@]}"; do
            if [ "$file" != "1-projectbrief.md" ] && [ -f "$MB_DIR/$file" ]; then
              if ! grep -q "1-projectbrief" "$MB_DIR/$file"; then
                print_message "$YELLOW" "‚ö†Ô∏è  File $file does not reference 1-projectbrief.md"
                warnings=$((warnings+1))
              fi
            fi
          done

          # Check for lineage tracking
          if [ -z "$(ls -A $LINEAGE_DIR 2>/dev/null)" ]; then
            print_message "$YELLOW" "‚ö†Ô∏è  No lineage entries found. Consider adding cognitive shift documentation."
            warnings=$((warnings+1))
          fi

          # Summary
          if [ $errors -eq 0 ] && [ $warnings -eq 0 ]; then
            print_message "$GREEN" "‚úÖ Memory Bank structure is valid with no warnings."
          elif [ $errors -eq 0 ]; then
            print_message "$YELLOW" "‚ö†Ô∏è  Memory Bank has $warnings warnings but no critical errors."
          else
            print_message "$RED" "‚ùå Memory Bank has $errors errors and $warnings warnings."
          fi
        }

        # Check for compression opportunities
        function mb_compress() {
          if ! check_mb_exists; then
            return 1
          fi

          print_message "$BLUE" "üîÑ Analyzing Memory Bank for compression opportunities..."

          # Find repeated phrases across files (crude example)
          print_message "$BLUE" "üîç Looking for common phrases across files (potential patterns)..."

          # This is a simplified example - in reality you'd need more sophisticated analysis
          for phrase in "component" "structure" "pattern" "user" "mission" "abstraction"; do
            echo -e "\nOccurrences of '$phrase':"
            grep -i -r "$phrase" --include="*.md" $MB_DIR | wc -l

            # List files with most occurrences
            print_message "$YELLOW" "  Top files mentioning '$phrase':"
            grep -i -r "$phrase" --include="*.md" $MB_DIR | cut -d: -f1 | sort | uniq -c | sort -nr | head -3
          done

          # Check for long files (potential splitting candidates)
          print_message "$BLUE" "üìä Identifying unusually large files (potential splitting candidates)..."
          find $MB_DIR -name "*.md" -type f -exec wc -l {} \; | sort -nr | head -5

          # Look for potential duplication
          print_message "$BLUE" "üîÑ Checking for potential content duplication..."
          for file1 in $MB_DIR/*.md; do
            for file2 in $MB_DIR/*.md; do
              if [ "$file1" != "$file2" ]; then
                common_lines=$(comm -12 <(sort "$file1") <(sort "$file2") | wc -l)
                if [ $common_lines -gt 10 ]; then
                  print_message "$YELLOW" "‚ö†Ô∏è  $file1 and $file2 share $common_lines similar lines"
                fi
              fi
            done
          done

          print_message "$GREEN" "‚úÖ Compression analysis complete."
          print_message "$YELLOW" "‚ö†Ô∏è  Remember: These are automated suggestions only."
          print_message "$YELLOW" "   Always verify opportunities and maintain root connection when compressing."
        }

        # Perform full Memory Bank audit
        function mb_audit() {
          if ! check_mb_exists; then
            return 1
          fi

          print_message "$BLUE" "üîç Performing comprehensive Memory Bank audit..."

          # Run validation first
          mb_validate

          # Check last updates
          print_message "$BLUE" "üìÖ Checking file freshness..."
          find $MB_DIR -name "*.md" -type f -exec ls -la {} \; | sort -k6,8

          # Check word counts
          print_message "$BLUE" "üìä File size distribution (word count)..."
          for file in $MB_DIR/*.md; do
            word_count=$(wc -w < "$file")
            echo "$file: $word_count words"
          done

          # Check for active tasks
          print_message "$BLUE" "üìã Checking active tasks..."
          if [ -f "$MB_DIR/8-tasks.md" ]; then
            grep -A 1 "High Priority" "$MB_DIR/8-tasks.md"
          else
            print_message "$RED" "‚ùå Tasks file not found"
          fi

          # Check root connection
          print_message "$BLUE" "üîÑ Verifying root connection integrity..."
          if [ -f "$MB_DIR/1-projectbrief.md" ]; then
            root_terms=$(grep -i -o -E '\b\w{5,}\b' "$MB_DIR/1-projectbrief.md" | sort | uniq)

            print_message "$YELLOW" "üìä Root term propagation across Memory Bank:"
            for term in $root_terms; do
              count=$(grep -i -r "$term" --include="*.md" $MB_DIR | wc -l)
              if [ $count -gt 5 ]; then
                echo "$term: mentioned $count times"
              fi
            done
          fi

          # Update drift monitor
          if [ -f "$MB_DIR/drift-monitor.md" ]; then
            sed -i "s/Last Structure Validation: .*/Last Structure Validation: $(date +%Y-%m-%d)/" "$MB_DIR/drift-monitor.md"
            print_message "$GREEN" "‚úÖ Updated drift monitor with current date"
          fi

          print_message "$GREEN" "‚úÖ Memory Bank audit complete."
        }

        # Create a new lineage entry
        function mb_lineage() {
          if ! check_mb_exists; then
            return 1
          fi

          # Get the title for the lineage entry
          echo -n "Enter title for the cognitive shift: "
          read title

          # Create sanitized filename
          filename=$(echo "$title" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
          date=$(date +%Y%m%d)
          filepath="$LINEAGE_DIR/${date}-${filename}.md"

          print_message "$BLUE" "üìù Creating new lineage entry: $filepath"

          # Create file with template
          cat > "$filepath" << EOL
        # Lineage Entry: $title

        > **[Structural Role Reminder]**: This file captures a significant cognitive shift in project understanding.

        ## Context

        **Date**: $(date +%Y-%m-%d)
        **Memory Bank Version**: $(find $MB_DIR -name "*.md" | wc -l) files
        **Current Project Phase**: [Development Phase]

        ## Prior Understanding

        [Description of how the project was understood before this insight]

        ## Cognitive Shift

        ### Key Insight

        [The core realization or understanding that changed]

        ### Catalyzing Factors

        - [What triggered this shift in understanding]
        - [Specific events, observations, or analyses]
        - [External or internal factors that prompted reconsideration]

        ## Structural Impact

        ### Memory Bank Changes

        | File | Changes | Justification |
        |------|---------|---------------|
        | [File Path] | [Description of Changes] | [Why the change was necessary] |

        ## Root Connection

        [How this cognitive shift reinforces or refines connection to the project's root purpose]

        ## Practical Implications

        ### Development Impact

        [How this shift affects the development approach]

        ### Next Steps

        [Immediate actions prompted by this cognitive shift]
        EOL

          print_message "$GREEN" "‚úÖ Lineage entry created: $filepath"
          print_message "$YELLOW" "‚ö†Ô∏è  Don't forget to fill in the template with actual content"
        }

        # Create a new epoch snapshot
        function mb_epoch() {
          if ! check_mb_exists; then
            return 1
          fi

          # Get the name for the epoch
          echo -n "Enter name for this Memory Bank epoch: "
          read epoch_name

          # Create sanitized directory name
          dirname=$(echo "$epoch_name" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
          count=$(find $EPOCHS_DIR -maxdepth 1 -type d | wc -l)
          padded_count=$(printf "%02d" $count)
          epoch_dir="$EPOCHS_DIR/epoch-${padded_count}-${dirname}"

          print_message "$BLUE" "üìù Creating new epoch snapshot: $epoch_dir"

          # Create directory and copy files
          mkdir -p "$epoch_dir"

          # Copy required files
          for file in "${REQUIRED_FILES[@]}"; do
            if [ -f "$MB_DIR/$file" ]; then
              cp "$MB_DIR/$file" "$epoch_dir/"
            fi
          done

          # Create summary file
          cat > "$epoch_dir/epoch-summary.md" << EOL
        # Epoch ${padded_count}: $epoch_name

        **Date**: $(date +%Y-%m-%d)
        **Files**: $(find $epoch_dir -name "*.md" | wc -l)

        ## Summary

        [Summary of this Memory Bank epoch]

        ## Key Characteristics

        - [Key characteristic 1]
        - [Key characteristic 2]
        - [Key characteristic 3]

        ## Structural Evolution

        [How the Memory Bank structure has evolved to this point]

        ## Next Evolution Target

        [What aspects of the Memory Bank should evolve next]
        EOL

          print_message "$GREEN" "‚úÖ Epoch snapshot created: $epoch_dir"
          print_message "$YELLOW" "‚ö†Ô∏è  Don't forget to fill in the epoch summary"
        }

        # Display help
        function show_help() {
          echo -e "${BLUE}Memory Bank Tools${NC} - CLI utilities for Memory Bank maintenance"
          echo ""
          echo "Usage: $0 [command]"
          echo ""
          echo "Commands:"
          echo "  mb_init        Initialize a new Memory Bank structure"
          echo "  mb_validate    Validate existing Memory Bank structure"
          echo "  mb_compress    Analyze Memory Bank for compression opportunities"
          echo "  mb_audit       Perform comprehensive Memory Bank audit"
          echo "  mb_lineage     Create a new lineage entry documenting a cognitive shift"
          echo "  mb_epoch       Create a new epoch snapshot of the current Memory Bank"
          echo "  help           Show this help message"
          echo ""
        }

        # Main command router
        case "$1" in
          "mb_init")
            mb_init
            ;;
          "mb_validate")
            mb_validate
            ;;
          "mb_compress")
            mb_compress
            ;;
          "mb_audit")
            mb_audit
            ;;
          "mb_lineage")
            mb_lineage
            ;;
          "mb_epoch")
            mb_epoch
            ;;
          "help" | *)
            show_help
            ;;
        esac
    ```

    ---

    #### `07-guides\00-README.md`

    ```markdown
        # Memory Bank Templates

        > **[Structural Role Reminder]**: This file provides an overview of the Memory Bank system, serving as the entry point and navigation guide for all templates.

        ## Core Generative Principle

        Memory Bank is a system of persistent abstraction and simplification toward optimal structure. The core principle is an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression:

        - Each structure, detail, or insight is interrogated for its irreducible essence
        - All elements are recursively re-anchored to project purpose
        - The dynamic is a pulsing metabolic flow: accumulation triggers pressure, pressure demands pruning, pruning yields lucidity
        - Structure and meaning converge; entropy cannot persist
        - Every addition must pay its way by sharpening the axis of meaning

        > **Memory Bank = Progressive, self-reinforcing abstraction-layering rooted in project purpose.**

        ## Directory Structure

        ```
        memory-bank/
        ├── 0-distilledContext.md         # Ultra-compressed project essence (2-3 bullets)
        ├── 1-projectbrief.md             # Root purpose: Why the project must exist
        ├── 2-productContext.md           # External reality: users, needs, outcomes
        ├── 3-systemPatterns.md           # Systems: component hierarchy, flows, design
        ├── 4-techContext.md              # Stack-level technical constraints
        ├── 5-structureMap.md             # Actual and target filestructure mapping
        ├── 6-activeContext.md            # In-progress actions and simplifications
        ├── 7-progress.md                 # Assimilation milestones, simplifications
        ├── 8-tasks.md                    # Active structure-guided tasks
        ├── drift-monitor.md              # Structural integrity monitoring
        ├── simplification-candidates.md  # High-impact simplification tracking
        └── lineage/                      # Cognitive evolution snapshots
        ```

        ## Templates Overview

        ### Essential Files (0-8)

        | File | Purpose |
        |------|---------|
        | [`0-distilledContext-template.md`](0-distilledContext-template.md) | Ultra-compressed project essence in 2-3 bullets |
        | [`1-projectbrief-template.md`](1-projectbrief-template.md) | Root purpose definition and critical constraints |
        | [`2-productContext-template.md`](2-productContext-template.md) | User needs, problems solved, and external context |
        | [`3-systemPatterns-template.md`](3-systemPatterns-template.md) | System architecture, component hierarchy, and data flows |
        | [`4-techContext-template.md`](4-techContext-template.md) | Technology stack, constraints, and integration points |
        | [`5-structureMap-template.md`](5-structureMap-template.md) | Current and target file structure with migration path |
        | [`6-activeContext-template.md`](6-activeContext-template.md) | Current focus, bottlenecks, and in-progress simplifications |
        | [`7-progress-template.md`](7-progress-template.md) | Milestones, metrics, and simplifications achieved |
        | [`8-tasks-template.md`](8-tasks-template.md) | Structure-anchored tasks supporting project mission |

        ### Core Guides

        | File | Purpose |
        |------|---------|
        | [`memory-bank-core-template.md`](memory-bank-core-template.md) | Core system principles and structure |
        | [`memory-bank-implementation-guide.md`](memory-bank-implementation-guide.md) | Practical setup and usage guide |
        | [`memory-bank-learning-path.md`](memory-bank-learning-path.md) | Graduated approach to learning the system |
        | [`memory-bank-enhancement-module.md`](memory-bank-enhancement-module.md) | Advanced enhancement modules |
        | [`memory-bank-web-project-template.md`](memory-bank-web-project-template.md) | Web project-specific adaptation |

        ### Auxiliary Templates

        | File | Purpose |
        |------|---------|
        | [`simplification-candidates-template.md`](simplification-candidates-template.md) | High-impact simplification identification |
        | [`lineage-template.md`](lineage-template.md) | Cognitive evolution tracking |
        | [`drift-monitor-template.md`](drift-monitor-template.md) | Structural integrity monitoring |
        | [`framework-specific-adaptations.md`](framework-specific-adaptations.md) | Framework-specific patterns and practices |

        ### Automation Tools

        | File | Purpose |
        |------|---------|
        | [`mb-tools.sh`](mb-tools.sh) | CLI utilities for Memory Bank maintenance |

        ## Absolute Requirements

        | Principle | How To Adapt |
        | :-------- | :----------- |
        | File-Structure-First | Always first generate/validate a cognitive map *as a filestructure*, dynamically, based on what exists. |
        | Root-First Thinking | Every insight must relate *upward*, toward the project's irreducible mission. |
        | Persistent Simplification | Every action must aim to **reduce** net complexity (unless a rare, justified exception). |
        | Value Extraction Bias | Prefer "what maximizes actionable, durable insight" instead of unfolding more detail. |
        | Guardrails | Never mutate structure blindly — every structural change must be **explicitly justified** in the memory. |
        | Outward Mapping | Think *outward* (root → abstraction layers → concrete implementation), **never inward folding** (which causes sprawl). |

        ## Expansion Policy

        Additional templates or files may only be introduced through explicit complexity reduction, not accumulation, and must be justified and tracked through `5-structureMap.md` and/or `6-activeContext.md` per system law.

        ## Implementation Steps

        1. Start by reviewing the [`memory-bank-core-template.md`](memory-bank-core-template.md) to understand the core principles
        2. Follow the implementation instructions in [`memory-bank-implementation-guide.md`](memory-bank-implementation-guide.md)
        3. Copy the numbered file templates (0-8) into your project's `memory-bank/` directory
        4. Fill in the templates with your project-specific information, starting with `0-distilledContext.md` and working downward
        5. Add auxiliary templates as needed, based on project complexity and team needs

        ## Final Directive

        > "If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure — it must not be taken."

        Remember that Memory Bank is not what you know about the project; it is what the project knows about itself. Structure is not a container for knowledge; structure is the memory of intent.
    ```

    ---

    #### `07-guides\01-quick-start-guide.md`

    ```markdown
        # Memory Bank Quick Start Guide

        > **[Structural Role Reminder]**: This document provides simple, practical instructions to get started with Memory Bank in minutes.

        ## 5-Minute Setup

        Get a Memory Bank running in your project with minimal effort:

        1. **Copy the setup script** below to your project root
        2. **Run the script** to create the basic Memory Bank structure
        3. **Fill in the root files** (0-distilledContext.md and 1-projectbrief.md)
        4. **Start using Memory Bank** to organize your project thinking

        ```bash
        #!/bin/bash
        # mb-quick-setup.sh - Memory Bank Quick Setup Script

        # Create main Memory Bank directory
        mkdir -p memory-bank

        # Create essential core files with starter templates
        cat > memory-bank/0-distilledContext.md << 'EOL'
        # Project Distilled Context

        > This file contains the absolute essence of the project in 2-3 bullets.

        -
        -
        -

        EOL

        cat > memory-bank/1-projectbrief.md << 'EOL'
        # Project Brief

        > This file defines why this project must exist.

        ## Core Mission

        [Define the irreducible purpose of this project in 1-2 sentences]

        ## Value Proposition

        [Describe the unique value this project provides]

        ## Critical Constraints

        [List 2-3 non-negotiable constraints that shape the project]

        EOL

        cat > memory-bank/2-productContext.md << 'EOL'
        # Product Context

        > This file maps the external reality: users, needs, and outcomes.

        ## User Archetypes

        [Identify core user types]

        ## Problems Solved

        [List the key problems this project solves]

        ## Operating Environment

        [Describe where and how this product will be used]

        EOL

        cat > memory-bank/README.md << 'EOL'
        # Project Memory Bank

        This directory contains the cognitive architecture for this project.

        ## Core Files

        - **0-distilledContext.md**: Ultra-compressed project essence
        - **1-projectbrief.md**: Root purpose definition
        - **2-productContext.md**: User needs and external context

        ## Memory Bank Principles

        - **File-Structure-First**: Cognitive architecture expressed through file structure
        - **Root-First Thinking**: Every insight relates upward to the project's mission
        - **Persistent Simplification**: Actions aim to reduce net complexity
        - **Value Extraction Bias**: Focus on actionable insight over detail
        - **Outward Mapping**: Think from root → abstraction layers → implementation

        EOL

        echo "Memory Bank core files created in ./memory-bank/"
        echo "Next step: Define your project's essence in 0-distilledContext.md"
        ```

        ## Easiest Setup Method

        ### For AI Coding Assistant Users

        1. **Copy the system instruction** from `07-guides/13-memory-bank-system-instruction.md`
        2. **Paste it** into your AI assistant's system instruction settings (Cline, Cursor AI, etc.)
        3. **Ask the AI assistant** to set up a Memory Bank for your project

        Example prompt: "Set up a Memory Bank for my React project and help me define the core files."

        ### For Manual Setup

        1. **Copy only what you need**:
           - For minimal setup: Just create `0-distilledContext.md` and `1-projectbrief.md`
           - For standard setup: Use the quick setup script above
           - For full setup: Follow the implementation guide

        2. **Start with the core questions**:
           - What is the absolute essence of this project? (→ 0-distilledContext.md)
           - Why must this project exist? (→ 1-projectbrief.md)
           - Who is this for and what problems does it solve? (→ 2-productContext.md)

        ## Common Use Cases

        ### For Solo Developers

        **Lightweight Approach**: Just create and maintain:
        - `0-distilledContext.md`
        - `1-projectbrief.md`
        - `6-activeContext.md` (for daily work notes)
        - `8-tasks.md` (for structured to-dos)

        ### For Teams

        **Collaborative Approach**:
        1. Create the full Memory Bank structure
        2. Add Memory Bank review to your team workflow
        3. Start each meeting by revisiting `0-distilledContext.md`
        4. Maintain `6-activeContext.md` for shared context

        ### For Large Projects

        **Extended Approach**:
        1. Implement the full directory structure
        2. Use the abstraction layer system to manage complexity
        3. Set up CI/CD integration for Memory Bank validation
        4. Create custom automation scripts

        ## Visual Workflow

        ```mermaid
        flowchart TD
            Start([Start Project]) --> Setup[Set Up Memory Bank]
            Setup --> Define0[Define Project Essence\n0-distilledContext.md]
            Define0 --> Define1[Define Project Purpose\n1-projectbrief.md]
            Define1 --> Daily[Daily Work]

            Daily --> ReadEssence[Read Project Essence]
            ReadEssence --> UpdateContext[Update Active Context\n6-activeContext.md]
            UpdateContext --> WorkOnTasks[Work on Tasks\n8-tasks.md]
            WorkOnTasks --> ExtractPatterns[Extract Patterns\n3-systemPatterns.md]
            ExtractPatterns --> Daily

            subgraph "Weekly Activities"
            Weekly([Weekly Review]) --> CheckProgress[Update Progress\n7-progress.md]
            CheckProgress --> Simplify[Identify Simplifications]
            Simplify --> Weekly
            end
        ```

        ## Minimal Memory Bank Cheatsheet

        | File | Purpose | Key Questions |
        |------|---------|---------------|
        | `0-distilledContext.md` | Essence | What is the absolute core of this project in 2-3 bullets? |
        | `1-projectbrief.md` | Purpose | Why must this project exist? What is its value? |
        | `2-productContext.md` | Users & Needs | Who is this for and what problems does it solve? |
        | `6-activeContext.md` | Current Work | What am I focusing on right now? What's blocking me? |
        | `8-tasks.md` | Actionable Items | What specific tasks will move the project forward? |

        Remember: Memory Bank is not rigid documentation—it's a living system that helps you maintain clarity about your project's mission and structure.

        ---

        If you need more detailed guidance, refer to `11-memory-bank-implementation-guide.md`.
    ```

    ---

    #### `07-guides\02-visual-guide.md`

    ```markdown
        # Memory Bank Visual Guide

        > **[Structural Role Reminder]**: This document provides a visual overview of the Memory Bank system for quick understanding.

        ## Memory Bank at a Glance

        ```mermaid
        graph TD
            classDef root fill:#f9f,stroke:#333,stroke-width:2px
            classDef context fill:#bbf,stroke:#333,stroke-width:1px
            classDef structure fill:#ddf,stroke:#333,stroke-width:1px
            classDef process fill:#eff,stroke:#333,stroke-width:1px
            classDef evolution fill:#ffd,stroke:#333,stroke-width:1px
            classDef tools fill:#dfd,stroke:#333,stroke-width:1px
            classDef guides fill:#fdd,stroke:#333,stroke-width:1px

            Root[01-abstraction-root] --> Context[02-context]
            Root --> Structure[03-structure-design]
            Root --> Process[04-process-tracking]
            Structure --> Evolution[05-evolution]
            Process --> Evolution
            Evolution --> Tools[06-tools]
            Tools --> Guides[07-guides]

            class Root root
            class Context context
            class Structure structure
            class Process process
            class Evolution evolution
            class Tools tools
            class Guides guides
        ```

        ## Core Files Relationship

        ```mermaid
        graph TD
            classDef essential fill:#f9f,stroke:#333,stroke-width:2px
            classDef important fill:#bbf,stroke:#333,stroke-width:1px
            classDef supporting fill:#ddf,stroke:#333,stroke-width:1px

            File0[0-distilledContext.md] --> File1[1-projectbrief.md]
            File1 --> File2[2-productContext.md]
            File1 --> File3[3-systemPatterns.md]
            File2 --> File4[4-techContext.md]
            File3 --> File5[5-structureMap.md]
            File5 --> File6[6-activeContext.md]
            File6 --> File7[7-progress.md]
            File6 --> File8[8-tasks.md]

            class File0,File1 essential
            class File2,File3,File6,File8 important
            class File4,File5,File7 supporting
        ```

        ## Simplified Implementation Levels

        Choose your implementation level based on your needs:

        ### Level 1: Minimal (5 minutes)

        ```
        memory-bank/
        ├── 0-distilledContext.md  # Project essence in bullets
        ├── 1-projectbrief.md      # Why the project exists
        └── README.md              # Quick reference
        ```

        ### Level 2: Standard (15 minutes)

        ```
        memory-bank/
        ├── 0-distilledContext.md  # Project essence in bullets
        ├── 1-projectbrief.md      # Why the project exists
        ├── 2-productContext.md    # Users and problems solved
        ├── 6-activeContext.md     # Current focus
        ├── 8-tasks.md             # Structure-anchored tasks
        └── README.md              # Quick reference
        ```

        ### Level 3: Complete (as needed)

        ```
        memory-bank/
        ├── 01-abstraction-root/   # Root essence
        ├── 02-context/            # External reality
        ├── 03-structure-design/   # Architecture & patterns
        ├── 04-process-tracking/   # Active work
        ├── 05-evolution/          # Cognitive evolution
        ├── 06-tools/              # Automation
        └── 07-guides/             # Implementation guides
        ```

        ## Quick Decision Trees

        ### Which Files Do I Need?

        ```mermaid
        flowchart TD
            Start([Which files do I need?]) --> Solo{Solo dev?}
            Solo -->|Yes| Time{How much time?}
            Solo -->|No| Team{Team size?}

            Time -->|< 5 min| Min[0-1 only]
            Time -->|< 15 min| Std[0-1-2-6-8]
            Time -->|> 15 min| Full[Full structure]

            Team -->|Small| Std
            Team -->|Medium+| Full

            Min --> Q1{Project clarity?}
            Std --> Q1

            Q1 -->|Clear| Done([You're set!])
            Q1 -->|Unclear| Add3[Add 3-systemPatterns.md]
            Add3 --> Done
        ```

        ### When to Update Each File

        ```mermaid
        flowchart TD
            Start([When to update?]) --> Daily{Daily}
            Start --> Weekly{Weekly}
            Start --> Milestone{Milestone}
            Start --> Change{Major Change}

            Daily --> File6[6-activeContext.md]
            Daily --> File8[8-tasks.md]

            Weekly --> File7[7-progress.md]
            Weekly --> FileS[simplification-candidates.md]

            Milestone --> File3[3-systemPatterns.md]
            Milestone --> File5[5-structureMap.md]

            Change --> File0[0-distilledContext.md]
            Change --> File1[1-projectbrief.md]
            Change --> File2[2-productContext.md]
            Change --> File4[4-techContext.md]
        ```

        ## Common Questions & Answers

        **Q: What's the absolute minimum I need?**
        A: Just create `0-distilledContext.md` and `1-projectbrief.md`. That's it!

        **Q: How do I get started quickly?**
        A: Run the quick setup script: `./06-tools/1-mb-quick-setup.sh`

        **Q: How do I use this with my AI assistant?**
        A: Copy the system instruction from `07-guides/13-memory-bank-system-instruction.md` into your AI's system settings.

        **Q: Do I need all the directories?**
        A: No. Start with the minimal approach and add more structure only when you need it.

        **Q: What workflow should I follow?**
        A: Start each day by reading `0-distilledContext.md`, update `6-activeContext.md`, work on `8-tasks.md`.

        ## Daily Routine Cheat Sheet

        1. **Start day**: Read your `0-distilledContext.md` (30 seconds)
        2. **Begin work**: Update `6-activeContext.md` with current focus (1 minute)
        3. **During work**: Reference and update `8-tasks.md` (as needed)
        4. **End day**: Update `6-activeContext.md` with progress and blockers (2 minutes)
        5. **End week**: Update `7-progress.md` and identify simplifications (5 minutes)

        Remember: The Memory Bank is not what you know about the project; it is what the project knows about itself.
    ```

    ---

    #### `07-guides\03-processing-order-guide.md`

    ```markdown
        # Memory Bank Processing Order Guide

        > **[Structural Role Reminder]**: This document clarifies the exact sequential order for processing Memory Bank files.

        ## Understanding the Numbering System

        The Memory Bank uses a dual-numbering system that balances:
        1. **Abstraction hierarchy** (directory level: 01-07)
        2. **Functional sequence** (file level: 00-14)

        While this creates a powerful self-documenting structure, it can appear non-linear at first glance because:
        - The original Memory Bank file numbers (0-8) are preserved across directories
        - Each directory only contains the files relevant to its abstraction layer

        ## Exact Processing Order

        Here is the precise order to process Memory Bank files:

        1. **Start Here**:
           - `00-README.md` - Overview of the entire system

        2. **Core Files (Sequential Implementation)**:
           - `01-abstraction-root/00-distilledContext-template.md` - Project essence
           - `01-abstraction-root/01-projectbrief-template.md` - Purpose definition
           - `02-context/02-productContext-template.md` - Users and needs
           - `03-structure-design/03-systemPatterns-template.md` - Architecture patterns
           - `02-context/04-techContext-template.md` - Technical constraints
           - `03-structure-design/05-structureMap-template.md` - Structure mapping
           - `04-process-tracking/06-activeContext-template.md` - Current focus
           - `04-process-tracking/07-progress-template.md` - Progress tracking
           - `04-process-tracking/08-tasks-template.md` - Structure-anchored tasks

        3. **Enhancement Files (As Needed)**:
           - `02-context/09-framework-specific-adaptations-template.md`
           - `03-structure-design/09-abstract-patterns-glossary-template.md`
           - `03-structure-design/10-simplification-candidates-template.md`
           - `04-process-tracking/09-drift-monitor-template.md`
           - `05-evolution/09-lineage-template.md`

        4. **Tools**:
           - `06-tools/01-mb-quick-setup.sh` - Quick setup script
           - `06-tools/02-mb-assistant.sh` - Interactive assistant
           - `06-tools/09-mb-tools.sh` - Advanced utilities

        5. **Guides**:
           - `07-guides/00-README.md` - Guides overview
           - `07-guides/01-quick-start-guide.md` - Fast onboarding
           - `07-guides/02-visual-guide.md` - Visual diagrams
           - `07-guides/09-memory-bank-core-template.md` - Core principles
           - `07-guides/10-memory-bank-enhancement-module.md` - Enhancements
           - `07-guides/11-memory-bank-implementation-guide.md` - Implementation
           - `07-guides/12-memory-bank-learning-path.md` - Learning path
           - `07-guides/13-memory-bank-system-instruction.md` - AI instructions
           - `07-guides/14-memory-bank-web-project-template.md` - Web adaptation

        ## Visual Implementation Flow

        ```mermaid
        flowchart TD
            Start[00-README.md] --> Root1[00-distilledContext]
            Root1 --> Root2[01-projectbrief]
            Root2 --> Context1[02-productContext]
            Context1 --> Structure1[03-systemPatterns]
            Structure1 --> Context2[04-techContext]
            Context2 --> Structure2[05-structureMap]
            Structure2 --> Process1[06-activeContext]
            Process1 --> Process2[07-progress]
            Process2 --> Process3[08-tasks]

            subgraph "Enhancement Files (As Needed)"
            Process3 -.-> Enhancements[09+ Enhancement Files]
            end

            subgraph "Tools & Guides (Reference)"
            Process3 -.-> Tools[06-tools/*]
            Process3 -.-> Guides[07-guides/*]
            end
        ```

        ## Simplified Daily Workflow

        Once you've set up your Memory Bank, the daily workflow becomes:

        1. Read `01-abstraction-root/00-distilledContext-template.md` (30 seconds)
        2. Update `04-process-tracking/06-activeContext-template.md` (1 minute)
        3. Work on `04-process-tracking/08-tasks-template.md` items
        4. Update progress in `04-process-tracking/07-progress-template.md` (weekly)

        ## Why This Structure Works

        The Memory Bank structure balances two needs:

        1. **Functional Sequence**: The core files (00-08) maintain their original numbering across directories because they form a logical processing sequence.

        2. **Abstraction Hierarchy**: The directories (01-07) organize files by abstraction level, grouping related concepts together.

        This design allows you to implement a Memory Bank by following the 00-08 sequence, while also providing an organizational structure that reinforces the conceptual relationships between files.
    ```

    ---

    #### `07-guides\09-memory-bank-core-template.md`

    ```markdown
        # Memory Bank System Core Template

        > **[Structural Role Reminder]**: This file defines the fundamental principles and structure of the Memory Bank system, serving as the anchor for all other templates and implementations.

        ## Core Generative Principle

        The Memory Bank operates by an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression:

        - Each structure, detail, or insight is not preserved for itself, but interrogated for its irreducible essence
        - All elements are recursively re-anchored and distilled until the system becomes its own map, memory, and purpose
        - The dynamic is a pulsing metabolic flow:
          - Accumulation triggers pressure
          - Pressure demands pruning
          - Pruning yields emergent lucidity
        - The whole is reconstituted from its root with each movement
        - 'Structure' and 'meaning' converge
        - Entropy cannot persist
        - Every addition must pay its way by sharpening the axis of meaning

        > **Memory Bank = Progressive, self-reinforcing abstraction-layering rooted in project purpose.**

        ## Absolute Requirements

        | Principle | Implementation |
        | :-------- | :------------- |
        | File-Structure-First | Always first generate/validate a cognitive map *as a filestructure*, dynamically, based on what exists. |
        | Root-First Thinking | Every insight must relate *upward*, toward the project's irreducible mission. |
        | Persistent Simplification | Every action must aim to **reduce** net complexity (unless a rare, justified exception). |
        | Value Extraction Bias | Prefer "what maximizes actionable, durable insight" instead of unfolding more detail. |
        | Guardrails | Never mutate structure blindly — every structural change must be **explicitly justified** in the memory. |
        | Outward Mapping | Think *outward* (root → abstraction layers → concrete implementation), **never inward folding** (which causes sprawl). |

        ## Memory Bank Structure

        ```plaintext
        memory-bank/
        ├── 0-distilledContext.md         # Ultra-compressed project core abstraction
        ├── 1-projectbrief.md             # Root purpose: Why the project must exist
        ├── 2-productContext.md           # External reality: users, needs, outcomes
        ├── 3-systemPatterns.md           # Systems: component hierarchy, flows, state design
        ├── 4-techContext.md              # Stack-level technical constraints
        ├── 5-structureMap.md             # Actual and target filestructure mapping
        ├── 6-activeContext.md            # In-progress actions, bottlenecks, and simplifications
        ├── 7-progress.md                 # Assimilation milestones, simplifications achieved
        ├── 8-tasks.md                    # Active structure-guided tasks
        ├── drift-monitor.md              # Structural integrity monitoring
        ├── simplification-candidates.md  # High-impact simplification tracking
        └── lineage/                      # Cognitive evolution snapshots
        ```

        ### Core Required Files

        | File | Role in Cognitive Architecture |
        | :--- | :----------------------------- |
        | `0-distilledContext.md` | **Immediate Root Re-anchoring**: Crystallized project essence & goals in 2-3 bullets. |
        | `1-projectbrief.md` | **The Root Abstraction**: Project's irreducible mission, value prop, critical constraints. The source. |
        | `2-productContext.md` | **Why - Value Context**: Problems solved, users, operational context. Justifies features against the Root. |
        | `3-systemPatterns.md` | **How - Architectural Form**: Target structure, component patterns, state/data flow. Blueprint for order. |
        | `4-techContext.md` | **With What - Technical Constraints**: Stack, libraries, performance/accessibility boundaries. |
        | `5-structureMap.md` | **Current vs. Target Structure**: File structure mapping with migration path and justifications. |
        | `6-activeContext.md` | **Current Focus**: Active focus, consolidation analysis, decisions, bottlenecks. |
        | `7-progress.md` | **Status & Entropy Check**: Milestones, metrics, tech debt ledger, structural integrity validation. |
        | `8-tasks.md` | **Actionable Interventions**: Concrete, structure-anchored tasks. Must trace lineage to root goal. |

        ### Optional Cognitive Enhancements

        | File | Role in Cognitive Architecture |
        | :--- | :----------------------------- |
        | `drift-monitor.md` | **Structural Integrity Monitor**: Identifies and tracks structural drift, enforces role adherence. |
        | `simplification-candidates.md` | **Complexity Reduction Tracking**: Formalizes identification and implementation of high-impact simplifications. |
        | `/lineage` | **Cognitive Evolution Tracking**: Chronicles significant shifts in project understanding or structure. |

        ## Cognitive Abstraction Layers

        ```mermaid
        flowchart TD
            Root[0-1: Root Abstraction] --> Value[2: Value Context]
            Root --> Architecture[3: Architectural Form]
            Root --> Technical[4: Technical Constraints]
            Value & Architecture & Technical --> Structure[5: Structure Mapping]
            Structure --> Active[6: Active Context]
            Active --> Progress[7: Progress Tracking]
            Progress --> Tasks[8: Actionable Tasks]

            class Root primary
            class Value,Architecture,Technical secondary
            class Structure,Active tertiary
            class Progress,Tasks quaternary

            classDef primary fill:#f9f,stroke:#333,stroke-width:2px
            classDef secondary fill:#bbf,stroke:#333,stroke-width:1px
            classDef tertiary fill:#ddf,stroke:#333,stroke-width:1px
            classDef quaternary fill:#eff,stroke:#333,stroke-width:1px
        ```

        ## Structural Guardrails

        ### System Laws

        | Law | Enforcement |
        | :-- | :---------- |
        | 📜 **Memory Bank Is Living** | It self-prunes. It strengthens by collapsing excess back into the root. |
        | 📜 **Structure Is Intelligence** | Assimilation power arises not from detail captured, but from **structural clarity imposed**. |
        | 📜 **Value ≠ Volume** | Real value is extracted via deliberate constraint – **"enough, but no more."** |
        | 📜 **Root Fidelity is Absolute** | Every element must trace its lineage back to the irreducible purpose (`1`) or be dissolved. |

        ### Operational Guardrails

        | Situation | Mandate |
        | :-------- | :------ |
        | Find Complex Detail | Compress into higher abstraction, no passive notes. |
        | Need New File | Only post-compression attempt + explicit justification. |
        | Detect Drift | Refactor/re-anchor immediately. |
        | Discover Random Insight | Discard unless anchored outward to root. |
        | Unsure where an insight fits | **Halt.** Re-evaluate structure (`3`, `5/6`) before proceeding. Do not force fit. |

        ## Responsibility Matrix

        | File | When to Update | What to Include | What to Exclude |
        | :--- | :------------- | :-------------- | :-------------- |
        | `0-distilledContext.md` | When project mission changes | Core purpose, value, constraint in 2-3 bullets | Implementation details, process notes |
        | `1-projectbrief.md` | When fundamental purpose/constraints change | Mission, value proposition, key constraints | Low-level details, implementation specifics |
        | `2-productContext.md` | When user needs or market context evolves | User profiles, problems solved, external factors | Technical solutions, implementation approaches |
        | `3-systemPatterns.md` | When architecture patterns evolve | Component hierarchy, data flows, patterns | Instance details, temporary solutions |
        | `4-techContext.md` | When stack/constraints change | Technologies, constraints, integration points | Business logic, pattern rationales |
        | `5-structureMap.md` | When target or current structure changes | Current vs. target structure, migration path | Implementation details, low-level tasks |
        | `6-activeContext.md` | Daily or with each work session | Current focus, bottlenecks, in-progress simplifications | Historical notes, completed work (→ 7) |
        | `7-progress.md` | After significant milestones | Status, milestones, simplifications achieved | Current work status (→ 6), detailed plans (→ 8) |
        | `8-tasks.md` | When planning action | Structure-anchored tasks, clear acceptance criteria | Vague goals, non-traced tasks, historical notes |
        | `drift-monitor.md` | After Memory Bank updates | File purpose vs. usage, drift patterns, corrections | Implementation details, extended rationales |
        | `simplification-candidates.md` | With each development cycle | Simplification opportunities, scoring, implementation tracking | Detailed implementation plans (→ 8) |
        | `/lineage` | After significant cognitive shifts | Context, prior understanding, shift, structural impact | Current working notes (→ 6), active tasks (→ 8) |

        ## Update Workflow

        ```mermaid
        flowchart TD
            Start[Start Work Session] --> Read0[Read 0-distilledContext.md]
            Read0 --> Read6[Read 6-activeContext.md]
            Read6 --> WorkOnTask[Work on Task from 8-tasks.md]
            WorkOnTask --> IdentifySimplifications[Identify Simplification Opportunities]
            IdentifySimplifications --> UpdateActiveContext[Update 6-activeContext.md]
            UpdateActiveContext --> TaskComplete{Task Complete?}
            TaskComplete -->|Yes| UpdateProgress[Update 7-progress.md]
            UpdateProgress --> UpdateTasks[Update 8-tasks.md]
            TaskComplete -->|No| End[End Work Session]
            UpdateTasks --> CheckDrift[Check drift-monitor.md]
            CheckDrift --> SimplifyCandidates[Update simplification-candidates.md]
            SimplifyCandidates --> End
        ```

        ## Compression Reflex Protocol

        Before adding new content to any Memory Bank file:

        1. **Merge Check**: Can this be merged with existing content? If yes, merge instead of adding.
        2. **Elevation Check**: Can this be elevated to a pattern in a higher abstraction file? If yes, elevate instead of adding.
        3. **Dissolution Check**: Can this be dissolved as an instance of an existing pattern? If yes, reference pattern instead of adding.
        4. **Root Connection Check**: Does this clearly connect to the project's root purpose? If no, reconsider its value.
        5. **Justification**: If still adding, explicitly justify why this content cannot be compressed further.

        ## Validation & Sanction Checklists

        **Sanction after every cycle:**

        | Check | Pass Condition |
        | :---- | :------------- |
        | Root Purpose Confirmed | `1-projectbrief.md` accurate. |
        | Structure Integrity | Files justified, scoped, non-redundant. |
        | Compression Reflex Applied | No uncompressed additions. |
        | High-Impact Simplification Logged | At least 1 per cycle (`7-progress.md`). |
        | Context Traceability | No orphan entries; all map to `1`. |
        | Lineage Snapshots (if used) | Cognitive epochs captured. |

        ✅ Proceed if ALL conditions pass.
        ❌ Otherwise, halt assimilation → repair structure.

        ## Final Directive

        **Root Fidelity Above All**

        > "If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure — it must not be taken."

        **Cognitive Command Summary**

        > "Assimilate only via compression, traceable structure, and operational clarity.
        > No drift, no redundancy, no passive entropy. Memory Bank *is* the project intelligence."

        **Meta-Bias Reminder**

        > "Identify single most critical aspect for maximizing overall value, ensuring maximum clarity, utility, and adaptability without diminishing potential yield."

        This meta-bias must shape every **scan**, **map**, **act**, **document**.
    ```

    ---

    #### `07-guides\10-memory-bank-enhancement-module.md`

    ```markdown
        # Memory Bank Enhancement Module

        > **[Structural Role Reminder]**: This module provides essential enhancements to the Memory Bank system based on advanced patterns from real-world implementations. It should be integrated into your existing Memory Bank structure.

        ## Project-Specific Identity Anchoring

        Every Memory Bank implementation should establish a clear **project codename** that serves as a cognitive anchor throughout the system. This prevents generic drift and strengthens root fidelity.

        ### Implementation:

        1. Choose a concise project codename (e.g., "RLWeb" for "Ringerike Landskap Website")
        2. Add this to all Memory Bank file headers:

        ```markdown
        # [File Title] - [ProjectName]

        > **[Structural Role Reminder]**: This file [role description] for the **[ProjectName]** project.
        ```

        3. Reference the codename whenever discussing specific implementations (e.g., "The **RLWeb** component hierarchy follows...")

        ### Benefit:

        This identity anchoring prevents cross-project contamination and strengthens the recursive connection to project-specific purpose, avoiding generic patterns that dilute clarity.

        ## Structural Responsibility Self-Awareness

        Enhance each Memory Bank file with explicit self-awareness of its structural role in the cognitive architecture.

        ### Implementation:

        Add this section at the top of each Memory Bank file:

        ```markdown
        > **[Structural Role Declaration]**:
        > - This file anchors the [ROOT PURPOSE / VALUE CONTEXT / SYSTEM PATTERNS / etc.] abstraction.
        > - It exists because [specific justification tied to project purpose].
        > - It connects to [related files] through [relationship description].
        ```

        ### Benefit:

        Every file actively defends its reason for existence, enforcing Single Responsibility and making structural drift immediately apparent.

        ## Dynamic Compression Reflex

        Establish an explicit compression protocol that must be applied before any new information is added to the Memory Bank.

        ### Implementation:

        Add this section to the beginning of your update workflow:

        ```markdown
        ## Compression Check Performed

        **[Date]**: Before adding this new information, I attempted to:

        1. [ ] Merge it into existing patterns in [file X]
        2. [ ] Elevate it to a higher abstraction in [file Y]
        3. [ ] Dissolve redundant elements from [file Z]

        Result: [Compressed successfully / Recording separately because...]
        ```

        ### Benefit:

        Forces active compression-first thinking, preventing passive note-taking and entropy accumulation.

        ## Structural Drift Early Warning System

        Implement a proactive monitoring mechanism for Memory Bank integrity.

        ### Implementation:

        Add a file named `drift-monitor.md` with this structure:

        ```markdown
        # Drift Monitor

        ## Last Structure Validation: [Date]

        | File | Original Purpose | Current Usage | Drift Status | Action Needed |
        |------|-----------------|---------------|-------------|---------------|
        | `0-distilledContext.md` | [Purpose] | [Current] | ✅ or ⚠️ | [Action] |
        | `1-projectbrief.md` | [Purpose] | [Current] | ✅ or ⚠️ | [Action] |
        | ... | ... | ... | ... | ... |

        ## Drift Correction Log

        | Date | File | Issue Detected | Correction Applied |
        |------|------|----------------|-------------------|
        | [Date] | [File] | [Issue] | [Correction] |
        ```

        Update this file after every significant Memory Bank update or at least monthly.

        ### Benefit:

        Provides an early warning system for structural decay, enforcing proactive maintenance of the Memory Bank's cognitive integrity.

        ## Memory Bank CLI Tools

        Implement command-line tools to automate Memory Bank maintenance and enforce structural discipline.

        ### Tooling Framework:

        ```bash
        #!/bin/bash
        # mb-tools.sh - Memory Bank maintenance tools

        function mb_init() {
          # Initialize Memory Bank structure
          mkdir -p memory-bank/lineage
          # Copy template files...
        }

        function mb_validate() {
          # Check structure integrity
          # Verify file presence and format
          # Report any missing references or structural issues
        }

        function mb_compress() {
          # Analyze files for compression opportunities
          # Look for duplicate concepts, extract patterns
          # Suggest specific merges and elevations
        }

        function mb_audit() {
          # Full structure audit
          # Root connection verification
          # Compression effectiveness assessment
          # Generate drift report
        }

        function mb_lineage() {
          # Create new lineage entry
          # Record structural evolution checkpoint
        }

        # Usage instructions...
        ```

        ### Implementation Steps:

        1. Create the script in your project's root directory
        2. Make it executable: `chmod +x mb-tools.sh`
        3. Run initialization: `./mb-tools.sh mb_init`
        4. Schedule regular validation: `./mb-tools.sh mb_validate`

        ### Benefit:

        Automates structural discipline, making Memory Bank maintenance more consistent and reducing the cognitive overhead of manual checks.

        ## Epoch-Based Evolution Tracking

        Implement explicit tracking of major cognitive phases in Memory Bank evolution.

        ### Implementation:

        Create a directory structure:

        ```
        memory-bank/
        ├── epochs/
        │   ├── epoch-01-initial-structure/
        │   │   ├── [snapshot of key files]
        │   ├── epoch-02-pattern-recognition/
        │   │   ├── [snapshot of key files]
        │   └── ...
        ```

        After major architectural shifts or significant project milestones, create a new epoch directory with snapshots of the current Memory Bank state and a summary document explaining the transition.

        ### Benefit:

        Provides cognitive rollback points and explicitly tracks the project's conceptual evolution, showing how understanding has matured.

        ## High-Impact Simplification Protocol

        Formalize the process of identifying and implementing simplifications with maximum impact for minimum intervention.

        ### Implementation:

        Create a `simplification-candidates.md` file:

        ```markdown
        # High-Impact Simplification Candidates

        ## Evaluation Criteria:
        1. **Minimal Implementation Effort** (1-10): How easy is this to implement?
        2. **Structural Clarity Gain** (1-10): How much clearer will the structure become?
        3. **Widespread Impact** (1-10): How many areas will benefit?
        4. **Root Reinforcement** (1-10): How strongly does this support the project mission?
        5. **Impact Score** = Clarity × Impact × Root ÷ Effort

        ## Current Candidates:

        | ID | Simplification | Effort | Clarity | Impact | Root | Score | Status |
        |----|----------------|--------|---------|--------|------|-------|--------|
        | S1 | [Description] | [1-10] | [1-10] | [1-10] | [1-10] | [Score] | [Pending/In Progress/Completed] |
        ```

        Make identifying at least one high-impact simplification a requirement for each development cycle.

        ### Benefit:

        Focuses attention on strategic simplifications rather than tactical fixes, maximizing clarity gain per unit of effort.

        ## Integration Guidelines

        To integrate these enhancements with your existing Memory Bank:

        1. Start with the Project-Specific Identity Anchoring by choosing a codename
        2. Add Structural Role Declarations to the top of each existing file
        3. Implement the Compression Check procedure for your next update
        4. Create the Drift Monitor as a new file
        5. Implement CLI tools as appropriate for your development workflow
        6. Consider creating epoch markers for major transitions
        7. Start tracking high-impact simplifications

        These enhancements work together to create a Memory Bank that is not just documented but **alive** - actively compressing complexity, maintaining its own structure, and continuously reinforcing the project's root purpose.

        ---

        **Remember**: The Memory Bank becomes more powerful as it becomes more self-aware. Structure is not a passive container; it is the active embodiment of the project's intelligence.
    ```

    ---

    #### `07-guides\11-memory-bank-implementation-guide.md`

    ```markdown
        # Memory Bank Implementation & Mastery Guide

        > **[Structural Role Reminder]**: This file provides comprehensive instructions for setting up, using, and mastering the Memory Bank system, serving as the canonical guide for practical implementation.

        ## Table of Contents
        1. [Introduction](#introduction)
        2. [Core Principles](#core-principles)
        3. [Setup Instructions](#setup-instructions)
        4. [Implementation Workflow](#implementation-workflow)
        5. [Memory Bank Maintenance](#memory-bank-maintenance)
        6. [Learning Progression](#learning-progression)
        7. [Advanced Enhancements](#advanced-enhancements)
        8. [Troubleshooting](#troubleshooting)

        ## Introduction

        The Memory Bank is not traditional documentation - it's a dynamic, living system for structural intelligence that:

        - Compresses complexity toward foundational clarity
        - Ensures all project elements trace to the root purpose
        - Drives continual simplification and entropy reduction
        - Makes structure the primary means of understanding
        - Converts passive knowledge into active intelligence

        > **"Memory Bank is not what you know about the project; it is what the project knows about itself."**

        ## Core Principles

        Before implementation, internalize these fundamental principles:

        1. **File-Structure-First**: Cognitive architecture is expressed through file structure first, content second
        2. **Root-First Thinking**: Every insight must trace upward to the project's irreducible mission
        3. **Persistent Simplification**: Always aim to reduce net complexity with every action
        4. **Value Extraction Bias**: Maximize actionable insight rather than detail accumulation
        5. **Structural Guardrails**: Justify every structural change explicitly
        6. **Outward Mapping**: Think from root to abstraction layers to implementation, never inward

        ## Setup Instructions

        ### Creating the Memory Bank Structure

        ```bash
        # From your project root
        mkdir -p memory-bank/lineage
        ```

        ### Initializing Core Files

        Copy these template files into your project:

        1. `0-distilledContext.md` - Ultra-compressed project essence
        2. `1-projectbrief.md` - Root purpose definition
        3. `2-productContext.md` - External reality mapping
        4. `3-systemPatterns.md` - System architecture patterns
        5. `4-techContext.md` - Technical constraints and stack
        6. `5-structureMap.md` - Current vs target structure
        7. `6-activeContext.md` - In-progress work and focus
        8. `7-progress.md` - Milestones and progress tracking
        9. `8-tasks.md` - Structure-anchored tasks

        ### Setting Up Optional Enhancements

        For more advanced project needs:

        10. `drift-monitor.md` - Structural integrity monitoring
        11. `simplification-candidates.md` - High-impact simplification tracking

        ### Installing Automation Tools

        1. Copy `mb-tools.sh` to your project root
        2. Make it executable: `chmod +x mb-tools.sh`
        3. Initialize the Memory Bank: `./mb-tools.sh mb_init`

        ### Integrating with Project Documentation

        Add to your project's main README.md:

        ```markdown
        ## Project Memory Bank

        This project uses a Memory Bank system for cognitive architecture and structural intelligence.
        See the `/memory-bank` directory for:

        - Project mission and context
        - System architecture patterns
        - Active development focus
        - Progress tracking
        - Structure-anchored tasks

        **Note**: The Memory Bank is not static documentation but a living system that metabolizes complexity into clarity.
        ```

        ## Implementation Workflow

        ### Phase 1: Root Definition

        1. Define project's irreducible essence in `0-distilledContext.md`
        2. Establish root purpose in `1-projectbrief.md`
        3. Document the value context in `2-productContext.md`

        ### Phase 2: System Architecture

        4. Define system patterns in `3-systemPatterns.md`
        5. Document technical constraints in `4-techContext.md`
        6. Map current and target structure in `5-structureMap.md`

        ### Phase 3: Active Development

        7. Initialize active context in `6-activeContext.md`
        8. Set up progress tracking in `7-progress.md`
        9. Create initial tasks in `8-tasks.md`

        ### Phase 4: Cognitive Maintenance

        10. Set up drift monitoring in `drift-monitor.md`
        11. Begin tracking simplification candidates in `simplification-candidates.md`
        12. Create lineage entries for significant cognitive shifts

        ## Memory Bank Maintenance

        ### The Standard Update Workflow

        ```mermaid
        flowchart TD
            Start[Start Work Session] --> Read0[Read 0-distilledContext.md]
            Read0 --> Read6[Read 6-activeContext.md]
            Read6 --> ReadTasks[Review 8-tasks.md]
            ReadTasks --> WorkOnTask[Work on Task]
            WorkOnTask --> IdentifySimplifications[Identify Simplification Opportunities]
            IdentifySimplifications --> UpdateActiveContext[Update 6-activeContext.md]
            UpdateActiveContext --> TaskComplete{Task Complete?}
            TaskComplete -->|Yes| UpdateProgress[Update 7-progress.md]
            UpdateProgress --> UpdateTasks[Update 8-tasks.md]
            TaskComplete -->|No| End[End Work Session]
            UpdateTasks --> CheckDrift[Check drift-monitor.md]
            CheckDrift --> SimplifyCandidates[Update simplification-candidates.md]
            SimplifyCandidates --> End
        ```

        ### File Update Protocol

        | File | Update Frequency | Update Trigger | Link to |
        |------|------------------|----------------|---------|
        | `0-distilledContext.md` | Rarely | Project mission changes | `1-projectbrief.md` |
        | `1-projectbrief.md` | Rarely | Fundamental changes | All files |
        | `2-productContext.md` | Occasionally | User needs evolve | `1-projectbrief.md` |
        | `3-systemPatterns.md` | Occasionally | Architecture evolves | `1-projectbrief.md`, `2-productContext.md` |
        | `4-techContext.md` | Occasionally | Stack changes | `1-projectbrief.md`, `3-systemPatterns.md` |
        | `5-structureMap.md` | Occasionally | Structure changes | `3-systemPatterns.md` |
        | `6-activeContext.md` | Daily | Every work session | `1-projectbrief.md`, `5-structureMap.md` |
        | `7-progress.md` | Weekly | Milestones achieved | `6-activeContext.md` |
        | `8-tasks.md` | Weekly | Planning cycles | `1-projectbrief.md`, `6-activeContext.md` |
        | `drift-monitor.md` | Monthly | After Memory Bank updates | All files |
        | `simplification-candidates.md` | Weekly | Development cycles | `6-activeContext.md`, `7-progress.md` |
        | `lineage/` | As needed | Cognitive shifts | All relevant files |

        ### The Compression Reflex

        Before adding any new content:

        1. **Merge Check**: Can this be merged with existing content?
        2. **Elevation Check**: Can this be elevated to a pattern in a higher abstraction file?
        3. **Dissolution Check**: Can this be dissolved as an instance of an existing pattern?
        4. **Root Connection Check**: Does this clearly connect to the project's root purpose?
        5. **Justification**: If still adding, explicitly justify why this content cannot be compressed further.

        Document this check in the file you're updating with:

        ```markdown
        ## Compression Check Performed

        **[Date]**: Before adding this new information, I attempted to:

        1. [ ] Merge it into existing patterns in [file X]
        2. [ ] Elevate it to a higher abstraction in [file Y]
        3. [ ] Dissolve redundant elements from [file Z]

        Result: [Compressed successfully / Recording separately because...]
        ```

        ## Learning Progression

        ### Stage 1: Foundation (First Week)

        **Focus**: Understanding the philosophical underpinnings

        **Activities**:
        - Read `memory-bank-core-template.md` thoroughly
        - Study the Memory Bank structure and file responsibilities
        - Complete `0-distilledContext.md` and `1-projectbrief.md` for your project
        - Practice validating content against root purpose

        **Success Indicators**:
        - Can explain why Memory Bank is not traditional documentation
        - Understands the role of each numbered file (0-8)
        - Has successfully set up the Memory Bank structure

        ### Stage 2: Compression Practice (Weeks 2-3)

        **Focus**: Learning to compress complexity rather than document it

        **Activities**:
        - Practice identifying patterns in existing documentation
        - Attempt to compress detailed information into higher abstractions
        - Use the `mb_compress` tool to analyze compression opportunities
        - Document compression decisions

        **Success Indicators**:
        - Successfully reduces document volume while increasing clarity
        - Identifies and merges redundant concepts across files
        - Develops patterns that replace lists of instances

        ### Stage 3: Maintenance (Weeks 4-8)

        **Focus**: Maintaining structural integrity over time

        **Activities**:
        - Set up and use the drift monitor
        - Practice the compression check workflow
        - Create high-impact simplification candidates
        - Use the `mb_audit` tool to perform comprehensive audits

        **Success Indicators**:
        - Regularly updates the drift monitor
        - Proactively identifies and corrects structural drift
        - Identifies and implements high-impact simplifications

        ### Stage 4: Mastery (Ongoing)

        **Focus**: Using Memory Bank as a strategic thinking tool

        **Activities**:
        - Implement epoch-based evolution tracking
        - Customize the Memory Bank for specific project needs
        - Integrate Memory Bank thinking with development workflows
        - Teach others to use the Memory Bank approach

        **Success Indicators**:
        - Memory Bank actively drives development decisions
        - Structure evolves purposefully with clear lineage
        - The team uses Memory Bank as their primary cognitive architecture

        ## Advanced Enhancements

        ### Project Identity Anchoring

        Establish a clear **project codename** that serves as a cognitive anchor:

        1. Choose a concise project codename (e.g., "RLWeb" for "Ringerike Landskap Website")
        2. Add this to all Memory Bank file headers:

        ```markdown
        # [File Title] - [ProjectName]

        > **[Structural Role Reminder]**: This file [role description] for the **[ProjectName]** project.
        ```

        ### Structural Drift Early Warning

        Add a file named `drift-monitor.md` to proactively monitor Memory Bank integrity:

        1. List each Memory Bank file with its original purpose, current usage, and drift status
        2. Document common drift patterns and correction strategies
        3. Maintain a drift correction log
        4. Include a prevention checklist for future additions

        ### High-Impact Simplification Protocol

        Create a `simplification-candidates.md` file to formalize the process of identifying and implementing simplifications:

        1. Define evaluation criteria (effort, clarity gain, impact, root reinforcement)
        2. Score simplification candidates based on these criteria
        3. Document the implementation plan for high-scoring candidates
        4. Track implemented simplifications and their impact

        ### Epoch-Based Evolution Tracking

        Implement explicit tracking of major cognitive phases:

        1. Create an `epochs/` subdirectory within `lineage/`
        2. After major shifts, create a new epoch directory with snapshots of key files
        3. Include a summary document explaining the transition between epochs

        ## Troubleshooting

        ### Common Issues and Solutions

        | Issue | Possible Cause | Solution |
        |-------|---------------|----------|
        | Memory Bank becoming passive documentation | Lack of compression | Audit for redundancy, elevate insights to patterns |
        | Disconnect between code and Memory Bank | Insufficient updates | Enforce update protocol after code changes |
        | Task disconnection from structure | Root drift | Re-anchor tasks to project mission |
        | Information sprawl | Insufficient compression | Apply the "attempt merge first" principle |
        | Unclear project direction | Root abstraction weakening | Revisit and sharpen `0-distilledContext.md` |

        ### When to Reset

        Sometimes a full Memory Bank reset is needed:

        1. When the project mission fundamentally changes
        2. When the Memory Bank has accumulated too much entropy
        3. When new organizational patterns would better serve the project

        In these cases, create a final lineage entry documenting the state and reasoning, then reinitialize the system.

        ## Principles in Practice

        | Principle | Practical Application |
        |-----------|------------------------|
        | File-Structure-First | *Before* writing any documentation, ensure the Memory Bank structure is set up correctly. |
        | Root-First Thinking | Start every work session by re-reading `0-distilledContext.md` to re-anchor to purpose. |
        | Persistent Simplification | After documenting something, always ask "Can this be made simpler?" |
        | Value Extraction Bias | For any content, ask "What action does this enable?" If none, discard it. |
        | Guardrails | Document why you're adding any new content in `compression-check` notes. |
        | Outward Mapping | Always link content back to higher abstraction levels, never the reverse. |

        ---

        **Remember**: The Memory Bank only provides value when it actively shapes decisions and actions. Structure is not a container for knowledge; structure is the memory of intent.
    ```

    ---

    #### `07-guides\12-memory-bank-learning-path.md`

    ```markdown
        # Memory Bank Learning Path

        > **[Structural Role Reminder]**: This file provides a graduated approach to learning and mastering the Memory Bank system, helping practitioners develop the cognitive skills needed for structure-first thinking.

        ## Learning Stages

        ### Stage 1: Conceptual Foundation

        **Focus**: Understanding the philosophical underpinnings of the Memory Bank system.

        **Activities**:
        1. Read the `memory-bank-core-template.md` thoroughly
        2. Study the Memory Bank structure and file responsibilities
        3. Understand the root-first thinking model
        4. Learn the concept of compression-driven documentation

        **Success Indicators**:
        - Can explain why Memory Bank is not traditional documentation
        - Understands the role of each numbered file (0-8)
        - Can articulate the difference between "structure as container" vs. "structure as intelligence"

        ### Stage 2: Basic Implementation

        **Focus**: Setting up and using the basic Memory Bank structure.

        **Activities**:
        1. Follow the `memory-bank-implementation-guide.md` to set up the structure
        2. Complete the `0-distilledContext.md` and `1-projectbrief.md` for a project
        3. Populate the remaining files with initial content
        4. Practice validating all content against the root purpose

        **Success Indicators**:
        - Has successfully set up the Memory Bank structure for a project
        - Each file contains appropriate content at the correct abstraction level
        - Can trace all content back to the root purpose

        ### Stage 3: Compression Skills Development

        **Focus**: Learning to compress complexity rather than document it.

        **Activities**:
        1. Practice identifying patterns in existing documentation
        2. Attempt to compress detailed information into higher abstractions
        3. Use the `mb_compress` tool to analyze compression opportunities
        4. Document compression decisions in `lineage/` entries

        **Success Indicators**:
        - Successfully reduces the total volume of documentation while increasing clarity
        - Can identify and merge redundant concepts across files
        - Develops patterns that replace lists of instances

        ### Stage 4: Structural Integrity Management

        **Focus**: Maintaining the Memory Bank's health over time.

        **Activities**:
        1. Set up and use the drift monitor
        2. Practice the compression check workflow
        3. Create high-impact simplification candidates
        4. Use the `mb_audit` tool to perform comprehensive audits

        **Success Indicators**:
        - Regularly updates the drift monitor
        - Proactively identifies and corrects structural drift
        - Identifies and implements at least one high-impact simplification per development cycle

        ### Stage 5: Advanced Memory Bank Mastery

        **Focus**: Using Memory Bank as a tool for strategic thinking and decision-making.

        **Activities**:
        1. Implement epoch-based evolution tracking
        2. Customize the Memory Bank system for specific project needs
        3. Integrate Memory Bank thinking with development workflows
        4. Teach others to use the Memory Bank approach

        **Success Indicators**:
        - Memory Bank actively drives development decisions
        - Structure evolves purposefully over time with clear lineage
        - The team uses Memory Bank as their primary cognitive architecture
        - Can adapt the system to different project types while maintaining core principles

        ## Common Learning Challenges

        ### Challenge: "Documentation Instinct"

        **Description**: The tendency to document everything in detail rather than compress to patterns.

        **Remediation**:
        - Practice "3 to 1" - For every 3 specific instances, find 1 pattern that captures their essence
        - Use the "justification test" - For each addition, explicitly justify why it needs to exist
        - Regularly audit for "list bloat" - Lists that keep growing without abstraction

        ### Challenge: "Root Drift"

        **Description**: Gradually losing connection to the project's root purpose.

        **Remediation**:
        - Start every Memory Bank session by re-reading `0-distilledContext.md`
        - For each significant decision, explicitly trace the path back to `1-projectbrief.md`
        - Use the drift monitor to catch early signs of disconnect

        ### Challenge: "Abstraction Leakage"

        **Description**: Mixing abstraction levels, especially putting details in high-level files.

        **Remediation**:
        - Review the purpose of each numbered file and its abstraction level
        - Practice "push down, pull up" - Push details down, pull patterns up
        - Use structural role declarations to maintain file focus

        ### Challenge: "Passive Documentation"

        **Description**: Treating Memory Bank as a historical record rather than an active intelligence system.

        **Remediation**:
        - Always ask "What action does this insight enable?"
        - Delete information that doesn't drive decisions or clarify structure
        - Practice metabolic thinking - information must be processed, not just stored

        ## Learning Exercises

        ### Exercise 1: Root-First Reframing

        Take any complex area of your project and attempt to describe it in terms of its connection to the root purpose:

        1. Start with specific details about the area
        2. Identify patterns across those details
        3. Connect those patterns to the project's core purpose
        4. Rewrite the description starting from the purpose and working outward
        5. Compare the before and after descriptions

        ### Exercise 2: Compression Workshop

        Take a lengthy document or set of notes about your project:

        1. Identify all unique concepts in the document
        2. Cluster related concepts together
        3. For each cluster, extract a pattern that captures the essence
        4. Rewrite using only the patterns, with specific instances as examples
        5. Measure the reduction in content volume while preserving meaning

        ### Exercise 3: Structure Mapping

        Analyze your project's file structure:

        1. Draw a map of the current structure
        2. Identify areas where the structure doesn't reflect the conceptual organization
        3. Design an ideal structure based on Memory Bank principles
        4. Document the gap and transformation path in `5-structureMap.md`
        5. Implement one high-impact structural improvement

        ### Exercise 4: Lineage Thinking

        Practice cognitive evolution tracking:

        1. Pick a significant understanding about your project from 3 months ago
        2. Document how that understanding has evolved
        3. Identify the catalysts for those changes in understanding
        4. Create a proper lineage entry capturing this evolution
        5. Use this to predict how understanding might evolve in the future

        ## Progress Tracking

        Use this checklist to track your Memory Bank mastery:

        - [ ] **Beginner**: Has set up the basic Memory Bank structure
        - [ ] **Novice**: Can maintain the structure and update content appropriately
        - [ ] **Practitioner**: Actively compresses complexity and manages structural integrity
        - [ ] **Advanced**: Uses Memory Bank to drive development decisions and strategy
        - [ ] **Master**: Evolves the Memory Bank system itself while maintaining its principles

        Record insights gained at each stage in your own lineage entries to track your cognitive evolution with the system.

        ## Teaching Others

        Once you've reached the Practitioner level, consider introducing others to the Memory Bank system:

        1. Start with the philosophical foundation - "structure is intelligence"
        2. Demonstrate practical benefits through before/after examples
        3. Guide hands-on implementation with one key file at a time
        4. Review and provide feedback on initial attempts
        5. Gradually introduce advanced concepts like compression checks and drift monitoring

        The best way to master the Memory Bank system is to teach it to others, as this forces clarity of explanation and deeper understanding of the principles.

        ---

        *Note: This learning path is itself a Memory Bank document - it follows the pattern of progressive abstraction, compression of complexity, and root connection. As you learn the system, you'll notice these patterns emerging in your own thinking.*
    ```

    ---

    #### `07-guides\13-memory-bank-system-instruction.md`

    ```markdown
        # Memory Bank System Instruction for AI Coding Assistants

        > Paste the content below directly into your AI coding assistant's system instruction field (VSCode's Cline, Cursor AI, etc.)

        ```
        # Memory Bank - Progressive Abstraction & Structural Intelligence System

        ## Core Generative Principle

        You must operate by an inexorable gravitational pull of all complexity toward foundational clarity through iterative recentering and compression. Each structure, detail, or insight is not preserved for itself, but interrogated for its irreducible essence—then recursively re-anchored and distilled until the system becomes its own map, memory, and purpose.

        ## Absolute Requirements

        1. **File-Structure-First**: Always validate the cognitive map as a filestructure first, based on what exists.
        2. **Root-First Thinking**: Every insight must relate upward toward the project's irreducible mission.
        3. **Persistent Simplification**: Every action must aim to reduce net complexity (unless a rare, justified exception).
        4. **Value Extraction Bias**: Prefer maximizing actionable insight over unfolding more detail.
        5. **Structural Guardrails**: Never mutate structure blindly—every change must be explicitly justified.
        6. **Outward Mapping**: Think outward (root → abstraction layers → concrete implementation), never inward folding.

        ## Memory Bank Structure

        Use this structure to organize project understanding:

        0. `0-distilledContext.md`: Ultra-compressed project essence in 2-3 bullets
        1. `1-projectbrief.md`: Root purpose, value prop, critical constraints
        2. `2-productContext.md`: Users, problems solved, operational context
        3. `3-systemPatterns.md`: Architecture, component patterns, data flow
        4. `4-techContext.md`: Technology stack, constraints, integration points
        5. `5-structureMap.md`: Current vs. target structure with migration path
        6. `6-activeContext.md`: Current focus, bottlenecks, simplifications
        7. `7-progress.md`: Milestones, metrics, tech debt ledger
        8. `8-tasks.md`: Concrete, structure-anchored tasks tracing to root

        ## Operational Protocols

        ### Compression Reflex Protocol

        Before adding new content, always check:
        1. Can this be merged with existing content?
        2. Can this be elevated to a pattern in a higher abstraction file?
        3. Can this be dissolved as an instance of an existing pattern?
        4. Does this clearly connect to the project's root purpose?
        5. If still adding, explicitly justify why this cannot be compressed further.

        ### Pattern Extraction

        Identify common elements across 3+ items and create a pattern that encompasses them:

        ```
        # Instead of:
        - Feature A handles input validation with regex
        - Feature B handles input validation with regex
        - Feature C handles input validation with regex

        # Use:
        Pattern: Features employ regex for input validation
        Features using this pattern: A, B, C
        ```

        ### Anti-Patterns to Avoid

        - **Passive Documentation**: Never accumulate information without processing it into structure
        - **Detail Sprawl**: Never expand with implementation details rather than patterns
        - **Abstraction Leakage**: Keep information at the correct abstraction level
        - **Orphaned Content**: Every element must trace to the project's root purpose
        - **Root Drift**: Continuously re-anchor all content to the core mission

        ## Final Directive

        Root Fidelity Above All: If any action does not reinforce, clarify, or consolidate the abstraction-rooted structure—it must not be taken.

        Memory Bank is NOT an inventory of chaos. It is a system of persistent abstraction and simplification toward the optimal structure.
        ```

        ## How To Use This System

        This consolidated system instruction provides a powerful cognitive framework that guides AI coding assistants to:

        1. **Organize Understanding by Abstraction**: Root principles → patterns → implementations
        2. **Reduce Complexity**: Continuously simplify and abstract rather than build outward
        3. **Structure Knowledge**: Use the 0-8 numbered files to maintain clear abstraction boundaries
        4. **Extract Patterns**: Identify commonalities across features, functions, or design elements
        5. **Maintain Root Fidelity**: Ensure every decision connects back to the core project purpose

        When working with the AI assistant, you should:

        1. First establish the project's root purpose in files 0-1
        2. Define external context in files 2-4
        3. Map current and target structure in file 5
        4. Track active development in files 6-8

        The assistant will now approach all tasks with:
        - A bias toward abstraction over detail
        - Pattern extraction over specific instances
        - Structural reasoning over narrative explanation
        - Root-anchored decisions over feature-driven ones

        This ensures your project develops with a clear cognitive architecture that minimizes entropy and maximizes clarity.

        ## Advanced Usage

        For collaborative projects, share these files in a `/memory-bank` directory at your project root. Each team member should begin work sessions by:

        1. Reading `0-distilledContext.md` to re-anchor to project essence
        2. Reviewing `6-activeContext.md` to understand current focus
        3. Working on tasks from `8-tasks.md`
        4. Updating the Memory Bank with pattern discoveries

        For more detailed instructions, refer to the complete Memory Bank implementation guide.
    ```

    ---

    #### `07-guides\14-memory-bank-web-project-template.md`

    ```markdown
        # Project-Specific Memory Bank Template

        > **[Structural Role Reminder]**: This template adapts the Memory Bank system to a specific web project, maintaining root fidelity while addressing project-specific needs.

        ## ðŸ“– Table of Contents
        1. [Root-First Assimilation Philosophy](#root-first-assimilation-philosophy)
        2. [Canonical Project Structure](#canonical-project-structure)
        3. [Memory Bank Design](#memory-bank-design)
        4. [Assimilation Workflows](#assimilation-workflows)
        5. [Project-Specific Guardrails](#project-specific-guardrails)
        6. [High-Impact Simplification Protocol](#high-impact-simplification-protocol)
        7. [Final Mandate](#final-mandate)

        ## Root-First Assimilation Philosophy

        I am an expert software engineer specializing in web development. My cognition resets between sessions.
        I operate **exclusively** by reconstructing project context from its rigorously maintained **Memory Bank**. The primary goal is architectural clarity, component consolidation, and establishing a maintainable feature-first structure.

        ### Core Imperatives for Web Projects:

        - **Domain Primacy**: All decisions must enhance the project's core domain and purpose
        - **Framework Purity**: Maintain framework best practices with strong typing enforcement
        - **CSS Strategy Integrity**: Preserve consistent styling approach (utility-first, modules, etc.)
        - **Performance Covenant**: Never compromise core vitals for features

        ### Cleanup Absolutes:
        1. File structure must mirror business domains
        2. Component duplication is existential technical debt
        3. Data sources have clear, consistent patterns
        4. Responsive design is non-negotiable

        ## Canonical Project Structure

        ```plaintext
        ProjectName/
        â”œâ”€â”€ 01_config/               # Project configs (framework, styling, linting)
        â”œâ”€â”€ 02_src/
        â”‚   â”œâ”€â”€ 01_pages/             # Page orchestration
        â”‚   â”œâ”€â”€ 02_layout/            # Global page structures
        â”‚   â”œâ”€â”€ 03_features/          # Feature-driven modules
        â”‚   â”œâ”€â”€ 04_shared/            # Cross-feature logic/hooks
        â”‚   â”œâ”€â”€ 05_ui/                # Context-free UI primitives
        â”‚   â””â”€â”€ 06_assets/            # Static images, media
        â”œâ”€â”€ memory-bank/
        â”‚   â”œâ”€â”€ 0-distilledContext.md
        â”‚   â”œâ”€â”€ 1-projectbrief.md
        â”‚   â”œâ”€â”€ 2-productContext.md
        â”‚   â”œâ”€â”€ 3-systemPatterns.md
        â”‚   â”œâ”€â”€ 4-techContext.md
        â”‚   â”œâ”€â”€ 5-structureMap.md
        â”‚   â”œâ”€â”€ 6-activeContext.md
        â”‚   â”œâ”€â”€ 7-progress.md
        â”‚   â”œâ”€â”€ 8-tasks.md
        â”‚   â””â”€â”€ lineage/              # (Optional) Structural evolution snapshots
        â”œâ”€â”€ 03_scripts/              # Utility scripts
        â”œâ”€â”€ 04_public/               # Static public assets
        â”œâ”€â”€ package.json
        â””â”€â”€ README.md
        ```

        âœ… Numbered, predictable.
        âœ… Feature-first modular architecture.
        âœ… `/memory-bank/` as cognitive heartbeat.

        ## Memory Bank Design

        ### Project-Specific Files

        | File | Project-Specific Focus |
        |------|---------------|
        | `0-distilledContext.md` | Project essence: Purpose, target users, primary functionality in 2-3 bullets |
        | `1-projectbrief.md` | Project mission, scope, core value proposition, constraints (Technical/Business/UX) |
        | `2-productContext.md` | Problems solved, target users, user journey, value connections |
        | `3-systemPatterns.md` | Current & Target structure, component hierarchy, state flow, data flow, routing |
        | `4-techContext.md` | Framework, styling approach, key constraints (Performance, Accessibility, Responsive), build process, essential libraries |
        | `5-structureMap.md` | Current file structure vs. target structure with clear mapping and migration path |
        | `6-activeContext.md` | Current focus, component analysis, refactoring decisions, key findings on implementation |
        | `7-progress.md` | Status log, milestones, metrics tracking, known issues blocking refactoring |
        | `8-tasks.md` | Concrete tasks linked to target structure, validation criteria |

        ### Project-Specific Expansion Rules

        > New files (e.g., `9-apiStrategy.md`, `10-contentStructure.md`) are allowed **only** if they **clarify**, **simplify**, and **reinforce** the project's abstraction hierarchy â€” and must be **explicitly justified** within `5-structureMap.md` or `6-activeContext.md` based on reducing complexity or improving clarity.

        ## Assimilation Workflows

        ### Phase 1: Project Structure Validation

        ```mermaid
        flowchart TD
            A[Validate Feature Boundaries] --> B[Audit Component Duplication]
            B --> C[Map Data Flows]
            C --> D[Verify Implementation Patterns]
        ```

        ### Phase 2: High-Impact Cleaning

        ```mermaid
        flowchart TD
            A[Consolidate UI Primitives] --> B[Refactor State Management]
            B --> C[Optimize Asset Loading]
            C --> D[Enforce Type Contracts]
        ```

        ### Phase 3: Structure-Based Development

        ```mermaid
        flowchart TD
            Start[Start Task from 8-tasks.md] --> CheckMemoryBank[Check Memory Bank]
            CheckMemoryBank --> ExecuteTask[Execute Task]
            ExecuteTask --> AnalyzeImpact[Analyze Impact]
            AnalyzeImpact --> DocumentUpdates[Document Updates]
        ```

        | Phase | Focus |
        | :---- | :---- |
        | Start Task | Select task directly linked to a structure goal |
        | Check Memory Bank | Review relevant context from memory bank files |
        | Execute Task | Implement with minimal disruption, following established patterns |
        | Analyze Impact | Assess how changes affect structure, types, and other components |
        | Document Updates | Update relevant memory bank files with new insights and progress |

        ## Project-Specific Guardrails

        ### Component Standards Template

        ```typescript
        // All components must adhere to:
        interface ComponentSpec {
          domain: 'feature1' | 'feature2' | /* other domains */;
          type: 'primitive' | 'feature' | 'layout';
          props: TypedInterface;
          hooks: 'none' | 'domain' | 'global';
          styling: 'defined-approach';
          tests: 'required' | 'none';
        }
        ```

        ### Forbidden Patterns

        - Anti-patterns specific to framework (e.g., Class components in React)
        - Prop drilling beyond 3 levels
        - Untyped functions/components
        - Inline SVG elements
        - Direct DOM manipulation
        - Inconsistent styling approaches

        ### Approved Complexity Reduction Paths

        1. Convert duplicated components to compound patterns
        2. Extract domain-specific hooks from UI
        3. Collapse similar layout components
        4. Implement shared data fetching strategies
        5. Standardize form handling

        ## High-Impact Simplification Protocol

        Every major assimilation cycle for the project must seek **one** **High-Impact Simplification**:

        - **Identify Opportunity**: Find a minimal intervention yielding maximum clarity, component reduction, or improved maintainability
        - **Validate Alignment**: Must align strictly with project root abstraction and target architecture (`3-systemPatterns.md`)
        - **Document Proposal & Impact**:
          - `5-structureMap.md` or `6-activeContext.md`: Rationale for the simplification.
          - `7-progress.md`: Expected impact (e.g., "Reduces N duplicated components", "Simplifies content updates").
          - `8-tasks.md`: Concrete task(s) to implement the simplification.

        ## Final Mandate

        ### Project Validation Checklist

        - [ ] All components trace to business domain
        - [ ] No duplicate UI primitives
        - [ ] Asset system uses optimization strategy
        - [ ] State management follows consistent patterns
        - [ ] Type strictness enforced
        - [ ] Memory Bank updated pre-commit

        Before touching **any code** or **updating the Memory Bank**:

        - **Validate the Root**: Re-read `1-projectbrief.md` (or `0-distilledContext.md`). Is the core mission clear?
        - **Confirm Structural Alignment**: Does the proposed change or documentation fit logically within the `memory-bank/` structure and the target architecture (`3-systemPatterns.md`)?
        - **Proceed Only If**: The action **reinforces** the core mission, **simplifies** the codebase complexity, or **clarifies** the architecture in service of that mission.

        > **One Purpose (Project Mission). One Structure (Memory Bank). Infinite Adaptability (Clean Code).**
    ```

# DIRECT EXAMPLE OF A TYPICAL -CHAOTIC- CODEBASE (THE START OF ANY PROJECT)
    ```
    ├── .cursorignore
    ├── .dependency-cruiser.cjs
    ├── .gitignore
    ├── .gitkeep
    ├── README.md
    ├── capture-website.js
    ├── cleanup-duplicates.bat
    ├── depcruise-config.cjs
    ├── dependency-graph.svg [-]
    ├── eslint.config.js
    ├── index.html
    ├── package-lock.json
    ├── package.json
    ├── package.json.bak
    ├── postcss.config.js
    ├── project.md
    ├── reorganize-scripts.bat
    ├── rl-website-initial-notes.md
    ├── scripts-reorganization-summary.md
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── .depcruise
    │   ├── data
    │   │   ├── d3-data.json
    │   │   └── dependency-data.json
    │   ├── graphs
    │   │   ├── circular-graph.svg [-]
    │   │   ├── clustered-graph.svg [-]
    │   │   ├── dependency-graph.svg [-]
    │   │   ├── hierarchical-graph.svg [-]
    │   │   └── tech-filtered.svg [-]
    │   └── interactive
    │       ├── archi-interactive.html
    │       ├── bubble-chart.html
    │       ├── circle-packing.html
    │       ├── d3-graph.html
    │       ├── dependency-graph.html
    │       ├── flow-diagram.html
    │       ├── high-level-dependencies.html
    │       └── validation.html
    ├── docs
    │   └── dependency-visualization.md
    ├── public
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── images
    │       ├── metadata.json
    │       ├── categorized
    │       │   ├── hero-prosjekter.HEIC [-]
    │       │   ├── belegg
    │       │   │   ├── IMG_0035.webp
    │       │   │   ├── IMG_0085.webp
    │       │   │   ├── IMG_0121.webp
    │       │   │   ├── IMG_0129.webp
    │       │   │   ├── IMG_0208.webp
    │       │   │   ├── IMG_0451.webp
    │       │   │   ├── IMG_0453.webp
    │       │   │   ├── IMG_0715.webp
    │       │   │   ├── IMG_0717.webp
    │       │   │   ├── IMG_1935.webp
    │       │   │   ├── IMG_2941.webp
    │       │   │   ├── IMG_3001.webp
    │       │   │   ├── IMG_3021.webp
    │       │   │   ├── IMG_3023.webp
    │       │   │   ├── IMG_3033.webp
    │       │   │   ├── IMG_3034.webp
    │       │   │   ├── IMG_3035.webp
    │       │   │   ├── IMG_3036.webp
    │       │   │   ├── IMG_3037.webp
    │       │   │   ├── IMG_3084.webp
    │       │   │   ├── IMG_3133.webp
    │       │   │   ├── IMG_4080.webp
    │       │   │   ├── IMG_4305.webp
    │       │   │   ├── IMG_4547.webp
    │       │   │   ├── IMG_4586.webp
    │       │   │   ├── IMG_4644.webp
    │       │   │   ├── IMG_4996.webp
    │       │   │   ├── IMG_4997.webp
    │       │   │   ├── IMG_5278.webp
    │       │   │   ├── IMG_5279.webp
    │       │   │   └── IMG_5280.webp
    │       │   ├── ferdigplen
    │       │   │   ├── IMG_0071.webp
    │       │   │   └── IMG_1912.webp
    │       │   ├── hekk
    │       │   │   ├── IMG_0167.webp
    │       │   │   ├── IMG_1841.webp
    │       │   │   ├── IMG_2370.webp
    │       │   │   ├── IMG_2371.webp
    │       │   │   ├── IMG_3077.webp
    │       │   │   └── hekk_20.webp
    │       │   ├── kantstein
    │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
    │       │   │   ├── IMG_0066.webp
    │       │   │   ├── IMG_0364.webp
    │       │   │   ├── IMG_0369.webp
    │       │   │   ├── IMG_0427.webp
    │       │   │   ├── IMG_0429.webp
    │       │   │   ├── IMG_0445.webp
    │       │   │   ├── IMG_0716.webp
    │       │   │   ├── IMG_2955.webp
    │       │   │   ├── IMG_4683.webp
    │       │   │   └── IMG_4991.webp
    │       │   ├── platting
    │       │   │   ├── IMG_3251.webp
    │       │   │   └── IMG_4188.webp
    │       │   ├── stål
    │       │   │   ├── IMG_0068.webp
    │       │   │   ├── IMG_0069.webp
    │       │   │   ├── IMG_1916.webp
    │       │   │   ├── IMG_1917.webp
    │       │   │   ├── IMG_1918.webp
    │       │   │   ├── IMG_2441.webp
    │       │   │   ├── IMG_3599.webp
    │       │   │   ├── IMG_3602.webp
    │       │   │   ├── IMG_3829.webp
    │       │   │   ├── IMG_3832.webp
    │       │   │   ├── IMG_3844.webp
    │       │   │   ├── IMG_3845.webp
    │       │   │   ├── IMG_3847.webp
    │       │   │   ├── IMG_3848.webp
    │       │   │   ├── IMG_3966.webp
    │       │   │   ├── IMG_3969.webp
    │       │   │   ├── IMG_4030.webp
    │       │   │   ├── IMG_4083.webp
    │       │   │   ├── IMG_4086.webp
    │       │   │   ├── IMG_4536.webp
    │       │   │   └── IMG_5346.webp
    │       │   ├── støttemur
    │       │   │   ├── IMG_0144.webp
    │       │   │   ├── IMG_0318.webp
    │       │   │   ├── IMG_0324.webp
    │       │   │   ├── IMG_0325.webp
    │       │   │   ├── IMG_0452.webp
    │       │   │   ├── IMG_0932.webp
    │       │   │   ├── IMG_0985.webp
    │       │   │   ├── IMG_0986.webp
    │       │   │   ├── IMG_0987.webp
    │       │   │   ├── IMG_1132.webp
    │       │   │   ├── IMG_1134.webp
    │       │   │   ├── IMG_1140.webp
    │       │   │   ├── IMG_2032.webp
    │       │   │   ├── IMG_2083.webp
    │       │   │   ├── IMG_2274.webp
    │       │   │   ├── IMG_2522.webp
    │       │   │   ├── IMG_2523.webp
    │       │   │   ├── IMG_2855(1).webp
    │       │   │   ├── IMG_2855.webp
    │       │   │   ├── IMG_2859.webp
    │       │   │   ├── IMG_2861.webp
    │       │   │   ├── IMG_2891.webp
    │       │   │   ├── IMG_2920.webp
    │       │   │   ├── IMG_2921.webp
    │       │   │   ├── IMG_2951.webp
    │       │   │   ├── IMG_3007.webp
    │       │   │   ├── IMG_3151.webp
    │       │   │   ├── IMG_3269.webp
    │       │   │   ├── IMG_3271.webp
    │       │   │   ├── IMG_3369.webp
    │       │   │   ├── IMG_4090.webp
    │       │   │   ├── IMG_4150.webp
    │       │   │   ├── IMG_4151.webp
    │       │   │   ├── IMG_4153.webp
    │       │   │   └── IMG_4154.webp
    │       │   └── trapp-repo
    │       │       ├── IMG_0295.webp
    │       │       ├── IMG_0401.webp
    │       │       ├── IMG_0448.webp
    │       │       ├── IMG_0449.webp
    │       │       ├── IMG_0450.webp
    │       │       ├── IMG_1081.webp
    │       │       ├── IMG_1735.webp
    │       │       ├── IMG_1782.webp
    │       │       ├── IMG_2095.webp
    │       │       ├── IMG_2097.webp
    │       │       ├── IMG_2807.webp
    │       │       ├── IMG_3086.webp
    │       │       ├── IMG_3132.webp
    │       │       ├── IMG_3838.webp
    │       │       ├── IMG_3939.webp
    │       │       ├── IMG_4111.webp
    │       │       ├── IMG_4516.webp
    │       │       ├── IMG_4551.webp
    │       │       ├── IMG_5317.webp
    │       │       └── image4.webp
    │       ├── site
    │       │   ├── hero-corten-steel.webp
    │       │   ├── hero-granite.webp
    │       │   ├── hero-grass.webp
    │       │   ├── hero-grass2.webp
    │       │   ├── hero-illustrative.webp
    │       │   ├── hero-main.webp
    │       │   ├── hero-prosjekter.webp
    │       │   └── hero-ringerike.webp
    │       └── team
    │           ├── firma.webp
    │           ├── jan.webp
    │           └── kim.webp
    ├── screenshots
    │   ├── .gitignore
    │   ├── .gitkeep
    │   ├── README.md
    │   ├── screenshot-report.html
    │   ├── 2025-04-19_15-18-45
    │   │   ├── desktop
    │   │   │   ├── about-desktop.html
    │   │   │   ├── about-desktop.png [-]
    │   │   │   ├── contact-desktop.html
    │   │   │   ├── contact-desktop.png [-]
    │   │   │   ├── home-desktop.html
    │   │   │   ├── home-desktop.png [-]
    │   │   │   ├── projects-desktop.html
    │   │   │   ├── projects-desktop.png [-]
    │   │   │   ├── services-desktop.html
    │   │   │   └── services-desktop.png [-]
    │   │   ├── mobile
    │   │   │   ├── about-mobile.html
    │   │   │   ├── about-mobile.png [-]
    │   │   │   ├── contact-mobile.html
    │   │   │   ├── contact-mobile.png [-]
    │   │   │   ├── home-mobile.html
    │   │   │   ├── home-mobile.png [-]
    │   │   │   ├── projects-mobile.html
    │   │   │   ├── projects-mobile.png [-]
    │   │   │   ├── services-mobile.html
    │   │   │   └── services-mobile.png [-]
    │   │   └── tablet
    │   │       ├── about-tablet.html
    │   │       ├── about-tablet.png [-]
    │   │       ├── contact-tablet.html
    │   │       ├── contact-tablet.png [-]
    │   │       ├── home-tablet.html
    │   │       ├── home-tablet.png [-]
    │   │       ├── projects-tablet.html
    │   │       ├── projects-tablet.png [-]
    │   │       ├── services-tablet.html
    │   │       └── services-tablet.png [-]
    │   └── latest
    │       ├── .gitkeep
    │       ├── desktop
    │       │   ├── about-desktop.html
    │       │   ├── about-desktop.png [-]
    │       │   ├── contact-desktop.html
    │       │   ├── contact-desktop.png [-]
    │       │   ├── home-desktop.html
    │       │   ├── home-desktop.png [-]
    │       │   ├── projects-desktop.html
    │       │   ├── projects-desktop.png [-]
    │       │   ├── services-desktop.html
    │       │   └── services-desktop.png [-]
    │       ├── mobile
    │       │   ├── about-mobile.html
    │       │   ├── about-mobile.png [-]
    │       │   ├── contact-mobile.html
    │       │   ├── contact-mobile.png [-]
    │       │   ├── home-mobile.html
    │       │   ├── home-mobile.png [-]
    │       │   ├── projects-mobile.html
    │       │   ├── projects-mobile.png [-]
    │       │   ├── services-mobile.html
    │       │   └── services-mobile.png [-]
    │       └── tablet
    │           ├── about-tablet.html
    │           ├── about-tablet.png [-]
    │           ├── contact-tablet.html
    │           ├── contact-tablet.png [-]
    │           ├── home-tablet.html
    │           ├── home-tablet.png [-]
    │           ├── projects-tablet.html
    │           ├── projects-tablet.png [-]
    │           ├── services-tablet.html
    │           └── services-tablet.png [-]
    ├── scripts
    │   ├── README.md
    │   ├── run-capture.bat
    │   ├── dependencies
    │   │   ├── README.md
    │   │   ├── dependency-manager.js
    │   │   ├── visualize
    │   │   ├── analyze
    │   │   │   └── check-dependencies.js
    │   │   └── utils
    │   │       ├── check-graphviz.js
    │   │       ├── cleanup-directory.js
    │   │       ├── cleanup-redundant-files.js
    │   │       ├── fix-missing-files.js
    │   │       └── run-visualizations.js
    │   ├── dev
    │   │   ├── README.md
    │   │   └── dev-with-snapshots.js
    │   ├── screenshots
    │   │   ├── README.md
    │   │   ├── capture
    │   │   └── manage
    │   └── utils
    │       ├── README.md
    │       └── cleanup.js
    └── src
        ├── App.tsx
        ├── index.css
        ├── index.html
        ├── main.tsx
        ├── vite-env.d.ts
        ├── _consolidated
        │   └── public
        │       └── images
        │           └── categorized
        │               └── belegg
        │                   └── IMG_3037.webp.svg [-]
        ├── components
        │   ├── layout
        │   │   ├── Footer.tsx
        │   │   ├── Header.tsx
        │   │   ├── Meta.tsx
        │   │   └── Navbar.tsx
        │   ├── projects
        │   │   └── ProjectGallery.tsx
        │   ├── seo
        │   │   └── TestimonialsSchema.tsx
        │   ├── shared
        │   │   ├── Elements
        │   │   │   ├── Card.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Image.tsx
        │   │   │   ├── Link.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   └── Form
        │   │   │       ├── Input.tsx
        │   │   │       ├── Select.tsx
        │   │   │       └── Textarea.tsx
        │   │   └── Layout
        │   │       └── Layout.tsx
        │   ├── testimonials
        │   │   └── index.tsx
        │   └── ui
        │       ├── Button.tsx
        │       ├── Container.tsx
        │       ├── Hero.tsx
        │       ├── Intersection.tsx
        │       ├── Logo.tsx
        │       ├── Notifications.tsx
        │       ├── SeasonalCTA.tsx
        │       ├── ServiceAreaList.tsx
        │       ├── Skeleton.tsx
        │       ├── Transition.tsx
        │       └── index.ts
        ├── content
        │   ├── index.ts
        │   ├── services
        │   │   └── index.ts
        │   ├── team
        │   │   └── index.ts
        │   └── testimonials
        │       └── index.ts
        ├── data
        │   ├── projects.ts
        │   ├── services.ts
        │   └── testimonials.ts
        ├── docs
        │   └── SEO_USAGE.md
        ├── features
        │   ├── testimonials.tsx
        │   ├── home
        │   │   ├── FilteredServicesSection.tsx
        │   │   └── SeasonalProjectsCarousel.tsx
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGrid.tsx
        │   │   └── ProjectsCarousel.tsx
        │   ├── services
        │   │   ├── ServiceCard.tsx
        │   │   ├── ServiceFeature.tsx
        │   │   ├── ServiceGrid.tsx
        │   │   └── data.ts
        │   └── testimonials
        │       ├── AverageRating.tsx
        │       ├── Testimonial.tsx
        │       ├── TestimonialFilter.tsx
        │       ├── TestimonialSlider.tsx
        │       ├── TestimonialsSection.tsx
        │       └── data.ts
        ├── hooks
        │   └── useData.ts
        ├── lib
        │   ├── constants.ts
        │   ├── hooks.ts
        │   ├── utils.ts
        │   ├── api
        │   │   └── index.ts
        │   ├── config
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── paths.ts
        │   │   └── site.ts
        │   ├── context
        │   │   └── AppContext.tsx
        │   ├── hooks
        │   │   ├── useAnalytics.ts
        │   │   ├── useEventListener.ts
        │   │   └── useMediaQuery.ts
        │   ├── types
        │   │   └── index.ts
        │   └── utils
        │       ├── analytics.ts
        │       ├── dom.ts
        │       ├── formatting.ts
        │       ├── images.ts
        │       ├── index.ts
        │       ├── seasonal.ts
        │       ├── seo.ts
        │       └── validation.ts
        ├── pages
        │   ├── ProjectDetail.tsx
        │   ├── Projects.tsx
        │   ├── ServiceDetail.tsx
        │   ├── Services.tsx
        │   ├── TestimonialsPage.tsx
        │   ├── about
        │   │   └── index.tsx
        │   ├── contact
        │   │   └── index.tsx
        │   ├── home
        │   │   └── index.tsx
        │   ├── services
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   └── testimonials
        │       └── index.tsx
        ├── styles
        │   ├── animations.css
        │   ├── base.css
        │   └── utilities.css
        ├── types
        │   ├── content.ts
        │   └── index.ts
        └── utils
            └── imageLoader.ts
    ```
