# 3. System Patterns - Ringerike Landskap Website

## Architecture Overview

The Ringerike Landskap website implements a layered architecture built on React that emphasizes content adaptation, component reusability, and clear separation of concerns. The core system follows a Component-First design pattern with specialized service layers handling cross-cutting concerns.

```mermaid
flowchart TD
    User[User/Client] --> ReactApp[React Application]
    ReactApp --> Pages[Page Components]
    Pages --> Features[Feature Components]
    Features --> UI[UI Components]
    
    ReactApp --> SS[Seasonal System]
    ReactApp --> GL[Geographic Layer]
    ReactApp --> CD[Content Discovery]
    
    SS --> FeatureAdaptation[Feature Adaptation]
    GL --> LocationFiltering[Location Filtering]
    CD --> ProjectData[Project Data]
    CD --> ServicesData[Services Data]
    
    ProjectData --> SeasonalProjects[Seasonal Projects]
    ServicesData --> SeasonalServices[Seasonal Services]
```

## Core Architectural Patterns

### 1. Seasonal Content Adaptation Pattern

This pattern dynamically modifies content based on the current season, ensuring relevant services and projects are highlighted.

```mermaid
flowchart LR
    Season[Season Detection] --> |Determines current season| Adaptation[Content Adaptation]
    Adaptation --> |Filters| Projects[Projects Repository]
    Adaptation --> |Filters| Services[Services Repository]
    Adaptation --> |Modifies| CTA[Call-to-Actions]
    Projects --> ProjectCarousel[Seasonal Project Carousel]
    Services --> ServiceList[Filtered Services Section]
    CTA --> SeasonalCTA[Seasonal CTA Component]
```

**Implementation:**
- `getCurrentSeason()` function determines season based on current month
- Season-specific content is loaded dynamically
- Components adapt their presentation based on season context
- Project and service priorities shift automatically with seasons

### 2. Geographic Authenticity Layer

This pattern integrates location-specific content throughout the site, ensuring geographic relevance.

```mermaid
flowchart TD
    GeoLayer[Geographic Layer] --> ServiceAreas[Service Areas]
    GeoLayer --> ProjectLocations[Project Locations]
    GeoLayer --> SEO[SEO Location Tags]
    
    ServiceAreas --> AreaList[Service Area List Component]
    ServiceAreas --> DistanceCalculation[Distance Calculation]
    
    ProjectLocations --> LocationFiltering[Location-based Filtering]
    ProjectLocations --> MapIntegration[Map Integration]
    
    SEO --> MetaTags[Meta Tags]
    SEO --> StructuredData[Structured Data]
```

**Implementation:**
- Service areas defined with precise metadata (distance, features)
- Projects tagged with location information
- Content optimized for local SEO with location-specific keywords
- Components can filter and sort based on geographic relevance

### 3. JSON-Driven Architecture Pattern

This pattern centralizes configuration and content in JSON files, separate from UI components.

```mermaid
flowchart TD
    JSON[JSON Files] --> Config[Site Configuration]
    JSON --> Projects[Project Definitions]
    JSON --> |Future| Services[Services Definitions]
    
    Config --> SiteSettings[Site Settings]
    Projects --> SeasonalDir[Seasonal Directories]
    
    SeasonalDir --> Vaar[Spring Projects]
    SeasonalDir --> Sommer[Summer Projects]
    SeasonalDir --> Hoest[Fall Projects]
    SeasonalDir --> Vinter[Winter Projects]
    
    DynamicLoader[Dynamic Content Loader] --> JSONFiles[JSON Files]
    DynamicLoader --> Images[Project Images]
    
    JSONFiles --> ContentAPI[Content API Layer]
    ContentAPI --> ReactComponents[React Components]
```

**Implementation:**
- Content stored in season-specific directories (`vaar`, `sommer`, `hoest`, `vinter`)
- JSON files contain metadata for projects, services, and site configuration
- Dynamic loading system discovers and incorporates content automatically
- Components consume a consistent API layer, abstracting the data source

### 4. Component Hierarchy Pattern

This pattern establishes a clear component hierarchy that promotes reusability and separation of concerns.

```mermaid
flowchart TD
    UI[UI Components] --> |Used by| Feature[Feature Components]
    Feature --> |Used by| Page[Page Components]
    
    UI --> Button[Button]
    UI --> Container[Container]
    UI --> Hero[Hero]
    UI --> Logo[Logo]
    
    Feature --> ProjectCard[Project Card]
    Feature --> ServiceCard[Service Card]
    Feature --> Testimonial[Testimonial]
    Feature --> SeasonalProjects[Seasonal Projects]
    
    Page --> Home[Home Page]
    Page --> Services[Services Page]
    Page --> Projects[Projects Page]
    Page --> About[About Page]
    Page --> Contact[Contact Page]
```

**Implementation:**
- UI components are purely presentational with no business logic
- Feature components encapsulate domain-specific functionality
- Page components compose features into complete user interfaces
- Props flow downward, events bubble upward
- Higher-order components used for cross-cutting concerns

## Data Flow Patterns

### Content Discovery and Loading Flow

```mermaid
flowchart LR
    Build[Build Process] --> Discovery[Content Discovery]
    Discovery --> |import.meta.glob| JSONFiles[JSON Files]
    Discovery --> |import.meta.glob| Images[Image Files]
    
    JSONFiles --> DataNormalization[Data Normalization]
    Images --> ImageProcessing[Image Processing]
    
    DataNormalization --> ContentAPI[Content API Layer]
    ImageProcessing --> ImageRegistry[Image Registry]
    
    ContentAPI --> Components[React Components]
    ImageRegistry --> Components
```

### User Interaction Flow

```mermaid
flowchart TD
    UserVisit[User Visits Site] --> SeasonDetection[Season Detection]
    UserVisit --> LocationDetection[Location Context]
    
    SeasonDetection --> ContentSelection[Content Selection]
    LocationDetection --> ContentSelection
    
    ContentSelection --> RelevantProjects[Relevant Projects]
    ContentSelection --> RelevantServices[Relevant Services]
    ContentSelection --> AppropriateContent[Appropriate CTAs]
    
    RelevantProjects --> UserEngagement[User Engagement]
    RelevantServices --> UserEngagement
    AppropriateContent --> UserEngagement
    
    UserEngagement --> Conversion[Conversion/Contact]
```

## System Integration Points

1. **Season Integration Points:**
   - `getCurrentSeason()` utility function
   - Seasonal project directories
   - Season-filtered component views

2. **Geographic Integration Points:**
   - Service area definitions
   - Location-tagged projects
   - Geographic filtering components

3. **Content Integration Points:**
   - JSON file structure
   - Dynamic content loading system
   - Image discovery and processing

4. **UI Integration Points:**
   - Component composition patterns
   - Prop interfaces between component layers
   - Common styling system

## Architectural Transitions

The codebase is currently transitioning from hardcoded data to the JSON-driven architecture:

1. **Completed Transitions:**
   - Project structure to seasonal JSON files
   - Site configuration to centralized JSON

2. **In-Progress Transitions:**
   - Services data to JSON structure
   - Testimonials to JSON structure
   - Legacy compatibility layer for gradual adoption

## Performance Optimization Patterns

1. **Image Optimization:**
   - WebP format for modern browsers
   - Responsive image loading
   - Lazy loading for off-screen content

2. **Component Optimization:**
   - Memoization of pure components
   - Conditional rendering for seasonal content
   - Code splitting for page components

3. **Data Optimization:**
   - Pre-filtered data based on season
   - Caching of processed JSON data
   - Progressive loading of content
