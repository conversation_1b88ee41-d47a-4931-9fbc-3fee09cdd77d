the following shows some example commands to generate diagrams from codebase using `depencency-cruiser` and `graphviz`:

    "depcruise:html": "depcruise --include-only \"^src\" --output-type html src > .depcruise/interactive/dependency-graph.html",
    "depcruise:svg": "depcruise --include-only \"^src\" --output-type dot src | dot -T svg > .depcruise/graphs/dependency-graph.svg",
    "depcruise:archi": "depcruise --version && depcruise --config depcruise-config.cjs --output-type archi src | dot -T svg | depcruise-wrap-stream-in-html > .depcruise/interactive/high-level-dependencies.html",
    "depcruise:archi-interactive": "depcruise --config depcruise-config.cjs --output-type dot src | dot -T svg | depcruise-wrap-stream-in-html > .depcruise/interactive/archi-interactive.html",
    "depcruise:json": "depcruise --include-only \"^src\" --output-type json src > .depcruise/data/dependency-data.json",
    "depcruise:interactive": "depcruise --include-only \"^src\" --output-type err-html src > .depcruise/interactive/validation.html",
    "depcruise:advanced-dot": "depcruise --include-only \"^src\" --output-type dot src | dot -Gdpi=300 -Gsize=\"15,15\" -Goverlap=scale -Gsplines=polyline -Nfontname=Helvetica -Efontname=Helvetica -T svg > .depcruise/graphs/enhanced-graph.svg",
    "depcruise:hierarchical": "depcruise --include-only \"^src\" --output-type dot src | dot -Grankdir=TB -Granksep=2.0 -Gnodesep=0.8 -Gsplines=ortho -T svg > .depcruise/graphs/hierarchical-graph.svg",
    "depcruise:circular": "depcruise --include-only \"^src\" --output-type dot src | circo -Gsplines=curved -Goverlap=scale -Gconcentrate=true -T svg > .depcruise/graphs/circular-graph.svg",
    "depcruise:focus": "depcruise --focus \"components/.*\" --highlight \"features/.*\" --output-type dot src | neato -Gmodel=subset -Goverlap=prism -Gsplines=true -T svg > .depcruise/graphs/focused-graph.svg",
    "depcruise:clustered": "depcruise --include-only \"^src\" --collapse \"^src/[^/]+\" --output-type dot src | fdp -Gpack=true -Gmodel=shortpath -GK=2 -Gmaxiter=1000 -T svg > .depcruise/graphs/clustered-graph.svg",
    "depcruise:interactive-svg": "depcruise --include-only \"^src\" --output-type dot src | dot -Gcmapx=true -Gsplines=true -T svg > .depcruise/graphs/interactive-graph.svg",
    "depcruise:multi": "depcruise --config depcruise-config.cjs --output-type dot src | dot -Tsvg > .depcruise/graphs/graph.svg && depcruise --config depcruise-config.cjs --output-type dot src | dot -Tpdf > .depcruise/graphs/graph.pdf && depcruise --config depcruise-config.cjs --output-type dot src | dot -Tpng -Gdpi=300 > .depcruise/graphs/graph.png",
    "depcruise:archi-advanced": "depcruise --config depcruise-config.cjs --output-type archi src | twopi -Gfontsize=12 -Nfontsize=10 -Gsize=\"10,10\" -Gbgcolor=\"#f5f5f5\" -Gcolor=\"#333333\" -T svg > .depcruise/graphs/radial-architecture.svg",
    "depcruise:all": "npm run depcruise:html && npm run depcruise:svg && npm run depcruise:archi && npm run depcruise:archi-interactive && npm run depcruise:json && npm run depcruise:interactive && npm run depcruise:hierarchical && npm run depcruise:circular && npm run depcruise:clustered && npm run depcruise:fix-paths",
    "depcruise:clean": "powershell -Command \"Remove-Item -Path .depcruise -Recurse -Force -ErrorAction SilentlyContinue; New-Item -Path .depcruise -ItemType Directory; New-Item -Path .depcruise\\graphs -ItemType Directory; New-Item -Path .depcruise\\interactive -ItemType Directory; New-Item -Path .depcruise\\data -ItemType Directory\"",
    "depcruise:fix-paths": "node scripts/fix-depcruise-paths.js",
    "depcruise:dashboard": "npm run depcruise:fix-paths && node scripts/create-dependency-dashboard.js && powershell -Command \"Start-Process (Join-Path $PWD '.depcruise/index.html')\"",
    "depcruise:open": "powershell -Command \"Start-Process (Join-Path $PWD '.depcruise/interactive/dependency-graph.html')\""


please write some more powerful examples that utilize a more proper and interactive format of the outputs
