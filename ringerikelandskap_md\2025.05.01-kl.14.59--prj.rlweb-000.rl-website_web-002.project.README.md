# Ringerike Landskap Website

This is the official website for Ringerike Landskap, a landscaping company based in Norway.

## Project Structure

The project is organized with a clear separation between development and production environments:

```
project/
├── config/               # Configuration files
│   ├── env/              # Environment-specific configuration
│   └── ...               # Other configuration files
├── tools/                # Development tools
│   ├── depcruise/        # Dependency visualization
│   ├── screenshots/      # Screenshot tools
│   └── www/              # Website deployment tools
├── website/              # Website code (development)
│   ├── public/           # Static assets
│   └── src/              # Source code
├── dist/                 # Build output (temporary)
└── www/                  # Production website
    ├── assets/           # Production assets
    ├── css/              # Production CSS
    ├── js/               # Production JavaScript
    └── index.html        # Production entry point
```

## Development

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)

### Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`

### Available Scripts

#### Development

- `npm run dev` - Start the development server
- `npm run lint` - Lint the code
- `npm run build` - Build the website for development

#### Production

- `npm run build:staging` - Build the website for staging
- `npm run build:production` - Build the website for production
- `npm run preview:staging` - Preview the staging build
- `npm run preview:production` - Preview the production build

#### Deployment

- `npm run www:init` - Initialize the www directory
- `npm run www:deploy:staging` - Deploy to staging
- `npm run www:deploy:production` - Deploy to production

## Dependency Visualization

This project includes a comprehensive dependency visualization system to help you understand the codebase structure and relationships between modules.

### Quick Commands

- `npm run deps` - Show available dependency commands
- `npm run deps:flow` - Generate and open the flow diagram (hierarchical visualization)
- `npm run deps:d3` - Generate and open the D3 interactive graph
- `npm run deps:all` - Generate all visualizations and open the dashboard
- `npm run deps:audit` - Run a complete dependency audit with analysis
- `npm run deps:analyze` - Analyze dependencies without generating visuals

### Prerequisites

The visualization system requires:

- Node.js and npm (included in the project setup)
- Graphviz (optional, for SVG-based visualizations) - [Install Guide](https://graphviz.org/download/)

Run `npm run deps:check` to verify your environment setup for visualizations.

### Documentation

For detailed information about the dependency visualization system, see [tools/depcruise/README.md](tools/depcruise/README.md).

## Configuration

All configuration files are located in the `config` directory. For more information, see [config/README.md](config/README.md).

## Production Website

The production website files are located in the `www` directory. For more information, see [www/README.md](www/README.md).
