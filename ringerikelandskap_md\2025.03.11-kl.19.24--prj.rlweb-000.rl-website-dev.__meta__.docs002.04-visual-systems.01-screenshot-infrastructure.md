# Screenshot Infrastructure

This document provides a detailed explanation of the screenshot capture system, which is a critical component of the Ringerike Landskap website development workflow.

## System Overview

The screenshot infrastructure captures and maintains visual records of the website, serving several important purposes:

1. **Visual Documentation**: Provides snapshots of the website's appearance at different stages
2. **AI-Assisted Development**: Enables AI tools to "see" the website output
3. **Visual Regression Testing**: Allows comparison of changes over time
4. **Responsive Design Verification**: Captures multiple viewport sizes

## Key Components

### Directory Structure

```
project/
├── .ai-analysis/               # AI-specific screenshots
│   ├── latest/                 # Most recent AI analysis
│   ├── snapshot-[timestamp]/   # Historical AI snapshots
│   └── metadata.json           # Snapshot tracking information
├── screenshots/                # General screenshot storage
│   ├── latest/                 # Most recent screenshots
│   ├── [YYYY-MM-DD_HH-MM-SS]/  # Timestamped screenshot sets
│   └── screenshot-report.html  # Visual report of screenshots
└── scripts/                    # Screenshot automation
    ├── screenshot-manager.js   # Main screenshot management
    ├── dev-with-snapshots.js   # Development with auto-capture
    ├── auto-snapshot.js        # Automated snapshot tool
    └── various helper scripts
```

### Core Scripts

1. **screenshot-manager.js**: Central script that handles:
   - Capturing screenshots at specified viewport sizes
   - Organizing screenshots into timestamped directories
   - Generating HTML reports
   - Cleaning up old screenshot sets

2. **dev-with-snapshots.js**: Enhanced development server that:
   - Starts the Vite development server
   - Watches for file changes
   - Automatically captures screenshots when changes occur
   - Updates the `latest` directories

3. **auto-snapshot.js**: AI-focused tool that:
   - Captures specific screenshots for AI analysis
   - Maintains the `.ai-analysis` directory
   - Tracks metadata about captures

### Capture Configuration

Screenshots are captured at these standard viewport sizes:

- **Mobile**: 375×667px
- **Tablet**: 768×1024px
- **Desktop**: 1440×900px

And for these primary pages:

- Home (/)
- About (/hvem-er-vi)
- Services (/hva-vi-gjor)
- Projects (/prosjekter)
- Contact (/kontakt)

## Usage in Development

### Standard Development Workflow

```bash
npm run dev:ai
```

This command:
1. Starts the Vite development server
2. Sets up file watchers for auto-capturing
3. Maintains the `latest` directories with current screenshots

### Manual Screenshot Capture

```bash
npm run screenshots:capture
```

This command manually captures a full set of screenshots across all configured viewports and pages.

### Screenshot Management

```bash
# View screenshot help
npm run screenshots

# Clean old screenshots (older than 7 days by default)
npm run screenshots:clean

# Refresh screenshots (capture, tidy, and clean)
npm run screenshots:refresh
```

## Integration with AI Tools

The system is designed to help AI tools (like Cursor) understand the visual output:

1. AI-specific snapshots are stored in `.ai-analysis/latest/`
2. Metadata about snapshots is maintained in `.ai-analysis/metadata.json`
3. Scripts detect changes and automatically update visual information

When working with AI assistants, they can reference:
```
/project/.ai-analysis/latest/
```

This provides consistent access to current visual state regardless of when snapshots were taken.

## Technical Implementation

The system uses:

- **Puppeteer**: For headless browser capture
- **Node.js file system APIs**: For directory management
- **Custom HTML templates**: For report generation
- **File watchers**: For detecting changes during development

## Critical Considerations

When working with the codebase:

1. **Do not modify** the core screenshot scripts without careful testing
2. **Do not delete** the `.ai-analysis` or `screenshots` directories
3. **Use the provided npm scripts** rather than calling scripts directly
4. Be aware that screenshot capture adds some overhead to development

## Extending the System

To add new pages or viewports to the capture system:

1. Edit `capture-website.js` to update the configuration
2. Add new entries to the `pages` or `viewports` arrays
3. Run `npm run screenshots:capture` to test

## Troubleshooting

If screenshots aren't being captured:

1. Ensure the development server is running
2. Check that the page is accessible at the expected URL
3. Verify there are no JavaScript errors preventing render
4. Try running `npm run screenshots:capture` manually

The screenshot infrastructure is a critical part of the development workflow and should be maintained carefully as the project evolves. 