# Ringerike Landskap Website

This directory contains the website code for Ringerike Landskap.

## Overview

The website is built using React, TypeScript, and Vite. It uses a component-based architecture with a focus on reusability and maintainability. This separation from development tools provides several benefits:

1. **Clean separation of concerns**: Website code is separate from development tools.
2. **Simplified development**: All website files are in one place.
3. **Environment-specific configuration**: Different configurations can be used for development, staging, and production.
4. **Improved organization**: Clear separation of concerns makes the project easier to maintain.

## Directory Structure

```
website/
├── public/           # Static assets
│   ├── images/       # Images
│   └── ...           # Other static assets
└── src/              # Source code
    ├── app/          # Application root shell and router
    ├── sections/     # Chronologically ordered, self-contained sections
    │   ├── 10-home/  # Home page and related components
    │   ├── 20-about/ # About page and related components
    │   ├── 30-services/ # Services pages and related components
    │   ├── 40-projects/ # Projects pages and related components
    │   ├── 50-testimonials/ # Testimonials pages and related components
    │   └── 60-contact/ # Contact page and related components
    ├── ui/           # Global, atomic UI components
    ├── layout/       # Shared layout components
    ├── data/         # Static data (services, projects, testimonials)
    ├── lib/          # Utility logic, API layer, config
    ├── types/        # TypeScript definitions
    └── hooks/        # Custom React hooks
```

## Development

To start the development server:

```bash
npm run dev
```

## Building

To build the website for different environments:

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

## Deployment

To deploy the website to different environments:

```bash
# Deploy to staging
npm run www:deploy:staging

# Deploy to production
npm run www:deploy:production
```

## Environment Configuration

The website uses environment-specific configuration files to handle different settings for development, staging, and production. These configurations are located in the `config/env` directory.
