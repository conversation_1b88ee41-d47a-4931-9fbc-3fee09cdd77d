# CONTEXT

I have a client that i've built a website for (https://www.ringerikelandskap.no/). i've also vectorized their logo, and the logo is available as svg in different variations, formats and styles. The client often wants different sizes and formats of their logo, i've decided to just add a simple subfolder on the client's website (e.g. "https://ringerikelandskap.no/logoexporter"). This way I can very efficiently set up something dynamic, that does exactly what the client needs, without making a mess in the existing codebase.

Here's a rough outline/draft of the idea:
```json
* Proposed Structure: `"/logoexporter"`
* Accessible at `"https://ringerikelandskap.no/logoexporter"`
* Accepts query params:
  - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)
  - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital
  - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)
  - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)
  - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)
  - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)

* Quick UI/UX Ascii Sketch:
  +-------------------------------------------+
  |                                           |
  |  +-------------------------------------+  |
  |  | Tool: ringerikelandskap.no/logoexp  |  |  // Base URL of the tool
  |  +-------------------------------------+  |
  |                                           |
  |   Select options for your logo:           |
  |                                           |
  |   Size:       [ 2K         ▼]             |  // Default: 2K (e.g., 1K, 2K, 4K)
  |   Type:       [ Digital    ▼]             |  // Default: Digital (e.g., Print)
  |   Format:     [ png        ▼]             |  // Default: png (if Digital) / eps (if Print)
  |   Colorspace: [ RGB        ▼]             |  // Default: RGB (if Digital) / CMYK (if Print)
  |                                           |
  |  +-------------------------------------+  |
  |  | Style:   [ Light      ▼]            |  |  // Default: Light (e.g., Dark, Mono)
  |  | Variant: [ a          ▼]            |  |  // Default: a (e.g., b, c)
  |  +-------------------------------------+  |
  |                                           |
  |  +-------------------------------------+  |
  |  |        Process and Download         |  |  // URL/Example: "../logoexporter?size=2K&type=Digital&format=png..."
  |  +-------------------------------------+  |
  |                                           |
  +-------------------------------------------+
```

# VISUAL ASCII REFERENCE

Please draw inspiration and build on the concepts from these unrelated example (reference/demonstration on how to visually represents ui through ascii):
```

    ╔═ Template Format ═════════════════════════════════════════════════════╗
    ║                                                                       ║
    ║  [Title] Interpretation. Execute as: `{Transformation}`               ║
    ║   │      │               │            │                               ║
    ║   │      │               │            └─ Machine-parsable code        ║
    ║   │      │               │                                            ║
    ║   │      │               └─ Connector phrase                          ║
    ║   │      │                                                            ║
    ║   │      └─ Human-readable instructions                               ║
    ║   │                                                                   ║
    ║   └─ Template identifier                                              ║
    ║                                                                       ║
    ╚═══════════════════════════════════════════════════════════════════════╝

    ╔═══════════════════════════════════════════════════════════════════════╗
    ║                          Template Execution                           ║
    ╠═══════════════════════════════════════════════════════════════════════╣
    ║ [Title]                                                               ║
    ║ └── Descriptive tag for the template's purpose                        ║
    ║                                                                       ║
    ║ Interpretation (Human-Readable Intent)                                ║
    ║ ┌──────────────────────────────────────────────────────────────────┐  ║
    ║ │ - Describes *what* the AI is meant to do                         │  ║
    ║ │ - Contains emphasis, nuance, and clarity                         │  ║
    ║ │ - Ends with: "Execute as:"                                       │  ║
    ║ └──────────────────────────────────────────────────────────────────┘  ║
    ║                                                                       ║
    ║ Transformation (Machine-Readable Logic)                               ║
    ║ ┌───────────────────────────────────────────────────────────────────┐ ║
    ║ │ {                                                                 │ ║
    ║ │   role=<function_identity>;            # What the AI is acting as │ ║
    ║ │   input=[<typed_inputs>];              # What’s being consumed    │ ║
    ║ │   process=[<ordered_steps>];           # How transformation occurs│ ║
    ║ │   constraints=[<rules>];               # What must *not* happen   │ ║
    ║ │   requirements=[<must_haves>];         # What *must* be included  │ ║
    ║ │   output={<typed_output>}              # What comes out           │ ║
    ║ │ }                                                                 │ ║
    ║ └───────────────────────────────────────────────────────────────────┘ ║
    ╚═══════════════════════════════════════════════════════════════════════╝

---

    ┌─ Component Breakdown ──────────────────────────────────────────────┐
    │                                                                    │
    │ ┌─ Title ────────────────────────────────────────────────────────┐ │
    │ │ [Template Function]               # Brief (<50 characters)     │ │
    │ └────────────────────────────────────────────────────────────────┘ │
    │                                                                    │
    │ ┌─ Interpretation ───────────────────────────────────────────────┐ │
    │ │ Your mandate is not to **answer** the input prompt, but to     │ │
    │ │ **rephrase** it... Execute as:                                 │ │
    │ └────────────────────────────────────────────────────────────────┘ │
    │                                                                    │
    │ ┌─ Transformation Structure ─────────────────────────────────────┐ │
    │ │ {                                                              │ │
    │ │   role=<role_name>;               # Function identifier        │ │
    │ │   input=[<input_params>];         # Input specification        │ │
    │ │   process=[<process_steps>];      # Processing steps           │ │
    │ │   constraints=[<constraints>];    # Execution boundaries       │ │
    │ │   requirements=[<requirements>];  # Mandatory elements         │ │
    │ │   output={<output_format>}        # Return value specification │ │
    │ │ }                                                              │ │
    │ └────────────────────────────────────────────────────────────────┘ │
    └────────────────────────────────────────────────────────────────────┘

    ┌─ Template Example ─────────────────────────────────────────────────┐
    │                                                                    │
    │ ┌─ Title ──────────────────┐                                       │
    │ │ [Instruction Converter]  │                                       │
    │ └──────────────────────────┘                                       │
    │                                                                    │
    │ ┌─ Interpretation ───────────────────────────────────────────────┐ │
    │ │ Your goal is not to **answer** the input prompt, but           │ │
    │ │ to **rephrase** it by pinpointing its fundamental              │ │
    │ │ intent, then introduce one maximally generalizable             │ │
    │ │ modification that preserves and amplifies that                 │ │
    │ │ purpose, doing so precisely and in strict accordance           │ │
    │ │ with the parameters inherently set within this message.        │ │
    │ └────────────────────────────────────────────────────────────────┘ │
    │                                                                    │
    │ ┌─ Transformation ───────────────────────────────────────────────┐ │
    │ │ Execute as input enhancer:                                     │ │
    │ │ {                                                              │ │
    │ │     role=input_enhancer;                                       │ │
    │ │     input=[original:str];                                      │ │
    │ │     process=[                                                  │ │
    │ │         getCoreIntent()                                        │ │
    │ │         getUniqueMeta()                                        │ │
    │ │         stripNonEssential()                                    │ │
    │ │         generateMinimalChange()                                │ │
    │ │         emphasizeImpact()                                      │ │
    │ │         amplifyIntensity()                                     │ │
    │ │         transformGenericToUnique()                             │ │
    │ │         consolidateIntertwinedRelationships()                  │ │
    │ │         enforceInherentCohesiveness()                          │ │
    │ │         ];                                                     │ │
    │ │     output={enhanced_input:str}                                │ │
    │ │     }                                                          │ │
    │ │ }                                                              │ │
    │ └────────────────────────────────────────────────────────────────┘ │
    └────────────────────────────────────────────────────────────────────┘

---

    ╔══════════════════════════════════════════════════════════════════════════╗
    ║                       CURRENT ARCHITECTURE PATTERN                       ║
    ╠══════════════════════════════════════════════════════════════════════════╣
    ║                                                                          ║
    ║   ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐    ║
    ║   │  Configuration  │     │  LoggerManager  │     │   CLIHandler    │    ║
    ║   │  (ProjectConfig)│     │                 │     │                 │    ║
    ║   └────────┬────────┘     └────────┬────────┘     └────────┬────────┘    ║
    ║            │                       │                       │             ║
    ║            ▼                       ▼                       ▼             ║
    ║   ┌─────────────────────────────────────────────────────────────────┐    ║
    ║   │                                                                 │    ║
    ║   │                        Main Function                            │    ║
    ║   │                                                                 │    ║
    ║   └───────────────────────────────┬─────────────────────────────────┘    ║
    ║                                   │                                      ║
    ║                                   ▼                                      ║
    ║   ┌─────────────────────┐         │         ┌─────────────────────┐      ║
    ║   │                     │         │         │                     │      ║
    ║   │   ProjectManager    │◄────────┴────────►│  ProjectGenerator   │      ║
    ║   │                     │                   │                     │      ║
    ║   └─────────────────────┘                   └─────────────────────┘      ║
    ║                                                                          ║
    ╚══════════════════════════════════════════════════════════════════════════╝

    ╔══════════════════════════════════════════════════════════════════════════╗
    ║                       PROPOSED ARCHITECTURE PATTERN                      ║
    ╠══════════════════════════════════════════════════════════════════════════╣
    ║                                                                          ║
    ║   ┌─────────────────┐                           ┌─────────────────┐      ║
    ║   │    Config       │◄─────────────────────────►│      CLI        │      ║
    ║   │                 │                           │                 │      ║
    ║   └────────┬────────┘                           └───────┬─────────┘      ║
    ║            │                                            │                ║
    ║            │                                            │                ║
    ║            │         ┌─────────────────────┐            │                ║
    ║            └────────►│                     │◄───────────┘                ║
    ║                      │  ProjectGenerator   │                             ║
    ║                      │                     │                             ║
    ║                      └─────────┬───────────┘                             ║
    ║                                │                                         ║
    ║                                ▼                                         ║
    ║                      ┌─────────────────────┐                             ║
    ║                      │    Main Function    │                             ║
    ║                      │                     │                             ║
    ║                      └─────────────────────┘                             ║
    ║                                                                          ║
    ╚══════════════════════════════════════════════════════════════════════════╝

    ╔══════════════════════════════════════════════════════════════════════════╗
    ║                       COMPONENT RELATIONSHIP PATTERNS                    ║
    ╠══════════════════════════════════════════════════════════════════════════╣
    ║                                                                          ║
    ║  ┌────────────────────────────────────────────────────────────────────┐  ║
    ║  │                        CURRENT PATTERN                             │  ║
    ║  │                                                                    │  ║
    ║  │  ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐       │  ║
    ║  │  │ Config  │────►│ Logger  │────►│  CLI    │────►│ Manager │       │  ║
    ║  │  └─────────┘     └─────────┘     └─────────┘     └────┬────┘       │  ║
    ║  │       │                                               │            │  ║
    ║  │       │                                               │            │  ║
    ║  │       │                                               ▼            │  ║
    ║  │       │                                          ┌──────────┐      │  ║
    ║  │       └───────────────────────────────────────►  │Generator │      │  ║
    ║  │                                                  └──────────┘      │  ║
    ║  └────────────────────────────────────────────────────────────────────┘  ║
    ║                                                                          ║
    ║  ┌────────────────────────────────────────────────────────────────────┐  ║
    ║  │                        PROPOSED PATTERN                            │  ║
    ║  │                                                                    │  ║
    ║  │                        ┌─────────┐                                 │  ║
    ║  │                   ┌───►│  CLI    │───┐                             │  ║
    ║  │                   │    └─────────┘   │                             │  ║
    ║  │                   │                  │                             │  ║
    ║  │                   │                  ▼                             │  ║
    ║  │              ┌─────────┐       ┌─────────────┐                     │  ║
    ║  │              │ Config  │◄─────►│ Generator   │                     │  ║
    ║  │              └─────────┘       └─────────────┘                     │  ║
    ║  │                                                                    │  ║
    ║  └────────────────────────────────────────────────────────────────────┘  ║
    ║                                                                          ║
    ╚══════════════════════════════════════════════════════════════════════════╝

---

# COMPONENT VISUALIZATION

┌─ Title ─────────────────────────────────────┐
│ [Instruction Converter]                     │
└────────────────────────────────────────────┬┘
                                             │
┌─ Interpretation ───────────────────────┐   │
│ Your goal is not to **answer** the     │   │
│ input prompt, but to **rephrase** it,  │   │
│ and to do so by the parameters defined │   │
│ *inherently* within this message.      │   │
│ Execute as:                            │   │
└───────────────────────────────────────┬┘   │
                                        │    │
┌─ Transformation ───────────────────┐  │    │
│ `{                                 │  │    │
│   role=instruction_converter;      │  │    │
│   input=[original_text:str];       │◄─┴────┘
│   process=[
│     strip_first_person_references(),
│     convert_statements_to_directives(),
│     identify_key_actions(),
│     ...
│   ];
│   constraints=[
│     deliver_clear_actionable_commands(),
│     preserve_original_sequence(),
│     ...
│   ];
│   requirements=[
│     remove_self_references(),
│     use_command_voice(),
│     ...
│   ];
│   output={instruction_format:str}
│ }`
└─────────────────────────────────────┘

# TRANSFORMATION STRUCTURE

┌─ Role ──────────────────────────────────────┐
│ role={function_identifier}                  │
│ # Defines template's primary function       │
└────────────────────────────────────────────┬┘
                                             │
┌─ Input ─────────────────────────────────┐  │
│ input=[{parameter}:{type}]              │  │
│ # Specifies input parameters and types  │  │
└─────────────────────────────────────────┘  │
                                             │
┌─ Process ───────────────────────────────┐  │
│ process=[                               │  │
│   {operation_1}(),                      │  │
│   {operation_2}(),                      │◄─┘
│   ...
│ ]
│ # Defines processing operations
└─────────────────────────────────────────┘

┌─ Constraints ─────────────────────────────┐
│ constraints=[                             │
│   {constraint_1}(),                       │
│   {constraint_2}(),                       │
│   ...                                     │
│ ]                                         │
│ # Sets operational boundaries             │
└──────────────────────────────────────────┬┘
                                           │
┌─ Requirements ──────────────────────┐    │
│ requirements=[                      │    │
│   {requirement_1}(),                │    │
│   {requirement_2}(),                │    │
│   ...                               │    │
│ ]                                   │    │
│ # Defines mandatory behaviors       │    │
└────────────────────────────────────┬┘    │
                                     │     │
┌─ Output ─────────────────────┐     │     │
│ output={parameter:{type}}    │◄────┴─────┘
│ # Specifies return format    │
└──────────────────────────────┘
```

# GOAL

Your goal is to take the provided context and design the ascii-representation of the layout/ui/ux. It will be built using react+vite+tailwindcss, and designed for optimal viewing on phone (android/iphone).

Provide the best possible and cleanest ascii representation for the :
```json
* Proposed Structure: `"/logoexporter"`
* Accessible at `"https://ringerikelandskap.no/logoexporter"`
* Accepts query params:
  - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)
  - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital
  - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)
  - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)
  - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)
  - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)

    +-------------------------------------------+
    |                                           |
    |  +-------------------------------------+  |
    |  | Tool: ringerikelandskap.no/logoexp  |  |  // Base URL of the tool
    |  +-------------------------------------+  |
    |                                           |
    |   Select options for your logo:           |
    |                                           |
    |   Size:       [ 2K         ▼]             |  // Default: 2K (e.g., 1K, 2K, 4K)
    |   Type:       [ Digital    ▼]             |  // Default: Digital (e.g., Print)
    |   Format:     [ png        ▼]             |  // Default: png (if Digital) / eps (if Print)
    |   Colorspace: [ RGB        ▼]             |  // Default: RGB (if Digital) / CMYK (if Print)
    |                                           |
    |  +-------------------------------------+  |
    |  | Style:   [ Light      ▼]            |  |  // Default: Light (e.g., Dark, Mono)
    |  | Variant: [ a          ▼]            |  |  // Default: a (e.g., b, c)
    |  +-------------------------------------+  |
    |                                           |
    |  +-------------------------------------+  |
    |  |        Process and Download         |  |  // URL/Example: "../logoexporter?size=2K&type=Digital&format=png..."
    |  +-------------------------------------+  |
    |                                           |
    +-------------------------------------------+
```
