# Scripts Directory

This directory contains utility scripts for the Ringerike Landskap website project.

## Organization

The scripts are organized into the following categories:

### 🖼️ `screenshots/`
Scripts for capturing and managing website screenshots.

- `screenshot-utils.js` - Shared utilities for screenshot functionality
- `screenshot-manager.js` - Central manager for all screenshot operations
- `capture/` - Scripts for capturing website screenshots
  - `capture-screenshots.js` - Captures screenshots at different viewport sizes
  - `capture-ai-snapshot.js` - Captures snapshots for AI analysis

### 🔄 `dependencies/`
Scripts for analyzing and visualizing code dependencies.

- `dependency-utils.js` - Shared utilities for dependency visualization
- `dependency-manager.js` - Central manager for all dependency operations
- `visualize/` - Scripts for creating different visualization formats
- `analyze/` - Scripts for analyzing dependencies without visualization
- `utils/` - Helper utilities for dependency operations

### 🚀 `dev/`
Scripts for enhancing the development workflow.

- `dev-with-snapshots.js` - Runs the development server with automatic snapshot functionality

### 🛠️ `utils/`
General utility scripts that don't fit into other categories.

## Usage

### Screenshot Management

```bash
# Show help information
npm run screenshots

# Capture new screenshots
npm run screenshots:capture

# Clean up old screenshots (default: older than 7 days)
npm run screenshots:clean [days]

# Tidy up the screenshots directory
npm run screenshots:tidy

# Refresh screenshots (capture, tidy, and clean in one command)
npm run screenshots:refresh

# Capture AI snapshot
npm run ai:snapshot
```

### Dependency Visualization

```bash
# Show help information
npm run deps

# Generate and open specific visualizations
npm run deps:flow       # Flow diagram
npm run deps:bubble     # Bubble chart
npm run deps:d3         # D3 graph
npm run deps:circle     # Circle packing

# Generate all visualizations and open dashboard
npm run deps:all

# Analysis operations
npm run deps:analyze    # Analyze dependencies without visualization
npm run deps:audit      # Run complete dependency audit
```

### Development

```bash
# Run development server with automatic snapshots
npm run dev:ai
```
