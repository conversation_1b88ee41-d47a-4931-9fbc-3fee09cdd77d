# Components Documentation

## UI Components

### Button

The Button component is a versatile, reusable button that supports multiple variants and can act as either a button or a link.

```tsx
<Button 
  variant="primary" 
  size="lg" 
  to="/kontakt"
>
  Kontakt oss
</Button>
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'text'
- `size`: 'sm' | 'md' | 'lg'
- `to`: Optional URL for link behavior
- `fullWidth`: Boolean to make button full width
- `icon`: Optional icon component
- `iconPosition`: 'left' | 'right'

### Container

The Container component provides consistent width constraints and padding.

```tsx
<Container size="lg" className="py-12">
  {children}
</Container>
```

**Props:**
- `size`: 'sm' | 'md' | 'lg' | 'xl'
- `as`: React element type (default: 'div')
- `padded`: <PERSON><PERSON><PERSON> to add padding

### Hero

The Hero component is used for page headers with background images and call-to-action buttons.

```tsx
<Hero
  title="Anleggsgartner & maskinentreprenør"
  subtitle="Med base på Røyse skaper vi varige uterom..."
  backgroundImage="/images/site/hero-main.webp"
  location="Røyse, Hole kommune"
  actionLink="/prosjekter"
  actionText="Se våre prosjekter"
/>
```

**Props:**
- `title`: Main heading
- `subtitle`: Optional subheading
- `backgroundImage`: URL for background image
- `location`: Optional location text
- `yearEstablished`: Optional year text
- `actionLink`: Optional CTA link
- `actionText`: Optional CTA text
- `height`: 'small' | 'medium' | 'large' | 'full'
- `overlay`: 'none' | 'light' | 'dark' | 'gradient'
- `textAlignment`: 'left' | 'center' | 'right'
- `textColor`: 'light' | 'dark'

### ServiceAreaList

Displays a list of service areas with their distances and descriptions.

```tsx
<ServiceAreaList areas={serviceAreas} />
```

**Props:**
- `areas`: Array of ServiceArea objects
- `className`: Optional CSS class

### SeasonalCTA

A call-to-action component that adapts based on the current season.

```tsx
<SeasonalCTA season="summer" />
```

**Props:**
- `season`: 'spring' | 'summer' | 'fall' | 'winter'
- `className`: Optional CSS class

## Feature Components

### ProjectCard

Displays a project with image, title, description, and metadata.

```tsx
<ProjectCard 
  project={project}
  variant="featured"
  showTestimonial={true}
/>
```

**Props:**
- `project`: ProjectType object
- `variant`: 'default' | 'featured' | 'compact'
- `showTestimonial`: Boolean to show testimonial
- `onProjectClick`: Optional click handler

### ProjectsCarousel

A carousel component for displaying projects.

```tsx
<ProjectsCarousel projects={recentProjects} />
```

**Props:**
- `projects`: Array of ProjectType objects
- `className`: Optional CSS class

### SeasonalProjectsCarousel

A carousel that filters and displays projects relevant to the current season.

```tsx
<SeasonalProjectsCarousel projects={recentProjects} />
```

**Props:**
- `projects`: Array of ProjectType objects
- `className`: Optional CSS class

### ServiceCard

Displays a service with image, title, description, and features.

```tsx
<ServiceCard 
  service={service}
  variant="compact"
/>
```

**Props:**
- `service`: ServiceType object
- `variant`: 'default' | 'compact' | 'featured'
- `className`: Optional CSS class

### SeasonalServicesSection

A section that displays services relevant to the current season.

```tsx
<SeasonalServicesSection />
```

### TestimonialsSection

Displays testimonials in a slider format.

```tsx
<TestimonialsSection testimonials={testimonials} />
```

**Props:**
- `testimonials`: Array of TestimonialType objects
- `className`: Optional CSS class

## Layout Components

### Header

The main navigation header with responsive menu.

```tsx
<Header />
```

### Footer

The footer with contact information, links, and copyright.

```tsx
<Footer />
```

## Page Components

### HomePage

The main landing page with seasonal adaptation.

```tsx
<HomePage />
```

### ServicesPage

The services page with filtering capabilities.

```tsx
<ServicesPage />
```

### ProjectsPage

The projects page with filtering capabilities.

```tsx
<ProjectsPage />
```

### ServiceDetailPage

Detailed view of a specific service.

```tsx
<ServiceDetailPage />
```

**URL Parameters:**
- `id`: Service ID

### ProjectDetailPage

Detailed view of a specific project.

```tsx
<ProjectDetailPage />
```

**URL Parameters:**
- `id`: Project ID

### AboutPage

Information about the company and team.

```tsx
<AboutPage />
```

### ContactPage

Contact form and information.

```tsx
<ContactPage />
```

### TestimonialsPage

Customer testimonials with filtering.

```tsx
<TestimonialsPage />
```

## Component Relationships

The components are organized in a hierarchical structure:

1. **Page Components** use **Layout Components** for structure
2. **Page Components** use **Feature Components** for functionality
3. **Feature Components** use **UI Components** for presentation
4. **UI Components** are the building blocks for all other components

This structure ensures:
- **Reusability**: Components can be reused across the application
- **Maintainability**: Changes to one component don't affect others
- **Consistency**: UI elements are consistent throughout the application
- **Scalability**: New features can be added without modifying existing code