# System Design
- The website is a responsive single-page application (SPA) built primarily to showcase landscaping services, completed projects, and enable booking of free consultations.
- Organized around a marketing-focused frontend, with potential expansion to a backend component for handling inquiries or managing a project portfolio database.
- Seasonal content adaptation (e.g., highlighting “Ferdigplen” in spring) requires a lightweight logic that dynamically updates displayed services/projects.

# Architecture pattern
- Follows a **component-based SPA** architecture using React.
- Utilizes **client-side routing** for navigation (e.g., React Router).
- Separation of concerns:
  - UI layer (frontend with Tailwind CSS + React components).
  - Data handling (static JSON or a lightweight backend API).
  - Possible integration with a headless CMS if dynamic content management is needed.

# State management
- **React Hooks** (useState, useEffect, etc.) handle local component state.
- **Custom Hooks** manage reusable logic (e.g., media queries, seasonal theme toggles).
- Potential use of **Context API** for global data (e.g., user preferences, filter criteria).

# Data flow
- **Front-end** retrieves static or server-provided data for projects, services, and testimonials.
- Seasonal logic updates displayed content based on date or user preference.
- Form submissions (bookings, inquiries) sent to a backend endpoint (REST or serverless function), which processes and possibly stores them in a database or forwards them via email.

# Technical Stack
- **Frontend**:
  - React 18+
  - TypeScript
  - Tailwind CSS
  - Vite build tool
  - React Router for routing
- **Backend** (proposed/minimal):
  - Could be Node.js or serverless functions to handle form submissions and store or forward inquiries
  - Optional headless CMS (e.g., Contentful, Strapi) for dynamic content

# Authentication Process
- **Public Site**:
  - No login required for visitors; all primary content is publicly accessible.
- **Admin Access** (if applicable):
  - Potentially protected routes or a CMS login for staff to manage project listings, testimonials, or seasonal highlights.
- **Mechanisms**:
  - Basic session-based or token-based authentication if an admin panel is introduced.

# Route Design
- **Public Routes**:
  - `/` (Home)
  - `/hva-vi-gjor` (Services)
  - `/prosjekter` (Projects)
  - `/hvem-er-vi` (About Us)
  - `/kontakt` (Contact)
- **Dynamic Routes**:
  - `/tjenester/:id` for service details
  - `/prosjekter/:id` for project details
- **Optional**:
  - `/kundehistorier` (Testimonials)
- **Navigation**:
  - Uses React Router for client-side navigation
  - Nested routes or layout components for consistent headers/footers

# API Design
- **Frontend to Backend**:
  - `POST /api/contact` to handle contact form submissions
  - `GET /api/projects` to fetch project data (if stored dynamically in a database)
  - `GET /api/services` to fetch service data
- **Response Formats**:
  - JSON-based (e.g., `application/json`) for data retrieval
  - Error handling with suitable HTTP status codes (400, 500, etc.)
- **Security**:
  - Basic validation and possibly Captcha on form submissions to prevent spam

# Database Design ERD
- **Possible Entities** (if using a backend database):
  1. **Services**
     - `id` (PK)
     - `name`
     - `description`
     - `season` (optional)
     - `images`
  2. **Projects**
     - `id` (PK)
     - `title`
     - `location`
     - `description`
     - `service_ids` (multiple or a join table)
     - `images`
     - `season`
  3. **Testimonials**
     - `id` (PK)
     - `client_name`
     - `rating`
     - `comment`
     - `project_id` (FK)
  4. **Inquiries** (contact form submissions)
     - `id` (PK)
     - `name`
     - `email`
     - `phone`
     - `message`
     - `timestamp`
- **Relationships**:
  - A project can reference one or many services (many-to-many with a join table if needed).
  - Testimonials can be linked to projects (one-to-many relationship).
  - Inquiries are standalone records tied to potential leads or existing customers.

