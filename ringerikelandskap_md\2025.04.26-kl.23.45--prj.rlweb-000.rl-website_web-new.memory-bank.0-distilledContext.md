# 0. Distilled Context - Ringerike Landskap Website

## Core Mission
A modern, responsive website for Ringerike Landskap AS (landscaping company in Røyse, Norway) that automatically adapts content based on season and geographic location, transitioning from hardcoded data to a JSON-driven architecture.

## Essential System Patterns

1. **Seasonal Content Adaptation**: The entire codebase is organized around Norwegian seasons (`vaar`, `sommer`, `hoest`, `vinter`), with automatic content adaptation based on current season through `getCurrentSeason()`.

2. **Geographic Authenticity Layer**: Content is precisely targeted to specific locations in the Ringerike region (Røyse, Hole, Vik, Hønefoss, Sundvollen, Jevnaker, Bærum) with location tagging and filtering.

3. **JSON-Driven Architecture Transition**: The codebase is evolving from hardcoded data to a dynamic, directory-based JSON configuration system for improved maintainability.

## Technical Foundation
- React 18.3.1 with TypeScript
- Vite build tool
- Tailwind CSS styling
- Directory-based seasonal content structure
- Three-tier component architecture (UI → Feature → Page)

## Current Evolution State
The project is transitioning to a more maintainable architecture with projects already migrated to JSON files in seasonal directories, while services and testimonials migration is in progress.

## High-Value Insight
Understanding the dual seasonal/geographic adaptation patterns is key to comprehending this codebase — all other aspects of the architecture serve to enable these core differentiating features.
