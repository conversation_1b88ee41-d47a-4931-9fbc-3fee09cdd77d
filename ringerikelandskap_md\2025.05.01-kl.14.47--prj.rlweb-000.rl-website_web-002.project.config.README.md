# Configuration Directory

This directory contains all configuration files for the Ringerike Landskap website.

## Directory Structure

```
config/
├── env/                  # Environment-specific configuration files
│   ├── .env.development  # Development environment
│   ├── .env.staging      # Staging environment
│   └── .env.production   # Production environment
├── eslint.config.js      # ESLint configuration
├── postcss.config.js     # PostCSS configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
├── tsconfig.node.json    # TypeScript configuration for Node.js
└── vite.config.ts        # Vite configuration
```

## Environment Configuration

The `env` directory contains environment-specific configuration files. These files are used by Vite to set environment variables for different environments.

### Development Environment

The development environment is used for local development. It uses the `.env.development` file.

```bash
npm run dev
```

### Staging Environment

The staging environment is used for testing before production. It uses the `.env.staging` file.

```bash
npm run build:staging
npm run preview:staging
```

### Production Environment

The production environment is used for the live website. It uses the `.env.production` file.

```bash
npm run build:production
npm run preview:production
```

## Build Configuration

The build configuration is defined in `vite.config.ts`. It includes settings for:

- Development server
- Build output
- Asset handling
- Environment variables
- Plugins

## TypeScript Configuration

The TypeScript configuration is defined in `tsconfig.json` and `tsconfig.node.json`. It includes settings for:

- Compiler options
- Module resolution
- Type checking
- Path aliases

## CSS Configuration

The CSS configuration is defined in `postcss.config.js` and `tailwind.config.js`. It includes settings for:

- PostCSS plugins
- Tailwind CSS theme
- Utility classes
- Animations

## Linting Configuration

The linting configuration is defined in `eslint.config.js`. It includes settings for:

- ESLint rules
- TypeScript integration
- React hooks rules
- Code style
