# Dir `src`

*Files marked `[-]` are shown in structure but not included in content.

### File Structure

```
├── App.tsx
├── app
│   ├── layout.tsx
│   └── page.tsx
├── components
│   ├── (sections)
│   │   ├── coverage-area
│   │   │   └── CoverageArea.tsx
│   │   ├── hero
│   │   │   └── Hero.tsx
│   │   ├── projects
│   │   │   ├── ProjectCard.tsx
│   │   │   └── ProjectsCarousel.tsx
│   │   ├── seasonal-planning
│   │   │   └── SeasonalPlanning.tsx
│   │   ├── services
│   │   │   ├── ServiceCard.tsx
│   │   │   ├── ServiceFeature.tsx
│   │   │   └── Services.tsx
│   │   └── testimonials
│   │       └── Testimonials.tsx
│   ├── ContactForm.tsx
│   ├── Navbar.tsx
│   ├── ServiceCard.tsx
│   ├── common
│   │   ├── Button.tsx
│   │   ├── Container.tsx
│   │   ├── Hero.tsx
│   │   ├── ImageGallery.tsx
│   │   ├── LocalExpertise.tsx
│   │   ├── LocalServiceArea.tsx
│   │   ├── Logo.tsx
│   │   ├── SeasonalCTA.tsx
│   │   ├── ServiceAreaList.tsx
│   │   └── WeatherAdaptedServices.tsx
│   ├── contact
│   │   └── ContactForm.tsx
│   ├── index.ts
│   ├── layout
│   │   ├── Footer.tsx
│   │   ├── Header.tsx
│   │   ├── Hero.tsx
│   │   ├── Layout.tsx
│   │   ├── Meta.tsx
│   │   └── Navbar.tsx
│   ├── local
│   │   ├── SeasonalGuide.tsx
│   │   ├── ServiceAreaMap.tsx
│   │   └── WeatherNotice.tsx
│   ├── projects
│   │   ├── ProjectCard.tsx
│   │   ├── ProjectFilter.tsx
│   │   ├── ProjectGallery.tsx
│   │   └── ProjectGrid.tsx
│   ├── seo
│   │   └── TestimonialsSchema.tsx
│   ├── services
│   │   ├── Gallery.tsx
│   │   └── ServiceCard.tsx
│   ├── shared
│   │   ├── Elements
│   │   │   ├── Card.tsx
│   │   │   ├── Form
│   │   │   │   ├── Input.tsx
│   │   │   │   ├── Select.tsx
│   │   │   │   ├── Textarea.tsx
│   │   │   │   └── index.ts
│   │   │   ├── Icon.tsx
│   │   │   ├── Image.tsx
│   │   │   ├── Link.tsx
│   │   │   ├── Loading.tsx
│   │   │   └── index.ts
│   │   ├── ErrorBoundary.tsx
│   │   └── Layout
│   │       ├── Layout.tsx
│   │       └── index.ts
│   └── ui
│       ├── Button.tsx
│       ├── Container.tsx
│       ├── Hero.tsx
│       ├── Intersection.tsx
│       ├── Logo.tsx
│       ├── Notifications.tsx
│       ├── SeasonalCTA.tsx
│       ├── ServiceAreaList.tsx
│       ├── Skeleton.tsx
│       ├── Transition.tsx
│       └── index.ts
├── config
│   ├── images.ts
│   ├── routes.ts
│   └── site.ts
├── content
│   ├── index.ts
│   ├── locations
│   │   └── index.ts
│   ├── projects
│   │   └── index.ts
│   ├── services
│   │   └── index.ts
│   ├── services.json
│   ├── team
│   │   └── index.ts
│   ├── team.json
│   └── testimonials
│       └── index.ts
├── data
│   ├── navigation.ts
│   ├── projects.ts
│   ├── services.ts
│   ├── team.ts
│   └── testimonials.ts
├── features
│   ├── home
│   │   ├── FilteredServicesSection.tsx
│   │   ├── SeasonalProjectsCarousel.tsx
│   │   ├── SeasonalServicesSection.tsx
│   │   └── index.ts
│   ├── home.tsx
│   ├── projects
│   │   ├── ProjectCard.tsx
│   │   ├── ProjectFilter.tsx
│   │   ├── ProjectGallery.tsx
│   │   ├── ProjectGrid.tsx
│   │   ├── ProjectsCarousel.tsx
│   │   ├── data.ts
│   │   └── index.ts
│   ├── projects.tsx
│   ├── services
│   │   ├── ServiceCard.tsx
│   │   ├── ServiceFeature.tsx
│   │   ├── ServiceGrid.tsx
│   │   ├── data.ts
│   │   └── index.ts
│   ├── services.tsx
│   ├── team.tsx
│   ├── testimonials
│   │   ├── AverageRating.tsx
│   │   ├── Testimonial.tsx
│   │   ├── TestimonialFilter.tsx
│   │   ├── TestimonialSlider.tsx
│   │   ├── TestimonialsSection.tsx
│   │   ├── data.ts
│   │   └── index.ts
│   └── testimonials.tsx
├── hooks
│   └── useData.ts
├── index.css
├── index.html
├── lib
│   ├── api
│   │   └── index.ts
│   ├── config
│   │   ├── images.ts
│   │   ├── index.ts
│   │   ├── paths.ts
│   │   └── site.ts
│   ├── constants
│   │   └── index.ts
│   ├── constants.ts
│   ├── content.ts
│   ├── context
│   │   └── AppContext.tsx
│   ├── hooks
│   │   ├── images.ts
│   │   ├── index.ts
│   │   ├── useAnalytics.ts
│   │   ├── useDebounce.ts
│   │   ├── useEventListener.ts
│   │   ├── useForm.ts
│   │   ├── useFormField.ts
│   │   ├── useIntersectionObserver.ts
│   │   ├── useLocalStorage.ts
│   │   └── useMediaQuery.ts
│   ├── hooks.ts
│   ├── index.ts
│   ├── types
│   │   ├── common.ts
│   │   ├── components.ts
│   │   ├── content.ts
│   │   └── index.ts
│   ├── types.ts
│   ├── utils
│   │   ├── analytics.ts
│   │   ├── date.ts
│   │   ├── images.ts
│   │   ├── index.ts
│   │   ├── seo.ts
│   │   └── validation.ts
│   └── utils.ts
├── main.tsx
├── pages
│   ├── AboutUs.tsx
│   ├── Home.tsx
│   ├── ProjectDetail.tsx
│   ├── Projects.tsx
│   ├── ServiceDetail.tsx
│   ├── Services.tsx
│   ├── TestimonialsPage.tsx
│   ├── about
│   │   └── index.tsx
│   ├── contact
│   │   └── index.tsx
│   ├── home
│   │   └── index.tsx
│   ├── projects
│   │   ├── detail.tsx
│   │   └── index.tsx
│   ├── services
│   │   ├── detail.tsx
│   │   └── index.tsx
│   └── testimonials
│       └── index.tsx
├── styles
│   ├── animations.css
│   ├── base.css
│   └── utilities.css
├── ui
│   └── index.tsx
├── utils
│   └── imageLoader.ts
└── vite-env.d.ts
```


#### `app\layout.tsx`

```tsx
import React from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from '../components/layout/Navbar';
import Footer from '../components/layout/Footer';
import { Meta } from '../components/layout/Meta';

const RootLayout = () => {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      <Meta />
      <Navbar />
      <main className="flex-grow">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default RootLayout;```


#### `app\page.tsx`

```tsx
import React from 'react';
import Hero from '@/components/(sections)/hero/Hero';
import ProjectsCarousel from '@/components/(sections)/projects/ProjectsCarousel';
import CoverageArea from '@/components/(sections)/coverage-area/CoverageArea';
import SeasonalPlanning from '@/components/(sections)/seasonal-planning/SeasonalPlanning';
import Services from '@/components/(sections)/services/Services';
import Testimonials from '@/components/(sections)/testimonials/Testimonials';

const HomePage = () => {
  return (
    <div itemScope itemType="http://schema.org/LandscapingBusiness">
      <Hero />
      <div className="space-y-16 py-12">
        <ProjectsCarousel />
        <Services />
        <CoverageArea />
        <SeasonalPlanning />
        <Testimonials />
      </div>
    </div>
  );
};

export default HomePage;```


#### `App.tsx`

```tsx
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./components/layout/Header";
import Footer from "./components/layout/Footer";

// Import page components
import HomePage from "./pages/home";
import AboutPage from "./pages/about";
import ServicesPage from "./pages/services";
import ServiceDetailPage from "./pages/services/detail";
import ProjectsPage from "./pages/projects";
import ProjectDetailPage from "./pages/projects/detail";
import ContactPage from "./pages/contact";
import TestimonialsPage from "./pages/testimonials";

function App() {
  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/hvem-er-vi" element={<AboutPage />} />
            <Route path="/hva-vi-gjor" element={<ServicesPage />} />
            <Route path="/tjenester/:id" element={<ServiceDetailPage />} />
            <Route path="/prosjekter" element={<ProjectsPage />} />
            <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />
            <Route path="/kontakt" element={<ContactPage />} />
            <Route path="/kundehistorier" element={<TestimonialsPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;```


#### `components\(sections)\coverage-area\CoverageArea.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { MapPin, Navigation } from 'lucide-react';
import { SERVICE_AREAS } from '@/lib/constants';
import { cn } from '@/lib/utils';

const CoverageArea = () => {
  return (
    <section className="py-12 bg-gray-50">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-green-100 rounded-lg">
                <Navigation className="w-6 h-6 text-green-500" />
              </div>
              <h2 className="text-2xl font-semibold">Vårt dekningsområde</h2>
            </div>
            
            <p className="text-gray-600 mb-8 leading-relaxed">
              Med base på Røyse betjener vi hele Ringerike-regionen med lokalkunnskap 
              og personlig service. Vi kjenner hver bakketopp og hvert jordsmonn i området.
            </p>

            <div className="space-y-3">
              {SERVICE_AREAS.map((area) => (
                <div 
                  key={area.city}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    area.isBase 
                      ? "bg-green-50 border border-green-100" 
                      : "hover:bg-white hover:shadow-sm"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <MapPin className={cn(
                      "w-4 h-4",
                      area.isBase ? "text-green-500" : "text-gray-400"
                    )} />
                    <span className={cn(
                      "text-sm",
                      area.isBase && "font-medium"
                    )}>
                      {area.city}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">{area.distance}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="relative h-[400px] rounded-lg overflow-hidden shadow-lg">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2034.7098040861837!2d10.2558!3d60.0558!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNjDCsDAzJzIwLjgiTiAxMMKwMTUnMjAuOSJF!5e0!3m2!1sno!2sno!4v1647856231968!5m2!1sno!2sno"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Kart over vårt dekningsområde"
              className="absolute inset-0"
            />
            <div 
              className="absolute inset-0 pointer-events-none"
              style={{
                boxShadow: 'inset 0 0 20px rgba(0,0,0,0.1)'
              }}
            />
          </div>
        </div>
      </Container>
    </section>
  );
};

export default CoverageArea;```


#### `components\(sections)\hero\Hero.tsx`

```tsx
import React from "react";
import { HeroProps } from "@/types";
import { Container } from "@/components/ui/Container";
import { Button } from "@/components/ui/Button";
import { ArrowRight } from "lucide-react";

const Hero: React.FC<HeroProps> = ({
    title = "Anleggsgartner i Ringerike",
    subtitle = "Med base på Røyse skaper vi varige uterom tilpasset Ringerikes unike terreng og klima. Vi kjenner hver bakketopp og hvert jordsmonn i regionen.",
    backgroundImage,
}) => {
    return (
        <section
            className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
            style={{
                backgroundImage: `url(${
                    backgroundImage ||
                    "https://images.unsplash.com/photo-1558904541-efa843a96f01?auto=format&fit=crop&q=80"
                })`,
                backgroundSize: "cover",
                backgroundPosition: "center",
            }}
            role="banner"
            aria-label="Hero section"
        >
            {/* Center square gradient overlay */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
          linear-gradient(
            52deg,
            transparent 16%,
            rgba(0,0,0,0.82) 35%,
            rgba(0,0,0,0.68) 68%,
            transparent 89%
          )
        `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 76%, rgba(0,0,0,0.92) 100%)",
                }}
                aria-hidden="true"
            />

            <Container className="relative z-10">
                <div className="max-w-3xl mx-auto text-center">
                    <h1
                        className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 tracking-tight"
                        itemProp="headline"
                    >
                        {title}
                    </h1>
                    {subtitle && (
                        <p
                            className="text-lg sm:text-xl max-w-2xl mx-auto leading-relaxed mb-8 text-gray-100"
                            itemProp="description"
                        >
                            {subtitle}
                        </p>
                    )}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button to="/kontakt" size="lg" className="group">
                            Book gratis befaring
                            <ArrowRight className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                        <Button
                            to="/hva-vi-gjor"
                            variant="outline"
                            size="lg"
                            className="bg-white/10 backdrop-blur-sm border-white text-white hover:bg-white/20"
                        >
                            Se våre tjenester
                        </Button>
                    </div>
                </div>
            </Container>

            {/* Animated gradient overlay */}
            <div
                className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-transparent opacity-60 mix-blend-overlay"
                style={{
                    animation: "gradient 8s ease-in-out infinite",
                    backgroundSize: "200% 200%",
                }}
                aria-hidden="true"
            />

            <style jsx>{`
                @keyframes gradient {
                    0% {
                        background-position: 0% 50%;
                    }
                    50% {
                        background-position: 100% 50%;
                    }
                    100% {
                        background-position: 0% 50%;
                    }
                }
            `}</style>
        </section>
    );
};

export default Hero;
```


#### `components\(sections)\projects\ProjectCard.tsx`

```tsx
import React from 'react';
import { MapPin, Calendar } from 'lucide-react';
import { ProjectType } from '@/types';
import { cn } from '@/lib/utils';

interface ProjectCardProps {
  project: ProjectType;
  onProjectClick: (id: string) => void;
  className?: string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onProjectClick, className }) => {
  return (
    <button 
      onClick={() => onProjectClick(project.id)}
      className={cn(
        "relative w-full h-[280px] md:h-[320px] text-left",
        "transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${project.image})` }}
        aria-hidden="true"
      />
      <div 
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />

      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex flex-wrap items-center gap-2 mb-2">
          <span className="inline-flex items-center gap-1.5 bg-green-500 text-white px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium">
            {project.category}
          </span>
          <div className="flex items-center gap-1.5 text-white/90">
            <MapPin className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.location}</span>
          </div>
          <div className="flex items-center gap-1.5 text-white/90">
            <Calendar className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.completionDate}</span>
          </div>
        </div>
        
        <h3 className="text-lg sm:text-xl font-medium mb-2 text-white line-clamp-1">
          {project.title}
        </h3>
        <p className="text-gray-200 mb-3 text-sm leading-relaxed line-clamp-2">
          {project.description}
        </p>
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Se detaljer om prosjektet {project.title}
      </span>
    </button>
  );
};

export default ProjectCard;```


#### `components\(sections)\projects\ProjectsCarousel.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Container } from '@/components/ui/Container';
import ProjectCard from './ProjectCard';
import { useProjects } from '@/hooks/useProjects';

const ProjectsCarousel = () => {
  const navigate = useNavigate();
  const { projects, loading } = useProjects();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? projects.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  const handleNext = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === projects.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (loading) {
    return (
      <Container>
        <div className="h-[400px] bg-gray-100 rounded-lg animate-pulse" />
      </Container>
    );
  }

  if (!projects.length) {
    return null;
  }

  return (
    <section className="py-12">
      <Container>
        <div className="mb-8">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Våre siste <span className="text-green-500">prosjekter</span>
          </h2>
          <p className="text-gray-600 max-w-2xl">
            Se hvordan vi har hjulpet kunder i Ringerike-regionen med å skape 
            vakre og funksjonelle uterom.
          </p>
        </div>

        <div 
          className="relative group"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
        >
          <div className="overflow-hidden rounded-lg">
            <div 
              className="relative transition-transform duration-500 ease-in-out"
              style={{ 
                transform: `translateX(-${currentIndex * 100}%)`,
                display: 'flex' 
              }}
            >
              {projects.map((project) => (
                <div 
                  key={project.id} 
                  className="w-full flex-shrink-0"
                >
                  <ProjectCard 
                    project={project}
                    onProjectClick={handleProjectClick}
                  />
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={handlePrevious}
            className="absolute -left-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-left-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Forrige prosjekt"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            className="absolute -right-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-right-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Neste prosjekt"
          >
            <ArrowRight className="w-5 h-5" />
          </button>

          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {projects.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex 
                    ? 'bg-white w-4' 
                    : 'bg-white/50 hover:bg-white/70'
                }`}
                aria-label={`Gå til prosjekt ${index + 1}`}
                aria-current={index === currentIndex}
              />
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default ProjectsCarousel;```


#### `components\(sections)\seasonal-planning\SeasonalPlanning.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { Button } from '@/components/ui/Button';
import { Cloud, Sun, CloudRain, Calendar } from 'lucide-react';

const getCurrentSeason = () => {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'vår';
  if (month >= 5 && month <= 7) return 'sommer';
  if (month >= 8 && month <= 10) return 'høst';
  return 'vinter';
};

const SeasonalPlanning = () => {
  const currentSeason = getCurrentSeason();
  
  const seasons = [
    {
      name: 'Vår',
      icon: <Cloud className="w-6 h-6" />,
      services: [
        'Vårklargjøring av hager',
        'Planting og såing',
        'Beskjæring av busker og trær'
      ],
      active: currentSeason === 'vår'
    },
    {
      name: 'Sommer',
      icon: <Sun className="w-6 h-6" />,
      services: [
        'Vanningsløsninger',
        'Vedlikehold av grøntarealer',
        'Etablering av nye bed'
      ],
      active: currentSeason === 'sommer'
    },
    {
      name: 'Høst',
      icon: <CloudRain className="w-6 h-6" />,
      services: [
        'Høstklargjøring',
        'Planting av løk',
        'Beskyttelse mot frost'
      ],
      active: currentSeason === 'høst'
    }
  ];

  return (
    <section className="py-12">
      <Container>
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 text-sm text-gray-600 mb-4">
            <Calendar className="w-4 h-4" />
            <span>Nåværende sesong: {currentSeason}</span>
          </div>
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Planlegg for <span className="text-green-500">alle sesonger</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Vi tilpasser våre tjenester etter årstidene for å sikre at ditt uterom 
            er vakkert og funksjonelt året rundt.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {seasons.map((season) => (
            <div 
              key={season.name}
              className={cn(
                "p-6 rounded-lg transition-all duration-300",
                season.active 
                  ? "bg-green-50 border border-green-100" 
                  : "bg-white hover:shadow-md"
              )}
            >
              <div className="flex items-center gap-3 mb-4">
                <div className={cn(
                  "p-2 rounded-lg",
                  season.active ? "bg-green-100" : "bg-gray-100"
                )}>
                  {React.cloneElement(season.icon, {
                    className: cn(
                      "w-6 h-6",
                      season.active ? "text-green-500" : "text-gray-500"
                    )
                  })}
                </div>
                <h3 className="text-lg font-medium">{season.name}</h3>
              </div>
              
              <ul className="space-y-3 mb-6">
                {season.services.map((service, index) => (
                  <li 
                    key={index}
                    className="flex items-start gap-2 text-sm text-gray-600"
                  >
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                    {service}
                  </li>
                ))}
              </ul>

              {season.active && (
                <Button 
                  to="/kontakt"
                  variant="primary"
                  className="w-full"
                >
                  Book befaring nå
                </Button>
              )}
            </div>
          ))}
        </div>

        <div className="text-center">
          <Button 
            to="/hva-vi-gjor"
            variant="outline"
            size="lg"
          >
            Se alle våre tjenester
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default SeasonalPlanning;```


#### `components\(sections)\services\ServiceCard.tsx`

```tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { ServiceType } from '@/types';
import { cn } from '@/lib/utils';

interface ServiceCardProps {
  service: ServiceType;
  className?: string;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({ service, className }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/tjenester/${service.id}`);
  };

  return (
    <button 
      onClick={handleClick}
      className={cn(
        "group relative w-full h-[280px] overflow-hidden rounded-lg shadow-sm text-left",
        "transition-all duration-300 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div 
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${service.image})` }}
        aria-hidden="true"
      />
      <div 
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />
      
      <div className="absolute bottom-0 left-0 right-0 p-6 transform transition-transform duration-300">
        <h3 className="text-xl font-semibold text-white mb-2">
          {service.title}
        </h3>
        <p className="text-gray-200 text-sm mb-4 line-clamp-2">
          {service.description}
        </p>
        <span 
          className="inline-flex items-center gap-1.5 text-green-400 text-sm font-medium group-hover:gap-2.5"
          aria-hidden="true"
        >
          Les mer
          <ArrowRight className="w-4 h-4 transition-all duration-300" />
        </span>
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Les mer om {service.title.toLowerCase()}
      </span>
    </button>
  );
};```


#### `components\(sections)\services\ServiceFeature.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ServiceFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

export const ServiceFeature: React.FC<ServiceFeatureProps> = ({
  icon,
  title,
  description,
  className
}) => {
  return (
    <div className={cn(
      "text-center p-6 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md",
      className
    )}>
      <div className="inline-block p-3 bg-green-50 rounded-full mb-4 text-green-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
};```


#### `components\(sections)\services\Services.tsx`

```tsx
import React from 'react';
import { Container } from '@/components/ui/Container';
import { ServiceCard } from './ServiceCard';
import { ServiceFeature } from './ServiceFeature';
import { useServices } from '@/hooks/useServices';
import { Shield, MapPin, Droplet } from 'lucide-react';

const Services = () => {
  const { services, loading } = useServices();

  const features = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: 'Lokalkunnskap',
      description: 'Med base på Røyse kjenner vi Ringerikes unike terreng og jordforhold.'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Tilpassede løsninger',
      description: 'Skreddersydde løsninger for ditt uterom og lokale forhold.'
    },
    {
      icon: <Droplet className="w-6 h-6" />,
      title: 'Klimatilpasset',
      description: 'Løsninger som tåler både tørre somre og våte høstdager.'
    }
  ];

  if (loading) {
    return <ServicesSkeleton />;
  }

  return (
    <section className="py-12 bg-gray-50">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-3xl font-semibold mb-4">
            Våre <span className="text-green-500">tjenester</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Fra Tyrifjordens bredder til åsene rundt Hønefoss – vi skaper varige 
            uterom tilpasset lokale forhold.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => (
            <ServiceFeature key={index} {...feature} />
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>
      </Container>
    </section>
  );
};

const ServicesSkeleton = () => (
  <section className="py-12 bg-gray-50">
    <Container>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div 
            key={i} 
            className="h-[280px] bg-gray-200 rounded-lg animate-pulse"
          />
        ))}
      </div>
    </Container>
  </section>
);

export default Services;```


#### `components\(sections)\testimonials\Testimonials.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { Container } from '@/components/ui/Container';
import { Star, ArrowLeft, ArrowRight, Quote } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialProps {
  name: string;
  location: string;
  text: string;
  rating: number;
  projectType: string;
  image?: string;
}

const testimonials: TestimonialProps[] = [
  {
    name: "Anders Hansen",
    location: "Hønefoss",
    text: "Fantastisk arbeid med vår nye hage. Profesjonelt team som leverte over forventning. Spesielt imponert over støttemuren og beplantningen.",
    rating: 5,
    projectType: "Komplett hageprosjekt",
    image: "https://images.unsplash.com/photo-1556157382-97eda2d62296?auto=format&fit=crop&q=80"
  },
  {
    name: "Maria Olsen",
    location: "Røyse",
    text: "Veldig fornøyd med den nye innkjørselen. Belegningssteinen ble lagt perfekt, og løsningen med avrenning fungerer utmerket.",
    rating: 5,
    projectType: "Belegningsstein",
    image: "https://images.unsplash.com/photo-1552058544-f2b08422138a?auto=format&fit=crop&q=80"
  },
  {
    name: "Erik Pedersen",
    location: "Hole",
    text: "Profesjonell og punktlig gjennomføring av prosjektet. God kommunikasjon underveis og resultatet ble akkurat som vi ønsket.",
    rating: 5,
    projectType: "Støttemur og trapp"
  }
];

const Testimonial: React.FC<TestimonialProps> = ({ 
  name, 
  location, 
  text, 
  rating, 
  projectType,
  image
}) => (
  <div 
    className="relative bg-white p-6 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
    itemScope 
    itemType="http://schema.org/Review"
  >
    <div className="absolute -top-3 -left-2 text-green-500/10">
      <Quote size={48} />
    </div>
    
    <div className="relative">
      <div className="flex items-center gap-4 mb-4">
        {image ? (
          <img 
            src={image} 
            alt={name}
            className="w-12 h-12 rounded-full object-cover"
          />
        ) : (
          <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
            <span className="text-green-500 text-lg font-medium">
              {name.charAt(0)}
            </span>
          </div>
        )}
        <div>
          <p className="font-medium" itemProp="author">{name}</p>
          <p className="text-sm text-gray-500">{location}</p>
        </div>
      </div>

      <div className="flex gap-1 mb-3">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={cn(
              "w-4 h-4",
              i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
            )}
          />
        ))}
      </div>

      <p 
        className="text-gray-600 mb-4 leading-relaxed" 
        itemProp="reviewBody"
      >
        {text}
      </p>

      <div className="text-sm text-green-600" itemProp="itemReviewed">
        {projectType}
      </div>
    </div>
  </div>
);

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePrevious = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating]);

  const handleNext = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating]);

  useEffect(() => {
    const interval = setInterval(handleNext, 6000);
    return () => clearInterval(interval);
  }, [handleNext]);

  const visibleTestimonials = () => {
    const numVisible = window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
    const indices = [];
    for (let i = 0; i < numVisible; i++) {
      indices.push((currentIndex + i) % testimonials.length);
    }
    return indices.map(index => testimonials[index]);
  };

  return (
    <section className="py-16 bg-gray-50">
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Hva våre <span className="text-green-500">kunder sier</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Vi er stolte av å ha hjulpet mange fornøyde kunder i Ringerike-regionen 
            med å skape vakre og funksjonelle uterom.
          </p>
        </div>

        <div className="relative">
          <div className="overflow-hidden">
            <div 
              className="flex gap-6 transition-transform duration-500 ease-out"
              style={{ 
                transform: `translateX(-${currentIndex * (100 / visibleTestimonials().length)}%)` 
              }}
            >
              {visibleTestimonials().map((testimonial, index) => (
                <div 
                  key={index} 
                  className="w-full flex-shrink-0"
                  style={{ flex: `0 0 ${100 / visibleTestimonials().length}%` }}
                >
                  <Testimonial {...testimonial} />
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={handlePrevious}
            className="absolute -left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Forrige tilbakemelding"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <button
            onClick={handleNext}
            className="absolute -right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Neste tilbakemelding"
          >
            <ArrowRight className="w-5 h-5" />
          </button>

          <div className="flex justify-center gap-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={cn(
                  "w-2 h-2 rounded-full transition-all duration-300",
                  index === currentIndex 
                    ? "bg-green-500 w-4" 
                    : "bg-gray-300 hover:bg-gray-400"
                )}
                aria-label={`Gå til tilbakemelding ${index + 1}`}
                aria-current={index === currentIndex}
              />
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default Testimonials;```


#### `components\common\Button.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface ButtonProps {
  children: React.ReactNode;
  to?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  to, 
  onClick, 
  type = 'button',
  variant = 'primary',
  className = '',
  fullWidth = false
}) => {
  const baseStyles = 'inline-flex items-center justify-center px-6 py-2.5 rounded-md transition-all duration-300 font-medium text-sm';
  const variantStyles = {
    primary: 'bg-green-500 text-white hover:bg-green-600 shadow-sm hover:shadow',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm hover:shadow',
    outline: 'border-2 border-green-500 text-green-500 hover:bg-green-50'
  };

  const widthClass = fullWidth ? 'w-full' : '';
  const buttonClasses = `${baseStyles} ${variantStyles[variant]} ${widthClass} ${className}`;

  if (to) {
    return (
      <Link to={to} className={buttonClasses}>
        {children}
      </Link>
    );
  }

  return (
    <button
      type={type}
      onClick={onClick}
      className={buttonClasses}
    >
      {children}
    </button>
  );
};

export default Button;```


#### `components\common\Container.tsx`

```tsx
import React from 'react';
import { cn } from '../../lib/utils';

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  as?: keyof JSX.IntrinsicElements;
  padded?: boolean;
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(({
  children,
  className,
  size = 'lg',
  as: Component = 'div',
  padded = true
}, ref) => {
  const sizes = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl',
    lg: 'max-w-7xl',
    xl: 'max-w-[90rem]'
  };

  return (
    <Component
      ref={ref}
      className={cn(
        sizes[size],
        'mx-auto',
        padded && 'px-4 sm:px-6 lg:px-8',
        className
      )}
    >
      {children}
    </Component>
  );
});

Container.displayName = 'Container';

export { Container };

export default Container```


#### `components\common\Hero.tsx`

```tsx
import { Link } from "react-router-dom";
import { HeroProps } from "../../types";
import { ChevronDown } from "lucide-react";

interface EnhancedHeroProps extends HeroProps {
    location?: string;
    yearEstablished?: string;
    actionLink?: string;
    actionText?: string;
}

const Hero: React.FC<EnhancedHeroProps> = ({
    title,
    subtitle,
    // backgroundImage = '/images/site/hero-grass.webp',
    // backgroundImage = '/images/site/hero-grass2.webp',
    // backgroundImage = '/images/site/hero-illustrative.webp',
    // backgroundImage = '/images/site/hero-corten-steel.webp',
    backgroundImage = "/images/site/hero-main.webp",
    location = "Røyse, Hole kommune",
    yearEstablished = "2015",
    actionLink,
    actionText = "Se våre prosjekter",
}) => {
    const scrollToProjects = () => {
        const projectsSection = document.getElementById("recent-projects");
        if (projectsSection) {
            projectsSection.scrollIntoView({ behavior: "smooth" });
        }
    };

    return (
        <section
            className="relative h-[calc(100vh-64px)] min-h-[600px] max-h-[800px] overflow-hidden"
            itemScope
            itemType="http://schema.org/LandscapingService"
        >
            {/* Background image with parallax effect */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Center square gradient overlay */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
            linear-gradient(
              47deg,
              transparent 12%,
              rgba(0,0,0,0.8) 30%,
              rgba(0,0,0,0.65) 75%,
              transparent 87%
            )
          `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 80%, rgba(0,0,0,0.9) 100%)",
                }}
                aria-hidden="true"
            />

            {/* Content */}
            <div className="relative h-full flex flex-col justify-center items-center text-center text-white px-4 sm:px-6 z-10">
                <div className="max-w-4xl">
                    {/* Main heading with enhanced typography */}
                    <h1
                        className="text-5xl sm:text-6xl md:text-7xl font-bold mb-8 sm:mb-10 tracking-tight
                       text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-200
                       animate-fadeInUp"
                        itemProp="name"
                    >
                        {title}
                    </h1>

                    {/* Subtitle with improved readability */}
                    {subtitle && (
                        <p
                            className="text-xl sm:text-2xl md:text-3xl max-w-3xl mx-auto leading-relaxed
                         font-light text-white animate-fadeInUp delay-100"
                            itemProp="description"
                        >
                            {subtitle}
                        </p>
                    )}

                    {/* Call to action */}
                    <div className="mt-16 animate-fadeInUp delay-200 flex flex-col items-center gap-8">
                        {actionText &&
                            (actionLink ? (
                                <Link
                                    to={actionLink}
                                    className="group inline-flex flex-col items-center gap-3 transition-transform hover:translate-y-1"
                                    aria-label={actionText}
                                >
                                    <span className="text-base sm:text-lg font-medium text-gray-200">
                                        {actionText}
                                    </span>
                                    <ChevronDown className="w-7 h-7 text-green-400 animate-bounce" />
                                </Link>
                            ) : (
                                <button
                                    onClick={scrollToProjects}
                                    className="group inline-flex flex-col items-center gap-3 transition-transform hover:translate-y-1"
                                    aria-label={actionText}
                                >
                                    <span className="text-base sm:text-lg font-medium text-gray-200">
                                        {actionText}
                                    </span>
                                    <ChevronDown className="w-7 h-7 text-green-400 animate-bounce" />
                                </button>
                            ))}

                        {/* Location badge with enhanced styling */}
                        <div className="animate-fadeIn">
                            <span
                                className="inline-flex items-center px-4 py-1.5 rounded-full bg-green-600/90 text-sm sm:text-base font-medium tracking-wide
                           shadow-lg backdrop-blur-sm transition-transform hover:scale-105"
                                itemProp="areaServed"
                            >
                                <span className="w-1.5 h-1.5 bg-white rounded-full mr-2" />
                                {location}
                            </span>
                        </div>
                    </div>

                    {/* SEO metadata with enhanced attributes */}
                    <meta itemProp="foundingDate" content={yearEstablished} />
                    <meta
                        itemProp="areaServed"
                        content="Ringerike, Hole, Jevnaker, Hønefoss"
                    />
                    <meta itemProp="priceRange" content="NOK" />
                    <meta itemProp="availableLanguage" content="nb-NO" />
                    <meta
                        itemProp="paymentAccepted"
                        content="Cash, Credit Card, Invoice"
                    />
                    <meta itemProp="openingHours" content="Mo-Fr 07:00-16:00" />
                </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />

            {/* Image attribution */}
            <div className="sr-only" role="contentinfo">
                Bilder fra våre prosjekter i Ringerike-området
            </div>
        </section>
    );
};

export default Hero;

// CSS animations should be moved to a global CSS file
```


#### `components\common\ImageGallery.tsx`

```tsx
import React, { useState } from 'react';
import { ImageFile } from '../../utils/imageLoader';
import { X } from 'lucide-react';

interface ImageGalleryProps {
  images: ImageFile[];
  columns?: number;
  gap?: number;
  className?: string;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ 
  images, 
  columns = 3, 
  gap = 4,
  className = '' 
}) => {
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);

  return (
    <>
      <div 
        className={`grid gap-${gap} ${
          columns === 2 ? 'grid-cols-1 sm:grid-cols-2' :
          columns === 3 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' :
          columns === 4 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4' :
          'grid-cols-1'
        } ${className}`}
      >
        {images.map((image, index) => (
          <div 
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={image.path}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {image.metadata?.title && (
              <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-3 translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <h4 className="text-sm font-medium">{image.metadata.title}</h4>
                {image.metadata.description && (
                  <p className="text-xs text-gray-200 mt-1">{image.metadata.description}</p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button 
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <div className="max-w-4xl w-full">
            <img
              src={selectedImage.path}
              alt={selectedImage.metadata?.title || ''}
              className="w-full h-auto rounded-lg"
            />
            {selectedImage.metadata && (
              <div className="text-white mt-4">
                <h3 className="text-lg font-medium">{selectedImage.metadata.title}</h3>
                {selectedImage.metadata.description && (
                  <p className="text-sm text-gray-300 mt-1">{selectedImage.metadata.description}</p>
                )}
                <div className="flex gap-4 mt-2 text-sm text-gray-400">
                  {selectedImage.metadata.location && (
                    <span>{selectedImage.metadata.location}</span>
                  )}
                  {selectedImage.metadata.date && (
                    <span>{selectedImage.metadata.date}</span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ImageGallery;```


#### `components\common\LocalExpertise.tsx`

```tsx
import React from 'react';
import { Map, Shield, Users } from 'lucide-react';

const LocalExpertise = () => {
  const expertise = [
    {
      icon: <Map className="w-5 h-5" />,
      title: 'Lokalkunnskap',
      description: 'Vi kjenner hver bakketopp og jordsmonn i Ringerike-regionen'
    },
    {
      icon: <Shield className="w-5 h-5" />,
      title: 'Klimatilpasset',
      description: 'Løsninger som tåler både tørre somre og våte høster'
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: 'Lokal forankring',
      description: 'Fast team med base på Røyse'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <h3 className="text-lg font-medium mb-4">
        Ekspertise i Ringerike-regionen
      </h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {expertise.map((item, index) => (
          <div 
            key={index}
            className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="text-green-500">{item.icon}</div>
            <div>
              <h4 className="font-medium text-sm mb-1">{item.title}</h4>
              <p className="text-sm text-gray-600">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LocalExpertise;```


#### `components\common\LocalServiceArea.tsx`

```tsx
import ServiceAreaList from './ServiceAreaList';

const LocalServiceArea = () => {
  return <ServiceAreaList />;
};

export default LocalServiceArea```


#### `components\common\Logo.tsx`

```tsx
import React from 'react';

interface LogoProps {
  className?: string;
  color?: string;
}

const Logo: React.FC<LogoProps> = ({ className = 'w-8 h-8', color = '#1e9545' }) => {
  return (
    <svg 
      viewBox="0 0 476.43 554.74" 
      className={className}
      aria-label="Ringerike Landskap logo"
    >
      <path
        fill={color}
        fillRule="evenodd"
        d="M182.5,274.98l46.8,81.27-23.4,46.76,42.34,11.13-17.83,42.3,53.49-57.89-52.37-5.57,113.66-214.85,71.32,131.66V90.6c0-23.59-19.15-42.72-42.77-42.72H102.69c-23.62,0-42.77,19.12-42.77,42.72v384.27c0,.52.02,1.04.04,1.56l122.54-201.45Z"
      />
    </svg>
  );
};

export default Logo```


#### `components\common\SeasonalCTA.tsx`

```tsx
import React from 'react';
import { Calendar, Snowflake } from 'lucide-react';

const SeasonalCTA = () => {
  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'vår';
    if (month >= 5 && month <= 7) return 'sommer';
    if (month >= 8 && month <= 10) return 'høst';
    return 'vinter';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sm:p-5">
      <div className="flex items-center gap-2 mb-4">
        <Snowflake className="w-5 h-5 text-blue-500" />
        <div className="flex items-center gap-1.5 text-sm">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span>Sesong: {getCurrentSeason()}</span>
        </div>
      </div>

      <h3 className="text-lg font-medium mb-3">
        Planlegg for Ringerike-våren
      </h3>
      
      <p className="text-sm text-gray-600 mb-4">
        Vinteren er perfekt for å planlegge neste sesongs hageprosjekter. 
        Vi har inngående kjennskap til lokale forhold i Ringerike.
      </p>

      <div className="space-y-3 mb-4">
        <h4 className="text-sm font-medium">Aktuelle tjenester:</h4>
        <ul className="space-y-2">
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Lokaltilpasset planlegging
          </li>
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Terrengvurdering
          </li>
          <li className="flex items-center gap-2 text-sm text-gray-600">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
            Klimatilpasset design
          </li>
        </ul>
      </div>

      <button className="w-full bg-green-500 text-white text-sm px-4 py-2.5 rounded-md hover:bg-green-600 transition-colors">
        Book gratis befaring i Ringerike
      </button>
    </div>
  );
};

export default SeasonalCTA;```


#### `components\common\ServiceAreaList.tsx`

```tsx
import { MapPin } from 'lucide-react';
import { SERVICE_AREAS } from '../../lib/constants';
import { ServiceArea } from '../../types';

interface ServiceAreaListProps {
  variant?: 'compact' | 'default';
  title?: string;
  showDescription?: boolean;
  className?: string;
  footerText?: string;
  areas?: ServiceArea[];
}

/**
 * A reusable component for displaying service areas
 * Can be used in different contexts with different styling variants
 */
const ServiceAreaList = ({
  variant = 'default',
  title = 'Vårt dekningsområde',
  showDescription = false,
  className = '',
  footerText = 'Med base på Røyse betjener vi hele Ringerike-regionen med lokalkunnskap og personlig service',
  areas = SERVICE_AREAS
}: ServiceAreaListProps) => {
  const isCompact = variant === 'compact';
  
  return (
    <div 
      className={`bg-white rounded-lg ${isCompact ? 'p-4' : 'shadow-sm p-4 sm:p-5 h-full'} ${className}`}
      itemScope 
      itemType="http://schema.org/LocalBusiness"
      role="region"
      aria-label="Service area information"
    >
      <div className="flex items-center gap-2 mb-4">
        <MapPin className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'} text-green-500`} />
        <h3 className={`${isCompact ? 'text-base' : 'text-lg'} font-medium tracking-tight`}>
          {title}
        </h3>
      </div>
      
      <div className="space-y-2">
        {areas.map((area) => (
          <div 
            key={area.city}
            className={`flex items-center justify-between p-2.5 rounded-md transition-colors duration-200 ${
              area.isBase 
                ? 'bg-green-50 border border-green-100' 
                : 'hover:bg-gray-50 hover:shadow-sm'
            }`}
          >
            <div className="flex items-center gap-2">
              {area.isBase && (
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
              )}
              <span className={`${isCompact ? 'text-xs' : 'text-sm'} ${area.isBase ? 'font-medium' : ''}`}>
                {area.city}
              </span>
            </div>
            <div className="flex flex-col items-end">
              <span className={`${isCompact ? 'text-xs' : 'text-sm'} text-gray-600`}>
                {area.distance}
              </span>
              {showDescription && area.description && (
                <span className="text-xs text-gray-500 mt-0.5">{area.description}</span>
              )}
            </div>
          </div>
        ))}
      </div>

      {footerText && (
        <p className="mt-4 text-sm text-gray-600 leading-relaxed">
          {footerText}
        </p>
      )}
    </div>
  );
};

export default ServiceAreaList; ```


#### `components\common\WeatherAdaptedServices.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const WeatherAdaptedServices = () => {
  const seasons = [
    {
      name: 'Vår',
      icon: <Cloud className="w-6 h-6" />,
      services: [
        'Vårklargjøring tilpasset Ringerikes klima',
        'Drenering for lokalt jordsmonn',
        'Tidlig plantesesong'
      ]
    },
    {
      name: 'Sommer',
      icon: <Sun className="w-6 h-6" />,
      services: [
        'Vanningsløsninger for tørre perioder',
        'Skyggeplanlegging med lokale arter',
        'Vedlikehold av grøntarealer'
      ]
    },
    {
      name: 'Høst',
      icon: <CloudRain className="w-6 h-6" />,
      services: [
        'Høstklargjøring for Ringerikes vinter',
        'Drenering og fallsikring',
        'Beskjæring av lokale arter'
      ]
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-xl font-semibold mb-6">
        Tilpasset Ringerikes årstider
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {seasons.map((season) => (
          <div 
            key={season.name}
            className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className="text-green-500">{season.icon}</div>
              <h4 className="font-medium">{season.name}</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              {season.services.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                  {service}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WeatherAdaptedServices;```


#### `components\contact\ContactForm.tsx`

```tsx
import React, { useState } from 'react';

interface FormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  description?: string;
}

const ContactForm = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: ''
  });
  
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<FormErrors>({});

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'name':
        return value.trim().length < 2 ? 'Navn må være minst 2 tegn' : '';
      case 'email':
        return !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Ugyldig e-postadresse' : '';
      case 'phone':
        return !/^(\+47)?[2-9]\d{7}$/.test(value.replace(/\s/g, '')) ? 'Ugyldig telefonnummer (8 siffer)' : '';
      case 'address':
        return value.trim().length < 5 ? 'Vennligst skriv inn full adresse' : '';
      case 'description':
        return value.trim().length < 10 ? 'Beskrivelsen må være minst 10 tegn' : '';
      default:
        return '';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Validate on change if field has been touched
    if (touched[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: validateField(name, value)
      }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({
      ...prev,
      [name]: validateField(name, value)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: FormErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key as keyof FormErrors] = error;
      }
    });
    
    // Mark all fields as touched
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    setErrors(newErrors);

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      console.log('Form submitted:', formData);
    }
  };

  const getInputClassName = (fieldName: keyof FormData) => {
    const baseClasses = "w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent";
    return `${baseClasses} ${
      touched[fieldName] && errors[fieldName]
        ? "border-red-500 text-red-900 placeholder-red-300"
        : "border-gray-300"
    }`;
  };

  return (
    <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8">
      <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
        Kom i kontakt <span className="text-green-500">med oss</span>
      </h2>
      <p className="text-gray-600 mb-8">
        Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
      </p>
      <form onSubmit={handleSubmit} className="space-y-4" noValidate>
        <div className="space-y-1">
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Navn *"
            className={getInputClassName('name')}
            required
          />
          {touched.name && errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="E-post *"
            className={getInputClassName('email')}
            required
          />
          {touched.email && errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Telefonnummer *"
            className={getInputClassName('phone')}
            required
          />
          {touched.phone && errors.phone && (
            <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <input
            type="text"
            name="address"
            value={formData.address}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Adresse *"
            className={getInputClassName('address')}
            required
          />
          {touched.address && errors.address && (
            <p className="text-red-500 text-sm mt-1">{errors.address}</p>
          )}
        </div>
        
        <div className="space-y-1">
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Beskriv prosjektet ditt *"
            rows={4}
            className={getInputClassName('description')}
            required
          />
          {touched.description && errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>
        
        <button
          type="submit"
          className="w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
        >
          Send melding
        </button>
      </form>
    </div>
  );
};

export default ContactForm;```


#### `components\ContactForm.tsx`

```tsx
import React from 'react';

const ContactForm = () => {
  return (
    <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8">
      <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
        Kom i kontakt <span className="text-green-500">med oss</span>
      </h2>
      <p className="text-gray-600 mb-8">
        Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
      </p>
      <form className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Navn"
            className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
          <input
            type="text"
            placeholder="Budsjett"
            className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
        <input
          type="email"
          placeholder="E-post"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <input
          type="tel"
          placeholder="Telefonnummer"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <input
          type="text"
          placeholder="Adresse"
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <textarea
          placeholder="Beskriv prosjektet ditt"
          rows={4}
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <button
          type="submit"
          className="w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
        >
          Send melding
        </button>
      </form>
    </div>
  );
};

export default ContactForm;```


#### `components\layout\Footer.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { MapPin, Phone, Mail, Clock, Facebook } from "lucide-react";
import { Logo } from "../ui/Logo";
import { Container } from "../ui/Container";

const Footer = () => {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gray-900 text-white pt-12 pb-6">
            <Container>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    {/* Company info */}
                    <div>
                        <div className="flex items-center gap-2 mb-4">
                            <Logo variant="full" className="text-white" />
                        </div>
                        <p className="text-gray-400 text-sm mb-4">
                            Vi skaper varige uterom tilpasset Ringerikes unike
                            terreng og klima. Med base på Røyse betjener vi hele
                            regionen med kvalitetsarbeid og personlig service.
                        </p>
                        <div className="flex items-center gap-2">
                            <Facebook className="w-5 h-5 text-gray-400" />
                            <a
                                href="https://www.facebook.com/ringerikelandskap"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Følg oss på Facebook
                            </a>
                        </div>
                    </div>

                    {/* Contact info */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Kontakt oss
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <MapPin className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p>Birchs vei 7</p>
                                    <p>3530 Røyse</p>
                                </div>
                            </li>
                            <li className="flex items-center gap-3">
                                <Phone className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href="tel:+4790214153"
                                    className="hover:text-green-400"
                                >
                                    +47 902 14 153
                                </a>
                            </li>
                            <li className="flex items-center gap-3">
                                <Mail className="w-5 h-5 text-green-500 flex-shrink-0" />
                                <a
                                    href="mailto:<EMAIL>"
                                    className="hover:text-green-400"
                                >
                                    <EMAIL>
                                </a>
                            </li>
                        </ul>
                    </div>

                    {/* Opening hours */}
                    <div>
                        <h3 className="text-lg font-semibold mb-4">
                            Åpningstider
                        </h3>
                        <ul className="space-y-3">
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Mandag - Fredag:
                                    </p>
                                    <p>07:00-16:00</p>
                                </div>
                            </li>
                            <li className="flex items-start gap-3">
                                <Clock className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p className="font-medium">
                                        Lørdag - Søndag:
                                    </p>
                                    <p>Stengt</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                {/* Quick links */}
                <div className="border-t border-gray-800 pt-8 pb-4">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex flex-wrap justify-center md:justify-start gap-6 mb-4 md:mb-0">
                            <Link
                                to="/"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hjem
                            </Link>
                            <Link
                                to="/hva-vi-gjor"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hva vi gjør
                            </Link>
                            <Link
                                to="/prosjekter"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Prosjekter
                            </Link>
                            <Link
                                to="/hvem-er-vi"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Hvem er vi
                            </Link>
                            <Link
                                to="/kontakt"
                                className="text-gray-400 hover:text-white text-sm"
                            >
                                Kontakt oss
                            </Link>
                        </div>
                        <div className="text-gray-500 text-sm">
                            © {currentYear} Ringerike Landskap AS. Org.nr: 123
                            456 789
                        </div>
                    </div>
                </div>

                {/* Privacy links */}
                <div className="flex justify-center md:justify-end mt-4 gap-4">
                    <Link
                        to="/personvern"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Personvern
                    </Link>
                    <Link
                        to="/cookies"
                        className="text-gray-500 hover:text-gray-400 text-xs"
                    >
                        Cookies
                    </Link>
                </div>
            </Container>
        </footer>
    );
};

export default Footer;```


#### `components\layout\Header.tsx`

```tsx
import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { Logo } from "../ui/Logo";
import { Container } from "../ui/Container";

const Header = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const location = useLocation();

    const navigation = [
        { name: "Hjem", href: "/" },
        { name: "Hva vi gjør", href: "/hva-vi-gjor" },
        { name: "Prosjekter", href: "/prosjekter" },
        { name: "Hvem er vi", href: "/hvem-er-vi" },
    ];

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    // Close mobile menu when route changes
    useEffect(() => {
        setIsMenuOpen(false);
    }, [location.pathname]);

    return (
        <header
            className={`sticky top-0 z-50 w-full transition-all duration-200 ${
                isScrolled
                    ? "bg-white shadow-md"
                    : "bg-white/90 backdrop-blur-sm"
            }`}
        >
            <Container>
                <div className="flex h-16 items-center justify-between">
                    <Link
                        to="/"
                        className="flex items-center"
                        aria-label="Ringerike Landskap"
                    >
                        <Logo
                            variant="full"
                            className={
                                isScrolled ? "text-gray-900" : "text-gray-900"
                            }
                        />
                    </Link>

                    {/* Desktop navigation */}
                    <nav className="hidden md:flex md:items-center md:space-x-8">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`text-sm font-medium transition-colors hover:text-green-600 ${
                                    location.pathname === item.href
                                        ? "text-green-600"
                                        : "text-gray-700"
                                }`}
                            >
                                {item.name}
                            </Link>
                        ))}
                        <Link
                            to="/kontakt"
                            className="rounded-md bg-green-500 px-4 py-2 text-sm font-medium text-white hover:bg-green-600 transition-colors"
                        >
                            Kontakt
                        </Link>
                    </nav>

                    {/* Mobile menu button */}
                    <button
                        type="button"
                        className="md:hidden rounded-md p-2 text-gray-700"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                        aria-expanded={isMenuOpen}
                        aria-controls="mobile-menu"
                    >
                        <span className="sr-only">
                            {isMenuOpen ? "Lukk meny" : "Åpne meny"}
                        </span>
                        {isMenuOpen ? (
                            <X className="h-6 w-6" />
                        ) : (
                            <Menu className="h-6 w-6" />
                        )}
                    </button>
                </div>
            </Container>

            {/* Mobile menu */}
            {isMenuOpen && (
                <div className="md:hidden" id="mobile-menu">
                    <div className="space-y-1 px-4 pb-3 pt-2">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`block rounded-md px-3 py-2 text-base font-medium ${
                                    location.pathname === item.href
                                        ? "bg-green-50 text-green-600"
                                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                }`}
                            >
                                {item.name}
                            </Link>
                        ))}
                        <Link
                            to="/kontakt"
                            className="block w-full rounded-md bg-green-500 px-3 py-2 text-center text-base font-medium text-white hover:bg-green-600 mt-4"
                        >
                            Kontakt
                        </Link>
                    </div>
                </div>
            )}
        </header>
    );
};

export default Header;```


#### `components\layout\Hero.tsx`

```tsx
import React from "react";
import { HeroProps } from "@/lib/types";
import { Container } from "@/components/ui/Container";
import { Button } from "@/components/ui/Button";
import { ArrowRight } from "lucide-react";

const Hero: React.FC<HeroProps> = ({
    title = "Anleggsgartner i Ringerike",
    subtitle = "Med base på Røyse skaper vi varige uterom tilpasset Ringerikes unike terreng og klima.",
    backgroundImage = "/images/categorized/hero-main.webp",
}) => {
    return (
        <section
            className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
            style={{
                backgroundImage: `url(${backgroundImage})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
            }}
            role="banner"
            aria-label="Hero section"
        >
            {/* Center square gradient overlay */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
          linear-gradient(
            45deg,
            transparent 14%,
            rgba(0,0,0,0.78) 33%,
            rgba(0,0,0,0.62) 70%,
            transparent 88%
          )
        `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 82%, rgba(0,0,0,0.88) 100%)",
                }}
                aria-hidden="true"
            />

            {/* Rest of the component remains the same */}
        </section>
    );
};

export default Hero;
```


#### `components\layout\Layout.tsx`

```tsx
import React from "react";
import Navbar from "./Navbar";
import Footer from "./Footer";

interface LayoutProps {
    children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    return (
        <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-grow">{children}</main>
            <Footer />
        </div>
    );
};

export default Layout;
```


#### `components\layout\Meta.tsx`

```tsx
import React from 'react';
import { Helmet } from 'react-helmet';
import { SITE_CONFIG, CONTACT_INFO, LOCAL_INFO } from '@/lib/constants';

interface MetaProps {
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
  schema?: Record<string, unknown>;
  keywords?: string[];
  location?: {
    city: string;
    region?: string;
  };
}

export const Meta: React.FC<MetaProps> = ({
  title,
  description = SITE_CONFIG.description,
  image = SITE_CONFIG.ogImage,
  type = 'website',
  noindex = false,
  schema,
  keywords = SITE_CONFIG.keywords,
  location
}) => {
  const fullTitle = title 
    ? `${title} | ${SITE_CONFIG.name}`
    : location
    ? `Anleggsgartner i ${location.city} | ${SITE_CONFIG.name}`
    : SITE_CONFIG.name;

  const localDescription = location
    ? `Din lokale anleggsgartner i ${location.city}. Vi har inngående kjennskap til lokale forhold og leverer skreddersydde løsninger for ditt uterom.`
    : description;

  const baseSchema = {
    "@context": "https://schema.org",
    "@type": "LandscapingBusiness",
    "name": SITE_CONFIG.name,
    "image": SITE_CONFIG.ogImage,
    "description": localDescription,
    "@id": SITE_CONFIG.url,
    "url": SITE_CONFIG.url,
    "telephone": SITE_CONFIG.phone,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": CONTACT_INFO.address.street,
      "addressLocality": CONTACT_INFO.address.city,
      "postalCode": CONTACT_INFO.address.postalCode,
      "addressRegion": CONTACT_INFO.address.county,
      "addressCountry": "NO"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 60.0558,
      "longitude": 10.2558
    },
    "areaServed": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": 60.0558,
        "longitude": 10.2558
      },
      "geoRadius": "25000"
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday"
      ],
      "opens": "07:00",
      "closes": "16:00"
    },
    "priceRange": "$$",
    "sameAs": [
      CONTACT_INFO.social.facebook
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Anleggsgartnertjenester i Hole og Ringerike",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Belegningsstein",
            "description": "Profesjonell legging av belegningsstein tilpasset lokale forhold"
          }
        },
        {
        }
      ]
    }
  }
}```


#### `components\layout\Navbar.tsx`

```tsx
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { Logo } from "@/components/ui/Logo";

const Navbar = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    return (
        <nav className="relative bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                    <Link to="/" className="flex items-center">
                        <Logo variant="full" className="text-gray-900" />
                    </Link>

                    {/* Mobile menu button */}
                    <div className="flex items-center sm:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="text-gray-600 hover:text-gray-900"
                        >
                            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
                        </button>
                    </div>

                    {/* Desktop menu */}
                    <div className="hidden sm:flex sm:items-center sm:gap-8">
                        <Link to="/" className="hover:text-green-600">
                            Hjem
                        </Link>
                        <Link to="/hvem-er-vi" className="hover:text-green-600">
                            Hvem er vi
                        </Link>
                        <Link
                            to="/hva-vi-gjor"
                            className="hover:text-green-600"
                        >
                            Hva vi gjør
                        </Link>
                        <Link to="/prosjekter" className="hover:text-green-600">
                            Prosjekter
                        </Link>
                        <Link
                            to="/kontakt"
                            className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600"
                        >
                            Kontakt
                        </Link>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            <div className={`sm:hidden ${isMenuOpen ? "block" : "hidden"}`}>
                <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                    <Link
                        to="/"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hjem
                    </Link>
                    <Link
                        to="/hvem-er-vi"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hvem er vi
                    </Link>
                    <Link
                        to="/hva-vi-gjor"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hva vi gjør
                    </Link>
                    <Link
                        to="/prosjekter"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Prosjekter
                    </Link>
                    <Link
                        to="/kontakt"
                        className="block px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Kontakt
                    </Link>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;
```


#### `components\local\SeasonalGuide.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const seasons = [
  {
    name: 'Vår',
    icon: <Cloud className="w-6 h-6" />,
    services: [
      'Vårklargjøring tilpasset Ringerikes klima',
      'Drenering for lokalt jordsmonn',
      'Tidlig plantesesong'
    ]
  },
  {
    name: 'Sommer',
    icon: <Sun className="w-6 h-6" />,
    services: [
      'Vanningsløsninger for tørre perioder',
      'Skyggeplanlegging med lokale arter',
      'Vedlikehold av grøntarealer'
    ]
  },
  {
    name: 'Høst',
    icon: <CloudRain className="w-6 h-6" />,
    services: [
      'Høstklargjøring for Ringerikes vinter',
      'Drenering og fallsikring',
      'Beskjæring av lokale arter'
    ]
  }
];

const SeasonalGuide = () => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-xl font-semibold mb-6">
        Tilpasset Ringerikes årstider
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {seasons.map((season) => (
          <div 
            key={season.name}
            className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className="text-green-500">{season.icon}</div>
              <h4 className="font-medium">{season.name}</h4>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              {season.services.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5" />
                  {service}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SeasonalGuide;```


#### `components\local\ServiceAreaMap.tsx`

```tsx
import ServiceAreaList from '../common/ServiceAreaList';

const ServiceAreaMap = () => {
  return (
    <ServiceAreaList 
      title="DekningsomrÃ¥de"
      showDescription={true}
      className="shadow-lg p-6"
      footerText="Vi tar oppdrag i hele Ringerike-regionen og tilpasser oss dine behov."
    />
  );
};

export default ServiceAreaMap;```


#### `components\local\WeatherNotice.tsx`

```tsx
import React from 'react';
import { Cloud, Sun, CloudRain } from 'lucide-react';

const WeatherNotice = () => {
  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'vår';
    if (month >= 5 && month <= 7) return 'sommer';
    return 'høst';
  };

  const getWeatherInfo = () => {
    const season = getCurrentSeason();
    switch (season) {
      case 'vår':
        return {
          icon: <Cloud className="w-8 h-8 text-blue-500" />,
          title: 'Vårklargjøring i Ringerike',
          description: 'Nå er det perfekt tid for planlegging av sommerens hageprosjekter. Vi kjenner de lokale forholdene og vet hva som skal til.'
        };
      case 'sommer':
        return {
          icon: <Sun className="w-8 h-8 text-yellow-500" />,
          title: 'Sommertid i Ringerike',
          description: 'Perfekt tid for etablering av nye uteområder. Vi har løsninger som tåler både sol og regn.'
        };
      default:
        return {
          icon: <CloudRain className="w-8 h-8 text-gray-500" />,
          title: 'Høstsesongen er her',
          description: 'La oss hjelpe deg med å forberede hagen for vinteren. Vi vet hva som kreves i Ringerike-klimaet.'
        };
    }
  };

  const weather = getWeatherInfo();

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center gap-4 mb-4">
        {weather.icon}
        <h3 className="text-xl font-semibold">{weather.title}</h3>
      </div>
      <p className="text-gray-600">{weather.description}</p>
    </div>
  );
};

export default WeatherNotice;```


#### `components\Navbar.tsx`

```tsx
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { Leaf, Menu, X } from "lucide-react";

const Navbar = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    return (
        <nav className="relative bg-white shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                    <Link to="/" className="flex items-center gap-2">
                        <Leaf className="w-8 h-8 text-green-600" />
                        <span className="text-xl font-semibold hidden sm:block">
                            RINGERIKE LANDSKAP
                        </span>
                    </Link>

                    {/* Mobile menu button */}
                    <div className="flex items-center sm:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="text-gray-600 hover:text-gray-900"
                        >
                            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
                        </button>
                    </div>

                    {/* Desktop menu */}
                    <div className="hidden sm:flex sm:items-center sm:gap-8">
                        <Link to="/" className="hover:text-green-600">
                            Hjem
                        </Link>
                        <Link to="/hvem-er-vi" className="hover:text-green-600">
                            Hvem er vi
                        </Link>
                        <Link
                            to="/hva-vi-gjor"
                            className="hover:text-green-600"
                        >
                            Hva vi gjør
                        </Link>
                        <Link
                            to="/kontakt"
                            className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600"
                        >
                            Kontakt
                        </Link>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            <div className={`sm:hidden ${isMenuOpen ? "block" : "hidden"}`}>
                <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                    <Link
                        to="/"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hjem
                    </Link>
                    <Link
                        to="/hvem-er-vi"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hvem er vi
                    </Link>
                    <Link
                        to="/hva-vi-gjor"
                        className="block px-3 py-2 rounded-md hover:bg-green-50 hover:text-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Hva vi gjør
                    </Link>
                    <Link
                        to="/kontakt"
                        className="block px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                        onClick={() => setIsMenuOpen(false)}
                    >
                        Kontakt
                    </Link>
                </div>
            </div>
        </nav>
    );
};

export default Navbar;
```


#### `components\projects\ProjectCard.tsx`

```tsx
import React, { useState } from 'react';
import { MapPin, Tag, ChevronRight, Star, Ruler, Clock, Leaf, Droplets, Mountain } from 'lucide-react';
import { ProjectCardProps } from '../../types';
import { Link } from 'react-router-dom';

const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  variant = 'default',
  showTestimonial = false 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const cardVariants = {
    default: {
      width: '100%',
      height: variant === 'compact' ? '320px' : '400px',
    },
    featured: {
      width: '100%',
      height: '480px',
    },
    compact: {
      width: '100%',
      height: '320px',
    }
  };

  // Personalized location descriptions that feel natural and local
  const getLocationContext = (location: string) => {
    const localContext = {
      'Røyse': {
        prefix: 'Her på',
        description: 'med utsikt over Tyrifjorden'
      },
      'Hole': {
        prefix: 'I hjertet av',
        description: 'mellom åser og vann'
      },
      'Vik': {
        prefix: 'Ved',
        description: 'langs Steinsfjorden'
      },
      'Hønefoss': {
        prefix: 'I',
        description: 'ved fossens kraft'
      },
      'Sundvollen': {
        prefix: 'Ved foten av',
        description: 'med Krokkleiva i ryggen'
      },
      'Jevnaker': {
        prefix: 'På',
        description: 'mot Randsfjorden'
      }
    };

    const context = localContext[location as keyof typeof localContext] || { prefix: 'I', description: '' };
    return {
      short: `${context.prefix} ${location}`,
      full: context.description
    };
  };

  // Enhance features with local terrain context
  const getTerrainFeatures = (specs: typeof project.specifications) => {
    const features = [];
    
    if (specs.materials.some(m => m.includes('naturstein'))) {
      features.push('Tilpasset terrenget');
    }
    if (specs.features.some(f => f.includes('drenering'))) {
      features.push('Lokal overvannshåndtering');
    }
    if (specs.features.some(f => f.includes('vanning'))) {
      features.push('Klimasmart løsning');
    }
    
    return features;
  };

  const locationContext = getLocationContext(project.location);
  const terrainFeatures = getTerrainFeatures(project.specifications);

  return (
    <article 
      className={`
        group relative rounded-xl overflow-hidden bg-white 
        shadow-lg hover:shadow-xl transition-all duration-500
        ${variant === 'featured' ? 'lg:col-span-2 lg:row-span-2' : ''}
      `}
      style={cardVariants[variant]}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      itemScope 
      itemType="http://schema.org/LandscapeProject"
    >
      {/* Image Container with Local Context */}
      <div className="relative w-full h-full">
        <div 
          className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-105"
          style={{ backgroundImage: `url(${project.image})` }}
          role="img"
          aria-label={`${project.title} - ${locationContext.short}`}
        />
        
        {/* Enhanced Gradient Overlay */}
        <div 
          className={`
            absolute inset-0 transition-opacity duration-500
            bg-gradient-to-t from-black/90 via-black/40 to-transparent
            ${isHovered ? 'opacity-95' : 'opacity-85'}
          `}
        />

        {/* Content Container */}
        <div className="absolute inset-0 p-6 flex flex-col justify-end">
          {/* Local Context Badge */}
          <div className="flex items-center gap-2 mb-4">
            <span 
              className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full 
                         bg-green-600/90 text-white text-sm font-medium
                         backdrop-blur-sm shadow-lg transform transition-all
                         group-hover:translate-y-0 group-hover:opacity-100"
              itemProp="areaServed"
            >
              <MapPin className="w-4 h-4" />
              {locationContext.short}
            </span>
            {locationContext.full && (
              <span className="text-xs text-gray-300 italic">
                {locationContext.full}
              </span>
            )}
          </div>

          {/* Project Content */}
          <div className="transform transition-all duration-300 group-hover:translate-y-0">
            <h3 
              className="text-xl sm:text-2xl font-bold text-white mb-2"
              itemProp="name"
            >
              {project.title}
            </h3>
            
            <p 
              className="text-gray-200 text-sm mb-4"
              itemProp="description"
            >
              {project.description}
            </p>

            {/* Project Details */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="flex items-center gap-2 text-gray-300">
                <Ruler className="w-4 h-4" />
                <span className="text-sm">{project.specifications.size}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-300">
                <Clock className="w-4 h-4" />
                <span className="text-sm">{project.specifications.duration}</span>
              </div>
            </div>

            {/* Terrain Features */}
            {terrainFeatures.length > 0 && (
              <div className="flex flex-wrap gap-3 mb-4">
                {terrainFeatures.map((feature, index) => (
                  <span 
                    key={index}
                    className="inline-flex items-center gap-1.5 text-xs text-green-300"
                  >
                    {feature.includes('terreng') ? (
                      <Mountain className="w-3 h-3" />
                    ) : feature.includes('vann') ? (
                      <Droplets className="w-3 h-3" />
                    ) : (
                      <Leaf className="w-3 h-3" />
                    )}
                    {feature}
                  </span>
                ))}
              </div>
            )}

            {/* Project Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {project.tags.slice(0, 3).map((tag, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center gap-1 px-2 py-1 
                           rounded-full bg-white/10 text-white text-xs
                           transition-all duration-300 hover:bg-white/20"
                >
                  <Tag className="w-3 h-3" />
                  {tag}
                </span>
              ))}
            </div>

            {/* Local Testimonial */}
            {showTestimonial && isHovered && (
              <div 
                className="mt-4 p-3 rounded-lg bg-black/30 backdrop-blur-sm
                         transform transition-all duration-300"
              >
                <div className="flex items-start gap-2 text-white">
                  <Star className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-1" />
                  <div>
                    <p className="text-sm italic mb-1">{project.testimonial.quote}</p>
                    <p className="text-xs text-gray-400">{project.testimonial.author}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Project Link */}
            <Link
              to={`/prosjekter/${project.id}`}
              className="inline-flex items-center gap-2 mt-4 text-white 
                       hover:text-green-400 transition-colors group/link"
            >
              <span className="text-sm font-medium">Utforsk prosjektet</span>
              <ChevronRight className="w-4 h-4 transform transition-transform 
                                   group-hover/link:translate-x-1" />
            </Link>
          </div>
        </div>
      </div>

      {/* Rich Local SEO Metadata */}
      <meta itemProp="dateCompleted" content={project.completionDate} />
      <meta itemProp="keywords" content={`${project.tags.join(', ')}, ${project.location}, landskapsarkitektur, hagedesign`} />
      <meta itemProp="locationCreated" content={`${project.location}, Ringerike, Buskerud`} />
      <meta itemProp="material" content={project.specifications.materials.join(', ')} />
      <meta itemProp="description" content={`${project.description} ${locationContext.short} ${locationContext.full}`} />
      <meta itemProp="creator" content="Ringerike Landskap - Din lokale anleggsgartner" />
    </article>
  );
};

export default ProjectCard; ```


#### `components\projects\ProjectFilter.tsx`

```tsx
import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Tag, Grid3X3 } from 'lucide-react';
import { ProjectFilterProps } from '../../types';

const ProjectFilter: React.FC<ProjectFilterProps> = ({
  categories,
  locations,
  tags,
  selectedFilters,
  onFilterChange
}) => {
  const FilterButton = ({ 
    type, 
    value, 
    isSelected 
  }: { 
    type: 'category' | 'location' | 'tag';
    value: string;
    isSelected: boolean;
  }) => (
    <motion.button
      onClick={() => onFilterChange(type, value)}
      className={`
        inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium
        transition-all duration-200 hover:shadow-md
        ${isSelected 
          ? 'bg-green-500 text-white shadow-lg scale-105' 
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }
      `}
      whileHover={{ scale: isSelected ? 1.05 : 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {type === 'location' && <MapPin className="w-4 h-4 mr-1.5" />}
      {type === 'tag' && <Tag className="w-4 h-4 mr-1.5" />}
      {type === 'category' && <Grid3X3 className="w-4 h-4 mr-1.5" />}
      {value}
    </motion.button>
  );

  return (
    <div className="space-y-6">
      {/* Categories */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Tjenester</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <FilterButton
              key={category}
              type="category"
              value={category}
              isSelected={selectedFilters.category === category}
            />
          ))}
        </div>
      </div>

      {/* Locations */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Områder</h3>
        <div className="flex flex-wrap gap-2">
          {locations.map(location => (
            <FilterButton
              key={location}
              type="location"
              value={location}
              isSelected={selectedFilters.location === location}
            />
          ))}
        </div>
      </div>

      {/* Tags */}
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-gray-500">Nøkkelord</h3>
        <div className="flex flex-wrap gap-2">
          {tags.map(tag => (
            <FilterButton
              key={tag}
              type="tag"
              value={tag}
              isSelected={selectedFilters.tag === tag}
            />
          ))}
        </div>
      </div>

      {/* Active Filters Summary - Screen Reader Only */}
      <div className="sr-only" role="status">
        {Object.entries(selectedFilters).map(([type, value]) => (
          value && (
            <span key={type}>
              Filtrerer etter {type}: {value}.
            </span>
          )
        ))}
      </div>
    </div>
  );
};

export default ProjectFilter; ```


#### `components\projects\ProjectGallery.tsx`

```tsx
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { cn } from '../../lib/utils';
import { IMAGE_CATEGORIES, getImagesFromCategory } from '../../utils/imageLoader';

interface ProjectGalleryProps {
  category?: keyof typeof IMAGE_CATEGORIES;
  className?: string;
}

const ProjectGallery: React.FC<ProjectGalleryProps> = ({
  category,
  className
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  
  // Get images for the category
  const images = category ? getImagesFromCategory(category) : [];

  if (!images.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Ingen bilder tilgjengelig for denne kategorien.</p>
      </div>
    );
  }

  return (
    <>
      <div className={cn(
        "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
        className
      )}>
        {images.map((image, index) => (
          <button
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image.path)}
          >
            <img
              src={image.path}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-sm font-medium">
                  {image.metadata?.title}
                </h3>
                {image.metadata?.description && (
                  <p className="text-xs mt-1 text-gray-200">
                    {image.metadata.description}
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <img
            src={selectedImage}
            alt="Project detail"
            className="max-w-full max-h-[90vh] rounded-lg"
          />
        </div>
      )}
    </>
  );
};

export default ProjectGallery;```


#### `components\projects\ProjectGrid.tsx`

```tsx
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProjectGridProps } from '../../types';
import ProjectCard from './ProjectCard';
import ProjectFilter from './ProjectFilter';

const ProjectGrid: React.FC<ProjectGridProps> = ({ 
  projects, 
  filter,
  layout = 'grid' 
}) => {
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [selectedFilters, setSelectedFilters] = useState(filter || {});

  // Extract unique values for filter options
  const categories = [...new Set(projects.map(p => p.category))];
  const locations = [...new Set(projects.map(p => p.location))];
  const tags = [...new Set(projects.flatMap(p => p.tags))];

  useEffect(() => {
    let result = [...projects];

    if (selectedFilters.category) {
      result = result.filter(p => p.category === selectedFilters.category);
    }
    if (selectedFilters.location) {
      result = result.filter(p => p.location === selectedFilters.location);
    }
    if (selectedFilters.tag) {
      result = result.filter(p => p.tags.includes(selectedFilters.tag));
    }

    setFilteredProjects(result);
  }, [projects, selectedFilters]);

  const handleFilterChange = (type: 'category' | 'location' | 'tag', value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [type]: prev[type] === value ? undefined : value
    }));
  };

  const gridLayouts = {
    grid: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",
    masonry: "columns-1 sm:columns-2 lg:columns-3 gap-6 sm:gap-8 [&>*]:mb-6 sm:[&>*]:mb-8",
    carousel: "flex gap-6 overflow-x-auto snap-x snap-mandatory pb-6 [&>*]:snap-center [&>*]:flex-shrink-0 [&>*]:w-[85vw] sm:[&>*]:w-[45vw] lg:[&>*]:w-[30vw]"
  };

  return (
    <section className="w-full">
      {/* Filters */}
      <div className="mb-8">
        <ProjectFilter
          categories={categories}
          locations={locations}
          tags={tags}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {/* Projects Grid */}
      <div className={gridLayouts[layout]}>
        <AnimatePresence mode="popLayout">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <ProjectCard
                project={project}
                variant={index === 0 ? 'featured' : 'default'}
                showTestimonial={true}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredProjects.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <p className="text-gray-500 text-lg">
            Ingen prosjekter funnet med valgte filtre.
          </p>
          <button
            onClick={() => setSelectedFilters({})}
            className="mt-4 text-green-600 hover:text-green-700 font-medium"
          >
            Nullstill filtre
          </button>
        </motion.div>
      )}

      {/* SEO Enhancement */}
      <div className="sr-only" role="contentinfo">
        <h2>Våre prosjekter i {locations.join(', ')}</h2>
        <p>
          Vi tilbyr følgende tjenester: {categories.join(', ')}. 
          Spesialisert i: {tags.join(', ')}.
        </p>
      </div>
    </section>
  );
};

export default ProjectGrid; ```


#### `components\seo\TestimonialsSchema.tsx`

```tsx
import React from "react";
import { TestimonialType } from "../features/testimonials/Testimonial";

interface TestimonialsSchemaProps {
    testimonials: TestimonialType[];
    companyName?: string;
    companyUrl?: string;
}

const TestimonialsSchema: React.FC<TestimonialsSchemaProps> = ({
    testimonials,
    companyName = "Ringerike Landskap",
    companyUrl = "https://ringerikelandskap.no",
}) => {
    // Calculate average rating
    const totalRating = testimonials.reduce(
        (sum, testimonial) => sum + testimonial.rating,
        0
    );
    const averageRating = totalRating / testimonials.length;

    // Create schema.org JSON-LD data
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: companyName,
        url: companyUrl,
        aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: averageRating.toFixed(1),
            reviewCount: testimonials.length,
            bestRating: "5",
            worstRating: "1",
        },
        review: testimonials.map((testimonial) => ({
            "@type": "Review",
            author: {
                "@type": "Person",
                name: testimonial.name,
            },
            reviewRating: {
                "@type": "Rating",
                ratingValue: testimonial.rating,
                bestRating: "5",
                worstRating: "1",
            },
            reviewBody: testimonial.text,
            datePublished: new Date().toISOString().split("T")[0], // Current date in YYYY-MM-DD format
        })),
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
    );
};

export default TestimonialsSchema;
```


#### `components\ServiceCard.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface ServiceCardProps {
  title: string;
  description: string;
  image: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, image }) => {
  return (
    <div className="relative h-[400px] group overflow-hidden rounded-lg">
      <div 
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        <Link 
          to="#" 
          className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;```


#### `components\services\Gallery.tsx`

```tsx
import React, { useState } from 'react';
import { GalleryImage } from '../../types';

interface GalleryProps {
  images: GalleryImage[];
}

const Gallery: React.FC<GalleryProps> = ({ images }) => {
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);

  return (
    <div>
      {/* Image grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div 
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer"
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={image.url}
              alt={image.alt}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 hover:scale-110"
            />
            {image.caption && (
              <div className="absolute inset-x-0 bottom-0 bg-black/60 text-white p-2 text-sm">
                {image.caption}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="max-w-4xl w-full">
            <img
              src={selectedImage.url}
              alt={selectedImage.alt}
              className="w-full h-auto rounded-lg"
            />
            {selectedImage.caption && (
              <p className="text-white text-center mt-4">{selectedImage.caption}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Gallery;```


#### `components\services\ServiceCard.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { ServiceType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ServiceCardProps {
  service: ServiceType;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  className = '',
  variant = 'default'
}) => {
  const { id, title, description, image, features } = service;

  // Generate the correct service link
  const serviceLink = `/tjenester/${id}`;

  if (variant === 'compact') {
    return (
      <Link
        to={serviceLink}
        className={cn(
          'flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300',
          className
        )}
      >
        <div className="w-full sm:w-1/3 h-40 sm:h-auto rounded-md overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-3">{description}</p>
          <span className="text-green-600 font-medium hover:text-green-700 transition-colors flex items-center gap-1">
            Les mer
            <ArrowRight className="w-4 h-4" />
          </span>
        </div>
      </Link>
    );
  }

  return (
    <div className={cn(
      'relative h-[400px] group overflow-hidden rounded-lg',
      className
    )}>
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        {features && features.length > 0 && (
          <ul className="mb-4 space-y-2">
            {features.slice(0, 3).map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-gray-200">
                <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                {feature}
              </li>
            ))}
          </ul>
        )}
        <Link
          to={serviceLink}
          className="inline-flex items-center gap-2 bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;```


#### `components\shared\Elements\Card.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import { Image } from './Image';
import { Link } from './Link';

interface CardProps {
  title: string;
  description?: string;
  image?: string;
  link?: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
  aspectRatio?: 'square' | 'video' | 'portrait';
  variant?: 'default' | 'hover' | 'interactive';
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  link,
  className,
  children,
  onClick,
  aspectRatio = 'square',
  variant = 'default'
}) => {
  const Component = link ? Link : onClick ? 'button' : 'div';
  const props = {
    ...(link && { to: link }),
    ...(onClick && { onClick }),
    className: cn(
      'group relative bg-white rounded-lg overflow-hidden transition-all duration-300',
      variant === 'hover' && 'hover:shadow-lg',
      variant === 'interactive' && 'cursor-pointer hover:-translate-y-1 hover:shadow-lg',
      className
    )
  };

  return (
    <Component {...props}>
      {image && (
        <Image
          src={image}
          alt={title}
          aspectRatio={aspectRatio}
          className="w-full"
        />
      )}
      <div className="p-4 sm:p-6">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        {description && (
          <p className="text-gray-600 text-sm line-clamp-2 mb-4">{description}</p>
        )}
        {children}
      </div>
    </Component>
  );
};

export default Card;```


#### `components\shared\Elements\Form\Input.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, helper, leftIcon, rightIcon, className, ...props }, ref) => {
    const id = props.id || props.name;

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id} 
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}
          <input
            ref={ref}
            className={cn(
              'w-full rounded-md shadow-sm transition-colors duration-200',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;```


#### `components\shared\Elements\Form\Select.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

interface Option {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  options: Option[];
  label?: string;
  error?: string;
  helper?: string;
  size?: 'sm' | 'md' | 'lg';
}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ options, label, error, helper, size = 'md', className, ...props }, ref) => {
    const id = props.id || props.name;

    const sizeClasses = {
      sm: 'py-1.5 text-sm',
      md: 'py-2',
      lg: 'py-2.5 text-lg'
    };

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          <select
            ref={ref}
            className={cn(
              'w-full rounded-md shadow-sm appearance-none pr-10',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              sizeClasses[size],
              className
            )}
            {...props}
          >
            {options.map(({ value, label }) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <ChevronDown className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;```


#### `components\shared\Elements\Form\Textarea.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helper?: string;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ label, error, helper, className, ...props }, ref) => {
    const id = props.id || props.name;

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <textarea
          ref={ref}
          className={cn(
            'w-full rounded-md shadow-sm transition-colors duration-200',
            'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
            error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            className
          )}
          {...props}
        />
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;```


#### `components\shared\Elements\Icon.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';
import * as LucideIcons from 'lucide-react';

type IconName = keyof typeof LucideIcons;

interface IconProps {
  name: IconName;
  size?: 'sm' | 'md' | 'lg' | number;
  className?: string;
}

const Icon: React.FC<IconProps> = ({ name, size = 'md', className }) => {
  const IconComponent = LucideIcons[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32
  };

  const iconSize = typeof size === 'string' ? sizeMap[size] : size;

  return (
    <IconComponent
      size={iconSize}
      className={cn('flex-shrink-0', className)}
      aria-hidden="true"
    />
  );
};

export default Icon;```


#### `components\shared\Elements\Image.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallback?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | string;
}

const Image: React.FC<ImageProps> = ({
  src,
  alt,
  className,
  fallback = 'https://images.unsplash.com/photo-1558904541-efa843a96f01?auto=format&fit=crop&q=80',
  aspectRatio = 'square',
  ...props
}) => {
  const [error, setError] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  const handleError = () => {
    setError(true);
    setLoading(false);
  };

  const handleLoad = () => {
    setLoading(false);
  };

  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]'
  };

  const ratioClass = aspectRatioClasses[aspectRatio as keyof typeof aspectRatioClasses] || aspectRatio;

  return (
    <div className={cn('relative overflow-hidden bg-gray-100', ratioClass, className)}>
      <img
        src={error ? fallback : src}
        alt={alt}
        className={cn(
          'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
          loading ? 'opacity-0' : 'opacity-100'
        )}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      {loading && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse" />
      )}
    </div>
  );
};

export default Image;```


#### `components\shared\Elements\Link.tsx`

```tsx
import React from 'react';
import { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { trackEvent } from '@/lib/utils/analytics';

interface LinkProps extends RouterLinkProps {
  external?: boolean;
  tracking?: {
    category: string;
    action: string;
    label?: string;
  };
}

const Link: React.FC<LinkProps> = ({
  to,
  children,
  className,
  external,
  tracking,
  ...props
}) => {
  const handleClick = () => {
    if (tracking) {
      trackEvent({
        category: tracking.category,
        action: tracking.action,
        label: tracking.label
      });
    }
  };

  if (external) {
    return (
      <a
        href={to.toString()}
        target="_blank"
        rel="noopener noreferrer"
        className={cn(
          'transition-colors duration-200',
          'hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {children}
      </a>
    );
  }

  return (
    <RouterLink
      to={to}
      className={cn(
        'transition-colors duration-200',
        'hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {children}
    </RouterLink>
  );
};

export default Link;```


#### `components\shared\Elements\Loading.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ size = 'md', className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        className
      )}
      role="status"
      aria-label="Laster..."
    >
      <svg
        className={cn(
          'animate-spin text-green-500',
          sizeClasses[size]
        )}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      <span className="sr-only">Laster...</span>
    </div>
  );
};

export default Loading;```


#### `components\shared\ErrorBoundary.tsx`

```tsx
import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/Button';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onReset?: () => void;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<Props, State> {
  public state: State = {
    hasError: false,
    error: undefined
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to your error reporting service
    console.error('Error caught by error boundary:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined });
    this.props.onReset?.();
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-6">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Beklager, noe gikk galt</h2>
            <p className="text-gray-600 mb-4">
              Det oppstod en uventet feil. Vennligst prøv igjen eller kontakt oss hvis problemet vedvarer.
            </p>
            {this.state.error && (
              <pre className="mt-4 p-4 bg-gray-100 rounded-md text-sm text-left overflow-auto max-w-full">
                <code>{this.state.error.toString()}</code>
              </pre>
            )}
          </div>
          <Button onClick={this.handleReset} variant="primary">
            Prøv igjen
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
} ```


#### `components\shared\Layout\Layout.tsx`

```tsx
import React from 'react';
import { useAnalytics } from '@/lib/hooks';
import { Header } from './Header';
import { Footer } from './Footer';
import { Meta } from './Meta';

interface LayoutProps {
  children: React.ReactNode;
  meta?: {
    title?: string;
    description?: string;
    image?: string;
  };
}

const Layout: React.FC<LayoutProps> = ({ children, meta }) => {
  useAnalytics();

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Meta {...meta} />
      <Header />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
};

export default Layout;```


#### `components\ui\Button.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

type ButtonVariant = "primary" | "secondary" | "outline" | "text";
type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: ButtonVariant;
    size?: ButtonSize;
    fullWidth?: boolean;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right";
    to?: string;
}

interface LinkButtonProps extends Omit<ButtonProps, "onClick"> {
    to: string;
    external?: boolean;
}

const getVariantClasses = (variant: ButtonVariant): string => {
    switch (variant) {
        case "primary":
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
        case "secondary":
            return "bg-gray-100 hover:bg-gray-200 text-gray-800 shadow-sm";
        case "outline":
            return "bg-transparent border border-green-500 text-green-500 hover:bg-green-50";
        case "text":
            return "bg-transparent text-green-500 hover:text-green-600 hover:bg-green-50";
        default:
            return "bg-green-500 hover:bg-green-600 text-white shadow-sm";
    }
};

const getSizeClasses = (size: ButtonSize): string => {
    switch (size) {
        case "sm":
            return "text-sm py-1.5 px-3";
        case "md":
            return "text-base py-2 px-4";
        case "lg":
            return "text-lg py-2.5 px-5";
        default:
            return "text-base py-2 px-4";
    }
};

const Button: React.FC<ButtonProps> = ({
    children,
    variant = "primary",
    size = "md",
    fullWidth = false,
    icon,
    iconPosition = "left",
    className = "",
    to,
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const widthClass = fullWidth ? "w-full" : "";

    if (to) {
        return (
            <Link
                to={to}
                className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            >
                {icon && iconPosition === "left" && (
                    <span className="mr-2">{icon}</span>
                )}
                {children}
                {icon && iconPosition === "right" && (
                    <span className="ml-2">{icon}</span>
                )}
            </Link>
        );
    }

    return (
        <button
            className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            {...props}
        >
            {icon && iconPosition === "left" && (
                <span className="mr-2">{icon}</span>
            )}
            {children}
            {icon && iconPosition === "right" && (
                <span className="ml-2">{icon}</span>
            )}
        </button>
    );
};

const LinkButton: React.FC<LinkButtonProps> = ({
    children,
    to,
    external = false,
    variant = "primary",
    size = "md",
    fullWidth = false,
    icon,
    iconPosition = "left",
    className = "",
    ...props
}) => {
    const baseClasses =
        "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(size);
    const widthClass = fullWidth ? "w-full" : "";

    if (external) {
        return (
            <a
                href={to}
                target="_blank"
                rel="noopener noreferrer"
                className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
                {...props}
            >
                {icon && iconPosition === "left" && (
                    <span className="mr-2">{icon}</span>
                )}
                {children}
                {icon && iconPosition === "right" && (
                    <span className="ml-2">{icon}</span>
                )}
            </a>
        );
    }

    return (
        <Link
            to={to}
            className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClass} ${className}`}
            {...props}
        >
            {icon && iconPosition === "left" && (
                <span className="mr-2">{icon}</span>
            )}
            {children}
            {icon && iconPosition === "right" && (
                <span className="ml-2">{icon}</span>
            )}
        </Link>
    );
};

export { Button, LinkButton };
export default Button;```


#### `components\ui\Container.tsx`

```tsx
import React from "react";

interface ContainerProps {
    children: React.ReactNode;
    className?: string;
    as?: React.ElementType;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
}

const Container: React.FC<ContainerProps> = ({
    children,
    className = "",
    as: Component = "div",
    maxWidth = "xl",
}) => {
    const maxWidthClasses = {
        xs: "max-w-xs",
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-7xl",
        "2xl": "max-w-screen-2xl",
        full: "max-w-full",
    };

    return (
        <Component
            className={`mx-auto px-4 sm:px-6 lg:px-8 w-full ${maxWidthClasses[maxWidth]} ${className}`}
        >
            {children}
        </Component>
    );
};

export { Container };
export default Container;```


#### `components\ui\Hero.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { ChevronDown } from "lucide-react";

export interface HeroProps {
    title: string;
    subtitle?: string;
    backgroundImage: string;
    location?: string;
    actionLink?: string;
    actionText?: string;
    height?: "small" | "medium" | "large" | "full";
    overlay?: "none" | "light" | "dark" | "gradient";
    textAlignment?: "left" | "center" | "right";
    textColor?: "light" | "dark";
    yearEstablished?: string;
}

const Hero: React.FC<HeroProps> = ({
    title,
    subtitle,
    backgroundImage = "/images/site/hero-main.webp",
    location = "Røyse, Hole kommune",
    actionLink,
    actionText = "Se våre prosjekter",
    height = "large",
    overlay = "gradient",
    textAlignment = "center",
    textColor = "light",
    yearEstablished = "2015"
}) => {
    const heightClasses = {
        small: "h-[300px]",
        medium: "h-[500px]",
        large: "h-[calc(100vh-64px)] min-h-[600px] max-h-[800px]",
        full: "h-screen",
    };

    const overlayClasses = {
        none: "",
        light: "after:absolute after:inset-0 after:bg-white/30",
        dark: "after:absolute after:inset-0 after:bg-black/50",
        gradient:
            "after:absolute after:inset-0 after:bg-gradient-to-t after:from-black/70 after:to-transparent",
    };

    const textAlignmentClasses = {
        left: "text-left items-start",
        center: "text-center items-center",
        right: "text-right items-end",
    };

    const textColorClasses = {
        light: "text-white",
        dark: "text-gray-900",
    };

    const scrollToContent = () => {
        window.scrollTo({
            top: window.innerHeight,
            behavior: "smooth",
        });
    };

    return (
        <section
            className={`relative overflow-hidden ${heightClasses[height]} ${overlayClasses[overlay]}`}
        >
            {/* Background image */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Center square gradient overlay */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
                    linear-gradient(
                      50deg,
                      transparent 15%,
                      rgba(0,0,0,0.75) 32%,
                      rgba(0,0,0,0.6) 72%,
                      transparent 85%
                    )
                  `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 78%, rgba(0,0,0,0.85) 100%)",
                }}
                aria-hidden="true"
            />

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col justify-center px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
                <div
                    className={`flex flex-col ${textAlignmentClasses[textAlignment]} gap-4 max-w-3xl mx-auto`}
                >
                    {location && (
                        <div className="inline-flex items-center bg-green-500/90 text-white px-4 py-1.5 rounded-full text-sm font-medium mb-2">
                            {location} • Etablert {yearEstablished}
                        </div>
                    )}

                    <h1
                        className={`text-4xl sm:text-5xl lg:text-6xl font-bold ${textColorClasses[textColor]}`}
                    >
                        {title}
                    </h1>

                    {subtitle && (
                        <p
                            className={`text-lg sm:text-xl mt-2 ${textColorClasses[textColor]} opacity-90 max-w-2xl`}
                        >
                            {subtitle}
                        </p>
                    )}

                    {actionText && actionLink && (
                        <div className="mt-8">
                            <Link
                                to={actionLink}
                                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                                {actionText}
                            </Link>
                        </div>
                    )}

                    {!actionLink && (
                        <button
                            onClick={scrollToContent}
                            className="mt-8 inline-flex flex-col items-center gap-2 text-white/80 hover:text-white transition-colors"
                        >
                            <span className="text-sm font-medium">
                                Scroll ned for mer
                            </span>
                            <ChevronDown className="w-6 h-6 animate-bounce" />
                        </button>
                    )}
                </div>
            </div>
        </section>
    );
};

export { Hero };
export default Hero;```


#### `components\ui\Intersection.tsx`

```tsx
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface IntersectionProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  onIntersect?: () => void;
  once?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

const Intersection: React.FC<IntersectionProps> = ({
  children,
  className,
  threshold = 0.1,
  rootMargin = '0px',
  onIntersect,
  once = true,
  as: Component = 'div'
}) => {
  const ref = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          onIntersect?.();
          if (once && ref.current) {
            observer.unobserve(ref.current);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, rootMargin, onIntersect, once]);

  return (
    <Component
      ref={ref}
      className={cn(
        'transition-opacity duration-700',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      {children}
    </Component>
  );
};

export { Intersection };
export default Intersection;```


#### `components\ui\Logo.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  color?: string;
  variant?: 'icon-only' | 'full' | 'text-only';
}

const Logo: React.FC<LogoProps> = ({ 
  className,
  color = '#1d9545',
  variant = 'icon-only'
}) => {
  if (variant === 'text-only') {
    return (
      <svg 
        viewBox="0 0 1024 90" 
        className={cn('h-6', className)}
        aria-label="Ringerike Landskap logo text"
      >
        <path
          fill="currentColor"
          d="m32.4 54.1h-4.1v22.1h-14.8v-58.4h22.5q11.4 0 16.6 3.8 5.3 3.9 5.3 12.3c0.5 7-4.1 13.3-11 15 2.1 0.5 4 1.6 5.4 3.2 2 2.2 3.7 4.8 4.9 7.5l8 16.6h-15.7l-7-14.5c-0.9-2.3-2.4-4.4-4.3-6-1.7-1.2-3.7-1.7-5.8-1.6zm-4.1-10.4h6.2q4.6 0 6.6-1.8 2.1-1.8 2.1-5.8-0.1-4-2.1-5.7-2-1.7-6.6-1.7h-6.2zm47-25.9h14.8v58.4h-14.8zm30.5 58.4v-58.4h16.5l20.9 40.1v-40.1h14v58.4h-16.6l-20.8-40v40h-14zm118.6-32.4v28.1c-3.7 1.8-7.5 3.2-11.5 4.1-4 0.9-8.1 1.4-12.3 1.4q-14.3 0-22.7-8.2-8.3-8.1-8.3-22.1 0-14.1 8.5-22.2 8.5-8.1 23.3-8.1 5.6 0 11 1.1c3.4 0.7 6.7 1.8 9.9 3.2v12.1c-3-1.8-6.2-3.2-9.6-4.2-3-0.9-6.2-1.3-9.5-1.3q-8.8 0-13.6 5-4.7 5-4.7 14.4 0 9.3 4.6 14.3 4.6 5.1 13 5.1 2.2 0 4.3-0.3 1.8-0.3 3.6-1v-11.3h-9v-10.1h23zm14.3 32.4v-58.4h39.8v11.4h-25v10.9h23.5v11.4h-23.5v13.4h25.9v11.3h-40.7zm73.9-22.1h-4.2v22.2h-14.7v-58.5h22.5q11.4 0 16.6 3.9 5.3 3.9 5.3 12.3c0.5 7-4.2 13.3-11 15 2.1 0.5 4 1.6 5.4 3.1 2 2.3 3.6 4.8 4.9 7.6l8 16.6h-15.8l-6.9-14.6c-1-2.3-2.4-4.3-4.3-5.9-1.7-1.2-3.7-1.7-5.8-1.6zm-4.2-10.5h6.3q4.6 0 6.6-1.7 2-1.8 2-5.8 0-4-2-5.7-2-1.7-6.6-1.7h-6.3zm47.1 32.5v-58.4h14.8v58.4h-14.8zm30.5 0v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30.5 30.8h-18.5l-22.8-23v23h-14.8zm62.2 0v-58.4h39.9v11.4h-25.1v10.8h23.6v11.4h-23.6v13.4h25.9v11.4h-40.7zm83.6 0v-58.4h14.8v47h26v11.4h-40.8zm86.2-10.6h-23.1l-3.7 10.6h-14.8l21.2-58.4h17.6l21.3 58.4h-14.9zm-19.5-10.8h15.7l-7.8-23.2zm46.8 21.4v-58.4h16.5l20.9 40.1v-40.1h13.9v58.4h-16.5l-20.8-40v40h-14zm67.1 0.1v-58.5h15.6q13 0 19.4 1.9c4.1 1.2 7.9 3.4 10.9 6.5 2.7 2.5 4.7 5.6 6 9.1 1.4 3.7 2.1 7.7 2 11.7 0.1 4-0.6 8-2 11.8-1.3 3.4-3.3 6.6-6 9.1-3.1 3.1-6.8 5.3-11 6.5q-6.5 1.9-19.3 1.9h-15.6zm20-47.1h-5.2v35.7h5.2q9.1 0 13.9-4.6 4.7-4.6 4.7-13.3 0-8.7-4.7-13.2-4.8-4.6-13.9-4.6zm76.1-11.7q5.2 0.8 10.3 2.2v12.4q-4.4-2.1-9.2-3.3-4.2-1-8.5-1.1c-2.7-0.2-5.4 0.3-7.8 1.5-1.7 0.9-2.7 2.7-2.6 4.6 0 1.4 0.6 2.8 1.7 3.7 1.9 1.1 4.1 1.9 6.3 2.2l6.2 1.3q9.6 2 13.6 6 4 3.9 4 11.3 0 9.7-5.6 14.4-5.6 4.7-17.1 4.7-5.6 0-11-1.1-5.6-1.1-10.9-3.1v-12.7c3.3 1.9 6.9 3.4 10.6 4.5 3.2 0.9 6.5 1.5 9.9 1.5 2.5 0.1 5.1-0.4 7.4-1.7q0.5-0.3 0.9-0.7c2.2-2.3 2.2-6-0.1-8.2q-1.8-1.5-7-2.6l-5.7-1.3q-8.6-1.9-12.6-6c-2.7-3-4.2-7-4-11.1q0-8.7 5.6-13.4 5.5-4.7 15.8-4.7q4.9 0 9.8 0.7zm28.3 58.8v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30 30.3 21-57.9h17.6l21.3 58.4h-14.9l-3.6-10.7h-23.1l-3.7 10.7h-14.8-17.8l-22.8-23v23h-14.8zm77.6-21.5h15.7l-7.8-23.2zm46.7 21.4v-58.4h24.5q11 0 16.8 5 5.9 4.9 5.9 14.1 0 9.2-5.9 14.1-5.8 5-16.8 5h-9.7v20.3h-14.8zm14.8-47.6v16.4h8.2c2.4 0.1 4.8-0.6 6.6-2.2 3.1-3.4 3.1-8.6 0-12-1.9-1.6-4.2-2.3-6.6-2.2z"
        />
      </svg>
    );
  }

  if (variant === 'full') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <svg 
          viewBox="0 0 1024 1024" 
          className="w-8 h-8"
          aria-hidden="true"
        >
          <path
            fill={color}
            d="M388.1 552.7l104.4 181-52.2 104.2 94.4 24.8-39.7 94.2 119.3-128.9-116.8-12.4 253.5-478.6 159 293.3v-488.3c0-52.6-42.8-95.2-95.4-95.2h-604.5c-52.6 0-95.3 42.6-95.4 95.2q0 0 0 0v856q0 1.7 0.1 3.4z"
          />
        </svg>
        <svg 
          viewBox="0 0 1024 90" 
          className="h-6"
          aria-hidden="true"
        >
          <path
            fill="currentColor"
            d="m32.4 54.1h-4.1v22.1h-14.8v-58.4h22.5q11.4 0 16.6 3.8 5.3 3.9 5.3 12.3c0.5 7-4.1 13.3-11 15 2.1 0.5 4 1.6 5.4 3.2 2 2.2 3.7 4.8 4.9 7.5l8 16.6h-15.7l-7-14.5c-0.9-2.3-2.4-4.4-4.3-6-1.7-1.2-3.7-1.7-5.8-1.6zm-4.1-10.4h6.2q4.6 0 6.6-1.8 2.1-1.8 2.1-5.8-0.1-4-2.1-5.7-2-1.7-6.6-1.7h-6.2zm47-25.9h14.8v58.4h-14.8zm30.5 58.4v-58.4h16.5l20.9 40.1v-40.1h14v58.4h-16.6l-20.8-40v40h-14zm118.6-32.4v28.1c-3.7 1.8-7.5 3.2-11.5 4.1-4 0.9-8.1 1.4-12.3 1.4q-14.3 0-22.7-8.2-8.3-8.1-8.3-22.1 0-14.1 8.5-22.2 8.5-8.1 23.3-8.1 5.6 0 11 1.1c3.4 0.7 6.7 1.8 9.9 3.2v12.1c-3-1.8-6.2-3.2-9.6-4.2-3-0.9-6.2-1.3-9.5-1.3q-8.8 0-13.6 5-4.7 5-4.7 14.4 0 9.3 4.6 14.3 4.6 5.1 13 5.1 2.2 0 4.3-0.3 1.8-0.3 3.6-1v-11.3h-9v-10.1h23zm14.3 32.4v-58.4h39.8v11.4h-25v10.9h23.5v11.4h-23.5v13.4h25.9v11.3h-40.7zm73.9-22.1h-4.2v22.2h-14.7v-58.5h22.5q11.4 0 16.6 3.9 5.3 3.9 5.3 12.3c0.5 7-4.2 13.3-11 15 2.1 0.5 4 1.6 5.4 3.1 2 2.3 3.6 4.8 4.9 7.6l8 16.6h-15.8l-6.9-14.6c-1-2.3-2.4-4.3-4.3-5.9-1.7-1.2-3.7-1.7-5.8-1.6zm-4.2-10.5h6.3q4.6 0 6.6-1.7 2-1.8 2-5.8 0-4-2-5.7-2-1.7-6.6-1.7h-6.3zm47.1 32.5v-58.4h14.8v58.4h-14.8zm30.5 0v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30.5 30.8h-18.5l-22.8-23v23h-14.8zm62.2 0v-58.4h39.9v11.4h-25.1v10.8h23.6v11.4h-23.6v13.4h25.9v11.4h-40.7zm83.6 0v-58.4h14.8v47h26v11.4h-40.8zm86.2-10.6h-23.1l-3.7 10.6h-14.8l21.2-58.4h17.6l21.3 58.4h-14.9zm-19.5-10.8h15.7l-7.8-23.2zm46.8 21.4v-58.4h16.5l20.9 40.1v-40.1h13.9v58.4h-16.5l-20.8-40v40h-14zm67.1 0.1v-58.5h15.6q13 0 19.4 1.9c4.1 1.2 7.9 3.4 10.9 6.5 2.7 2.5 4.7 5.6 6 9.1 1.4 3.7 2.1 7.7 2 11.7 0.1 4-0.6 8-2 11.8-1.3 3.4-3.3 6.6-6 9.1-3.1 3.1-6.8 5.3-11 6.5q-6.5 1.9-19.3 1.9h-15.6zm20-47.1h-5.2v35.7h5.2q9.1 0 13.9-4.6 4.7-4.6 4.7-13.3 0-8.7-4.7-13.2-4.8-4.6-13.9-4.6zm76.1-11.7q5.2 0.8 10.3 2.2v12.4q-4.4-2.1-9.2-3.3-4.2-1-8.5-1.1c-2.7-0.2-5.4 0.3-7.8 1.5-1.7 0.9-2.7 2.7-2.6 4.6 0 1.4 0.6 2.8 1.7 3.7 1.9 1.1 4.1 1.9 6.3 2.2l6.2 1.3q9.6 2 13.6 6 4 3.9 4 11.3 0 9.7-5.6 14.4-5.6 4.7-17.1 4.7-5.6 0-11-1.1-5.6-1.1-10.9-3.1v-12.7c3.3 1.9 6.9 3.4 10.6 4.5 3.2 0.9 6.5 1.5 9.9 1.5 2.5 0.1 5.1-0.4 7.4-1.7q0.5-0.3 0.9-0.7c2.2-2.3 2.2-6-0.1-8.2q-1.8-1.5-7-2.6l-5.7-1.3q-8.6-1.9-12.6-6c-2.7-3-4.2-7-4-11.1q0-8.7 5.6-13.4 5.5-4.7 15.8-4.7q4.9 0 9.8 0.7zm28.3 58.8v-58.4h14.8v21.3l21.3-21.3h17.2l-27.7 27.6 30 30.3 21-57.9h17.6l21.3 58.4h-14.9l-3.6-10.7h-23.1l-3.7 10.7h-14.8-17.8l-22.8-23v23h-14.8zm77.6-21.5h15.7l-7.8-23.2zm46.7 21.4v-58.4h24.5q11 0 16.8 5 5.9 4.9 5.9 14.1 0 9.2-5.9 14.1-5.8 5-16.8 5h-9.7v20.3h-14.8zm14.8-47.6v16.4h8.2c2.4 0.1 4.8-0.6 6.6-2.2 3.1-3.4 3.1-8.6 0-12-1.9-1.6-4.2-2.3-6.6-2.2z"
          />
        </svg>
      </div>
    );
  }

  // Default icon-only variant
  return (
    <svg 
      viewBox="0 0 1024 1024" 
      className={cn('w-8 h-8', className)}
      aria-label="Ringerike Landskap logo"
    >
      <path
        fill={color}
        d="M388.1 552.7l104.4 181-52.2 104.2 94.4 24.8-39.7 94.2 119.3-128.9-116.8-12.4 253.5-478.6 159 293.3v-488.3c0-52.6-42.8-95.2-95.4-95.2h-604.5c-52.6 0-95.3 42.6-95.4 95.2q0 0 0 0v856q0 1.7 0.1 3.4z"
      />
    </svg>
  );
};

export { Logo };
export default Logo;```


#### `components\ui\Notifications.tsx`

```tsx
import React from 'react';
import { X, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useApp } from '@/lib/context/AppContext';
import { cn } from '@/lib/utils';

const Notifications: React.FC = () => {
  const { state, removeNotification } = useApp();
  const { notifications } = state;

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={cn(
            'p-4 rounded-lg shadow-lg flex items-start gap-3 animate-fade-in',
            notification.type === 'error' && 'bg-red-50 text-red-800 border border-red-200',
            notification.type === 'success' && 'bg-green-50 text-green-800 border border-green-200',
            notification.type === 'info' && 'bg-blue-50 text-blue-800 border border-blue-200'
          )}
        >
          <div className="flex-shrink-0">
            {notification.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
            {notification.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
            {notification.type === 'info' && <Info className="w-5 h-5 text-blue-500" />}
          </div>
          <div className="flex-1">
            <p className="text-sm">{notification.message}</p>
          </div>
          <button
            onClick={() => removeNotification(notification.id)}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );
};

export { Notifications };
export default Notifications;```


#### `components\ui\SeasonalCTA.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { getCurrentSeason } from '../../lib/utils';

interface SeasonalCTAProps {
  season?: "spring" | "summer" | "fall" | "winter";
  className?: string;
}

const SeasonalCTA: React.FC<SeasonalCTAProps> = ({
  season = "winter",
  className = "",
}) => {
  const currentSeason = getCurrentSeason();
  
  const seasonData = {
    spring: {
      title: "Planlegg for Ringerike-v√•ren",
      description:
        "V√•ren er perfekt for √• planlegge neste sesongs hageprosjekter. Vi har inng√•ende kjennskap til lokale forhold i Ringerike.",
      icon: "üå±",
      services: [
        "Lokaltilpasset planlegging",
        "Terrengvurdering",
        "Klimatilpasset design",
      ],
      cta: "Book gratis befaring i Ringerike",
    },
    summer: {
      title: "Nyt sommeren i en vakker hage",
      description:
        "F√• mest mulig ut av sommeren med en velstelt og innbydende hage. Vi hjelper deg med vedlikehold og forbedringer.",
      icon: "‚òÄÔ∏è",
      services: [
        "Vedlikehold av uterom",
        "Vanningssystemer",
        "Beplantning og stell",
      ],
      cta: "Book gratis befaring i Ringerike",
    },
    fall: {
      title: "H√∏stklargj√∏ring og planlegging",
      description:
        "Forbered hagen for vinteren og planlegg neste √•rs prosjekter. N√• er det perfekt tid for √• plante tr√¶r og busker.",
      icon: "üçÇ",
      services: ["H√∏stbeplantning", "Beskj√¶ring", "Vinterklargj√∏ring"],
      cta: "Book gratis befaring i Ringerike",
    },
    winter: {
      title: "Planlegg for Ringerike-v√•ren",
      description:
        "Vinteren er perfekt for √• planlegge neste sesongs hageprosjekter. Vi har inng√•ende kjennskap til lokale forhold i Ringerike.",
      icon: "‚ùÑÔ∏è",
      services: [
        "Lokaltilpasset planlegging",
        "Terrengvurdering",
        "Klimatilpasset design",
      ],
      cta: "Book gratis befaring i Ringerike",
    },
  };

  // Map the current season to the season prop
  const seasonMapping = {
    'v√•r': 'spring',
    'sommer': 'summer',
    'h√∏st': 'fall',
    'vinter': 'winter'
  } as const;
  
  const mappedSeason = seasonMapping[currentSeason] || 'winter';
  const { title, description, icon, services, cta } = seasonData[season || mappedSeason];

  return (
    <div
      className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}
    >
      <div className="p-6 space-y-4">
        <div className="flex items-center gap-2 text-sm text-blue-600">
          <span>{icon}</span>
          <span>
            Sesong: {currentSeason}
          </span>
        </div>

        <h3 className="text-xl font-semibold">{title}</h3>

        <p className="text-gray-600 text-sm">{description}</p>

        <div className="space-y-2 py-2">
          <p className="text-gray-700 font-medium text-sm">
            Aktuelle tjenester:
          </p>
          <ul className="space-y-1">
            {services.map((service, index) => (
              <li
                key={index}
                className="flex items-center gap-2 text-sm"
              >
                <span className="text-green-500">‚Ä¢</span>
                <span>{service}</span>
              </li>
            ))}
          </ul>
        </div>

        <Link
          to="/kontakt"
          className="block w-full bg-green-500 text-white text-center py-3 rounded-md hover:bg-green-600 transition-colors"
        >
          {cta}
        </Link>
      </div>
    </div>
  );
};

export { SeasonalCTA };
export default SeasonalCTA;```


#### `components\ui\ServiceAreaList.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin } from 'lucide-react';
import { ServiceArea } from '../../lib/types';

interface ServiceAreaListProps {
  areas: ServiceArea[];
  className?: string;
}

const ServiceAreaList: React.FC<ServiceAreaListProps> = ({
  areas,
  className = "",
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
      <h3 className="text-xl font-semibold mb-4">Våre serviceområder</h3>
      <p className="text-gray-600 mb-6">
        Vi tilbyr våre tjenester i følgende områder i Ringerike og
        omegn:
      </p>

      <div className="space-y-4">
        {areas.map((area, index) => (
          <div
            key={index}
            className={`flex items-start p-3 rounded-md ${
              area.isBase
                ? "bg-green-50 border-l-4 border-green-500"
                : "hover:bg-gray-50"
            }`}
          >
            <MapPin className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <div className="flex items-center gap-2">
                <h4 className="font-medium">{area.city}</h4>
                <span className="text-sm text-gray-500">
                  {area.distance}
                </span>
                {area.isBase && (
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                    Hovedområde
                  </span>
                )}
              </div>
              {area.description && (
                <p className="text-sm text-gray-600 mt-1">
                  {area.description}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <Link 
          to="/kontakt"
          className="text-green-600 hover:text-green-700 font-medium text-sm"
        >
          Kontakt oss for tjenester i ditt område
        </Link>
      </div>
    </div>
  );
};

export { ServiceAreaList };
export default ServiceAreaList;```


#### `components\ui\Skeleton.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ 
  className,
  animate = true
}) => {
  return (
    <div
      className={cn(
        'bg-gray-200 rounded-md',
        animate && 'animate-pulse',
        className
      )}
    />
  );
};

export { Skeleton };
export default Skeleton;```


#### `components\ui\Transition.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface TransitionProps {
  children: React.ReactNode;
  show?: boolean;
  appear?: boolean;
  className?: string;
  type?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right';
  duration?: 'fast' | 'normal' | 'slow';
}

const Transition: React.FC<TransitionProps> = ({
  children,
  show = true,
  appear = false,
  className,
  type = 'fade',
  duration = 'normal'
}) => {
  const [isVisible, setIsVisible] = React.useState(show && !appear);

  React.useEffect(() => {
    if (show) {
      setIsVisible(true);
    }
  }, [show]);

  const handleTransitionEnd = () => {
    if (!show) {
      setIsVisible(false);
    }
  };

  const baseStyles = 'transition-all';
  
  const durationStyles = {
    fast: 'duration-200',
    normal: 'duration-300',
    slow: 'duration-500'
  };

  const typeStyles = {
    fade: 'opacity-0',
    'slide-up': 'opacity-0 translate-y-4',
    'slide-down': 'opacity-0 -translate-y-4',
    'slide-left': 'opacity-0 translate-x-4',
    'slide-right': 'opacity-0 -translate-x-4'
  };

  const visibleStyles = {
    fade: 'opacity-100',
    'slide-up': 'opacity-100 translate-y-0',
    'slide-down': 'opacity-100 translate-y-0',
    'slide-left': 'opacity-100 translate-x-0',
    'slide-right': 'opacity-100 translate-x-0'
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={cn(
        baseStyles,
        durationStyles[duration],
        show ? visibleStyles[type] : typeStyles[type],
        className
      )}
      onTransitionEnd={handleTransitionEnd}
      role="presentation"
    >
      {children}
    </div>
  );
};

export { Transition };
export default Transition;```


#### `features\home\FilteredServicesSection.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar } from 'lucide-react';
import { useData } from '@/hooks/useData';
import { getSeasonalServices, getSeasonDisplayName } from '@/lib/api';
import { getCurrentSeason } from '@/lib/utils';

const FilteredServicesSection = () => {
  const currentSeason = getCurrentSeason();
  const { data: seasonalServices, loading } = useData(() => getSeasonalServices(6), []);

  if (loading) {
    return (
      <div>
        <h2 className="text-xl sm:text-2xl font-semibold mb-3">
          Tjenester for <span className="text-green-500">Ringerikes terreng</span>
        </h2>
        <p className="text-sm sm:text-base text-gray-600 mb-4">
          Fra Tyrifjordens bredder til åsene rundt Hønefoss – vi leverer løsninger skreddersydd for lokale forhold og klima.
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-40 bg-gray-200 animate-pulse rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl sm:text-2xl font-semibold mb-3">
        Tjenester for <span className="text-green-500">Ringerikes terreng</span>
      </h2>
      <p className="text-sm sm:text-base text-gray-600 mb-4">
        Fra Tyrifjordens bredder til åsene rundt Hønefoss – vi leverer løsninger skreddersydd for lokale forhold og klima.
      </p>
      
      {/* Seasonal tip */}
      <div className="mb-6 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-green-600" />
          <p className="text-sm text-gray-700">
            <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for tjenester som ferdigplen, hekk og beplantning.{' '}
            <Link 
              to={`/hva-vi-gjor?sesong=${currentSeason}`}
              className="text-green-600 hover:underline font-medium"
            >
              Vis tjenester for {getSeasonDisplayName(currentSeason)}
            </Link>
          </p>
        </div>
      </div>

      {/* Services grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {seasonalServices?.map((service) => (
          <div key={service.id} className="flex items-start gap-4">
            <div className="w-24 h-24 flex-shrink-0 rounded-lg overflow-hidden">
              <img 
                src={service.image} 
                alt={service.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h3 className="font-medium mb-2">{service.title}</h3>
              <p className="text-sm text-gray-600 mb-2 line-clamp-2">{service.description}</p>
              <Link 
                to={`/tjenester/${service.id}`}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Les mer
              </Link>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 text-center">
        <Link
          to="/hva-vi-gjor"
          className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
        >
          Se alle våre tjenester
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default FilteredServicesSection;```


#### `features\home\SeasonalProjectsCarousel.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useData } from '@/hooks/useData';
import { getSeasonalProjects } from '@/lib/api';
import ProjectCard from '@/features/projects/ProjectCard';

const SeasonalProjectsCarousel = () => {
  const navigate = useNavigate();
  const { data: seasonalProjects, loading } = useData(() => getSeasonalProjects(6), []);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !seasonalProjects?.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? seasonalProjects.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, seasonalProjects?.length]);

  const handleNext = useCallback(() => {
    if (isAnimating || !seasonalProjects?.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === seasonalProjects.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, seasonalProjects?.length]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (loading) {
    return (
      <div className="aspect-square bg-gray-200 animate-pulse rounded-lg"></div>
    );
  }

  if (!seasonalProjects?.length) {
    return null;
  }

  return (
    <section>
      <div 
        className="relative group"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
      >
        <div className="overflow-hidden rounded-lg">
          <div 
            className="relative transition-transform duration-500 ease-in-out"
            style={{ 
              transform: `translateX(-${currentIndex * 100}%)`,
              display: 'flex' 
            }}
          >
            {seasonalProjects.map((project) => (
              <div 
                key={project.id} 
                className="w-full flex-shrink-0"
              >
                <div className="aspect-[4/3]">
                  <ProjectCard 
                    project={project}
                    onProjectClick={handleProjectClick}
                    showTestimonial={true}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={handlePrevious}
          className="absolute -left-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-left-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Forrige prosjekt"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>

        <button
          onClick={handleNext}
          className="absolute -right-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-right-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Neste prosjekt"
        >
          <ArrowRight className="w-5 h-5" />
        </button>

        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
          {seasonalProjects.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-white w-4' 
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              aria-label={`GÃ¥ til prosjekt ${index + 1}`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default SeasonalProjectsCarousel;```


#### `features\home\SeasonalServicesSection.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { services } from '@/data/services';
import { getCurrentSeason } from '@/lib/utils';
import { ServiceCard } from '@/features/services';
import { Container } from '@/components/ui';

// Map seasons to relevant service categories and features
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};

const SeasonalServicesSection = () => {
  const currentSeason = getCurrentSeason();
  const seasonMapping = SEASONAL_SERVICES[currentSeason];

  // Filter services based on current season
  const seasonalServices = services.filter(service => {
    // Check if service category matches any seasonal category
    const categoryMatch = seasonMapping.categories.some(category => 
      service.title.toLowerCase().includes(category.toLowerCase())
    );
    
    // Check if any service feature matches seasonal features
    const featureMatch = service.features?.some(feature => 
      seasonMapping.features.some(seasonFeature => 
        feature.toLowerCase().includes(seasonFeature.toLowerCase())
      )
    );
    
    return categoryMatch || featureMatch;
  });

  // Get season display name
  const getSeasonDisplayName = (season: string): string => {
    switch (season) {
      case 'vår': return 'våren';
      case 'sommer': return 'sommeren';
      case 'høst': return 'høsten';
      case 'vinter': return 'vinteren';
      default: return season;
    }
  };

  return (
    <Container>
      <div>
        <h2 className="text-xl sm:text-2xl font-semibold mb-3">
          Tjenester for <span className="text-green-500">Ringerikes terreng</span>
        </h2>
        <p className="text-sm sm:text-base text-gray-600 mb-6">
          Fra Tyrifjordens bredder til åsene rundt Hønefoss – vi leverer løsninger skreddersydd for {getSeasonDisplayName(currentSeason)}.
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {seasonalServices.slice(0, 3).map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              variant="compact"
            />
          ))}
        </div>

        {/* Seasonal tip */}
        <div className="mt-6 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
          <p className="text-sm text-gray-700">
            <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for{' '}
            {seasonMapping.categories.join(', ').toLowerCase()}.{' '}
            <Link 
              to={`/hva-vi-gjor?sesong=${currentSeason}`}
              className="text-green-600 hover:underline font-medium"
            >
              Se alle tjenester for {getSeasonDisplayName(currentSeason)}
            </Link>
          </p>
        </div>
      </div>
    </Container>
  );
};

export default SeasonalServicesSection;```


#### `features\home.tsx`

```tsx
import React, { useState, useEffect } from "react";
import { ArrowLeft, ArrowRight, MapPin, Calendar } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { ProjectType } from "../types";
import { Button } from "../ui";
import { services } from "../data/services";

export interface ProjectCarouselProps {
    projects: ProjectType[];
}

export const ProjectCarousel: React.FC<ProjectCarouselProps> = ({
    projects,
}) => {
    const navigate = useNavigate();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAnimating, setIsAnimating] = useState(false);
    const [isHovered, setIsHovered] = useState(false);

    useEffect(() => {
        if (isHovered) return;

        const interval = setInterval(() => {
            handleNext();
        }, 5000);

        return () => clearInterval(interval);
    }, [currentIndex, isHovered]);

    const handlePrevious = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (isAnimating) return;
        setIsAnimating(true);
        setCurrentIndex((prev) =>
            prev === 0 ? projects.length - 1 : prev - 1
        );
        setTimeout(() => setIsAnimating(false), 500);
    };

    const handleNext = (e?: React.MouseEvent) => {
        if (e) e.stopPropagation();
        if (isAnimating) return;
        setIsAnimating(true);
        setCurrentIndex((prev) =>
            prev === projects.length - 1 ? 0 : prev + 1
        );
        setTimeout(() => setIsAnimating(false), 500);
    };

    const getServiceTitle = (categoryId: string) => {
        const service = services.find((s) => s.id === categoryId.toLowerCase());
        return service ? service.title : categoryId;
    };

    const handleProjectClick = () => {
        const currentProject = projects[currentIndex];
        navigate(`/prosjekter/${currentProject.id}`);
    };

    const currentProject = projects[currentIndex];

    return (
        <div
            className="group relative"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div
                className="relative overflow-hidden bg-gray-100 rounded-lg shadow-sm cursor-pointer"
                itemScope
                itemType="http://schema.org/Project"
                role="region"
                aria-label="Project showcase"
                onClick={handleProjectClick}
            >
                <div className="relative h-[400px] sm:h-[500px] md:h-[600px]">
                    <div
                        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-105"
                        style={{
                            backgroundImage: `url(${currentProject.image})`,
                            backgroundPosition:
                                currentProject.category.toLowerCase() ===
                                "stottemurer"
                                    ? "center"
                                    : "center 70%",
                        }}
                        itemProp="image"
                        aria-hidden="true"
                    />
                    {/* Bottom gradient overlay - 50% height */}
                    <div
                        className="absolute bottom-0 left-0 right-0 h-[50%] bg-gradient-to-t from-black/95 via-black/80 to-transparent transition-opacity duration-500 group-hover:opacity-40"
                        aria-hidden="true"
                    />
                    {/* Top concentrated gradient overlay - 15% height */}
                    <div
                        className="absolute top-0 left-0 right-0 h-[15%] bg-gradient-to-b from-black/80 to-transparent transition-opacity duration-500 group-hover:opacity-40"
                        aria-hidden="true"
                    />

                    {/* Index indicator moved to top */}
                    <div className="absolute top-4 left-1/2 -translate-x-1/2 flex gap-2 z-20">
                        {projects.map((_, index) => (
                            <button
                                key={index}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setCurrentIndex(index);
                                }}
                                className={`w-2 h-2 rounded-full transition-all shadow-md ${
                                    index === currentIndex
                                        ? "bg-white w-4"
                                        : "bg-white/70 hover:bg-white"
                                }`}
                                aria-label={`Gå til prosjekt ${index + 1}`}
                                aria-current={
                                    index === currentIndex ? "true" : "false"
                                }
                            />
                        ))}
                    </div>

                    <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
                        <div className="max-w-3xl space-y-4">
                            <div className="space-y-4 transform transition-all duration-500 group-hover:scale-90 origin-bottom-left">
                                {/* Project title and description */}
                                <div className="transition-opacity duration-500 group-hover:opacity-60">
                                    <h3
                                        className="text-xl sm:text-2xl font-semibold mb-3 text-white"
                                        itemProp="name"
                                    >
                                        {currentProject.title}
                                    </h3>
                                    <p
                                        className="text-gray-200 mb-4 text-base leading-relaxed line-clamp-3 max-w-2xl"
                                        itemProp="description"
                                    >
                                        {currentProject.description}
                                    </p>
                                </div>

                                {/* Project metadata */}
                                <div className="flex flex-wrap items-center gap-4 transition-opacity duration-500 group-hover:opacity-60">
                                    <div className="flex items-center gap-2 text-white/90">
                                        <MapPin className="w-4 h-4" />
                                        <span
                                            className="text-sm"
                                            itemProp="location"
                                        >
                                            {currentProject.location}
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2 text-white/90">
                                        <Calendar className="w-4 h-4" />
                                        <span className="text-sm">
                                            {currentProject.completionDate}
                                        </span>
                                    </div>
                                </div>

                                {currentProject.specifications && (
                                    <div className="flex gap-4 transition-opacity duration-500 group-hover:opacity-60">
                                        <div className="text-white/80">
                                            <span className="text-xs uppercase">
                                                Størrelse
                                            </span>
                                            <p className="text-sm font-medium">
                                                {
                                                    currentProject
                                                        .specifications.size
                                                }
                                            </p>
                                        </div>
                                        <div className="text-white/80">
                                            <span className="text-xs uppercase">
                                                Varighet
                                            </span>
                                            <p className="text-sm font-medium">
                                                {
                                                    currentProject
                                                        .specifications.duration
                                                }
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {/* Category and tags */}
                                <div className="pt-4 border-t border-white/10 transition-opacity duration-500 group-hover:opacity-60">
                                    <div className="flex flex-wrap items-center gap-2 text-[11px] sm:text-xs text-white/60">
                                        <Link
                                            to={`/tjenester/${currentProject.category.toLowerCase()}`}
                                            className="hover:text-white/80 transition-colors"
                                            itemProp="category"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            {getServiceTitle(
                                                currentProject.category
                                            )}
                                        </Link>
                                        {currentProject.tags &&
                                            currentProject.tags.length > 0 && (
                                                <>
                                                    <span className="text-white/30">
                                                        •
                                                    </span>
                                                    <span className="text-white/60">
                                                        {currentProject.tags.join(
                                                            ", "
                                                        )}
                                                    </span>
                                                </>
                                            )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button
                onClick={handlePrevious}
                className="absolute left-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 text-gray-800 hover:bg-white hover:shadow-lg transition-all duration-300 opacity-0 group-hover:opacity-100 focus:opacity-100"
                aria-label="Forrige prosjekt"
            >
                <ArrowLeft className="w-5 h-5" />
            </button>

            <button
                onClick={handleNext}
                className="absolute right-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 text-gray-800 hover:bg-white hover:shadow-lg transition-all duration-300 opacity-0 group-hover:opacity-100 focus:opacity-100"
                aria-label="Neste prosjekt"
            >
                <ArrowRight className="w-5 h-5" />
            </button>
        </div>
    );
};

export const FeaturedProjects: React.FC<{
    title: string;
    subtitle?: string;
    projects: ProjectType[];
    className?: string;
}> = ({ title, subtitle, projects, className = "" }) => {
    return (
        <section className={`py-12 ${className}`}>
            <div className="container mx-auto px-4">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold mb-4">{title}</h2>
                    {subtitle && (
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            {subtitle}
                        </p>
                    )}
                </div>
                <ProjectCarousel projects={projects} />
            </div>
        </section>
    );
};
```


#### `features\projects\ProjectCard.tsx`

```tsx
import React from 'react';
import { MapPin, Calendar, Tag } from 'lucide-react';
import { ProjectType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ProjectCardProps {
  project: ProjectType;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
  showTestimonial?: boolean;
  onProjectClick?: (id: string) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project,
  variant = 'default',
  className = '',
  showTestimonial = false,
  onProjectClick
}) => {
  const handleClick = () => {
    if (onProjectClick) {
      onProjectClick(project.id);
    }
  };

  return (
    <button 
      onClick={handleClick}
      className={cn(
        "relative w-full h-full text-left",
        "transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 hover:scale-105"
        style={{ backgroundImage: `url(${project.image})` }}
        aria-hidden="true"
      />
      <div 
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />

      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex flex-wrap items-center gap-2 mb-2">
          <span className="inline-flex items-center gap-1.5 bg-green-500 text-white px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium">
            {project.category}
          </span>
          <div className="flex items-center gap-1.5 text-white/90">
            <MapPin className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.location}</span>
          </div>
          <div className="flex items-center gap-1.5 text-white/90">
            <Calendar className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.completionDate}</span>
          </div>
        </div>
        
        <h3 className="text-lg sm:text-xl font-medium mb-2 text-white line-clamp-1">
          {project.title}
        </h3>
        <p className="text-gray-200 mb-3 text-sm leading-relaxed line-clamp-2">
          {project.description}
        </p>

        {/* Project tags */}
        {project.tags && project.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {project.tags.slice(0, 3).map((tag, index) => (
              <span 
                key={index}
                className="inline-flex items-center gap-1 px-2 py-0.5 bg-white/10 rounded-full text-xs text-white/80"
              >
                <Tag className="w-3 h-3" />
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Testimonial */}
        {showTestimonial && project.testimonial && (
          <div className="mt-2 p-2 bg-black/30 rounded text-white/90 text-sm italic">
            "{project.testimonial.quote}" â€” {project.testimonial.author}
          </div>
        )}
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Se detaljer om prosjektet {project.title}
      </span>
    </button>
  );
};

export default ProjectCard;

export { ProjectCard }```


#### `features\projects\ProjectFilter.tsx`

```tsx
import React from 'react';
import { MapPin, Tag, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectFilterProps {
  categories: string[];
  locations: string[];
  tags?: string[];
  selectedFilters: {
    category?: string;
    location?: string;
    tag?: string;
  };
  onFilterChange: (type: 'category' | 'location' | 'tag', value: string) => void;
}

export const ProjectFilter: React.FC<ProjectFilterProps> = ({
  categories,
  locations,
  tags = [],
  selectedFilters,
  onFilterChange
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
        <Filter className="w-5 h-5" />
        <h3>Filtrer prosjekter</h3>
      </div>

      {categories.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Kategori</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.category
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('category', '')}
            >
              Alle
            </button>
            {categories.map((category) => (
              <button
                key={category}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.category === category
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('category', category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}

      {locations.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Sted</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.location
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('location', '')}
            >
              Alle steder
            </button>
            {locations.map((location) => (
              <button
                key={location}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.location === location
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('location', location)}
              >
                {location}
              </button>
            ))}
          </div>
        </div>
      )}

      {tags.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Stikkord</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.tag
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('tag', '')}
            >
              Alle
            </button>
            {tags.map((tag) => (
              <button
                key={tag}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.tag === tag
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('tag', tag)}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectFilter;```


#### `features\projects\ProjectGallery.tsx`

```tsx
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  className?: string;
}

export const ProjectGallery: React.FC<ProjectGalleryProps> = ({
  images,
  className
}) => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image, index) => (
          <button
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(index)}
          >
            <img
              src={image.src}
              alt={image.alt}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-sm font-medium">
                  {image.alt}
                </h3>
                {image.caption && (
                  <p className="text-xs mt-1 text-gray-200">
                    {image.caption}
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Modal */}
      {selectedImage !== null && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <img
            src={images[selectedImage].src}
            alt={images[selectedImage].alt}
            className="max-w-full max-h-[90vh] rounded-lg"
          />
          {images[selectedImage].caption && (
            <div className="absolute bottom-4 left-0 right-0 text-center text-white">
              <p className="bg-black/50 mx-auto max-w-2xl p-2 rounded">
                {images[selectedImage].caption}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProjectGallery;```


#### `features\projects\ProjectGrid.tsx`

```tsx
import React from 'react';
import { ProjectType } from '@/lib/types';
import { ProjectCard } from './ProjectCard';
import { cn } from '@/lib/utils';

interface ProjectGridProps {
  projects: ProjectType[];
  filter?: {
    category?: string;
    location?: string;
    tag?: string;
  };
  layout?: 'grid' | 'masonry' | 'carousel';
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
}

export const ProjectGrid: React.FC<ProjectGridProps> = ({
  projects,
  filter = {},
  layout = 'grid',
  variant = 'default',
  className = ''
}) => {
  // Filter projects based on criteria
  const filteredProjects = projects.filter((project) => {
    if (filter.category && project.category !== filter.category) return false;
    if (filter.location && project.location !== filter.location) return false;
    if (filter.tag && !project.tags.includes(filter.tag)) return false;
    return true;
  });

  if (layout === 'masonry') {
    // Simple masonry-like layout with different heights
    return (
      <div className={cn(
        'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
        className
      )}>
        {filteredProjects.map((project, index) => (
          <div
            key={project.id}
            className={index % 3 === 0 ? 'row-span-2' : ''}
          >
            <ProjectCard project={project} variant={variant} />
          </div>
        ))}
      </div>
    );
  }

  if (layout === 'carousel') {
    // Simple carousel layout
    return (
      <div className={cn(
        'flex overflow-x-auto gap-6 pb-4',
        className
      )}>
        {filteredProjects.map((project) => (
          <div
            key={project.id}
            className="min-w-[300px] max-w-[400px]"
          >
            <ProjectCard project={project} variant={variant} />
          </div>
        ))}
      </div>
    );
  }

  // Default grid layout
  return (
    <div className={cn(
      'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
      className
    )}>
      {filteredProjects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          variant={variant}
        />
      ))}
    </div>
  );
};

export default ProjectGrid;```


#### `features\projects\ProjectsCarousel.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ProjectCard from './ProjectCard';
import { ProjectType } from '../../lib/types';

interface ProjectsCarouselProps {
  projects: ProjectType[];
  className?: string;
}

const ProjectsCarousel: React.FC<ProjectsCarouselProps> = ({ 
  projects,
  className
}) => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? projects.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  const handleNext = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === projects.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (!projects.length) {
    return null;
  }

  return (
    <section className={className}>
      <div 
        className="relative group"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
      >
        <div className="overflow-hidden rounded-lg">
          <div 
            className="relative transition-transform duration-500 ease-in-out"
            style={{ 
              transform: `translateX(-${currentIndex * 100}%)`,
              display: 'flex' 
            }}
          >
            {projects.map((project) => (
              <div 
                key={project.id} 
                className="w-full flex-shrink-0"
              >
                <div className="h-[350px] md:h-[400px] lg:h-[450px]">
                  <ProjectCard 
                    project={project}
                    onProjectClick={handleProjectClick}
                    showTestimonial={true}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={handlePrevious}
          className="absolute -left-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-left-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Forrige prosjekt"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>

        <button
          onClick={handleNext}
          className="absolute -right-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-right-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Neste prosjekt"
        >
          <ArrowRight className="w-5 h-5" />
        </button>

        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
          {projects.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-white w-4' 
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              aria-label={`GÃ¥ til prosjekt ${index + 1}`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsCarousel;```


#### `features\projects.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { MapPin, Calendar, Filter } from "lucide-react";
import { ProjectType, ProjectFilterProps } from "../types";

export interface ProjectCardProps {
    project: ProjectType;
    className?: string;
    variant?: "default" | "featured" | "compact";
    showTestimonial?: boolean;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({
    project,
    className = "",
    variant = "default",
    showTestimonial = false,
}) => {
    if (variant === "compact") {
        return (
            <Link
                to={`/prosjekter/${project.id}`}
                className={`flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow ${className}`}
            >
                <div className="w-full sm:w-1/3 h-40 sm:h-auto rounded-md overflow-hidden">
                    <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
                <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 transition-colors">
                        {project.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                        {project.description}
                    </p>
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        {project.location && (
                            <div className="flex items-center gap-1">
                                <MapPin className="w-4 h-4" />
                                <span>{project.location}</span>
                            </div>
                        )}
                        {project.category && (
                            <span className="inline-block px-2 py-1 bg-gray-100 rounded-md text-xs">
                                {project.category}
                            </span>
                        )}
                    </div>
                </div>
            </Link>
        );
    }

    if (variant === "featured") {
        return (
            <Link
                to={`/prosjekter/${project.id}`}
                className={`group block overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow ${className}`}
            >
                <div className="relative h-[300px]">
                    <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-90 group-hover:opacity-95 transition-opacity" />
                    <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <h3 className="text-2xl font-semibold mb-2">
                            {project.title}
                        </h3>
                        <p className="text-gray-200 mb-4 line-clamp-2">
                            {project.description}
                        </p>
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
                            {project.location && (
                                <div className="flex items-center gap-1">
                                    <MapPin className="w-4 h-4" />
                                    <span>{project.location}</span>
                                </div>
                            )}
                            {project.completionDate && (
                                <div className="flex items-center gap-1">
                                    <Calendar className="w-4 h-4" />
                                    <span>{project.completionDate}</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                {showTestimonial && project.testimonial && (
                    <div className="p-4 bg-gray-50 italic text-gray-600">
                        "{project.testimonial.quote}" -{" "}
                        {project.testimonial.author}
                    </div>
                )}
            </Link>
        );
    }

    // Default variant
    return (
        <Link
            to={`/prosjekter/${project.id}`}
            className={`group block overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow ${className}`}
        >
            <div className="relative h-64 overflow-hidden">
                <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80 group-hover:opacity-90 transition-opacity" />
            </div>

            <div className="p-4">
                <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 transition-colors">
                    {project.title}
                </h3>

                <p className="text-gray-600 mb-4 line-clamp-2">
                    {project.description}
                </p>

                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    {project.location && (
                        <div className="flex items-center gap-1">
                            <MapPin className="w-4 h-4" />
                            <span>{project.location}</span>
                        </div>
                    )}

                    {project.completionDate && (
                        <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>{project.completionDate}</span>
                        </div>
                    )}

                    {project.category && (
                        <span className="inline-block px-2 py-1 bg-gray-100 rounded-md text-xs">
                            {project.category}
                        </span>
                    )}
                </div>
            </div>
        </Link>
    );
};

export const ProjectFilter: React.FC<ProjectFilterProps> = ({
    categories,
    locations,
    tags,
    selectedFilters,
    onFilterChange,
}) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
                <Filter className="w-5 h-5" />
                <h3>Filtrer prosjekter</h3>
            </div>

            {categories.length > 0 && (
                <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Kategori</h4>
                    <div className="flex flex-wrap gap-2">
                        <button
                            className={`px-3 py-1 text-sm rounded-full ${
                                !selectedFilters.category
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                            onClick={() => onFilterChange("category", "")}
                        >
                            Alle
                        </button>
                        {categories.map((category) => (
                            <button
                                key={category}
                                className={`px-3 py-1 text-sm rounded-full ${
                                    selectedFilters.category === category
                                        ? "bg-green-100 text-green-800"
                                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                }`}
                                onClick={() =>
                                    onFilterChange("category", category)
                                }
                            >
                                {category}
                            </button>
                        ))}
                    </div>
                </div>
            )}

            {locations.length > 0 && (
                <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Sted</h4>
                    <div className="flex flex-wrap gap-2">
                        <button
                            className={`px-3 py-1 text-sm rounded-full ${
                                !selectedFilters.location
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                            onClick={() => onFilterChange("location", "")}
                        >
                            Alle steder
                        </button>
                        {locations.map((location) => (
                            <button
                                key={location}
                                className={`px-3 py-1 text-sm rounded-full ${
                                    selectedFilters.location === location
                                        ? "bg-green-100 text-green-800"
                                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                }`}
                                onClick={() =>
                                    onFilterChange("location", location)
                                }
                            >
                                {location}
                            </button>
                        ))}
                    </div>
                </div>
            )}

            {tags.length > 0 && (
                <div>
                    <h4 className="text-sm font-medium mb-2">Stikkord</h4>
                    <div className="flex flex-wrap gap-2">
                        <button
                            className={`px-3 py-1 text-sm rounded-full ${
                                !selectedFilters.tag
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                            onClick={() => onFilterChange("tag", "")}
                        >
                            Alle
                        </button>
                        {tags.map((tag) => (
                            <button
                                key={tag}
                                className={`px-3 py-1 text-sm rounded-full ${
                                    selectedFilters.tag === tag
                                        ? "bg-green-100 text-green-800"
                                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                }`}
                                onClick={() => onFilterChange("tag", tag)}
                            >
                                {tag}
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export const ProjectGrid: React.FC<{
    projects: ProjectType[];
    filter?: {
        category?: string;
        location?: string;
        tag?: string;
    };
    layout?: "grid" | "masonry" | "carousel";
    variant?: "default" | "featured" | "compact";
    className?: string;
}> = ({
    projects,
    filter = {},
    layout = "grid",
    variant = "default",
    className = "",
}) => {
    // Filter projects based on criteria
    const filteredProjects = projects.filter((project) => {
        if (filter.category && project.category !== filter.category)
            return false;
        if (filter.location && project.location !== filter.location)
            return false;
        if (filter.tag && !project.tags.includes(filter.tag)) return false;
        return true;
    });

    if (layout === "masonry") {
        // Simple masonry-like layout with different heights
        return (
            <div
                className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
            >
                {filteredProjects.map((project, index) => (
                    <div
                        key={project.id}
                        className={index % 3 === 0 ? "row-span-2" : ""}
                    >
                        <ProjectCard project={project} variant={variant} />
                    </div>
                ))}
            </div>
        );
    }

    if (layout === "carousel") {
        // Simple carousel layout (in a real app, you'd use a carousel library)
        return (
            <div className={`flex overflow-x-auto gap-6 pb-4 ${className}`}>
                {filteredProjects.map((project) => (
                    <div
                        key={project.id}
                        className="min-w-[300px] max-w-[400px]"
                    >
                        <ProjectCard project={project} variant={variant} />
                    </div>
                ))}
            </div>
        );
    }

    // Default grid layout
    return (
        <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
        >
            {filteredProjects.map((project) => (
                <ProjectCard
                    key={project.id}
                    project={project}
                    variant={variant}
                />
            ))}
        </div>
    );
};

export const ProjectsCarousel: React.FC<{
    projects: ProjectType[];
}> = ({ projects }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    const handleNext = () => {
        setCurrentIndex((prev) => (prev === projects.length - 1 ? 0 : prev + 1));
    };

    const handlePrevious = () => {
        setCurrentIndex((prev) => (prev === 0 ? projects.length - 1 : prev - 1));
    };

    useEffect(() => {
        const interval = setInterval(() => {
            handleNext();
        }, 5000);
        return () => clearInterval(interval);
    }, []);

    if (!projects.length) return null;

    return (
        <div className="relative">
            <div className="overflow-hidden rounded-lg">
                <div
                    className="flex transition-transform duration-500"
                    style={{ transform: `translateX(-${currentIndex * 100}%)` }}
                >
                    {projects.map((project) => (
                        <div key={project.id} className="w-full flex-shrink-0">
                            <ProjectCard project={project} variant="featured" />
                        </div>
                    ))}
                </div>
            </div>

            <button
                onClick={handlePrevious}
                className="absolute left-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 rounded-full shadow-md text-gray-800 hover:bg-white"
                aria-label="Previous project"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                    />
                </svg>
            </button>

            <button
                onClick={handleNext}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 rounded-full shadow-md text-gray-800 hover:bg-white"
                aria-label="Next project"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                    />
                </svg>
            </button>

            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                {projects.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-2 h-2 rounded-full ${
                            index === currentIndex
                                ? "bg-white w-4"
                                : "bg-white/50 hover:bg-white/70"
                        }`}
                        aria-label={`Go to project ${index + 1}`}
                        aria-current={index === currentIndex}
                    />
                ))}
            </div>
        </div>
    );
};```


#### `features\services\ServiceCard.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { ServiceType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ServiceCardProps {
  service: ServiceType;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  className = '',
  variant = 'default'
}) => {
  const { id, title, description, image, features } = service;

  if (variant === 'compact') {
    return (
      <div className={cn(
        'flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow',
        className
      )}>
        <div className="w-full sm:w-1/3 h-40 sm:h-auto rounded-md overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-3">
            {description}
          </p>
          <Link
            to={`/tjenester/${id}`}
            className="text-green-600 font-medium hover:text-green-700 transition-colors flex items-center gap-1"
          >
            Les mer
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </div>
    );
  }

  if (variant === 'featured') {
    return (
      <div className={cn(
        'bg-white rounded-lg shadow-md overflow-hidden',
        className
      )}>
        <div className="h-48 overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-3">{title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-3">
            {description}
          </p>
          {features && features.length > 0 && (
            <ul className="mb-4 space-y-1">
              {features.slice(0, 3).map((feature, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600">
                  <span className="text-green-500 mr-2">â€¢</span>
                  {feature}
                </li>
              ))}
            </ul>
          )}
          <Link
            to={`/tjenester/${id}`}
            className="inline-block bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors"
          >
            Les mer
          </Link>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn(
      'relative h-[400px] group overflow-hidden rounded-lg',
      className
    )}>
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        <Link
          to={`/tjenester/${id}`}
          className="inline-flex items-center gap-2 bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;```


#### `features\services\ServiceFeature.tsx`

```tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ServiceFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

export const ServiceFeature: React.FC<ServiceFeatureProps> = ({
  icon,
  title,
  description,
  className
}) => {
  return (
    <div className={cn(
      "text-center p-6 bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-md",
      className
    )}>
      <div className="inline-block p-3 bg-green-50 rounded-full mb-4 text-green-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
};

export default ServiceFeature;```


#### `features\services\ServiceGrid.tsx`

```tsx
import React from 'react';
import { ServiceType } from '@/lib/types';
import { ServiceCard } from './ServiceCard';
import { cn } from '@/lib/utils';

interface ServiceGridProps {
  services: ServiceType[];
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
}

export const ServiceGrid: React.FC<ServiceGridProps> = ({
  services,
  variant = 'default',
  className = ''
}) => {
  return (
    <div className={cn(
      'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
      className
    )}>
      {services.map((service) => (
        <ServiceCard
          key={service.id}
          service={service}
          variant={variant}
        />
      ))}
    </div>
  );
};

export default ServiceGrid;```


#### `features\services.tsx`

```tsx
import React from "react";
import { Link } from "react-router-dom";
import { ServiceType } from "../types";

export interface ServiceCardProps {
    service: ServiceType;
    className?: string;
    variant?: "default" | "compact" | "featured";
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
    service,
    className = "",
    variant = "default",
}) => {
    const { id, title, description, image } = service;

    if (variant === "compact") {
        return (
            <div
                className={`flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow ${className}`}
            >
                <div className="w-full sm:w-1/3 h-40 sm:h-auto rounded-md overflow-hidden">
                    <img
                        src={image}
                        alt={title}
                        className="w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
                <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2">{title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                        {description}
                    </p>
                    <Link
                        to={`/tjenester/${id}`}
                        className="text-green-600 font-medium hover:text-green-700 transition-colors"
                    >
                        Les mer
                    </Link>
                </div>
            </div>
        );
    }

    if (variant === "featured") {
        return (
            <div
                className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}
            >
                <div className="h-48 overflow-hidden">
                    <img
                        src={image}
                        alt={title}
                        className="w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
                <div className="p-6">
                    <h3 className="text-xl font-semibold mb-3">{title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                        {description}
                    </p>
                    <Link
                        to={`/tjenester/${id}`}
                        className="inline-block bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors"
                    >
                        Les mer
                    </Link>
                </div>
            </div>
        );
    }

    // Default variant
    return (
        <div
            className={`relative h-[400px] group overflow-hidden rounded-lg ${className}`}
        >
            <div
                className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style={{ backgroundImage: `url(${image})` }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
                <h3 className="text-2xl font-semibold mb-3">{title}</h3>
                <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
                <Link
                    to={`/tjenester/${id}`}
                    className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
                >
                    Les mer
                </Link>
            </div>
        </div>
    );
};

export const ServiceGrid: React.FC<{
    services: ServiceType[];
    variant?: "default" | "compact" | "featured";
    className?: string;
}> = ({ services, variant = "default", className = "" }) => {
    return (
        <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
        >
            {services.map((service) => (
                <ServiceCard
                    key={service.id}
                    service={service}
                    variant={variant}
                />
            ))}
        </div>
    );
};

export const ServiceFeatures: React.FC<{
    features: string[];
    className?: string;
}> = ({ features, className = "" }) => {
    return (
        <ul className={`space-y-2 ${className}`}>
            {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                    <svg
                        className="h-5 w-5 text-green-500 mr-2 mt-0.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                        />
                    </svg>
                    <span>{feature}</span>
                </li>
            ))}
        </ul>
    );
};

export const ServiceSection: React.FC<{
    title: string;
    subtitle?: string;
    services: ServiceType[];
    variant?: "default" | "compact" | "featured";
    className?: string;
}> = ({ title, subtitle, services, variant = "default", className = "" }) => {
    return (
        <section className={`py-12 ${className}`}>
            <div className="container mx-auto px-4">
                <div className="text-center mb-12">
                    <h2 className="text-3xl font-bold mb-4">{title}</h2>
                    {subtitle && (
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            {subtitle}
                        </p>
                    )}
                </div>
                <ServiceGrid services={services} variant={variant} />
            </div>
        </section>
    );
};
```


#### `features\team.tsx`

```tsx
import React from "react";
import { Phone, Mail, Users } from "lucide-react";

export interface TeamMemberType {
    name: string;
    title: string;
    phone: string;
    email: string;
    image: string;
    bio?: string;
    specialties?: string[];
}

export interface TeamMemberProps {
    member: TeamMemberType;
    className?: string;
    variant?: "default" | "compact" | "detailed";
}

export const TeamMember: React.FC<TeamMemberProps> = ({
    member,
    className = "",
    variant = "default",
}) => {
    const { name, title, phone, email, image, bio, specialties } = member;

    if (variant === "compact") {
        return (
            <div
                className={`flex items-center gap-4 p-4 bg-white rounded-lg shadow-sm ${className}`}
            >
                <div className="w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
                    <img
                        src={image}
                        alt={name}
                        className="w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
                <div>
                    <h3 className="font-semibold">{name}</h3>
                    <p className="text-green-500 text-sm">{title}</p>
                </div>
            </div>
        );
    }

    if (variant === "detailed") {
        return (
            <div
                className={`bg-white rounded-lg overflow-hidden shadow-lg ${className}`}
            >
                <div className="relative h-[300px]">
                    <img
                        src={image}
                        alt={name}
                        className="absolute inset-0 w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
                <div className="p-6">
                    <h3 className="text-2xl font-semibold mb-1">{name}</h3>
                    <p className="text-green-500 mb-4">{title}</p>

                    {bio && <p className="text-gray-600 mb-4">{bio}</p>}

                    {specialties && specialties.length > 0 && (
                        <div className="mb-4">
                            <h4 className="font-medium mb-2">Spesialiteter:</h4>
                            <div className="flex flex-wrap gap-2">
                                {specialties.map((specialty, index) => (
                                    <span
                                        key={index}
                                        className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm"
                                    >
                                        {specialty}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}

                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <Phone className="w-5 h-5 text-green-500" />
                            <a
                                href={`tel:${phone}`}
                                className="text-gray-600 hover:text-green-600"
                            >
                                {phone}
                            </a>
                        </div>
                        <div className="flex items-center gap-2">
                            <Mail className="w-5 h-5 text-green-500" />
                            <a
                                href={`mailto:${email}`}
                                className="text-green-500 hover:text-green-600"
                            >
                                {email}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Default variant
    return (
        <div
            className={`bg-white rounded-lg overflow-hidden shadow-lg ${className}`}
        >
            <div className="relative h-[234px] sm:h-[312px]">
                <img
                    src={image}
                    alt={name}
                    className="absolute inset-0 w-full h-full object-cover"
                    loading="lazy"
                />
            </div>
            <div className="p-6">
                <h3 className="text-xl font-semibold mb-1">{name}</h3>
                <p className="text-green-500 mb-4">{title}</p>
                <div className="space-y-2">
                    <p className="text-gray-600">{phone}</p>
                    <a
                        href={`mailto:${email}`}
                        className="text-green-500 hover:text-green-600"
                    >
                        {email}
                    </a>
                </div>
            </div>
        </div>
    );
};

export const TeamGrid: React.FC<{
    members: TeamMemberType[];
    variant?: "default" | "compact" | "detailed";
    className?: string;
}> = ({ members, variant = "default", className = "" }) => {
    return (
        <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
        >
            {members.map((member, index) => (
                <TeamMember key={index} member={member} variant={variant} />
            ))}
        </div>
    );
};

export const TeamSection: React.FC<{
    title: string;
    subtitle?: string;
    members: TeamMemberType[];
    variant?: "default" | "compact" | "detailed";
    className?: string;
}> = ({ title, subtitle, members, variant = "default", className = "" }) => {
    return (
        <section className={`py-12 ${className}`}>
            <div className="container mx-auto px-4">
                <div className="text-center mb-12">
                    <div className="flex items-center justify-center gap-2 text-green-600 mb-2">
                        <Users className="h-6 w-6" />
                        <span className="font-medium">VÃ¥rt team</span>
                    </div>
                    <h2 className="text-3xl font-bold mb-4">{title}</h2>
                    {subtitle && (
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            {subtitle}
                        </p>
                    )}
                </div>
                <TeamGrid members={members} variant={variant} />
            </div>
        </section>
    );
};
```


#### `features\testimonials\AverageRating.tsx`

```tsx
import React from 'react';
import { Star } from 'lucide-react';
import { TestimonialType } from './Testimonial';
import { cn } from '../../lib/utils';

interface AverageRatingProps {
  testimonials: TestimonialType[];
  className?: string;
  showCount?: boolean;
}

const AverageRating: React.FC<AverageRatingProps> = ({
  testimonials,
  className = '',
  showCount = true
}) => {
  if (!testimonials.length) return null;

  // Calculate average rating
  const totalRating = testimonials.reduce(
    (sum, testimonial) => sum + testimonial.rating,
    0
  );
  const averageRating = totalRating / testimonials.length;
  const roundedRating = Math.round(averageRating * 10) / 10; // Round to 1 decimal place

  return (
    <div className={cn('flex items-center', className)}>
      <div className="flex items-center mr-2">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={cn(
              'w-5 h-5',
              i < Math.floor(roundedRating)
                ? 'text-yellow-400 fill-current'
                : i < roundedRating
                ? 'text-yellow-400 fill-current opacity-50'
                : 'text-gray-300'
            )}
          />
        ))}
      </div>
      <span className="font-medium">{roundedRating.toFixed(1)}</span>
      {showCount && (
        <span className="text-gray-500 text-sm ml-2">
          ({testimonials.length}{' '}
          {testimonials.length === 1 ? 'vurdering' : 'vurderinger'})
        </span>
      )}
    </div>
  );
};

export default AverageRating;```


#### `features\testimonials\Testimonial.tsx`

```tsx
import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface TestimonialType {
  name: string;
  location: string;
  text: string;
  rating: number;
  projectType: string;
  image?: string;
}

interface TestimonialProps {
  testimonial: TestimonialType;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export const Testimonial: React.FC<TestimonialProps> = ({
  testimonial,
  className = '',
  variant = 'default'
}) => {
  const { name, location, text, rating, projectType, image } = testimonial;

  const renderStars = () => (
    <div className="flex gap-1">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={cn(
            'w-4 h-4',
            i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          )}
        />
      ))}
    </div>
  );

  if (variant === 'compact') {
    return (
      <div className={cn('p-4 bg-white rounded-lg shadow-sm', className)}>
        {renderStars()}
        <p className="text-gray-700 text-sm my-2 line-clamp-2">
          {text}
        </p>
        <p className="font-medium text-sm">{name}</p>
      </div>
    );
  }

  if (variant === 'featured') {
    return (
      <div className={cn(
        'p-6 bg-white rounded-lg shadow-md border-l-4 border-green-500',
        className
      )}>
        {renderStars()}
        <p className="text-gray-700 my-4 italic">"{text}"</p>
        <div className="flex justify-between items-end">
          <div className="flex items-center gap-3">
            {image && (
              <img 
                src={image} 
                alt={name} 
                className="w-10 h-10 rounded-full object-cover"
              />
            )}
            <div>
              <p className="font-medium">{name}</p>
              <p className="text-sm text-gray-500">{location}</p>
            </div>
          </div>
          <p className="text-sm text-green-600">{projectType}</p>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn(
      'bg-white p-5 rounded-lg shadow-sm',
      className
    )} itemScope itemType="http://schema.org/Review">
      <div className="flex gap-1 mb-3">{renderStars()}</div>
      <p className="text-gray-700 text-sm mb-3" itemProp="reviewBody">
        {text}
      </p>
      <div className="border-t pt-3">
        <p className="font-medium text-sm" itemProp="author">{name}</p>
        <p className="text-xs text-gray-500">{location}</p>
        <p className="text-xs text-green-600" itemProp="itemReviewed">
          {projectType}
        </p>
      </div>
    </div>
  );
};

export default Testimonial;```


#### `features\testimonials\TestimonialFilter.tsx`

```tsx
import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialFilterProps {
  selectedRating: number | null;
  onRatingChange: (rating: number | null) => void;
  counts: { [key: number]: number };
  totalCount: number;
}

export const TestimonialFilter: React.FC<TestimonialFilterProps> = ({
  selectedRating,
  onRatingChange,
  counts,
  totalCount
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6">
      <h3 className="font-medium mb-3">Filter etter vurdering</h3>

      <div className="space-y-2">
        <button
          onClick={() => onRatingChange(null)}
          className={cn(
            'w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors',
            selectedRating === null ? 'bg-green-50 text-green-700' : ''
          )}
        >
          <span className="flex items-center">
            <span>Alle vurderinger</span>
          </span>
          <span className="text-gray-500 text-sm">{totalCount}</span>
        </button>

        {[5, 4, 3, 2, 1].map((rating) => (
          <button
            key={rating}
            onClick={() => onRatingChange(rating)}
            className={cn(
              'w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors',
              selectedRating === rating ? 'bg-green-50 text-green-700' : ''
            )}
            disabled={!counts[rating]}
          >
            <span className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    'w-4 h-4',
                    i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  )}
                />
              ))}
            </span>
            <span className="text-gray-500 text-sm">
              {counts[rating] || 0}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default TestimonialFilter;```


#### `features\testimonials\TestimonialSlider.tsx`

```tsx
import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { TestimonialType, Testimonial } from './Testimonial';
import { cn } from '@/lib/utils';

interface TestimonialSliderProps {
  testimonials: TestimonialType[];
  className?: string;
  title?: string;
  subtitle?: string;
  itemsPerView?: number;
}

export const TestimonialSlider: React.FC<TestimonialSliderProps> = ({
  testimonials,
  className = '',
  title,
  subtitle,
  itemsPerView = 1
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePrevious = useCallback(() => {
    if (isAnimating || testimonials.length <= itemsPerView) return;
    setIsAnimating(true);
    setCurrentIndex((prev) =>
      prev === 0 ? Math.max(0, testimonials.length - itemsPerView) : prev - 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, testimonials.length, itemsPerView]);

  const handleNext = useCallback(() => {
    if (isAnimating || testimonials.length <= itemsPerView) return;
    setIsAnimating(true);
    setCurrentIndex((prev) =>
      prev >= testimonials.length - itemsPerView ? 0 : prev + 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, testimonials.length, itemsPerView]);

  // Auto-advance the slider
  useEffect(() => {
    const interval = setInterval(handleNext, 6000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Reset current index when testimonials or itemsPerView changes
  useEffect(() => {
    setCurrentIndex(0);
  }, [testimonials, itemsPerView]);

  // If we don't have enough testimonials to fill the slider, don't show navigation
  const showNavigation = testimonials.length > itemsPerView;

  // Calculate which testimonials to display
  const visibleTestimonials = () => {
    const endIndex = Math.min(
      currentIndex + itemsPerView,
      testimonials.length
    );
    return testimonials.slice(currentIndex, endIndex);
  };

  // Calculate total number of pages
  const totalPages = Math.ceil(testimonials.length / itemsPerView);
  const currentPage = Math.floor(currentIndex / itemsPerView);

  if (testimonials.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {title && (
            <h3 className="text-xl font-semibold mb-2">{title}</h3>
          )}
          {subtitle && (
            <p className="text-gray-600 text-sm">{subtitle}</p>
          )}
        </div>
      )}

      <div className="relative">
        {/* Navigation buttons */}
        {showNavigation && (
          <>
            <button
              onClick={handlePrevious}
              className="absolute top-1/2 -left-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
              aria-label="Forrige"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <button
              onClick={handleNext}
              className="absolute top-1/2 -right-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
              aria-label="Neste"
            >
              <ArrowRight className="w-5 h-5" />
            </button>
          </>
        )}

        {/* Testimonial slider */}
        <div
          className="grid gap-4 transition-all duration-500"
          style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${itemsPerView}, minmax(0, 1fr))`,
            gap: '1rem',
            transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`
          }}
        >
          {visibleTestimonials().map((testimonial, index) => (
            <div key={index} className="w-full">
              <Testimonial testimonial={testimonial} />
            </div>
          ))}
        </div>

        {/* Pagination dots */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2 mt-6">
            {[...Array(totalPages)].map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={cn(
                  'w-2 h-2 rounded-full transition-all',
                  index === currentPage
                    ? 'bg-green-500 w-4'
                    : 'bg-gray-300'
                )}
                aria-label={`GÃ¥ til side ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestimonialSlider;```


#### `features\testimonials\TestimonialsSection.tsx`

```tsx
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Container } from '../../components/ui';
import TestimonialSlider from './TestimonialSlider';
import AverageRating from './AverageRating';
import { TestimonialType } from './Testimonial';

interface TestimonialsSectionProps {
  testimonials: TestimonialType[];
  className?: string;
}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  testimonials,
  className = ''
}) => {
  const [itemsPerView, setItemsPerView] = useState(1);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setItemsPerView(3);
      } else if (window.innerWidth >= 768) {
        setItemsPerView(2);
      } else {
        setItemsPerView(1);
      }
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <Container>
        <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
            Hva våre <span className="text-green-500">kunder sier</span>
          </h2>

          <div className="flex justify-center mb-4">
            <AverageRating testimonials={testimonials} />
          </div>

          <p className="text-gray-600 text-sm text-center mb-8 max-w-2xl mx-auto">
            Vi er stolte av å ha hjulpet mange fornøyde kunder i
            Ringerike-regionen
          </p>

          <TestimonialSlider
            testimonials={testimonials.slice(0, 5)}
            itemsPerView={itemsPerView}
          />

          <div className="text-center mt-8">
            <Link
              to="/kundehistorier"
              className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
            >
              Se alle kundehistorier
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default TestimonialsSection;```


#### `features\testimonials.tsx`

```tsx
import React, { useState, useEffect } from "react";
import { Star, ArrowLeft, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

// Types
export interface TestimonialType {
    name: string;
    location: string;
    text: string;
    rating: number;
    projectType: string;
}

// Testimonial Component
interface TestimonialProps {
    testimonial: TestimonialType;
    className?: string;
    variant?: "default" | "compact" | "featured";
}

export const Testimonial: React.FC<TestimonialProps> = ({
    testimonial,
    className = "",
    variant = "default",
}) => {
    const { name, location, text, rating, projectType } = testimonial;

    const renderStars = () => (
        <div className="flex gap-1">
            {[...Array(5)].map((_, i) => (
                <Star
                    key={i}
                    className={`w-4 h-4 ${
                        i < rating
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300"
                    }`}
                />
            ))}
        </div>
    );

    if (variant === "compact") {
        return (
            <div className={`p-4 bg-white rounded-lg shadow-sm ${className}`}>
                {renderStars()}
                <p className="text-gray-700 text-sm my-2 line-clamp-2">
                    {text}
                </p>
                <p className="font-medium text-sm">{name}</p>
            </div>
        );
    }

    if (variant === "featured") {
        return (
            <div
                className={`p-6 bg-white rounded-lg shadow-md border-l-4 border-green-500 ${className}`}
            >
                {renderStars()}
                <p className="text-gray-700 my-4 italic">"{text}"</p>
                <div className="flex justify-between items-end">
                    <div>
                        <p className="font-medium">{name}</p>
                        <p className="text-sm text-gray-500">{location}</p>
                    </div>
                    <p className="text-sm text-green-600">{projectType}</p>
                </div>
            </div>
        );
    }

    // Default variant
    return (
        <div
            className={`bg-white p-5 rounded-lg shadow-sm ${className}`}
            itemScope
            itemType="http://schema.org/Review"
        >
            <div className="flex gap-1 mb-3">{renderStars()}</div>
            <p className="text-gray-700 text-sm mb-3" itemProp="reviewBody">
                {text}
            </p>
            <div className="border-t pt-3">
                <p className="font-medium text-sm" itemProp="author">
                    {name}
                </p>
                <p className="text-xs text-gray-500">{location}</p>
                <p className="text-xs text-green-600" itemProp="itemReviewed">
                    {projectType}
                </p>
            </div>
        </div>
    );
};

// TestimonialSlider Component
interface TestimonialSliderProps {
    testimonials: TestimonialType[];
    className?: string;
    title?: string;
    subtitle?: string;
    itemsPerView: number;
}

export const TestimonialSlider: React.FC<TestimonialSliderProps> = ({
    testimonials,
    className = "",
    title,
    subtitle,
    itemsPerView = 1,
}) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAnimating, setIsAnimating] = useState(false);

    const handlePrevious = () => {
        if (isAnimating || testimonials.length <= itemsPerView) return;
        setIsAnimating(true);
        setCurrentIndex((prev) =>
            prev === 0
                ? Math.max(0, testimonials.length - itemsPerView)
                : prev - 1
        );
        setTimeout(() => setIsAnimating(false), 500);
    };

    const handleNext = () => {
        if (isAnimating || testimonials.length <= itemsPerView) return;
        setIsAnimating(true);
        setCurrentIndex((prev) =>
            prev >= testimonials.length - itemsPerView ? 0 : prev + 1
        );
        setTimeout(() => setIsAnimating(false), 500);
    };

    // Reset current index when testimonials or itemsPerView changes
    useEffect(() => {
        setCurrentIndex(0);
    }, [testimonials, itemsPerView]);

    // If we don't have enough testimonials to fill the slider, don't show navigation
    const showNavigation = testimonials.length > itemsPerView;

    // Calculate which testimonials to display
    const visibleTestimonials = () => {
        const endIndex = Math.min(
            currentIndex + itemsPerView,
            testimonials.length
        );
        return testimonials.slice(currentIndex, endIndex);
    };

    // Calculate total number of pages
    const totalPages = Math.ceil(testimonials.length / itemsPerView);
    const currentPage = Math.floor(currentIndex / itemsPerView);

    if (testimonials.length === 0) {
        return null;
    }

    return (
        <div className={`${className}`}>
            {(title || subtitle) && (
                <div className="text-center mb-8">
                    {title && (
                        <h3 className="text-xl font-semibold mb-2">{title}</h3>
                    )}
                    {subtitle && (
                        <p className="text-gray-600 text-sm">{subtitle}</p>
                    )}
                </div>
            )}

            <div className="relative">
                {/* Navigation buttons */}
                {showNavigation && (
                    <>
                        <button
                            onClick={handlePrevious}
                            className="absolute top-1/2 -left-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
                            aria-label="Forrige"
                        >
                            <ArrowLeft className="w-5 h-5" />
                        </button>
                        <button
                            onClick={handleNext}
                            className="absolute top-1/2 -right-4 -translate-y-1/2 p-2 rounded-full bg-white shadow-md text-gray-600 hover:text-gray-900 transition-colors z-10"
                            aria-label="Neste"
                        >
                            <ArrowRight className="w-5 h-5" />
                        </button>
                    </>
                )}

                {/* Testimonial slider */}
                <div
                    className={`grid gap-4 transition-all duration-500`}
                    style={{
                        display: "grid",
                        gridTemplateColumns: `repeat(${itemsPerView}, minmax(0, 1fr))`,
                        gap: "1rem",
                    }}
                >
                    {visibleTestimonials().map((testimonial, index) => (
                        <div key={index} className="w-full">
                            <Testimonial testimonial={testimonial} />
                        </div>
                    ))}
                </div>

                {/* Pagination dots */}
                {totalPages > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                        {[...Array(totalPages)].map((_, index) => (
                            <button
                                key={index}
                                onClick={() =>
                                    setCurrentIndex(index * itemsPerView)
                                }
                                className={`w-2 h-2 rounded-full transition-all ${
                                    index === currentPage
                                        ? "bg-green-500 w-4"
                                        : "bg-gray-300"
                                }`}
                                aria-label={`GÃ¥ til side ${index + 1}`}
                            />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

// AverageRating Component
interface AverageRatingProps {
    testimonials: TestimonialType[];
    className?: string;
    showCount?: boolean;
}

export const AverageRating: React.FC<AverageRatingProps> = ({
    testimonials,
    className = "",
    showCount = true,
}) => {
    if (!testimonials.length) return null;

    // Calculate average rating
    const totalRating = testimonials.reduce(
        (sum, testimonial) => sum + testimonial.rating,
        0
    );
    const averageRating = totalRating / testimonials.length;
    const roundedRating = Math.round(averageRating * 10) / 10; // Round to 1 decimal place

    return (
        <div className={`flex items-center ${className}`}>
            <div className="flex items-center mr-2">
                {[...Array(5)].map((_, i) => (
                    <Star
                        key={i}
                        className={`w-5 h-5 ${
                            i < Math.floor(roundedRating)
                                ? "text-yellow-400 fill-current"
                                : i < roundedRating
                                ? "text-yellow-400 fill-current opacity-50"
                                : "text-gray-300"
                        }`}
                    />
                ))}
            </div>
            <span className="font-medium">{roundedRating.toFixed(1)}</span>
            {showCount && (
                <span className="text-gray-500 text-sm ml-2">
                    ({testimonials.length}{" "}
                    {testimonials.length === 1 ? "vurdering" : "vurderinger"})
                </span>
            )}
        </div>
    );
};

// TestimonialFilter Component
interface TestimonialFilterProps {
    selectedRating: number | null;
    onRatingChange: (rating: number | null) => void;
    counts: { [key: number]: number };
    totalCount: number;
}

export const TestimonialFilter: React.FC<TestimonialFilterProps> = ({
    selectedRating,
    onRatingChange,
    counts,
    totalCount,
}) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow mb-6">
            <h3 className="font-medium mb-3">Filter etter vurdering</h3>

            <div className="space-y-2">
                <button
                    onClick={() => onRatingChange(null)}
                    className={`w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors ${
                        selectedRating === null
                            ? "bg-green-50 text-green-700"
                            : ""
                    }`}
                >
                    <span className="flex items-center">
                        <span>Alle vurderinger</span>
                    </span>
                    <span className="text-gray-500 text-sm">{totalCount}</span>
                </button>

                {[5, 4, 3, 2, 1].map((rating) => (
                    <button
                        key={rating}
                        onClick={() => onRatingChange(rating)}
                        className={`w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors ${
                            selectedRating === rating
                                ? "bg-green-50 text-green-700"
                                : ""
                        }`}
                        disabled={!counts[rating]}
                    >
                        <span className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                                <Star
                                    key={i}
                                    className={`w-4 h-4 ${
                                        i < rating
                                            ? "text-yellow-400 fill-current"
                                            : "text-gray-300"
                                    }`}
                                />
                            ))}
                        </span>
                        <span className="text-gray-500 text-sm">
                            {counts[rating] || 0}
                        </span>
                    </button>
                ))}
            </div>
        </div>
    );
};

// TestimonialForm Component
interface TestimonialFormProps {
    onSubmit: (formData: {
        name: string;
        location: string;
        text: string;
        rating: number;
        projectType: string;
    }) => void;
    className?: string;
}

export const TestimonialForm: React.FC<TestimonialFormProps> = ({
    onSubmit,
    className = "",
}) => {
    const [name, setName] = useState("");
    const [location, setLocation] = useState("");
    const [text, setText] = useState("");
    const [rating, setRating] = useState(5);
    const [projectType, setProjectType] = useState("");
    const [hoveredRating, setHoveredRating] = useState<number | null>(null);
    const [submitted, setSubmitted] = useState(false);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit({ name, location, text, rating, projectType });
        setSubmitted(true);
        // Reset form
        setName("");
        setLocation("");
        setText("");
        setRating(5);
        setProjectType("");
    };

    const projectTypes = [
        "Hagedesign",
        "Terrasse og beplantning",
        "StÃ¸ttemur",
        "Belegningsstein",
        "Platting og pergola",
        "CortenstÃ¥l og beplantning",
        "Komplett hageprosjekt",
        "InnkjÃ¸rsel og beplantning",
        "Terrassering",
        "Uteplass med pergola",
        "Annet",
    ];

    if (submitted) {
        return (
            <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
                <div className="text-center py-8">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg
                            className="w-8 h-8 text-green-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                            />
                        </svg>
                    </div>
                    <h3 className="text-xl font-semibold mb-2">
                        Takk for din tilbakemelding!
                    </h3>
                    <p className="text-gray-600 mb-4">
                        Vi setter stor pris pÃ¥ at du tok deg tid til Ã¥ dele din
                        opplevelse med oss.
                    </p>
                    <button
                        onClick={() => setSubmitted(false)}
                        className="text-green-600 hover:text-green-700 font-medium"
                    >
                        Send en ny tilbakemelding
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
            <h3 className="text-xl font-semibold mb-4">Del din opplevelse</h3>
            <p className="text-gray-600 mb-6">
                Vi setter pris pÃ¥ din tilbakemelding. Din opplevelse hjelper oss
                Ã¥ forbedre vÃ¥re tjenester og gir andre kunder verdifull innsikt.
            </p>

            <form onSubmit={handleSubmit}>
                <div className="mb-4">
                    <label
                        htmlFor="name"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Navn *
                    </label>
                    <input
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        required
                    />
                </div>

                <div className="mb-4">
                    <label
                        htmlFor="location"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Sted *
                    </label>
                    <input
                        type="text"
                        id="location"
                        value={location}
                        onChange={(e) => setLocation(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        required
                    />
                </div>

                <div className="mb-4">
                    <label
                        htmlFor="projectType"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Prosjekttype *
                    </label>
                    <select
                        id="projectType"
                        value={projectType}
                        onChange={(e) => setProjectType(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        required
                    >
                        <option value="">Velg prosjekttype</option>
                        {projectTypes.map((type) => (
                            <option key={type} value={type}>
                                {type}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Din vurdering *
                    </label>
                    <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map((value) => (
                            <button
                                key={value}
                                type="button"
                                onClick={() => setRating(value)}
                                onMouseEnter={() => setHoveredRating(value)}
                                onMouseLeave={() => setHoveredRating(null)}
                                className="focus:outline-none"
                            >
                                <Star
                                    className={`w-6 h-6 ${
                                        (
                                            hoveredRating !== null
                                                ? value <= hoveredRating
                                                : value <= rating
                                        )
                                            ? "text-yellow-400 fill-current"
                                            : "text-gray-300"
                                    }`}
                                />
                            </button>
                        ))}
                    </div>
                </div>

                <div className="mb-6">
                    <label
                        htmlFor="text"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Din tilbakemelding *
                    </label>
                    <textarea
                        id="text"
                        value={text}
                        onChange={(e) => setText(e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        required
                    />
                </div>

                <button
                    type="submit"
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                    Send tilbakemelding
                </button>
            </form>
        </div>
    );
};

// TestimonialsSection Component
export interface TestimonialsSectionProps {
    testimonials: TestimonialType[];
    className?: string;
}

export const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
    testimonials,
    className = "",
}) => {
    // Use only the first 5 testimonials for the homepage
    const homeTestimonials = testimonials.slice(0, 5);
    const [itemsPerView, setItemsPerView] = useState(1);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth >= 1024) {
                setItemsPerView(3);
            } else if (window.innerWidth >= 768) {
                setItemsPerView(2);
            } else {
                setItemsPerView(1);
            }
        };

        // Set initial value
        handleResize();

        // Add event listener
        window.addEventListener("resize", handleResize);

        // Clean up
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <section className={`py-12 bg-gray-50 ${className}`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 className="text-2xl font-semibold mb-3 text-center">
                    Tilbakemeldinger fra{" "}
                    <span className="text-green-500">lokale kunder</span>
                </h2>

                <div className="flex justify-center mb-4">
                    <AverageRating testimonials={testimonials} />
                </div>

                <p className="text-gray-600 text-sm text-center mb-8 max-w-2xl mx-auto">
                    Vi er stolte av Ã¥ ha hjulpet mange fornÃ¸yde kunder i
                    Ringerike-regionen
                </p>

                <TestimonialSlider
                    testimonials={homeTestimonials}
                    itemsPerView={itemsPerView}
                />

                <div className="text-center mt-8">
                    <Link
                        to="/kundehistorier"
                        className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
                    >
                        Se alle kundehistorier
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 ml-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5l7 7-7 7"
                            />
                        </svg>
                    </Link>
                </div>
            </div>
        </section>
    );
};

// Export a default object with all components for easy importing
const Testimonials = {
    Testimonial,
    TestimonialSlider,
    AverageRating,
    TestimonialFilter,
    TestimonialForm,
    TestimonialsSection,
};

export default Testimonials;
```


#### `lib\context\AppContext.tsx`

```tsx
import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the shape of our global state
interface AppState {
  theme: 'light' | 'dark';
  language: 'no' | 'en';
  notifications: Array<{ id: string; message: string; type: 'info' | 'success' | 'error' }>;
}

// Define the context value shape
interface AppContextValue {
  state: AppState;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'no' | 'en') => void;
  addNotification: (message: string, type: 'info' | 'success' | 'error') => void;
  removeNotification: (id: string) => void;
}

// Create the context with a default value
const AppContext = createContext<AppContextValue | undefined>(undefined);

// Initial state
const initialState: AppState = {
  theme: 'light',
  language: 'no',
  notifications: [],
};

// Provider component
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AppState>(initialState);

  // Theme setter
  const setTheme = (theme: 'light' | 'dark') => {
    setState(prev => ({ ...prev, theme }));
    // Also apply theme to document for immediate visual feedback
    document.documentElement.classList.toggle('dark', theme === 'dark');
  };

  // Language setter
  const setLanguage = (language: 'no' | 'en') => {
    setState(prev => ({ ...prev, language }));
  };

  // Add notification
  const addNotification = (message: string, type: 'info' | 'success' | 'error') => {
    const id = Date.now().toString();
    setState(prev => ({
      ...prev,
      notifications: [...prev.notifications, { id, message, type }],
    }));
    
    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      removeNotification(id);
    }, 5000);
  };

  // Remove notification
  const removeNotification = (id: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(notification => notification.id !== id),
    }));
  };

  // Context value
  const value: AppContextValue = {
    state,
    setTheme,
    setLanguage,
    addNotification,
    removeNotification,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// Custom hook for using the context
export const useApp = (): AppContextValue => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}; ```


#### `main.tsx`

```tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
```


#### `pages\about\index.tsx`

```tsx
import React from "react";
import { Zap, ThumbsUp, MessageSquare } from "lucide-react";
import { Link } from "react-router-dom";
import Hero from "../../components/ui/Hero";
import Container from "../../components/ui/Container";

const AboutPage = () => {
  const teamMembers = [
    {
      name: "Kim Tuvsjøen",
      title: "Anleggsgartner",
      phone: "+47 902 14 153",
      email: "<EMAIL>",
      image: "/images/team/kim.webp"
    },
    {
      name: "Jan Iversen",
      title: "Anleggsgartner",
      phone: "+47 990 30 341",
      email: "<EMAIL>",
      image: "/images/team/jan.webp"
    },
  ];

  const features = [
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Tilpasning som teller",
      description:
        "Vi skreddersyr løsninger som passer både norsk klima og unike lokale forhold. Enten du er bedrift eller privatperson, får du løsninger som varer og ser bra ut.",
    },
    {
      icon: <ThumbsUp className="w-8 h-8" />,
      title: "Kvalitet hele veien",
      description:
        "Fra solide steiner til presist plantede hekk – vi går aldri på kompromiss med kvaliteten. Våre tjenester er like pålitelige som de er vakre, for både store og små prosjekter.",
    },
    {
      icon: <MessageSquare className="w-8 h-8" />,
      title: "Lokal tilpasning",
      description:
        "Vi kjenner de lokale forholdene, og skaper løsninger som passer perfekt til omgivelsene – alltid tilpasset det norske klimaet.",
    },
  ];

  return (
    <div>
      <Hero
        title="Hvem er vi"
        subtitle="Ringerike Landskap AS er et ungt, engasjert og voksende anleggsgartnerfirma og maskinentreprenør, basert i Røyse. Vi legger stolthet i arbeidet vi gjør, og fokuserer på høy kvalitet i både service og utførelse."
        backgroundImage="/images/site/hero-ringerike.webp"
      />

      <Container className="py-8 sm:py-12">
        {/* About Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center mb-12">
          <div className="relative h-[200px] sm:h-[280px] rounded-lg overflow-hidden">
            <img
              src="/images/team/firma.webp"
              alt="Ringerike Landskap kontor"
              className="absolute inset-0 w-full h-full object-cover"
              loading="lazy"
            />
          </div>
          <div>
            <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
              Om <span className="text-green-500">Ringerike Landskap</span>
            </h2>
            <p className="text-gray-600 mb-6">
              Ringerike Landskap AS er et ungt, engasjert og voksende anleggsgartnerfirma og maskinentreprenør,
              basert i Røyse. Vi spesialiserer oss på helhetlig planlegging, opparbeiding og vedlikehold av
              uteområder for både private hjem og næringseiendommer.
            </p>
            <p className="text-gray-600 mb-8">
              Har du spørsmål, ønsker å diskutere et prosjekt eller trenger hjelp? Ikke nøl med å kontakte oss!
            </p>
            <Link
              to="/kontakt"
              className="inline-block bg-green-500 text-white px-6 py-3 rounded-md hover:bg-green-600 transition-colors"
            >
              Kom i kontakt
            </Link>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-12">
          <h2 className="text-2xl sm:text-3xl font-semibold mb-8 text-center">
            Hvem er <span className="text-green-500">vi</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {teamMembers.map((member, index) => (
              <div
                key={index}
                className="bg-white rounded-lg overflow-hidden shadow-lg"
              >
                <div className="relative h-[234px] sm:h-[312px]">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="absolute inset-0 w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-1">
                    {member.name}
                  </h3>
                  <p className="text-green-500 mb-4">
                    {member.title}
                  </p>
                  <div className="space-y-2">
                    <p className="text-gray-600">
                      {member.phone}
                    </p>
                    <a
                      href={`mailto:${member.email}`}
                      className="text-green-500 hover:text-green-600"
                    >
                      {member.email}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="inline-block p-3 bg-green-100 rounded-full mb-4 text-green-500">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default AboutPage;```


#### `pages\AboutUs.tsx`

```tsx
import { Zap, ThumbsUp, MessageSquare } from "lucide-react";
import { Link } from "react-router-dom";
import Hero from "../components/common/Hero";

const AboutUs = () => {
    const teamMembers = [
        {
            name: "Kim Tuvsjøen",
            title: "Anleggsgartner",
            phone: "+47 902 14 153",
            email: "<EMAIL>",
            image: "/images/team/kim.webp"
        },
        {
            name: "Jan Iversen",
            title: "Anleggsgartner",
            phone: "+47 990 30 341",
            email: "<EMAIL>",
            image: "/images/team/jan.webp"
        },
    ];

    const features = [
        {
            icon: <Zap className="w-8 h-8" />,
            title: "Tilpasning som teller",
            description:
                "Vi skreddersyr løsninger som passer både norsk klima og unike lokale forhold. Enten du er bedrift eller privatperson, får du løsninger som varer og ser bra ut.",
        },
        {
            icon: <ThumbsUp className="w-8 h-8" />,
            title: "Kvalitet hele veien",
            description:
                "Fra solide steiner til presist plantede hekk – vi går aldri på kompromiss med kvaliteten. Våre tjenester er like pålitelige som de er vakre, for både store og små prosjekter.",
        },
        {
            icon: <MessageSquare className="w-8 h-8" />,
            title: "Lokal tilpasning",
            description:
                "Vi kjenner de lokale forholdene, og skaper løsninger som passer perfekt til omgivelsene – alltid tilpasset det norske klimaet.",
        },
    ];

    return (
        <div>
            <Hero
                title="Hvem er vi"
                subtitle="Ringerike Landskap AS er et ungt, engasjert og voksende anleggsgartnerfirma og maskinentreprenør, basert i Røyse. Vi legger stolthet i arbeidet vi gjør, og fokuserer på høy kvalitet i både service og utførelse."
                backgroundImage="/images/site/hero-ringerike.webp"
            />

            <div className="max-w-7xl mx-auto py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
                {/* About Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center mb-12">
                    <div className="relative h-[200px] sm:h-[280px] rounded-lg overflow-hidden">
                        <img
                            src="/images/team/firma.webp"
                            alt="Ringerike Landskap kontor"
                            className="absolute inset-0 w-full h-full object-cover"
                            loading="lazy"
                        />
                    </div>
                    <div>
                        <h2 className="text-2xl sm:text-3xl font-semibold mb-4">
                            Om{" "}
                            <span className="text-green-500">
                                Ringerike Landskap
                            </span>
                        </h2>
                        <p className="text-gray-600 mb-6">
                            Ringerike Landskap AS er et ungt, engasjert og
                            voksende anleggsgartnerfirma og maskinentreprenør,
                            basert i Røyse. Vi spesialiserer oss på helhetlig
                            planlegging, opparbeiding og vedlikehold av
                            uteområder for både private hjem og
                            næringseiendommer.
                        </p>
                        <p className="text-gray-600 mb-8">
                            Har du spørsmål, ønsker å diskutere et prosjekt
                            eller trenger hjelp? Ikke nøl med å kontakte oss!
                        </p>
                        <Link
                            to="/kontakt"
                            className="inline-block bg-green-500 text-white px-6 py-3 rounded-md hover:bg-green-600 transition-colors"
                        >
                            Kom i kontakt
                        </Link>
                    </div>
                </div>

                {/* Team Section */}
                <div className="mb-12">
                    <h2 className="text-2xl sm:text-3xl font-semibold mb-8 text-center">
                        Hvem er <span className="text-green-500">vi</span>
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {teamMembers.map((member, index) => (
                            <div
                                key={index}
                                className="bg-white rounded-lg overflow-hidden shadow-lg"
                            >
                                <div className="relative h-[234px] sm:h-[312px]">
                                    <img
                                        src={member.image}
                                        alt={member.name}
                                        className="absolute inset-0 w-full h-full object-cover"
                                        loading="lazy"
                                    />
                                </div>
                                <div className="p-6">
                                    <h3 className="text-xl font-semibold mb-1">
                                        {member.name}
                                    </h3>
                                    <p className="text-green-500 mb-4">
                                        {member.title}
                                    </p>
                                    <div className="space-y-2">
                                        <p className="text-gray-600">
                                            {member.phone}
                                        </p>
                                        <a
                                            href={`mailto:${member.email}`}
                                            className="text-green-500 hover:text-green-600"
                                        >
                                            {member.email}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Features Section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {features.map((feature, index) => (
                        <div key={index} className="text-center">
                            <div className="inline-block p-3 bg-green-100 rounded-full mb-4 text-green-500">
                                {feature.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-3">
                                {feature.title}
                            </h3>
                            <p className="text-gray-600">
                                {feature.description}
                            </p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default AboutUs;```


#### `pages\contact\index.tsx`

```tsx
import React, { useState } from 'react';
import Container from '../../components/ui/Container';
import Hero from '../../components/ui/Hero';

interface FormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  description?: string;
}

const ContactPage = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: ''
  });
  
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'name':
        return value.trim().length < 2 ? 'Navn må være minst 2 tegn' : '';
      case 'email':
        return !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Ugyldig e-postadresse' : '';
      case 'phone':
        return !/^(\+47)?[2-9]\d{7}$/.test(value.replace(/\s/g, '')) ? 'Ugyldig telefonnummer (8 siffer)' : '';
      case 'address':
        return value.trim().length < 5 ? 'Vennligst skriv inn full adresse' : '';
      case 'description':
        return value.trim().length < 10 ? 'Beskrivelsen må være minst 10 tegn' : '';
      default:
        return '';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Validate on change if field has been touched
    if (touched[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: validateField(name, value)
      }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({
      ...prev,
      [name]: validateField(name, value)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: FormErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key as keyof FormErrors] = error;
      }
    });
    
    // Mark all fields as touched
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    setErrors(newErrors);

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      console.log('Form submitted:', formData);
      setIsSubmitted(true);
    }
  };

  const getInputClassName = (fieldName: keyof FormData) => {
    const baseClasses = "w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent";
    return `${baseClasses} ${
      touched[fieldName] && errors[fieldName]
        ? "border-red-500 text-red-900 placeholder-red-300"
        : "border-gray-300"
    }`;
  };

  return (
    <div>
      <Hero 
        title="Kontakt oss"
        subtitle="Vi er her for å hjelpe deg med ditt neste prosjekt"
        backgroundImage="/images/site/hero-granite.webp"
      />
      
      <Container className="py-12">
        <div className="max-w-2xl mx-auto">
          {isSubmitted ? (
            <div className="text-center py-12 bg-green-50 rounded-lg">
              <h2 className="text-2xl font-semibold text-green-700 mb-4">Takk for din henvendelse!</h2>
              <p className="text-gray-600 mb-6">
                Vi har mottatt din melding og vil kontakte deg så snart som mulig.
              </p>
              <button 
                onClick={() => setIsSubmitted(false)}
                className="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
              >
                Send en ny henvendelse
              </button>
            </div>
          ) : (
            <>
              <h2 className="text-2xl sm:text-3xl font-semibold mb-2">
                Kom i kontakt <span className="text-green-500">med oss</span>
              </h2>
              <p className="text-gray-600 mb-8">
                Har du spørsmål, ønsker en befaring eller trenger hjelp? Ikke nøl med å kontakte oss!
              </p>
              <form onSubmit={handleSubmit} className="space-y-4" noValidate>
                <div className="space-y-1">
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Navn *"
                    className={getInputClassName('name')}
                    required
                  />
                  {touched.name && errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                  )}
                </div>
                
                <div className="space-y-1">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="E-post *"
                    className={getInputClassName('email')}
                    required
                  />
                  {touched.email && errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>
                
                <div className="space-y-1">
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Telefonnummer *"
                    className={getInputClassName('phone')}
                    required
                  />
                  {touched.phone && errors.phone && (
                    <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                  )}
                </div>
                
                <div className="space-y-1">
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Adresse *"
                    className={getInputClassName('address')}
                    required
                  />
                  {touched.address && errors.address && (
                    <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                  )}
                </div>
                
                <div className="space-y-1">
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Beskriv prosjektet ditt *"
                    rows={4}
                    className={getInputClassName('description')}
                    required
                  />
                  {touched.description && errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                  )}
                </div>
                
                <button
                  type="submit"
                  className="w-full sm:w-auto bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
                >
                  Send melding
                </button>
              </form>
            </>
          )}
        </div>
      </Container>
    </div>
  );
};

export default ContactPage;```


#### `pages\home\index.tsx`

```tsx
import React from 'react';
import { Link } from 'react-router-dom';
import Hero from '../../components/ui/Hero';
import Container from '../../components/ui/Container';
import { ServiceAreaList, SeasonalCTA } from '../../components/ui';
import SeasonalProjectsCarousel from '../../features/home/<USER>';
import FilteredServicesSection from '../../features/home/<USER>';
import { TestimonialsSection } from '../../features/testimonials';
import { useData } from '@/hooks/useData';
import { getServiceAreas, getTestimonials } from '@/lib/api';

const HomePage = () => {
  const { data: serviceAreas, loading: areasLoading } = useData(getServiceAreas, []);
  const { data: testimonials, loading: testimonialsLoading } = useData(getTestimonials, []);

  return (
    <div itemScope itemType="http://schema.org/LandscapingBusiness">
      <Hero
        title="Anleggsgartner & maskinentreprenør"
        subtitle="Som anleggsgartner og maskinentreprenør med lang erfaring i ringeriksregionen, med base på Røyse skaper vi varige uterom tilpasset dine ønsker. Vi kjenner grunnforhold i regionen og fokuserer på høy kvalitet i både service og utførelse."
        location="Røyse, Hole kommune"
        yearEstablished="2015"
        actionLink="/prosjekter"
        actionText="Se våre prosjekter"
        backgroundImage="/images/site/hero-main.webp"
      />

      <Container className="py-8 sm:py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
          <div className="lg:col-span-2">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl sm:text-2xl font-semibold">
                Aktuelle <span className="text-green-500">prosjekter</span>
              </h2>
              <Link
                to="/prosjekter"
                className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
              >
                Se våre prosjekter
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <SeasonalProjectsCarousel />
          </div>
          <div className="h-full">
            {areasLoading ? (
              <div className="h-full bg-gray-200 animate-pulse rounded-lg"></div>
            ) : (
              <ServiceAreaList areas={serviceAreas || []} />
            )}
          </div>
        </div>

        <div className="mt-8 sm:mt-12 grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
          <div className="lg:col-span-2">
            <FilteredServicesSection />
          </div>
          <div className="h-full">
            <SeasonalCTA />
          </div>
        </div>
      </Container>

      {testimonialsLoading ? (
        <div className="py-16 bg-gray-50">
          <Container>
            <div className="h-64 bg-gray-200 animate-pulse rounded-lg"></div>
          </Container>
        </div>
      ) : (
        <TestimonialsSection testimonials={testimonials || []} />
      )}
    </div>
  );
};

export default HomePage;```


#### `pages\Home.tsx`

```tsx
import { Link } from "react-router-dom";
import { Hero, Container, SeasonalCTA } from "../ui";
import { ServiceCard } from "../features/services";
import { ProjectCarousel } from "../features/home";
import { TestimonialsSection } from "../features/testimonials";
import { ServiceAreaList } from "../ui";
import { services } from "../data/services";
import { recentProjects } from "../data/projects";
import { testimonials } from "../data/testimonials";

// Sample service areas - in a real app, this would come from a data file
const serviceAreas = [
    {
        city: "Røyse",
        distance: "0 km",
        isBase: true,
        description: "Vårt hovedkontor og base for operasjoner",
    },
    {
        city: "Hønefoss",
        distance: "15 km",
        description: "Dekker hele Hønefoss og omegn",
    },
    {
        city: "Hole kommune",
        distance: "5-10 km",
        description: "Komplett dekning av Hole kommune",
    },
    {
        city: "Jevnaker",
        distance: "25 km",
    },
    {
        city: "Sundvollen",
        distance: "8 km",
    },
];

const Home = () => {
    return (
        <div itemScope itemType="http://schema.org/LandscapingBusiness">
            <Hero
                title="Anleggsgartner & maskinentreprenør"
                subtitle="Som anleggsgartner og maskinentreprenør med lang erfaring i ringeriksregionen, med base på Røyse skaper vi varige uterom tilpasset dine ønsker. Vi kjenner grunnforhold i regionen og fokuserer på høy kvalitet i både service og utførelse."
                location="Røyse, Hole kommune"
                yearEstablished="2015"
                actionLink="/prosjekter"
                actionText="Se våre prosjekter"
            />

            <Container className="py-8 sm:py-12">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
                    <div className="lg:col-span-2">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl sm:text-2xl font-semibold">
                                Nylige{" "}
                                <span className="text-green-500">
                                    prosjekter
                                </span>
                            </h2>
                            <Link
                                to="/prosjekter"
                                className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                            >
                                Se våre prosjekter
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5l7 7-7 7"
                                    />
                                </svg>
                            </Link>
                        </div>
                        <ProjectCarousel projects={recentProjects} />
                    </div>
                    <div className="h-full">
                        <ServiceAreaList areas={serviceAreas} />
                    </div>
                </div>

                <div className="mt-8 sm:mt-12 grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
                    <div className="lg:col-span-2">
                        <h2 className="text-xl sm:text-2xl font-semibold mb-3 sm:mb-4">
                            Tjenester for{" "}
                            <span className="text-green-500">
                                Ringerikes terreng
                            </span>
                        </h2>
                        <p className="text-sm sm:text-base text-gray-600 mb-6">
                            Fra Tyrifjordens bredder til åsene rundt Hønefoss –
                            vi leverer løsninger skreddersydd for lokale forhold
                            og klima.
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            {services.slice(0, 4).map((service) => (
                                <ServiceCard
                                    key={service.id}
                                    service={service}
                                    variant="compact"
                                />
                            ))}
                        </div>
                    </div>
                    <div className="h-full">
                        <SeasonalCTA />
                    </div>
                </div>
            </Container>

            <TestimonialsSection testimonials={testimonials} />
        </div>
    );
};

export default Home;
```


#### `pages\ProjectDetail.tsx`

```tsx
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getProjectById } from '../data/projects';
import { getServiceById } from '../data/services';
import Hero from '../components/common/Hero';
import Container from '../components/common/Container';
import Button from '../components/common/Button';
import { MapPin, Calendar } from 'lucide-react';

const ProjectDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const project = getProjectById(id || '');
  
  if (!project) {
    navigate('/');
    return null;
  }

  const service = getServiceById(project.category.toLowerCase());

  return (
    <div>
      <Hero 
        title={project.title}
        backgroundImage={project.image}
      />
      
      <Container size="small" className="py-12">
        <div className="flex flex-wrap items-center gap-3 mb-6">
          <Button
            to={`/tjenester/${project.category.toLowerCase()}`}
            variant="primary"
            className="!px-4 !py-2"
          >
            {service?.title || project.category}
          </Button>
          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>{project.location}</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>{project.completionDate}</span>
          </div>
        </div>

        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 text-lg leading-relaxed">
            {project.description}
          </p>
        </div>

        <div className="mt-12">
          <h2 className="text-2xl font-semibold mb-6">Lignende tjenester</h2>
          {service && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">{service.title}</h3>
              <p className="text-gray-600 mb-6">{service.description}</p>
              <Button
                to={`/tjenester/${service.id}`}
                variant="primary"
              >
                Les mer om {service.title.toLowerCase()}
              </Button>
            </div>
          )}
        </div>
      </Container>
    </div>
  );
};

export default ProjectDetail;```


#### `pages\projects\detail.tsx`

```tsx
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getProjectById } from '../../data/projects';
import { getServiceById, getServiceIdFromProjectCategory } from '../../data/services';
import Hero from '../../components/ui/Hero';
import Container from '../../components/ui/Container';
import Button from '../../components/ui/Button';
import { MapPin, Calendar, Tag } from 'lucide-react';

const ProjectDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const project = getProjectById(id || '');
  
  if (!project) {
    navigate('/prosjekter');
    return null;
  }

  // Get the corresponding service for this project category
  const serviceId = getServiceIdFromProjectCategory(project.category);
  const service = getServiceById(serviceId);

  return (
    <div>
      <Hero 
        title={project.title}
        backgroundImage={project.image}
      />
      
      <Container size="sm" className="py-12">
        <div className="flex flex-wrap items-center gap-3 mb-6">
          {service && (
            <Button
              to={`/tjenester/${serviceId}`}
              variant="primary"
              className="!px-4 !py-2"
            >
              {service.title}
            </Button>
          )}
          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>{project.location}</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>{project.completionDate}</span>
          </div>
        </div>

        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 text-lg leading-relaxed">
            {project.description}
          </p>
        </div>

        {/* Project specifications */}
        {project.specifications && (
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Prosjektdetaljer</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Spesifikasjoner</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <span className="font-medium">Størrelse:</span>
                    <span>{project.specifications.size}</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="font-medium">Varighet:</span>
                    <span>{project.specifications.duration}</span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Materialer</h3>
                <ul className="space-y-1">
                  {project.specifications.materials.map((material, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                      <span>{material}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            {project.specifications.features && project.specifications.features.length > 0 && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2">Funksjoner</h3>
                <ul className="space-y-1">
                  {project.specifications.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Project tags */}
        {project.tags && project.tags.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Stikkord</h3>
            <div className="flex flex-wrap gap-2">
              {project.tags.map((tag, index) => (
                <div key={index} className="flex items-center gap-1 px-3 py-1 bg-gray-100 rounded-full text-sm">
                  <Tag className="w-3 h-3" />
                  <span>{tag}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Testimonial */}
        {project.testimonial && (
          <div className="mt-8 bg-green-50 border-l-4 border-green-500 p-6 rounded-r-lg">
            <p className="text-gray-700 italic mb-2">"{project.testimonial.quote}"</p>
            <p className="text-gray-600 font-medium">— {project.testimonial.author}</p>
          </div>
        )}

        {/* Related service */}
        <div className="mt-12">
          <h2 className="text-2xl font-semibold mb-6">Relatert tjeneste</h2>
          {service ? (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3">{service.title}</h3>
              <p className="text-gray-600 mb-6">{service.description}</p>
              <Button
                to={`/tjenester/${serviceId}`}
                variant="primary"
              >
                Les mer om {service.title.toLowerCase()}
              </Button>
            </div>
          ) : (
            <p className="text-gray-600">Ingen relatert tjeneste funnet.</p>
          )}
        </div>

        {/* Back to projects button */}
        <div className="mt-8 text-center">
          <Button
            to="/prosjekter"
            variant="outline"
          >
            Tilbake til prosjekter
          </Button>
        </div>
      </Container>
    </div>
  );
};

export default ProjectDetailPage;```


#### `pages\projects\index.tsx`

```tsx
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Hero from "../../components/ui/Hero";
import Container from "../../components/ui/Container";
import { MapPin, Calendar, Filter, Calendar as CalendarIcon } from "lucide-react";
import { IMAGE_PATHS } from "../../lib/config/images";
import { getCurrentSeason } from "../../lib/utils";
import { useData } from "@/hooks/useData";
import { 
  getFilteredProjects, 
  getProjectCategories, 
  getProjectLocations, 
  getProjectTags,
  getSeasonDisplayName
} from "@/lib/api";

const ProjectsPage = () => {
  const currentSeason = getCurrentSeason();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<string | null>(null);

  // Get unique categories, locations, and tags
  const { data: categories = [] } = useData(getProjectCategories, []);
  const { data: locations = [] } = useData(getProjectLocations, []);
  const { data: tags = [] } = useData(getProjectTags, []);

  // Get filtered projects
  const { data: filteredProjects = [], loading } = useData(
    () => getFilteredProjects(
      selectedCategory || undefined,
      selectedLocation || undefined,
      selectedTag || undefined,
      selectedSeason || undefined
    ),
    [selectedCategory, selectedLocation, selectedTag, selectedSeason]
  );

  // Reset all filters
  const resetFilters = () => {
    setSelectedCategory(null);
    setSelectedLocation(null);
    setSelectedTag(null);
    setSelectedSeason(null);
  };

  return (
    <div>
      <Hero
        title="Våre prosjekter"
        subtitle="Utforsk våre tidligere prosjekter og la deg inspirere til ditt neste uterom"
        actionText=""
        backgroundImage={IMAGE_PATHS.site.hero.prosjekter}
      />

      <Container className="py-12">
        {/* Filters */}
        <div className="mb-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
            <Filter className="w-5 h-5" />
            <h2 className="text-xl font-semibold">Filtrer prosjekter</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Category filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori
                </label>
                <select
                  value={selectedCategory || ""}
                  onChange={(e) =>
                    setSelectedCategory(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle kategorier</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Location filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sted
                </label>
                <select
                  value={selectedLocation || ""}
                  onChange={(e) =>
                    setSelectedLocation(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle steder</option>
                  {locations.map((location) => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Tag filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Stikkord
                </label>
                <select
                  value={selectedTag || ""}
                  onChange={(e) =>
                    setSelectedTag(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle stikkord</option>
                  {tags.map((tag) => (
                    <option key={tag} value={tag}>
                      {tag}
                    </option>
                  ))}
                </select>
              </div>

              {/* Season filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <div className="flex items-center gap-1.5">
                    <CalendarIcon className="w-4 h-4" />
                    <span>Sesong</span>
                  </div>
                </label>
                <select
                  value={selectedSeason || ""}
                  onChange={(e) =>
                    setSelectedSeason(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle sesonger</option>
                  <option value="vår">Vår</option>
                  <option value="sommer">Sommer</option>
                  <option value="høst">Høst</option>
                  <option value="vinter">Vinter</option>
                </select>
              </div>
            </div>
          </div>

          {/* Reset button */}
          <div className="mt-4 flex justify-end">
            <button
              onClick={resetFilters}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Nullstill filtre
            </button>
          </div>

          {/* Current season suggestion */}
          {!selectedSeason && (
            <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
              <div className="flex items-center gap-2">
                <CalendarIcon className="w-5 h-5 text-green-600" />
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for prosjekter innen{' '}
                  {currentSeason === 'vår' ? 'hekk, beplantning og ferdigplen' : 
                   currentSeason === 'sommer' ? 'platting, cortenstål og belegningsstein' :
                   currentSeason === 'høst' ? 'støttemurer, kantstein og trapper' :
                   'planlegging og design'}.{' '}
                  <button 
                    onClick={() => setSelectedSeason(currentSeason)}
                    className="text-green-600 hover:underline font-medium"
                  >
                    Vis prosjekter for {getSeasonDisplayName(currentSeason)}
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Projects grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProjects.map((project) => (
                <Link
                  key={project.id}
                  to={`/prosjekter/${project.id}`}
                  className="group block overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow"
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80 group-hover:opacity-90 transition-opacity" />
                  </div>

                  <div className="p-4">
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 transition-colors">
                      {project.title}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {project.description}
                    </p>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{project.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{project.completionDate}</span>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                        {project.category}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {filteredProjects.length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold mb-2">
                  Ingen prosjekter funnet
                </h3>
                <p className="text-gray-600">
                  Prøv å endre filtrene eller{" "}
                  <button
                    onClick={resetFilters}
                    className="text-green-600 hover:underline"
                  >
                    vis alle prosjekter
                  </button>
                </p>
              </div>
            )}
          </>
        )}
      </Container>
    </div>
  );
};

export default ProjectsPage;```


#### `pages\Projects.tsx`

```tsx
import { useState, useMemo } from "react";
import { Link } from "react-router-dom";
import { recentProjects } from "../data/projects";
import { services } from "../data/services";
import Hero from "../components/common/Hero";
import Container from "../components/common/Container";
import { MapPin, Calendar } from "lucide-react";
import { IMAGE_PATHS } from "@/lib/config/images";

const Projects = () => {
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

    // Get unique locations from projects
    const locations = useMemo(() => {
        const locationSet = new Set(
            recentProjects.map((project) => project.location)
        );
        return Array.from(locationSet);
    }, []);

    // Filter projects based on selected filters
    const filteredProjects = useMemo(() => {
        let filtered = [...recentProjects];

        if (selectedCategory) {
            filtered = filtered.filter(
                (project) =>
                    project.category.toLowerCase() ===
                    selectedCategory.toLowerCase()
            );
        }

        if (selectedLocation) {
            filtered = filtered.filter(
                (project) => project.location === selectedLocation
            );
        }

        return filtered;
    }, [selectedCategory, selectedLocation]);

    // Reset all filters
    const resetFilters = () => {
        setSelectedCategory(null);
        setSelectedLocation(null);
    };

    return (
        <div>
            <Hero
                title="Våre prosjekter"
                subtitle="Utforsk våre tidligere prosjekter og la deg inspirere til ditt neste uterom"
                actionText=""
                backgroundImage={IMAGE_PATHS.site.hero.prosjekter}
            />

            <Container className="py-12">
                {/* Filters */}
                <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                    <h2 className="text-xl font-semibold mb-4">
                        Filtrer prosjekter
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Category filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Kategori
                            </label>
                            <select
                                value={selectedCategory || ""}
                                onChange={(e) =>
                                    setSelectedCategory(e.target.value || null)
                                }
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            >
                                <option value="">Alle kategorier</option>
                                {services.map((service) => (
                                    <option key={service.id} value={service.id}>
                                        {service.title}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Location filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Sted
                            </label>
                            <select
                                value={selectedLocation || ""}
                                onChange={(e) =>
                                    setSelectedLocation(e.target.value || null)
                                }
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            >
                                <option value="">Alle steder</option>
                                {locations.map((location) => (
                                    <option key={location} value={location}>
                                        {location}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Reset button */}
                        <div className="flex items-end">
                            <button
                                onClick={resetFilters}
                                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                            >
                                Nullstill filtre
                            </button>
                        </div>
                    </div>
                </div>

                {/* Projects grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredProjects.map((project) => (
                        <Link
                            key={project.id}
                            to={`/prosjekter/${project.id}`}
                            className="group block overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow"
                        >
                            <div className="relative h-64 overflow-hidden">
                                <img
                                    src={project.image}
                                    alt={project.title}
                                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    loading="lazy"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80 group-hover:opacity-90 transition-opacity" />
                            </div>

                            <div className="p-4">
                                <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 transition-colors">
                                    {project.title}
                                </h3>

                                <p className="text-gray-600 mb-4 line-clamp-2">
                                    {project.description}
                                </p>

                                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                                    <div className="flex items-center gap-1">
                                        <MapPin className="w-4 h-4" />
                                        <span>{project.location}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Calendar className="w-4 h-4" />
                                        <span>{project.completionDate}</span>
                                    </div>
                                </div>

                                <div className="mt-3 pt-3 border-t border-gray-100">
                                    <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                                        {services.find(
                                            (s) =>
                                                s.id ===
                                                project.category.toLowerCase()
                                        )?.title || project.category}
                                    </span>
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>

                {filteredProjects.length === 0 && (
                    <div className="text-center py-12">
                        <h3 className="text-xl font-semibold mb-2">
                            Ingen prosjekter funnet
                        </h3>
                        <p className="text-gray-600">
                            Prøv å endre filtrene eller{" "}
                            <button
                                onClick={resetFilters}
                                className="text-green-600 hover:underline"
                            >
                                vis alle prosjekter
                            </button>
                        </p>
                    </div>
                )}
            </Container>
        </div>
    );
};

export default Projects;```


#### `pages\ServiceDetail.tsx`

```tsx
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getServiceById, getServiceImageCategory } from '../data/services';
import Hero from '../components/common/Hero';
import { CheckCircle } from 'lucide-react';
import ProjectGallery from '../components/projects/ProjectGallery';
import { IMAGE_CATEGORIES } from '../utils/imageLoader';

const ServiceDetail = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const service = getServiceById(id || '');
    
    if (!service) {
        navigate('/');
        return null;
    }

    // Get the corresponding image category for this service
    const imageCategory = getServiceImageCategory(id || '');

    return (
        <div>
            <Hero 
                title={service.title}
                backgroundImage={service.image}
                subtitle="Se våre gjennomførte prosjekter og la deg inspirere"
            />
            
            <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
                {/* Service description */}
                <div className="max-w-3xl mx-auto mb-16">
                    <p className="text-lg text-gray-700 leading-relaxed mb-8">
                        {service.longDescription || service.description}
                    </p>

                    {service.features && (
                        <div className="bg-gray-50 rounded-lg p-6 mb-12">
                            <h3 className="text-xl font-semibold mb-4">Dette får du:</h3>
                            <ul className="space-y-3">
                                {service.features.map((feature, index) => (
                                    <li key={index} className="flex items-center gap-3">
                                        <CheckCircle className="text-green-500 w-5 h-5 flex-shrink-0" />
                                        <span>{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>

                {/* Project Gallery */}
                {imageCategory && (
                    <div className="mb-16">
                        <h2 className="text-3xl font-semibold mb-8 text-center">
                            Våre <span className="text-green-500">prosjekter</span>
                        </h2>
                        <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
                            Se eksempler på våre gjennomførte {service.title.toLowerCase()}-prosjekter 
                            i Ringerike-området
                        </p>
                        <ProjectGallery 
                            category={imageCategory as keyof typeof IMAGE_CATEGORIES} 
                        />
                    </div>
                )}

                {/* Contact CTA */}
                <div className="mt-16 text-center">
                    <h3 className="text-2xl font-semibold mb-4">
                        Interessert i {service.title.toLowerCase()}?
                    </h3>
                    <p className="text-gray-600 mb-8">
                        Ta kontakt for en uforpliktende prat om ditt prosjekt
                    </p>
                    <button
                        onClick={() => navigate('/kontakt')}
                        className="bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
                    >
                        Kontakt oss
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ServiceDetail;```


#### `pages\services\detail.tsx`

```tsx
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getServiceById, getServiceImageCategory } from '../../data/services';
import { filterProjects } from '../../data/projects';
import Hero from '../../components/ui/Hero';
import { CheckCircle } from 'lucide-react';
import ProjectGallery from '../../components/projects/ProjectGallery';
import { IMAGE_CATEGORIES } from '../../utils/imageLoader';
import Container from '../../components/ui/Container';
import Button from '../../components/ui/Button';

const ServiceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const service = getServiceById(id || '');
  
  if (!service) {
    navigate('/hva-vi-gjor');
    return null;
  }

  // Get the corresponding image category for this service
  const imageCategory = getServiceImageCategory(id || '');

  // Get projects related to this service
  const relatedProjects = filterProjects(service.title);

  return (
    <div>
      <Hero 
        title={service.title}
        backgroundImage={service.image}
        subtitle="Se våre gjennomførte prosjekter og la deg inspirere"
      />
      
      <Container>
        <div className="py-16">
          {/* Service description */}
          <div className="max-w-3xl mx-auto mb-16">
            <p className="text-lg text-gray-700 leading-relaxed mb-8">
              {service.longDescription || service.description}
            </p>

            {service.features && (
              <div className="bg-gray-50 rounded-lg p-6 mb-12">
                <h3 className="text-xl font-semibold mb-4">Dette får du:</h3>
                <ul className="space-y-3">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <CheckCircle className="text-green-500 w-5 h-5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Related Projects */}
          {relatedProjects.length > 0 && (
            <div className="mb-16">
              <h2 className="text-3xl font-semibold mb-8 text-center">
                Våre <span className="text-green-500">{service.title.toLowerCase()}-prosjekter</span>
              </h2>
              <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
                Se eksempler på våre gjennomførte {service.title.toLowerCase()}-prosjekter 
                i Ringerike-området
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {relatedProjects.map(project => (
                  <div key={project.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="h-48 overflow-hidden">
                      <img 
                        src={project.image} 
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-2">{project.title}</h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{project.description}</p>
                      <Button 
                        to={`/prosjekter/${project.id}`}
                        variant="outline"
                        className="w-full"
                      >
                        Se prosjektet
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Project Gallery */}
          {imageCategory && (
            <div className="mb-16">
              <h2 className="text-3xl font-semibold mb-8 text-center">
                Vårt <span className="text-green-500">bildegalleri</span>
              </h2>
              <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
                Se flere bilder av våre {service.title.toLowerCase()}-prosjekter
              </p>
              <ProjectGallery 
                category={imageCategory as keyof typeof IMAGE_CATEGORIES} 
              />
            </div>
          )}

          {/* Contact CTA */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-semibold mb-4">
              Interessert i {service.title.toLowerCase()}?
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Ta kontakt for en uforpliktende prat om ditt prosjekt. Vi kommer gjerne på befaring for å se på mulighetene.
            </p>
            <Button
              to="/kontakt"
              variant="primary"
              className="px-8 py-3"
            >
              Kontakt oss for gratis befaring
            </Button>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ServiceDetailPage;```


#### `pages\services\index.tsx`

```tsx
import React, { useState, useMemo } from "react";
import { Link } from "react-router-dom";
import Hero from "../../components/ui/Hero";
import { MapPin, Shield, Droplet, Filter, Calendar } from "lucide-react";
import Container from "../../components/ui/Container";
import { services } from "../../data/services";
import Button from "../../components/ui/Button";
import { getCurrentSeason } from "../../lib/utils";

// Map seasons to relevant service categories and features
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};

const ServicesPage = () => {
  const currentSeason = getCurrentSeason();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<string | null>(null);

  // Get unique service categories
  const categories = useMemo(() => {
    const categoriesSet = new Set(services.map(service => {
      // Extract category from service title or id
      const words = service.title.split(' ');
      return words.length > 1 ? words[0] : service.title;
    }));
    return Array.from(categoriesSet);
  }, []);

  // Get unique features across all services
  const features = useMemo(() => {
    const featuresSet = new Set(
      services.flatMap(service => service.features || [])
    );
    return Array.from(featuresSet);
  }, []);

  // Filter services based on selected filters
  const filteredServices = useMemo(() => {
    return services.filter(service => {
      // Filter by category
      if (selectedCategory) {
        const serviceCategory = service.title.split(' ')[0];
        if (!service.title.toLowerCase().includes(selectedCategory.toLowerCase()) && 
            serviceCategory.toLowerCase() !== selectedCategory.toLowerCase()) {
          return false;
        }
      }

      // Filter by feature
      if (selectedFeature && service.features) {
        if (!service.features.some(feature => 
          feature.toLowerCase().includes(selectedFeature.toLowerCase())
        )) {
          return false;
        }
      }

      // Filter by season
      if (selectedSeason) {
        const seasonMapping = SEASONAL_SERVICES[selectedSeason as keyof typeof SEASONAL_SERVICES];
        
        // Check if service category matches any seasonal category
        const categoryMatch = seasonMapping.categories.some(category => 
          service.title.toLowerCase().includes(category.toLowerCase())
        );
        
        // Check if any service feature matches seasonal features
        const featureMatch = service.features?.some(feature => 
          seasonMapping.features.some(seasonFeature => 
            feature.toLowerCase().includes(seasonFeature.toLowerCase())
          )
        );
        
        if (!categoryMatch && !featureMatch) {
          return false;
        }
      }

      return true;
    });
  }, [selectedCategory, selectedFeature, selectedSeason]);

  // Reset all filters
  const resetFilters = () => {
    setSelectedCategory(null);
    setSelectedFeature(null);
    setSelectedSeason(null);
  };

  const benefits = [
    {
      icon: <MapPin className="w-6 h-6 text-green-500" />,
      title: "Lokalkunnskap",
      description:
        "Med base på Røyse kjenner vi Ringerikes unike terreng og jordforhold som vår egen baklomme.",
    },
    {
      icon: <Shield className="w-6 h-6 text-green-500" />,
      title: "Tilpassede løsninger",
      description:
        "Hver hage og hvert prosjekt er unikt. Vi skreddersyr løsninger som passer ditt uterom og lokale forhold.",
    },
    {
      icon: <Droplet className="w-6 h-6 text-green-500" />,
      title: "Klimatilpasset",
      description:
        "Alle løsninger er spesialtilpasset for å takle både tørre somre og våte høstdager i Ringerike.",
    },
  ];

  // Get season display name
  const getSeasonDisplayName = (season: string): string => {
    switch (season) {
      case 'vår': return 'Våren';
      case 'sommer': return 'Sommeren';
      case 'høst': return 'Høsten';
      case 'vinter': return 'Vinteren';
      default: return season;
    }
  };

  return (
    <div itemScope itemType="http://schema.org/Service">
      <Hero
        title="Anleggsgartnertjenester i Ringerike"
        subtitle="Vi tilbyr alt fra legging av ferdigplen til bygging av støttemurer, tilpasset både private hager og næringseiendommer. Med inngående kjennskap til områdets klima og terreng, leverer vi løsninger som varer i generasjoner."
        backgroundImage="/images/site/hero-corten-steel.webp"
      />

      <Container className="py-8 sm:py-12">
        {/* Filters */}
        <div className="mb-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
            <Filter className="w-5 h-5" />
            <h2 className="text-xl font-semibold">Filtrer tjenester</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Category filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori
                </label>
                <select
                  value={selectedCategory || ""}
                  onChange={(e) =>
                    setSelectedCategory(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle kategorier</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Feature filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Funksjon
                </label>
                <select
                  value={selectedFeature || ""}
                  onChange={(e) =>
                    setSelectedFeature(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle funksjoner</option>
                  {features.map((feature) => (
                    <option key={feature} value={feature}>
                      {feature}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Season filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <div className="flex items-center gap-1.5">
                    <Calendar className="w-4 h-4" />
                    <span>Sesong</span>
                  </div>
                </label>
                <select
                  value={selectedSeason || ""}
                  onChange={(e) =>
                    setSelectedSeason(e.target.value || null)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="">Alle sesonger</option>
                  <option value="vår">Vår</option>
                  <option value="sommer">Sommer</option>
                  <option value="høst">Høst</option>
                  <option value="vinter">Vinter</option>
                </select>
              </div>

              {/* Reset button */}
              <div className="flex items-end">
                <button
                  onClick={resetFilters}
                  className="w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Nullstill filtre
                </button>
              </div>
            </div>
          </div>

          {/* Current season suggestion */}
          {!selectedSeason && (
            <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-green-600" />
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Tips:</span> {getSeasonDisplayName(currentSeason)} er en god tid for{' '}
                  {SEASONAL_SERVICES[currentSeason as keyof typeof SEASONAL_SERVICES].categories.join(', ').toLowerCase()}.{' '}
                  <button 
                    onClick={() => setSelectedSeason(currentSeason)}
                    className="text-green-600 hover:underline font-medium"
                  >
                    Vis tjenester for {currentSeason}en
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Services grid */}
        <div className="space-y-12 mb-16">
          {filteredServices.length > 0 ? (
            filteredServices.map((service, index) => (
              <div
                key={service.id}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-8 items-center ${
                  index % 2 === 0 ? "" : "lg:flex-row-reverse"
                }`}
                itemProp="hasOfferCatalog"
              >
                <div className="relative h-[200px] sm:h-[280px] rounded-lg overflow-hidden">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="absolute inset-0 w-full h-full object-cover"
                    itemProp="image"
                    loading="lazy"
                  />
                </div>
                <div className="space-y-4">
                  <h2
                    className="text-xl sm:text-2xl font-semibold"
                    itemProp="name"
                  >
                    {service.title}
                  </h2>
                  <p
                    className="text-gray-700 leading-relaxed"
                    itemProp="description"
                  >
                    {service.longDescription ||
                      service.description}
                  </p>
                  {service.features && (
                    <ul className="space-y-2">
                      {service.features.map(
                        (feature, index) => (
                          <li
                            key={index}
                            className="flex items-center gap-2 text-gray-600 text-sm"
                          >
                            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                            {feature}
                          </li>
                        )
                      )}
                    </ul>
                  )}
                  <Link
                    to={`/tjenester/${service.id}`}
                    className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors mt-4"
                  >
                    Les mer om {service.title.toLowerCase()}
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">
                Ingen tjenester funnet
              </h3>
              <p className="text-gray-600 mb-4">
                Prøv å endre filtrene eller{" "}
                <button
                  onClick={resetFilters}
                  className="text-green-600 hover:underline"
                >
                  vis alle tjenester
                </button>
              </p>
            </div>
          )}
        </div>

        {/* Benefits section - MOVED BELOW SERVICES */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="text-center p-6 bg-white rounded-lg shadow-sm"
            >
              <div className="inline-block p-3 bg-green-50 rounded-full mb-4">
                {benefit.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {benefit.title}
              </h3>
              <p className="text-sm text-gray-600">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
          <h2 className="text-xl sm:text-2xl font-semibold mb-3">
            Usikker på hvilken løsning som passer best?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto text-sm">
            Med inngående kjennskap til Ringerike-området hjelper vi
            deg å finne den beste løsningen for ditt uterom. Vi
            kommer gjerne på gratis befaring for å se på
            mulighetene.
          </p>
          <Button
            to="/kontakt"
            variant="primary"
            className="px-8 py-3"
          >
            Book gratis befaring
          </Button>
        </div>
      </Container>
    </div>
  );
};

export default ServicesPage;```


#### `pages\Services.tsx`

```tsx
import React from "react";
import Hero from "../components/common/Hero";
import { MapPin, Shield, Droplet } from "lucide-react";
import Container from "../components/common/Container";
import { services } from "../data/services";

const Services = () => {
    const benefits = [
        {
            icon: <MapPin className="w-6 h-6 text-green-500" />,
            title: "Lokalkunnskap",
            description:
                "Med base på Røyse kjenner vi Ringerikes unike terreng og jordforhold som vår egen baklomme.",
        },
        {
            icon: <Shield className="w-6 h-6 text-green-500" />,
            title: "Tilpassede løsninger",
            description:
                "Hver hage og hvert prosjekt er unikt. Vi skreddersyr løsninger som passer ditt uterom og lokale forhold.",
        },
        {
            icon: <Droplet className="w-6 h-6 text-green-500" />,
            title: "Klimatilpasset",
            description:
                "Alle løsninger er spesialtilpasset for å takle både tørre somre og våte høstdager i Ringerike.",
        },
    ];

    return (
        <div itemScope itemType="http://schema.org/Service">
            <Hero
                title="Anleggsgartnertjenester i Ringerike"
                subtitle="Vi tilbyr alt fra legging av ferdigplen til bygging av støttemurer, tilpasset både private hager og næringseiendommer. Med inngående kjennskap til områdets klima og terreng, leverer vi løsninger som varer i generasjoner."
                backgroundImage="/images/site/hero-corten-steel.webp"
            />

            <Container className="py-8 sm:py-12">
                {/* Benefits section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                    {benefits.map((benefit, index) => (
                        <div
                            key={index}
                            className="text-center p-6 bg-white rounded-lg shadow-sm"
                        >
                            <div className="inline-block p-3 bg-green-50 rounded-full mb-4">
                                {benefit.icon}
                            </div>
                            <h3 className="text-lg font-semibold mb-2">
                                {benefit.title}
                            </h3>
                            <p className="text-sm text-gray-600">
                                {benefit.description}
                            </p>
                        </div>
                    ))}
                </div>

                {/* Services grid */}
                <div className="space-y-12">
                    {services.map((service, index) => (
                        <div
                            key={service.id}
                            className={`grid grid-cols-1 lg:grid-cols-2 gap-8 items-center ${
                                index % 2 === 0 ? "" : "lg:flex-row-reverse"
                            }`}
                            itemProp="hasOfferCatalog"
                        >
                            <div className="relative h-[200px] sm:h-[280px] rounded-lg overflow-hidden">
                                <img
                                    src={service.image}
                                    alt={service.title}
                                    className="absolute inset-0 w-full h-full object-cover"
                                    itemProp="image"
                                    loading="lazy"
                                />
                            </div>
                            <div className="space-y-4">
                                <h2
                                    className="text-xl sm:text-2xl font-semibold"
                                    itemProp="name"
                                >
                                    {service.title}
                                </h2>
                                <p
                                    className="text-gray-700 leading-relaxed"
                                    itemProp="description"
                                >
                                    {service.longDescription ||
                                        service.description}
                                </p>
                                {service.features && (
                                    <ul className="space-y-2">
                                        {service.features.map(
                                            (feature, index) => (
                                                <li
                                                    key={index}
                                                    className="flex items-center gap-2 text-gray-600 text-sm"
                                                >
                                                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                                                    {feature}
                                                </li>
                                            )
                                        )}
                                    </ul>
                                )}
                                <button
                                    onClick={() =>
                                        (window.location.href = `/tjenester/${service.id}`)
                                    }
                                    className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors mt-4"
                                >
                                    Les mer om {service.title.toLowerCase()}
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* CTA Section */}
                <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
                    <h2 className="text-xl sm:text-2xl font-semibold mb-3">
                        Usikker på hvilken løsning som passer best?
                    </h2>
                    <p className="text-gray-600 mb-6 max-w-2xl mx-auto text-sm">
                        Med inngående kjennskap til Ringerike-området hjelper vi
                        deg å finne den beste løsningen for ditt uterom. Vi
                        kommer gjerne på gratis befaring for å se på
                        mulighetene.
                    </p>
                    <button
                        onClick={() => (window.location.href = "/kontakt")}
                        className="inline-block bg-green-500 text-white px-8 py-3 rounded-md hover:bg-green-600 transition-colors"
                    >
                        Book gratis befaring
                    </button>
                </div>
            </Container>
        </div>
    );
};

export default Services;
```


#### `pages\testimonials\index.tsx`

```tsx
import React, { useState, useEffect, useMemo } from "react";
import { Sparkles } from "lucide-react";
import { Link } from "react-router-dom";
import Hero from "../../components/ui/Hero";
import Container from "../../components/ui/Container";
import { testimonials } from "../../data/testimonials";
import TestimonialsSchema from "../../components/seo/TestimonialsSchema";

// Import testimonial components
import { 
  Testimonial, 
  TestimonialSlider, 
  AverageRating, 
  TestimonialFilter 
} from "../../features/testimonials";

const TestimonialsPage: React.FC = () => {
  const [itemsPerView, setItemsPerView] = useState(1);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);

  // Calculate rating counts
  const ratingCounts = useMemo(() => {
    const counts: { [key: number]: number } = {};
    testimonials.forEach((testimonial) => {
      counts[testimonial.rating] = (counts[testimonial.rating] || 0) + 1;
    });
    return counts;
  }, []);

  // Filter testimonials by rating
  const filteredTestimonials = useMemo(() => {
    if (selectedRating === null) return testimonials;
    return testimonials.filter(
      (testimonial) => testimonial.rating === selectedRating
    );
  }, [selectedRating]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setItemsPerView(3);
      } else if (window.innerWidth >= 768) {
        setItemsPerView(2);
      } else {
        setItemsPerView(1);
      }
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="bg-gray-50 py-16">
      <TestimonialsSchema testimonials={testimonials} />
      <Container>
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Hva våre kunder sier
          </h1>
          <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
            <Sparkles className="h-5 w-5" />
            <span className="font-medium">
              Ekte tilbakemeldinger fra fornøyde kunder
            </span>
          </div>
          <div className="flex justify-center mb-4">
            <AverageRating
              testimonials={testimonials}
              className="mb-2"
            />
          </div>
          <p className="max-w-2xl mx-auto text-gray-600">
            Vi er stolte av arbeidet vårt og verdsetter
            tilbakemeldingene fra våre kunder. Her er noen av
            historiene fra folk som har opplevd forskjellen med
            Ringerike Landskap.
          </p>
        </div>

        {/* Featured testimonial */}
        <div className="mb-16">
          <TestimonialSlider
            testimonials={filteredTestimonials}
            title="Kundeopplevelser"
            subtitle="Se hva våre kunder sier om oss"
            itemsPerView={1}
            className="bg-white shadow-xl rounded-lg p-8"
          />
        </div>

        {/* Filter and grid */}
        <div className="mt-16 grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filter sidebar */}
          <div className="lg:col-span-1">
            <TestimonialFilter
              selectedRating={selectedRating}
              onRatingChange={setSelectedRating}
              counts={ratingCounts}
              totalCount={testimonials.length}
            />
          </div>

          {/* Testimonials grid */}
          <div className="lg:col-span-3">
            <h2 className="text-2xl font-bold mb-8">
              Alle kundehistorier
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredTestimonials.map((testimonial, index) => (
                <div
                  key={index}
                  className="bg-white shadow rounded-lg overflow-hidden"
                >
                  <Testimonial
                    testimonial={testimonial}
                    variant="default"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Testimonial categories */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-center mb-8">
            Tilbakemeldinger etter prosjekttype
          </h2>

          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                Hagedesign
              </h3>
              <AverageRating
                testimonials={testimonials.filter(
                  (t) =>
                    t.projectType.includes("hage") ||
                    t.projectType.includes("Hage")
                )}
                showCount={false}
              />
            </div>
            <TestimonialSlider
              testimonials={testimonials.filter(
                (t) =>
                  t.projectType.includes("hage") ||
                  t.projectType.includes("Hage")
              )}
              itemsPerView={itemsPerView}
            />
          </div>

          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                Terrasser og uteplasser
              </h3>
              <AverageRating
                testimonials={testimonials.filter(
                  (t) =>
                    t.projectType.includes("Terrasse") ||
                    t.projectType.includes("terrasse") ||
                    t.projectType.includes("Platting") ||
                    t.projectType.includes("platting") ||
                    t.projectType.includes("Uteplass") ||
                    t.projectType.includes("uteplass")
                )}
                showCount={false}
              />
            </div>
            <TestimonialSlider
              testimonials={testimonials.filter(
                (t) =>
                  t.projectType.includes("Terrasse") ||
                  t.projectType.includes("terrasse") ||
                  t.projectType.includes("Platting") ||
                  t.projectType.includes("platting") ||
                  t.projectType.includes("Uteplass") ||
                  t.projectType.includes("uteplass")
              )}
              itemsPerView={itemsPerView}
            />
          </div>

          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                Støttemurer og belegningsstein
              </h3>
              <AverageRating
                testimonials={testimonials.filter(
                  (t) =>
                    t.projectType.includes("Støttemur") ||
                    t.projectType.includes("støttemur") ||
                    t.projectType.includes(
                      "Belegningsstein"
                    ) ||
                    t.projectType.includes(
                      "belegningsstein"
                    )
                )}
                showCount={false}
              />
            </div>
            <TestimonialSlider
              testimonials={testimonials.filter(
                (t) =>
                  t.projectType.includes("Støttemur") ||
                  t.projectType.includes("støttemur") ||
                  t.projectType.includes("Belegningsstein") ||
                  t.projectType.includes("belegningsstein")
              )}
              itemsPerView={itemsPerView}
            />
          </div>
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">
            Klar for å skape din drømmehage?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            La oss hjelpe deg med å transformere ditt uterom til et
            vakkert og funksjonelt område som du og din familie kan
            nyte i mange år fremover.
          </p>
          <Link
            to="/kontakt"
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
          >
            Kontakt oss i dag
          </Link>
        </div>
      </Container>
    </div>
  );
};

export default TestimonialsPage;```


#### `pages\TestimonialsPage.tsx`

```tsx
import React, { useState, useEffect, useMemo } from "react";
import { Sparkles } from "lucide-react";
import {
    TestimonialSlider,
    Testimonial,
    AverageRating,
    TestimonialFilter,
} from "../features/testimonials";
import { testimonials } from "../data/testimonials";
import { Link } from "react-router-dom";
import TestimonialsSchema from "../components/seo/TestimonialsSchema";

const TestimonialsPage: React.FC = () => {
    const [itemsPerView, setItemsPerView] = useState(1);
    const [selectedRating, setSelectedRating] = useState<number | null>(null);

    // Calculate rating counts
    const ratingCounts = useMemo(() => {
        const counts: { [key: number]: number } = {};
        testimonials.forEach((testimonial) => {
            counts[testimonial.rating] = (counts[testimonial.rating] || 0) + 1;
        });
        return counts;
    }, []);

    // Filter testimonials by rating
    const filteredTestimonials = useMemo(() => {
        if (selectedRating === null) return testimonials;
        return testimonials.filter(
            (testimonial) => testimonial.rating === selectedRating
        );
    }, [selectedRating]);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth >= 1024) {
                setItemsPerView(3);
            } else if (window.innerWidth >= 768) {
                setItemsPerView(2);
            } else {
                setItemsPerView(1);
            }
        };

        // Set initial value
        handleResize();

        // Add event listener
        window.addEventListener("resize", handleResize);

        // Clean up
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <div className="bg-gray-50 py-16">
            <TestimonialsSchema testimonials={testimonials} />
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Hva våre kunder sier
                    </h1>
                    <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
                        <Sparkles className="h-5 w-5" />
                        <span className="font-medium">
                            Ekte tilbakemeldinger fra fornøyde kunder
                        </span>
                    </div>
                    <div className="flex justify-center mb-4">
                        <AverageRating
                            testimonials={testimonials}
                            className="mb-2"
                        />
                    </div>
                    <p className="max-w-2xl mx-auto text-gray-600">
                        Vi er stolte av arbeidet vårt og verdsetter
                        tilbakemeldingene fra våre kunder. Her er noen av
                        historiene fra folk som har opplevd forskjellen med
                        Ringerike Landskap.
                    </p>
                </div>

                {/* Featured testimonial */}
                <div className="mb-16">
                    <TestimonialSlider
                        testimonials={filteredTestimonials}
                        title="Kundeopplevelser"
                        subtitle="Se hva våre kunder sier om oss"
                        itemsPerView={1}
                        className="bg-white shadow-xl rounded-lg p-8"
                    />
                </div>

                {/* Filter and grid */}
                <div className="mt-16 grid grid-cols-1 lg:grid-cols-4 gap-8">
                    {/* Filter sidebar */}
                    <div className="lg:col-span-1">
                        <TestimonialFilter
                            selectedRating={selectedRating}
                            onRatingChange={setSelectedRating}
                            counts={ratingCounts}
                            totalCount={testimonials.length}
                        />
                    </div>

                    {/* Testimonials grid */}
                    <div className="lg:col-span-3">
                        <h2 className="text-2xl font-bold mb-8">
                            Alle kundehistorier
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {filteredTestimonials.map((testimonial, index) => (
                                <div
                                    key={index}
                                    className="bg-white shadow rounded-lg overflow-hidden"
                                >
                                    <Testimonial
                                        testimonial={testimonial}
                                        variant="default"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Testimonial categories */}
                <div className="mt-16">
                    <h2 className="text-2xl font-bold text-center mb-8">
                        Tilbakemeldinger etter prosjekttype
                    </h2>

                    <div className="mb-12">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-semibold">
                                Hagedesign
                            </h3>
                            <AverageRating
                                testimonials={testimonials.filter(
                                    (t) =>
                                        t.projectType.includes("hage") ||
                                        t.projectType.includes("Hage")
                                )}
                                showCount={false}
                            />
                        </div>
                        <TestimonialSlider
                            testimonials={testimonials.filter(
                                (t) =>
                                    t.projectType.includes("hage") ||
                                    t.projectType.includes("Hage")
                            )}
                            itemsPerView={itemsPerView}
                        />
                    </div>

                    <div className="mb-12">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-semibold">
                                Terrasser og uteplasser
                            </h3>
                            <AverageRating
                                testimonials={testimonials.filter(
                                    (t) =>
                                        t.projectType.includes("Terrasse") ||
                                        t.projectType.includes("terrasse") ||
                                        t.projectType.includes("Platting") ||
                                        t.projectType.includes("platting") ||
                                        t.projectType.includes("Uteplass") ||
                                        t.projectType.includes("uteplass")
                                )}
                                showCount={false}
                            />
                        </div>
                        <TestimonialSlider
                            testimonials={testimonials.filter(
                                (t) =>
                                    t.projectType.includes("Terrasse") ||
                                    t.projectType.includes("terrasse") ||
                                    t.projectType.includes("Platting") ||
                                    t.projectType.includes("platting") ||
                                    t.projectType.includes("Uteplass") ||
                                    t.projectType.includes("uteplass")
                            )}
                            itemsPerView={itemsPerView}
                        />
                    </div>

                    <div className="mb-12">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-semibold">
                                Støttemurer og belegningsstein
                            </h3>
                            <AverageRating
                                testimonials={testimonials.filter(
                                    (t) =>
                                        t.projectType.includes("Støttemur") ||
                                        t.projectType.includes("støttemur") ||
                                        t.projectType.includes(
                                            "Belegningsstein"
                                        ) ||
                                        t.projectType.includes(
                                            "belegningsstein"
                                        )
                                )}
                                showCount={false}
                            />
                        </div>
                        <TestimonialSlider
                            testimonials={testimonials.filter(
                                (t) =>
                                    t.projectType.includes("Støttemur") ||
                                    t.projectType.includes("støttemur") ||
                                    t.projectType.includes("Belegningsstein") ||
                                    t.projectType.includes("belegningsstein")
                            )}
                            itemsPerView={itemsPerView}
                        />
                    </div>
                </div>

                {/* Call to action */}
                <div className="mt-16 text-center">
                    <h2 className="text-2xl font-bold mb-4">
                        Klar for å skape din drømmehage?
                    </h2>
                    <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                        La oss hjelpe deg med å transformere ditt uterom til et
                        vakkert og funksjonelt område som du og din familie kan
                        nyte i mange år fremover.
                    </p>
                    <Link
                        to="/kontakt"
                        className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
                    >
                        Kontakt oss i dag
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default TestimonialsPage;
```


#### `ui\index.tsx`

```tsx
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { ChevronDown, MapPin } from "lucide-react";
import { HeroProps } from "../types";
import { ServiceArea } from "../types";

// Button Component
export interface ButtonProps {
    children: React.ReactNode;
    to?: string;
    onClick?: () => void;
    type?: "button" | "submit" | "reset";
    variant?: "primary" | "secondary" | "outline";
    className?: string;
    fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
    children,
    to,
    onClick,
    type = "button",
    variant = "primary",
    className = "",
    fullWidth = false,
}) => {
    const baseStyles =
        "inline-flex items-center justify-center px-6 py-2.5 rounded-md transition-all duration-300 font-medium text-sm";
    const variantStyles = {
        primary:
            "bg-green-500 text-white hover:bg-green-600 shadow-sm hover:shadow",
        secondary:
            "bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm hover:shadow",
        outline: "border-2 border-green-500 text-green-500 hover:bg-green-50",
    };

    const widthClass = fullWidth ? "w-full" : "";
    const buttonClasses = `${baseStyles} ${variantStyles[variant]} ${widthClass} ${className}`;

    if (to) {
        return (
            <Link to={to} className={buttonClasses}>
                {children}
            </Link>
        );
    }

    return (
        <button type={type} onClick={onClick} className={buttonClasses}>
            {children}
        </button>
    );
};

// Container Component
export interface ContainerProps {
    children: React.ReactNode;
    className?: string;
    size?: "sm" | "md" | "lg" | "xl";
    as?: React.ElementType;
    padded?: boolean;
}

export const Container = React.forwardRef<HTMLElement, ContainerProps>(
    (
        {
            children,
            className = "",
            size = "lg",
            as: Component = "div",
            padded = true,
        },
        ref
    ) => {
        const sizes = {
            sm: "max-w-3xl",
            md: "max-w-5xl",
            lg: "max-w-7xl",
            xl: "max-w-[90rem]",
        };

        return (
            <Component
                ref={ref}
                className={`${sizes[size]} mx-auto ${
                    padded ? "px-4 sm:px-6 lg:px-8" : ""
                } ${className}`}
            >
                {children}
            </Component>
        );
    }
);

// Hero Component
export interface EnhancedHeroProps extends HeroProps {
    location?: string;
    yearEstablished?: string;
    actionLink?: string;
    actionText?: string;
}

export const Hero: React.FC<EnhancedHeroProps> = ({
    title,
    subtitle,
    backgroundImage = "/images/site/hero-main.webp",
    location = "Røyse, Hole kommune",
    yearEstablished = "2015",
    actionLink,
    actionText = "Se våre prosjekter",
}) => {
    const scrollToProjects = () => {
        const projectsSection = document.getElementById("recent-projects");
        if (projectsSection) {
            projectsSection.scrollIntoView({ behavior: "smooth" });
        }
    };

    return (
        <section
            className="relative h-[calc(100vh-64px)] min-h-[600px] max-h-[800px] overflow-hidden"
            itemScope
            itemType="http://schema.org/LandscapingService"
        >
            {/* Background image with parallax effect */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Center square gradient overlay - Main page variation */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
                    linear-gradient(
                      42deg,
                      transparent 10%,
                      rgba(0,0,0,0.85) 35%,
                      rgba(0,0,0,0.7) 70%,
                      transparent 90%,
                    )
                  `,
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening - Main page variation */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 75%, rgba(0,0,0,0.95) 100%)",
                }}
                aria-hidden="true"
            />

            {/* Content */}
            <div className="relative h-full flex flex-col justify-center items-center text-center text-white px-4 sm:px-6 lg:px-8">
                <div className="max-w-4xl mx-auto">
                    {/* Established badge */}
                    <div className="inline-block mb-6 px-4 py-2 rounded-full bg-green-500/80 backdrop-blur-sm text-sm font-medium">
                        Etablert {yearEstablished} • {location}
                    </div>

                    {/* Main heading */}
                    <h1
                        className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-md"
                        itemProp="name"
                    >
                        {title ||
                            "Profesjonell hagedesign og anlegg i Ringerike"}
                    </h1>

                    {/* Subtitle */}
                    <p
                        className="text-xl sm:text-2xl mb-8 max-w-3xl mx-auto drop-shadow-md"
                        itemProp="description"
                    >
                        {subtitle ||
                            "Vi skaper vakre og funksjonelle uterom som varer. Kontakt oss for en uforpliktende befaring."}
                    </p>

                    {/* CTA buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                            to="/kontakt"
                            variant="primary"
                            className="text-base px-8 py-3"
                        >
                            Få gratis befaring
                        </Button>
                        {actionLink ? (
                            <Button
                                to={actionLink}
                                variant="outline"
                                className="text-base px-8 py-3 border-white text-white hover:bg-white/10"
                            >
                                {actionText}
                            </Button>
                        ) : (
                            <Button
                                onClick={scrollToProjects}
                                variant="outline"
                                className="text-base px-8 py-3 border-white text-white hover:bg-white/10"
                            >
                                {actionText}
                            </Button>
                        )}
                    </div>
                </div>

                {/* Scroll indicator */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                    <ChevronDown
                        size={32}
                        onClick={scrollToProjects}
                        className="cursor-pointer"
                    />
                </div>
            </div>
        </section>
    );
};

// Logo Component
export interface LogoProps {
    variant?: "default" | "white";
    className?: string;
}

export const Logo: React.FC<LogoProps> = ({
    variant = "default",
    className = "",
}) => {
    const textColor = variant === "white" ? "text-white" : "text-green-700";

    return (
        <div className={`flex items-center ${className}`}>
            <span className={`text-xl font-bold ${textColor}`}>
                Ringerike Landskap
            </span>
        </div>
    );
};

// Section Component
export interface SectionProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    className?: string;
    id?: string;
    background?: "white" | "light" | "dark" | "primary";
}

export const Section: React.FC<SectionProps> = ({
    children,
    title,
    subtitle,
    className = "",
    id,
    background = "white",
}) => {
    const bgClasses = {
        white: "bg-white",
        light: "bg-gray-50",
        dark: "bg-gray-900 text-white",
        primary: "bg-green-50",
    };

    return (
        <section
            id={id}
            className={`py-12 md:py-16 lg:py-20 ${bgClasses[background]} ${className}`}
        >
            <Container>
                {(title || subtitle) && (
                    <div className="text-center mb-12">
                        {title && (
                            <h2 className="text-3xl font-bold mb-4">{title}</h2>
                        )}
                        {subtitle && (
                            <p className="text-gray-600 max-w-2xl mx-auto">
                                {subtitle}
                            </p>
                        )}
                    </div>
                )}
                {children}
            </Container>
        </section>
    );
};

// ImageGallery Component
export interface ImageGalleryProps {
    images: string[];
    className?: string;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
    images,
    className = "",
}) => {
    const [activeIndex, setActiveIndex] = useState(0);

    if (!images || images.length === 0) {
        return null;
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Main image */}
            <div className="relative h-96 overflow-hidden rounded-lg">
                <img
                    src={images[activeIndex]}
                    alt={`Gallery image ${activeIndex + 1}`}
                    className="w-full h-full object-cover"
                />
            </div>

            {/* Thumbnails */}
            {images.length > 1 && (
                <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 gap-2">
                    {images.map((image, index) => (
                        <button
                            key={index}
                            onClick={() => setActiveIndex(index)}
                            className={`h-20 rounded-md overflow-hidden ${
                                index === activeIndex
                                    ? "ring-2 ring-green-500"
                                    : "opacity-70 hover:opacity-100"
                            }`}
                        >
                            <img
                                src={image}
                                alt={`Thumbnail ${index + 1}`}
                                className="w-full h-full object-cover"
                            />
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

// ServiceAreaList Component
export interface ServiceAreaListProps {
    areas: ServiceArea[];
    className?: string;
}

export const ServiceAreaList: React.FC<ServiceAreaListProps> = ({
    areas,
    className = "",
}) => {
    return (
        <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
            <h3 className="text-xl font-semibold mb-4">Våre serviceområder</h3>
            <p className="text-gray-600 mb-6">
                Vi tilbyr våre tjenester i følgende områder i Ringerike og
                omegn:
            </p>

            <div className="space-y-4">
                {areas.map((area, index) => (
                    <div
                        key={index}
                        className={`flex items-start p-3 rounded-md ${
                            area.isBase
                                ? "bg-green-50 border-l-4 border-green-500"
                                : "hover:bg-gray-50"
                        }`}
                    >
                        <MapPin className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                        <div>
                            <div className="flex items-center gap-2">
                                <h4 className="font-medium">{area.city}</h4>
                                <span className="text-sm text-gray-500">
                                    {area.distance}
                                </span>
                                {area.isBase && (
                                    <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                        Hovedområde
                                    </span>
                                )}
                            </div>
                            {area.description && (
                                <p className="text-sm text-gray-600 mt-1">
                                    {area.description}
                                </p>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// LocalExpertise Component
export interface LocalExpertiseProps {
    location?: string;
    className?: string;
}

export const LocalExpertise: React.FC<LocalExpertiseProps> = ({
    location = "Ringerike",
    className = "",
}) => {
    return (
        <div className={`bg-green-50 p-6 rounded-lg ${className}`}>
            <h3 className="text-xl font-semibold mb-3">
                Lokal ekspertise i {location}
            </h3>
            <p className="text-gray-700 mb-4">
                Som lokale eksperter i {location}-området har vi inngående
                kunnskap om lokale forhold, jordtyper og plantesorter som trives
                best i vårt klima.
            </p>
            <ul className="space-y-2">
                <li className="flex items-start">
                    <svg
                        className="h-5 w-5 text-green-500 mr-2 mt-0.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                        />
                    </svg>
                    <span>Kunnskap om lokale jordforhold</span>
                </li>
                <li className="flex items-start">
                    <svg
                        className="h-5 w-5 text-green-500 mr-2 mt-0.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                        />
                    </svg>
                    <span>Tilpassede løsninger for lokalt klima</span>
                </li>
                <li className="flex items-start">
                    <svg
                        className="h-5 w-5 text-green-500 mr-2 mt-0.5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                        />
                    </svg>
                    <span>Rask responstid og lokal tilstedeværelse</span>
                </li>
            </ul>
        </div>
    );
};

// SeasonalCTA Component
export interface SeasonalCTAProps {
    season?: "spring" | "summer" | "fall" | "winter";
    className?: string;
}

export const SeasonalCTA: React.FC<SeasonalCTAProps> = ({
    season = "winter",
    className = "",
}) => {
    const seasonData = {
        spring: {
            title: "Planlegg for Ringerike-våren",
            description:
                "Våren er perfekt for å planlegge neste sesongs hageprosjekter. Vi har inngående kjennskap til lokale forhold i Ringerike.",
            icon: "🌱",
            services: [
                "Lokaltilpasset planlegging",
                "Terrengvurdering",
                "Klimatilpasset design",
            ],
            cta: "Book gratis befaring i Ringerike",
        },
        summer: {
            title: "Nyt sommeren i en vakker hage",
            description:
                "Få mest mulig ut av sommeren med en velstelt og innbydende hage. Vi hjelper deg med vedlikehold og forbedringer.",
            icon: "☀️",
            services: [
                "Vedlikehold av uterom",
                "Vanningssystemer",
                "Beplantning og stell",
            ],
            cta: "Book gratis befaring i Ringerike",
        },
        fall: {
            title: "Høstklargjøring og planlegging",
            description:
                "Forbered hagen for vinteren og planlegg neste års prosjekter. Nå er det perfekt tid for å plante trær og busker.",
            icon: "🍂",
            services: ["Høstbeplantning", "Beskjæring", "Vinterklargjøring"],
            cta: "Book gratis befaring i Ringerike",
        },
        winter: {
            title: "Planlegg for Ringerike-våren",
            description:
                "Vinteren er perfekt for å planlegge neste sesongs hageprosjekter. Vi har inngående kjennskap til lokale forhold i Ringerike.",
            icon: "❄️",
            services: [
                "Lokaltilpasset planlegging",
                "Terrengvurdering",
                "Klimatilpasset design",
            ],
            cta: "Book gratis befaring i Ringerike",
        },
    };

    const { title, description, icon, services, cta } = seasonData[season];

    return (
        <div
            className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}
        >
            <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm text-blue-600">
                    <span>{icon}</span>
                    <span>
                        Sesong:{" "}
                        {season === "winter"
                            ? "vinter"
                            : season === "spring"
                            ? "vår"
                            : season === "summer"
                            ? "sommer"
                            : "høst"}
                    </span>
                </div>

                <h3 className="text-xl font-semibold">{title}</h3>

                <p className="text-gray-600 text-sm">{description}</p>

                <div className="space-y-2 py-2">
                    <p className="text-gray-700 font-medium text-sm">
                        Aktuelle tjenester:
                    </p>
                    <ul className="space-y-1">
                        {services.map((service, index) => (
                            <li
                                key={index}
                                className="flex items-center gap-2 text-sm"
                            >
                                <span className="text-green-500">•</span>
                                <span>{service}</span>
                            </li>
                        ))}
                    </ul>
                </div>

                <a
                    href="/kontakt"
                    className="block w-full bg-green-500 text-white text-center py-3 rounded-md hover:bg-green-600 transition-colors"
                >
                    {cta}
                </a>
            </div>
        </div>
    );
};

// WeatherAdaptedServices Component
export interface WeatherAdaptedServicesProps {
    className?: string;
}

export const WeatherAdaptedServices: React.FC<WeatherAdaptedServicesProps> = ({
    className = "",
}) => {
    const [currentWeather, setCurrentWeather] = useState<string>("sunny");

    // Simulate weather detection - in a real app, this would use a weather API
    useEffect(() => {
        // This is just a placeholder - in a real app, you'd fetch actual weather data
        const weathers = ["sunny", "rainy", "snowy", "cloudy"];
        const randomWeather =
            weathers[Math.floor(Math.random() * weathers.length)];
        setCurrentWeather(randomWeather);
    }, []);

    const weatherServices = {
        sunny: {
            title: "Perfekt vær for hagearbeid",
            services: [
                "Anlegging av plen og beplantning",
                "Installasjon av vanningssystemer",
                "Bygging av terrasser og uteplasser",
            ],
        },
        rainy: {
            title: "Tjenester tilpasset regnvær",
            services: [
                "Drenering og vannhåndtering",
                "Tak over uteplasser og terrasser",
                "Regnbed og vannhåndteringsløsninger",
            ],
        },
        snowy: {
            title: "Vintertjenester",
            services: [
                "Snørydding og vintervedlikehold",
                "Planlegging av vårprosjekter",
                "Beskyttelse av planter mot frost",
            ],
        },
        cloudy: {
            title: "Ideelt for planlegging",
            services: [
                "Befaring og konsultasjon",
                "Hagedesign og planlegging",
                "Mindre hageprosjekter og vedlikehold",
            ],
        },
    };

    const { title, services } =
        weatherServices[currentWeather as keyof typeof weatherServices];

    return (
        <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
            <h3 className="text-xl font-semibold mb-3">{title}</h3>
            <p className="text-gray-600 mb-4">
                Vi tilpasser våre tjenester etter dagens værforhold for å sikre
                best mulig resultat.
            </p>
            <ul className="space-y-2">
                {services.map((service, index) => (
                    <li key={index} className="flex items-start">
                        <svg
                            className="h-5 w-5 text-green-500 mr-2 mt-0.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                            />
                        </svg>
                        <span>{service}</span>
                    </li>
                ))}
            </ul>
            <div className="mt-4">
                <Button to="/tjenester" variant="outline">
                    Se alle våre tjenester
                </Button>
            </div>
        </div>
    );
};
```
