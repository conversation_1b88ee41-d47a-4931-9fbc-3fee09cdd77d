# Filtering System Analysis

This document provides an in-depth analysis of the filtering system in the Ringerike Landskap website codebase, which is a core functionality that enables dynamic content display based on various criteria including season, category, location, and more.

## 1. Filtering System Components

The filtering system consists of several interconnected components spread across the codebase:

### 1.1. Core Filtering Logic

**Location**: `src/lib/utils/filtering.ts`

This file serves as the single source of truth for all filtering operations throughout the application. It contains:

- Service filtering functions
  - `filterServices()`: Main function for filtering services
  - `serviceMatchesCategory()`: Check if a service matches a category
  - `serviceHasFeature()`: Check if a service has a specific feature
  - `serviceMatchesSeason()`: Check if a service is relevant for a season

- Project filtering functions
  - `filterProjects()`: Main function for filtering projects
  - `projectMatchesCategory()`: Check if a project matches a category
  - `projectMatchesLocation()`: Check if a project matches a location
  - `projectHasTag()`: Check if a project has a specific tag
  - `projectMatchesSeason()`: Check if a project is relevant for a season
  - `projectMatchesService()`: Check if a project matches a service

- Seasonal utility functions
  - `getCurrentSeason()`: Get the current season based on the month
  - `getSeasonDisplayName()`: Get the display name for a season
  - `getEnglishSeasonName()`: Convert a Norwegian season name to English

- Helper functions
  - `getServiceMainCategory()`: Extract the main category from a service title
  - `getUniqueFeatures()`: Get unique features from a list of services
  - `getUniqueProjectLocations()`: Get unique locations from projects

### 1.2. Seasonal Data Constants

**Location**: `src/lib/constants/seasonal.ts`

This file contains all constants related to seasonal data:

- `SEASONS`: Array of Norwegian season names
- `SEASON_MAPPING`: Mapping of Norwegian season names to English
- `SEASON_DISPLAY_NAMES`: Mapping of seasons to display names
- `SEASONAL_SERVICES`: Maps seasons to relevant service categories and features
- `SEASONAL_PROJECTS`: Maps seasons to relevant project categories and tags
- `MONTH_TO_SEASON`: Maps month indices to seasons

### 1.3. Seasonal Data Hook

**Location**: `src/lib/hooks/useSeasonalData.ts`

A custom React hook that provides components with seasonal data:

- `currentSeason`: The current season in Norwegian
- `currentSeasonEnglish`: The current season in English
- `currentSeasonDisplay`: The display name for the current season
- `seasonalCategories`: Categories relevant to the current season
- `seasonalFeatures`: Features relevant to the current season
- `seasonalTags`: Tags relevant to the current season
- Boolean flags: `isWinter`, `isSpring`, `isSummer`, `isFall`

### 1.4. API Layer Integration

**Location**: `src/lib/api/enhanced.ts`

The API layer integrates with the filtering system, providing:

- `getFilteredServices()`: Get services filtered by criteria
- `getFilteredProjects()`: Get projects filtered by criteria
- `getSeasonalServices()`: Get seasonal services based on current season
- `getSeasonalProjects()`: Get seasonal projects based on current season

Each function uses the core filtering logic from `filtering.ts` and incorporates caching for performance.

## 2. Component Usage Examples

### 2.1. Projects Page

**Location**: `src/sections/40-projects/index.tsx`

The projects page uses the filtering system to:

- Filter projects based on selected category and location
- Get unique locations from projects, filtered by selected category
- Get service IDs that have associated projects based on current filters

```tsx
// Filter projects based on selected filters
const filteredProjects = useMemo(() => {
    if (selectedCategory) {
        // Use the specialized utility for service-project relationship
        return getProjectsForService(projectsData, selectedCategory, servicesData, selectedLocation);
    } else {
        // Use the general filtering utility when no category is selected
        return filterProjects(projectsData, null, selectedLocation);
    }
}, [selectedCategory, selectedLocation, projectsData, servicesData]);
```

### 2.2. Home Page Filtered Services

**Location**: `src/sections/10-home/FilteredServicesSection.tsx`

The filtered services section uses the seasonal hook to display services relevant to the current season:

```tsx
// Use our new seasonal hook to get seasonal data
const { currentSeason, currentSeasonDisplay } = useSeasonalData();

// Get seasonal services from API (which uses filtering.ts internally)
const { data: seasonalServices, loading } = useData(() => getSeasonalServices(6), []);
```

### 2.3. Project Filter Component

**Location**: `src/sections/40-projects/ProjectFilter.tsx`

The ProjectFilter component provides a UI for filtering projects:

```tsx
interface ProjectFilterProps {
  categories: string[];
  locations: string[];
  tags?: string[];
  selectedFilters: {
    category?: string;
    location?: string;
    tag?: string;
  };
  onFilterChange: (type: 'category' | 'location' | 'tag', value: string) => void;
}
```

## 3. Data Flow Diagram

```mermaid
flowchart TD
    %% Data sources
    seasonalConst[Seasonal Constants<br/><i>constants/seasonal.ts</i>]
    seasonalConst --> filteringUtils[Filtering Utils<br/><i>utils/filtering.ts</i>]
    
    %% Seasonal data flow
    filteringUtils --> |getCurrentSeason|seasonalHook[useSeasonalData<br/><i>hooks/useSeasonalData.ts</i>]
    seasonalHook --> |currentSeason|components[Section Components]
    
    %% API data flow
    filteringUtils --> |filterServices<br/>filterProjects|apiLayer[API Layer<br/><i>api/enhanced.ts</i>]
    apiLayer --> |getFilteredServices<br/>getFilteredProjects|components
    
    %% Component interactions
    components --> projectsPage[Projects Page<br/><i>sections/40-projects/index.tsx</i>]
    components --> filteredServices[Filtered Services<br/><i>sections/10-home/FilteredServicesSection.tsx</i>]
    projectsPage --> |selectedCategory<br/>selectedLocation|filteringUtils
    filteredServices --> |currentSeason|filteringUtils
    
    %% Style nodes
    classDef utils fill:#d5e8d4,stroke:#82b366,stroke-width:1px;
    classDef data fill:#dae8fc,stroke:#6c8ebf,stroke-width:1px;
    classDef component fill:#ffe6cc,stroke:#d79b00,stroke-width:1px;
    
    class filteringUtils utils;
    class seasonalConst,seasonalHook,apiLayer data;
    class components,projectsPage,filteredServices component;
```

## 4. Strengths of the Current System

1. **Centralized Filtering Logic**: All filtering functions are consolidated in a single `filtering.ts` file, making it easy to maintain and update.

2. **Type Safety**: The system uses TypeScript interfaces to ensure type safety throughout the filtering process.

3. **Reusability**: The filtering functions are designed to be reusable across different components and can be combined for complex filtering scenarios.

4. **Caching**: The API layer incorporates caching to improve performance when the same filters are applied multiple times.

5. **Season Awareness**: The system automatically detects the current season and can filter content accordingly.

6. **Consistent Implementation**: The filtering functions follow a consistent pattern, making them easy to understand and extend.

## 5. Areas for Improvement

### 5.1. Functional Improvements

1. **Remove Filter Logic from API Layer**: 
   - The `enhanced.ts` file in the API layer contains some filtering logic, which could be moved entirely to the filtering module for better separation of concerns.
   - **Example**: The `getSeasonalServices` and `getSeasonalProjects` functions could delegate all filtering to the filtering module.

2. **Standardize Filter Parameter Naming**:
   - Some functions use parameters like `category` and `feature` while others use `category` and `tag`. Standardizing these names would improve consistency.
   - **Example**: `filterServices` uses `category`, `feature`, `season` while `filterProjects` uses `category`, `location`, `tag`, `season`.

3. **Reduce Logic Duplication**:
   - There's some duplication between seasonal and filtering logic. For example, `getCurrentSeason()` logic exists in both filtering and seasonal utilities.
   - **Example**: Consolidate all season-related functions into a dedicated module.

### 5.2. Structural Improvements

1. **Split Filtering Module by Domain**:
   - The filtering.ts file handles multiple concerns (services, projects, seasons). Splitting it into domain-specific modules could improve maintainability.
   - **Example**: Create `serviceFilters.ts`, `projectFilters.ts`, and `seasonalFilters.ts`.

2. **Create a Dedicated Seasonal Module**:
   - Seasonal utilities are currently mixed with filtering utilities. Creating a dedicated seasonal module would separate these concerns.
   - **Example**: Move all seasonal utilities to `src/lib/utils/seasonal.ts`.

3. **Improve Documentation**:
   - While the code is well-documented, adding more examples and use cases would help new developers understand how to use the filtering system.
   - **Example**: Add a dedicated README.md for the filtering system with examples.

### 5.3. Performance Improvements

1. **Optimize Filter Function Calls**:
   - Some filter functions might be called repeatedly with the same parameters. Implementing memoization at the component level could improve performance.
   - **Example**: Wrap common filter combinations in memoized helper functions.

2. **Batch Filter Operations**:
   - When multiple filters are applied sequentially, they each iterate over the full array. Implementing a batch filter operation could reduce iterations.
   - **Example**: Create a chainable filter API that only performs the final iteration when all filters are applied.

## 6. Implementation Recommendations

### 6.1. Split Filtering Module

Create specialized modules for different filtering domains:

```typescript
// src/lib/utils/filters/serviceFilters.ts
export const serviceMatchesCategory = (service, category) => {...}
export const serviceHasFeature = (service, feature) => {...}
export const filterServices = (services, options) => {...}

// src/lib/utils/filters/projectFilters.ts
export const projectMatchesCategory = (project, category) => {...}
export const projectMatchesLocation = (project, location) => {...}
export const filterProjects = (projects, options) => {...}

// src/lib/utils/filters/index.ts
export * from './serviceFilters';
export * from './projectFilters';
export * from './seasonalFilters';
```

### 6.2. Create a FilterOptions Interface

Consolidate filter parameters into a consistent options object:

```typescript
// src/lib/utils/filters/types.ts
export interface ServiceFilterOptions {
  category?: string | null;
  feature?: string | null;
  season?: string | null;
}

export interface ProjectFilterOptions {
  category?: string | null;
  location?: string | null;
  tag?: string | null;
  season?: string | null;
}

// Usage
export const filterServices = (
  services: ServiceType[],
  options: ServiceFilterOptions
): ServiceType[] => {...}
```

### 6.3. Remove Filtering Logic from API Layer

Refactor API functions to delegate filtering:

```typescript
// src/lib/api/enhanced.ts
export const getSeasonalServices = async (limit?: number): Promise<ServiceType[]> => {
  const currentSeason = getCurrentSeason();
  const cacheKey = `seasonalServices:${currentSeason}:${limit || 'all'}`;

  return getFromCacheOrFetch(cacheKey, async () => {
    logDataAccess('direct', 'seasonalServices', { season: currentSeason, limit });
    
    // Get all services and use the filtering utility
    const services = await getServices();
    const filtered = filterServices(services, { season: currentSeason });
    
    return limit ? filtered.slice(0, limit) : filtered;
  });
}
```

### 6.4. Create a Dedicated Seasonal Module

Move all seasonal logic to a dedicated module:

```typescript
// src/lib/utils/seasonal.ts
import { MONTH_TO_SEASON, SEASON_DISPLAY_NAMES, SEASON_MAPPING } from '@/lib/constants/seasonal';
import { SeasonType, EnglishSeasonType } from '@/lib/types/content';

/**
 * Gets the current season based on the current month
 */
export const getCurrentSeason = (): SeasonType => {
  const month = new Date().getMonth();
  return MONTH_TO_SEASON[month];
};

/**
 * Gets the display name for a season
 */
export const getSeasonDisplayName = (season: SeasonType): string => {
  return SEASON_DISPLAY_NAMES[season] || season;
};

/**
 * Converts a Norwegian season name to English
 */
export const getEnglishSeasonName = (season: SeasonType): EnglishSeasonType => {
  return SEASON_MAPPING[season] || (season as EnglishSeasonType);
};
```

## 7. Conclusion

The filtering system in the Ringerike Landskap website codebase is well-designed and follows good practices for code organization and reusability. The system effectively handles filtering for services and projects based on various criteria, including season, category, location, and more.

By implementing the recommended improvements, the filtering system can be made even more maintainable, performant, and easy to use. The key focus areas should be:

1. Improving separation of concerns by moving filtering logic out of the API layer
2. Standardizing parameter naming and function signatures
3. Reducing logic duplication by creating specialized modules
4. Enhancing performance through memoization and batch operations
5. Improving documentation with examples and use cases

These improvements will not only make the code more maintainable but also improve developer experience when working with the filtering system.
