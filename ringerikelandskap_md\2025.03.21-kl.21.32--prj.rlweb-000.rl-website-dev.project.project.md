# Dir `project`

### File Structure

```
├── capture-website.js
├── eslint.config.js
├── postcss.config.js
├── tailwind.config.js
└── scripts
    ├── auto-snapshot.js
    ├── cleanup-legacy-screenshots.js
    ├── cleanup.js
    ├── dev-with-snapshots.js
    ├── refresh-screenshots.js
    ├── screenshot-manager.js
    ├── tidy-screenshots.js
    └── visualizations
        ├── check-dependencies.js
        ├── check-graphviz.js
        ├── cleanup-directory.js
        ├── cleanup-redundant-files.js
        ├── create-bubble-chart.js
        ├── create-circle-packing.js
        ├── create-d3-graph.js
        ├── create-dependency-dashboard.js
        ├── create-flow-diagram.js
        ├── dependency-manager.js
        ├── fix-depcruise-paths.js
        ├── fix-missing-files.js
        └── run-visualizations.js
```


#### `capture-website.js`

```javascript
import puppeteer from "puppeteer";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
    // Base directory for screenshots
    baseDir: path.join(__dirname, "screenshots"),

    // Maximum number of snapshot directories to keep (oldest will be deleted)
    maxSnapshots: 5,

    // Whether to keep HTML files (can be large)
    keepHtmlFiles: true,

    // Whether to generate a report
    generateReport: true,

    // Port for the local development server
    port: 5173,

    // Timestamp format for directory names (YYYY-MM-DD_HH-MM-SS)
    timestampFormat: () => {
        const now = new Date();
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
            2,
            "0"
        )}-${String(now.getDate()).padStart(2, "0")}_${String(
            now.getHours()
        ).padStart(2, "0")}-${String(now.getMinutes()).padStart(
            2,
            "0"
        )}-${String(now.getSeconds()).padStart(2, "0")}`;
    },
};

// Define viewport sizes to capture
const viewports = [
    { width: 375, height: 667, name: "mobile" },
    { width: 768, height: 1024, name: "tablet" },
    { width: 1440, height: 900, name: "desktop" },
];

// Define pages to capture
const pages = [
    { path: "/", name: "home" },
    { path: "/hvem-er-vi", name: "about" },
    { path: "/hva-vi-gjor", name: "services" },
    { path: "/prosjekter", name: "projects" },
    { path: "/kontakt", name: "contact" },
];

/**
 * Creates necessary directories for screenshots
 * @returns {string} Path to the current snapshot directory
 */
function setupDirectories() {
    // Create base screenshots directory if it doesn't exist
    if (!fs.existsSync(CONFIG.baseDir)) {
        fs.mkdirSync(CONFIG.baseDir, { recursive: true });
    }

    // Create a timestamped directory for this snapshot
    const timestamp = CONFIG.timestampFormat();
    const snapshotDir = path.join(CONFIG.baseDir, timestamp);
    fs.mkdirSync(snapshotDir, { recursive: true });

    // Create subdirectories for each viewport
    viewports.forEach((viewport) => {
        fs.mkdirSync(path.join(snapshotDir, viewport.name), {
            recursive: true,
        });
    });

    // Create a latest symlink/directory
    const latestDir = path.join(CONFIG.baseDir, "latest");
    if (fs.existsSync(latestDir)) {
        fs.rmSync(latestDir, { recursive: true, force: true });
    }
    fs.mkdirSync(latestDir, { recursive: true });

    // Create viewport subdirectories in latest
    viewports.forEach((viewport) => {
        fs.mkdirSync(path.join(latestDir, viewport.name), { recursive: true });
    });

    return { snapshotDir, latestDir };
}

/**
 * Cleans up old snapshot directories based on retention policy
 */
function cleanupOldSnapshots() {
    try {
        const baseDir = CONFIG.baseDir;
        const dirs = fs
            .readdirSync(baseDir)
            .filter((name) => {
                // Only consider timestamp-formatted directories (exclude 'latest' and any other files)
                const fullPath = path.join(baseDir, name);
                return (
                    fs.statSync(fullPath).isDirectory() &&
                    name !== "latest" &&
                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(name)
                );
            })
            .map((name) => ({
                name,
                path: path.join(baseDir, name),
                time: fs.statSync(path.join(baseDir, name)).mtime.getTime(),
            }))
            .sort((a, b) => b.time - a.time); // Sort newest first

        // Keep only the most recent directories based on maxSnapshots
        if (dirs.length > CONFIG.maxSnapshots) {
            const dirsToDelete = dirs.slice(CONFIG.maxSnapshots);
            dirsToDelete.forEach((dir) => {
                console.log(`Removing old snapshot: ${dir.name}`);
                fs.rmSync(dir.path, { recursive: true, force: true });
            });
        }
    } catch (error) {
        console.error("Error cleaning up old snapshots:", error);
    }
}

/**
 * Generates an HTML report of all screenshots
 * @param {string} snapshotDir Path to the current snapshot directory
 */
function generateReport(snapshotDir, latestDir) {
    const reportPath = path.join(CONFIG.baseDir, "screenshot-report.html");

    let reportHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ringerike Landskap Website Screenshots</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            h1, h2, h3 {
                color: #1e9545;
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
            }
            h2 {
                margin-top: 40px;
                padding-bottom: 10px;
                border-bottom: 2px solid #eee;
            }
            .viewport-tabs {
                display: flex;
                margin-bottom: 20px;
                border-bottom: 1px solid #ddd;
            }
            .viewport-tab {
                padding: 10px 20px;
                cursor: pointer;
                background: #f5f5f5;
                border: 1px solid #ddd;
                border-bottom: none;
                margin-right: 5px;
                border-radius: 5px 5px 0 0;
            }
            .viewport-tab.active {
                background: #fff;
                border-bottom: 1px solid #fff;
                margin-bottom: -1px;
                font-weight: bold;
                color: #1e9545;
            }
            .viewport-content {
                display: none;
            }
            .viewport-content.active {
                display: block;
            }
            .screenshot-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
            }
            .screenshot-card {
                border: 1px solid #eee;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            .screenshot-card img {
                width: 100%;
                height: auto;
                display: block;
            }
            .screenshot-info {
                padding: 15px;
            }
            .screenshot-info h3 {
                margin: 0 0 10px 0;
                font-size: 18px;
            }
            .screenshot-links {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            .screenshot-links a {
                color: #1e9545;
                text-decoration: none;
            }
            .screenshot-links a:hover {
                text-decoration: underline;
            }
            .timestamp {
                color: #666;
                font-size: 14px;
                margin-bottom: 30px;
                text-align: center;
            }
            .snapshots-list {
                margin-bottom: 30px;
            }
            .snapshots-list a {
                display: inline-block;
                margin-right: 10px;
                color: #1e9545;
                text-decoration: none;
            }
            .snapshots-list a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <h1>Ringerike Landskap Website Screenshots</h1>
        <p class="timestamp">Generated on ${new Date().toLocaleString()}</p>

        <div class="viewport-tabs">
            ${viewports
                .map(
                    (viewport, index) =>
                        `<div class="viewport-tab ${
                            index === 0 ? "active" : ""
                        }" data-viewport="${viewport.name}">${
                            viewport.name.charAt(0).toUpperCase() +
                            viewport.name.slice(1)
                        } (${viewport.width}x${viewport.height})</div>`
                )
                .join("")}
        </div>

        ${viewports
            .map(
                (viewport, index) => `
            <div class="viewport-content ${index === 0 ? "active" : ""}" id="${
                    viewport.name
                }-content">
                <div class="screenshot-grid">
                    ${pages
                        .map((page) => {
                            const screenshotPath = `latest/${viewport.name}/${page.name}-${viewport.name}.png`;
                            const htmlPath = `latest/${viewport.name}/${page.name}-${viewport.name}.html`;

                            return `
                            <div class="screenshot-card">
                                <img src="${screenshotPath}" alt="${
                                page.name
                            } ${viewport.name} screenshot" loading="lazy">
                                <div class="screenshot-info">
                                    <h3>${
                                        page.name.charAt(0).toUpperCase() +
                                        page.name.slice(1)
                                    } Page</h3>
                                    <div class="screenshot-links">
                                        <a href="${screenshotPath}" target="_blank">View Image</a>
                                        ${
                                            CONFIG.keepHtmlFiles
                                                ? `<a href="${htmlPath}" target="_blank">View HTML</a>`
                                                : ""
                                        }
                                    </div>
                                </div>
                            </div>
                        `;
                        })
                        .join("")}
                </div>
            </div>
        `
            )
            .join("")}

        <script>
            document.querySelectorAll('.viewport-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and content
                    document.querySelectorAll('.viewport-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.viewport-content').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    document.getElementById(tab.dataset.viewport + '-content').classList.add('active');
                });
            });
        </script>
    </body>
    </html>
    `;

    fs.writeFileSync(reportPath, reportHtml);
    console.log(`Report generated at ${reportPath}`);
}

async function captureWebsite() {
    console.log("Setting up directories...");
    const { snapshotDir, latestDir } = setupDirectories();

    console.log("Launching browser...");
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    // Capture screenshots for each page at each viewport size
    for (const viewport of viewports) {
        console.log(
            `Setting viewport to ${viewport.name}: ${viewport.width}x${viewport.height}`
        );
        await page.setViewport({
            width: viewport.width,
            height: viewport.height,
        });

        for (const pageConfig of pages) {
            const url = `http://localhost:${CONFIG.port}${pageConfig.path}`;
            console.log(`Navigating to ${url}`);

            try {
                await page.goto(url, {
                    waitUntil: "networkidle2",
                    timeout: 10000,
                });

                // Wait for any animations or lazy-loaded content
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // File paths for snapshot directory
                const screenshotPath = path.join(
                    snapshotDir,
                    viewport.name,
                    `${pageConfig.name}-${viewport.name}.png`
                );
                const htmlPath = path.join(
                    snapshotDir,
                    viewport.name,
                    `${pageConfig.name}-${viewport.name}.html`
                );

                // File paths for latest directory
                const latestScreenshotPath = path.join(
                    latestDir,
                    viewport.name,
                    `${pageConfig.name}-${viewport.name}.png`
                );
                const latestHtmlPath = path.join(
                    latestDir,
                    viewport.name,
                    `${pageConfig.name}-${viewport.name}.html`
                );

                // Take full page screenshot
                await page.screenshot({
                    path: screenshotPath,
                    fullPage: true,
                });

                // Copy to latest directory
                fs.copyFileSync(screenshotPath, latestScreenshotPath);

                console.log(`Screenshot saved to ${screenshotPath}`);

                // Optionally save the HTML content
                if (CONFIG.keepHtmlFiles) {
                    const html = await page.content();
                    fs.writeFileSync(htmlPath, html);
                    fs.writeFileSync(latestHtmlPath, html);
                    console.log(`HTML saved to ${htmlPath}`);
                }
            } catch (error) {
                console.error(`Error capturing ${url}:`, error.message);
            }
        }
    }

    await browser.close();

    // Clean up old snapshots
    cleanupOldSnapshots();

    // Generate report
    if (CONFIG.generateReport) {
        generateReport(snapshotDir, latestDir);
    }

    console.log("Done capturing website!");
}

captureWebsite().catch(console.error);
```


#### `eslint.config.js`

```javascript
import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  }
);
```


#### `postcss.config.js`

```javascript
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
```


#### `tailwind.config.js`

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        green: {
          50: '#f0f9f4',
          100: '#dbf0e3',
          200: '#b8e0ca',
          300: '#89c9a8',
          400: '#57ab83',
          500: '#1e9545',
          600: '#157c3a',
          700: '#116530',
          800: '#0e4f27',
          900: '#0c4121',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-out forwards',
        'slide-up': 'slideUp 0.5s ease-out forwards',
        'gradient': 'gradient 8s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { 
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
    },
  },
  plugins: [],
};```


#### `scripts\auto-snapshot.js`

```javascript
import { exec } from "child_process";
import { promisify } from "util";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import puppeteer from "puppeteer";

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

// Configuration
const CONFIG = {
    // Development server port
    port: 5173,

    // Delay before capturing screenshots (ms)
    captureDelay: 2000,

    // AI analysis directory
    aiDir: path.join(projectRoot, ".ai-analysis"),

    // Latest screenshots directory
    latestDir: path.join(projectRoot, ".ai-analysis", "latest"),

    // Maximum number of snapshots to keep
    maxSnapshots: 10,

    // Pages to capture
    pages: [
        { path: "/", name: "home" },
        { path: "/hvem-er-vi", name: "about" },
        { path: "/hva-vi-gjor", name: "services" },
        { path: "/prosjekter", name: "projects" },
        { path: "/kontakt", name: "contact" },
    ],

    // Viewport to use for AI analysis
    viewport: { width: 1440, height: 900, name: "desktop" },
};

/**
 * Ensures the AI analysis directory exists
 */
function setupAiDirectory() {
    // Create main AI directory if it doesn't exist
    if (!fs.existsSync(CONFIG.aiDir)) {
        fs.mkdirSync(CONFIG.aiDir, { recursive: true });
    }

    // Create latest directory if it doesn't exist
    if (!fs.existsSync(CONFIG.latestDir)) {
        fs.mkdirSync(CONFIG.latestDir, { recursive: true });
    }

    // Create a metadata.json file if it doesn't exist
    const metadataPath = path.join(CONFIG.aiDir, "metadata.json");
    if (!fs.existsSync(metadataPath)) {
        fs.writeFileSync(
            metadataPath,
            JSON.stringify(
                {
                    snapshots: [],
                    lastSnapshot: null,
                },
                null,
                2
            )
        );
    }

    return metadataPath;
}

/**
 * Updates the metadata file with snapshot information
 */
function updateMetadata(metadataPath, snapshotId) {
    const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf8"));

    // Add new snapshot
    metadata.snapshots.push({
        id: snapshotId,
        timestamp: new Date().toISOString(),
        pages: CONFIG.pages.map((page) => page.name),
    });

    // Keep only the most recent snapshots
    if (metadata.snapshots.length > CONFIG.maxSnapshots) {
        const removedSnapshots = metadata.snapshots.slice(
            0,
            metadata.snapshots.length - CONFIG.maxSnapshots
        );
        metadata.snapshots = metadata.snapshots.slice(
            metadata.snapshots.length - CONFIG.maxSnapshots
        );

        // Remove old snapshot directories
        removedSnapshots.forEach((snapshot) => {
            const snapshotDir = path.join(CONFIG.aiDir, snapshot.id);
            if (fs.existsSync(snapshotDir)) {
                fs.rmSync(snapshotDir, { recursive: true, force: true });
            }
        });
    }

    // Update last snapshot
    metadata.lastSnapshot = snapshotId;

    // Write updated metadata
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

    return metadata;
}

/**
 * Captures a screenshot of a page
 */
async function capturePage(page, snapshotDir) {
    const url = `http://localhost:${CONFIG.port}${page.path}`;
    const outputPath = path.join(snapshotDir, `${page.name}.png`);
    const latestOutputPath = path.join(CONFIG.latestDir, `${page.name}.png`);

    try {
        // Use puppeteer directly instead of via a temporary script
        const browser = await puppeteer.launch();
        const browserPage = await browser.newPage();

        await browserPage.setViewport({
            width: CONFIG.viewport.width,
            height: CONFIG.viewport.height,
        });

        await browserPage.goto(url, {
            waitUntil: "networkidle2",
            timeout: 10000,
        });

        // Wait for any animations
        await new Promise((resolve) =>
            setTimeout(resolve, CONFIG.captureDelay)
        );

        // Take the screenshot
        await browserPage.screenshot({
            path: outputPath,
            fullPage: true,
        });

        // Copy to latest directory
        fs.copyFileSync(outputPath, latestOutputPath);

        await browser.close();

        console.log(`Captured ${page.name} page`);
        return { outputPath, latestOutputPath };
    } catch (error) {
        console.error(`Error capturing ${page.name} page:`, error.message);
        return null;
    }
}

/**
 * Generates an AI-readable summary of the snapshot
 */
function generateAiSummary(snapshotDir, metadata) {
    const summaryPath = path.join(snapshotDir, "ai-summary.md");
    const latestSummaryPath = path.join(CONFIG.latestDir, "ai-summary.md");

    const summary = `# Website Snapshot for AI Analysis

## Snapshot Information
- **ID**: ${metadata.lastSnapshot}
- **Timestamp**: ${new Date().toISOString()}
- **Pages Captured**: ${CONFIG.pages.map((page) => page.name).join(", ")}

## Screenshot Paths
${CONFIG.pages
    .map(
        (page) =>
            `- **${page.name}**: \`${path.join(
                snapshotDir,
                `${page.name}.png`
            )}\``
    )
    .join("\n")}

## Latest Screenshot Paths (Always Current)
${CONFIG.pages
    .map(
        (page) =>
            `- **${page.name}**: \`${path.join(
                CONFIG.latestDir,
                `${page.name}.png`
            )}\``
    )
    .join("\n")}

## Viewport Information
- **Width**: ${CONFIG.viewport.width}px
- **Height**: ${CONFIG.viewport.height}px
- **Name**: ${CONFIG.viewport.name}

## Usage Instructions
These screenshots can be used by AI assistants to understand the current state of the website.
When making code changes, reference these images to understand the visual impact of your changes.

## Access Paths
To reference the specific snapshot in conversations, use:
\`\`\`
/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${
        metadata.lastSnapshot
    }
\`\`\`

To always reference the latest screenshots, use:
\`\`\`
/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest
\`\`\`
`;

    // Write to snapshot directory
    fs.writeFileSync(summaryPath, summary);

    // Write to latest directory
    fs.writeFileSync(latestSummaryPath, summary);

    return { summaryPath, latestSummaryPath };
}

/**
 * Main function to capture snapshots for AI analysis
 */
async function captureForAiAnalysis() {
    try {
        console.log("Capturing website snapshots for AI analysis...");

        // Setup AI directory
        const metadataPath = setupAiDirectory();

        // Create a unique ID for this snapshot
        const snapshotId = `snapshot-${Date.now()}`;
        const snapshotDir = path.join(CONFIG.aiDir, snapshotId);
        fs.mkdirSync(snapshotDir, { recursive: true });

        // Capture screenshots for each page
        for (const page of CONFIG.pages) {
            await capturePage(page, snapshotDir);
        }

        // Update metadata
        const metadata = updateMetadata(metadataPath, snapshotId);

        // Generate AI summary
        const { summaryPath, latestSummaryPath } = generateAiSummary(
            snapshotDir,
            metadata
        );

        console.log(`\nSnapshot captured successfully: ${snapshotId}`);
        console.log(`AI summary generated at: ${summaryPath}`);
        console.log(`Latest summary updated at: ${latestSummaryPath}`);

        console.log(
            `\nTo reference the specific snapshot in conversations, use:`
        );
        console.log(
            `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${metadata.lastSnapshot}`
        );

        console.log(`\nTo always reference the latest screenshots, use:`);
        console.log(
            `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
        );

        return {
            success: true,
            snapshotId,
            snapshotDir,
            latestDir: CONFIG.latestDir,
            summaryPath,
            latestSummaryPath,
        };
    } catch (error) {
        console.error("Error capturing snapshots for AI analysis:", error);
        return {
            success: false,
            error: error.message,
        };
    }
}

// Run the capture process
captureForAiAnalysis();
```


#### `scripts\cleanup-legacy-screenshots.js`

```javascript
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

// Screenshot directory
const screenshotsDir = path.join(projectRoot, "screenshots");

/**
 * Cleans up legacy screenshots that are not in the organized structure
 */
function cleanupLegacyScreenshots() {
    try {
        console.log("Cleaning up legacy screenshots...");

        // Get all files in the screenshots directory
        const files = fs.readdirSync(screenshotsDir);

        // Filter for legacy screenshot files (not directories and not the report)
        const legacyFiles = files.filter((file) => {
            const filePath = path.join(screenshotsDir, file);
            const isDirectory = fs.statSync(filePath).isDirectory();

            // Keep directories with timestamp format or 'latest'
            if (isDirectory) {
                return !(
                    file === "latest" ||
                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(file)
                );
            }

            // Keep the screenshot report
            if (file === "screenshot-report.html") {
                return false;
            }

            // All other files are legacy
            return true;
        });

        if (legacyFiles.length === 0) {
            console.log("No legacy screenshots found");
            return;
        }

        console.log(`Found ${legacyFiles.length} legacy screenshot files`);

        // Create a backup directory
        const backupDir = path.join(screenshotsDir, "legacy-backup");
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir);
        }

        // Move legacy files to backup directory
        legacyFiles.forEach((file) => {
            const sourcePath = path.join(screenshotsDir, file);
            const destPath = path.join(backupDir, file);

            try {
                fs.renameSync(sourcePath, destPath);
                console.log(`Moved ${file} to backup directory`);
            } catch (error) {
                console.error(`Error moving ${file}: ${error.message}`);
            }
        });

        console.log(`Moved ${legacyFiles.length} legacy files to ${backupDir}`);
        console.log(
            "You can delete the backup directory if you no longer need these files"
        );
    } catch (error) {
        console.error("Error cleaning up legacy screenshots:", error);
    }
}

// Run the cleanup
cleanupLegacyScreenshots();
```


#### `scripts\cleanup.js`

```javascript
import { readdirSync, rmdirSync, statSync } from 'fs';
import { join } from 'path';

function removeEmptyDirs(dir) {
  const files = readdirSync(dir);
  
  for (const file of files) {
    const path = join(dir, file);
    if (statSync(path).isDirectory()) {
      removeEmptyDirs(path);
      if (readdirSync(path).length === 0) {
        rmdirSync(path);
        console.log(`Removed empty directory: ${path}`);
      }
    }
  }
}

removeEmptyDirs('src');```


#### `scripts\dev-with-snapshots.js`

```javascript
import { exec, spawn } from "child_process";
import { promisify } from "util";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { createServer } from "http";

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

// Configuration
const CONFIG = {
    // Minimum time between snapshots (ms)
    snapshotCooldown: 30000, // 30 seconds

    // Port for the snapshot trigger server
    triggerPort: 5174,

    // Development server port
    devPort: 5173,

    // Latest screenshots directory
    latestDir: path.join(projectRoot, ".ai-analysis", "latest"),
};

// Track the last snapshot time
let lastSnapshotTime = 0;

/**
 * Starts the development server
 */
function startDevServer() {
    console.log("Starting development server...");

    const devProcess = spawn("npm", ["run", "dev"], {
        cwd: projectRoot,
        stdio: "inherit",
        shell: true,
    });

    devProcess.on("error", (error) => {
        console.error("Error starting development server:", error);
    });

    return devProcess;
}

/**
 * Takes a snapshot if enough time has passed since the last one
 */
async function takeSnapshotIfNeeded() {
    const now = Date.now();

    // Check if enough time has passed since the last snapshot
    if (now - lastSnapshotTime < CONFIG.snapshotCooldown) {
        console.log("Snapshot cooldown active, skipping...");
        return false;
    }

    // Update the last snapshot time
    lastSnapshotTime = now;

    try {
        console.log("Taking snapshot for AI analysis...");
        await execAsync(`npm run ai:snapshot`, { cwd: projectRoot });

        // Display the latest directory path for easy reference
        console.log("\n=== LATEST SCREENSHOTS READY FOR AI REFERENCE ===");
        console.log(
            `Path: /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
        );
        console.log("=================================================\n");

        return true;
    } catch (error) {
        console.error("Error taking snapshot:", error.message);
        return false;
    }
}

/**
 * Creates a simple HTTP server to trigger snapshots
 */
function createTriggerServer() {
    const server = createServer(async (req, res) => {
        // Set CORS headers
        res.setHeader("Access-Control-Allow-Origin", "*");
        res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        res.setHeader("Access-Control-Allow-Headers", "Content-Type");

        // Handle preflight requests
        if (req.method === "OPTIONS") {
            res.writeHead(204);
            res.end();
            return;
        }

        // Handle snapshot trigger
        if (req.url === "/trigger-snapshot") {
            console.log("Snapshot triggered via HTTP request");

            const success = await takeSnapshotIfNeeded();

            res.writeHead(200, { "Content-Type": "application/json" });
            res.end(
                JSON.stringify({
                    success,
                    message: success
                        ? "Snapshot taken successfully"
                        : "Snapshot skipped (cooldown active)",
                    latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                })
            );
            return;
        }

        // Handle status check
        if (req.url === "/status") {
            res.writeHead(200, { "Content-Type": "application/json" });
            res.end(
                JSON.stringify({
                    status: "running",
                    lastSnapshot: new Date(lastSnapshotTime).toISOString(),
                    cooldownActive:
                        Date.now() - lastSnapshotTime < CONFIG.snapshotCooldown,
                    latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                })
            );
            return;
        }

        // Handle latest path request
        if (req.url === "/latest-path") {
            res.writeHead(200, { "Content-Type": "application/json" });
            res.end(
                JSON.stringify({
                    latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                })
            );
            return;
        }

        // Default response
        res.writeHead(404, { "Content-Type": "application/json" });
        res.end(JSON.stringify({ error: "Not found" }));
    });

    server.listen(CONFIG.triggerPort, () => {
        console.log(
            `Snapshot trigger server running at http://localhost:${CONFIG.triggerPort}`
        );
        console.log(
            `- To trigger a snapshot: http://localhost:${CONFIG.triggerPort}/trigger-snapshot`
        );
        console.log(
            `- To check status: http://localhost:${CONFIG.triggerPort}/status`
        );
        console.log(
            `- To get latest path: http://localhost:${CONFIG.triggerPort}/latest-path`
        );
    });

    return server;
}

/**
 * Creates a file watcher to trigger snapshots on changes
 */
function createFileWatcher() {
    // Directories to watch
    const watchDirs = [path.join(projectRoot, "src")];

    // Extensions to watch
    const watchExtensions = [
        ".ts",
        ".tsx",
        ".js",
        ".jsx",
        ".css",
        ".scss",
        ".html",
    ];

    // Create watchers for each directory
    const watchers = watchDirs.map((dir) => {
        console.log(`Watching directory for changes: ${dir}`);

        return fs.watch(
            dir,
            { recursive: true },
            async (eventType, filename) => {
                if (!filename) return;

                // Check if the file extension should trigger a snapshot
                const ext = path.extname(filename).toLowerCase();
                if (!watchExtensions.includes(ext)) return;

                console.log(`File changed: ${filename}`);
                await takeSnapshotIfNeeded();
            }
        );
    });

    return watchers;
}

/**
 * Main function to run development with automatic snapshots
 */
async function devWithSnapshots() {
    try {
        console.log("Starting development with automatic snapshots...");

        // Start the development server
        const devProcess = startDevServer();

        // Wait for the development server to start
        console.log("Waiting for development server to start...");
        await new Promise((resolve) => setTimeout(resolve, 5000));

        // Create the trigger server
        const triggerServer = createTriggerServer();

        // Create file watchers
        const watchers = createFileWatcher();

        // Take an initial snapshot
        console.log("Taking initial snapshot...");
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait for dev server to be fully ready
        await takeSnapshotIfNeeded();

        console.log("\nDevelopment with automatic snapshots is running!");
        console.log("- Development server: http://localhost:5173");
        console.log(
            "- Snapshot trigger: http://localhost:5174/trigger-snapshot"
        );
        console.log(
            "- File changes in src/ will automatically trigger snapshots"
        );
        console.log("- Latest screenshots are always available at:");
        console.log(
            `  /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
        );
        console.log("- Press Ctrl+C to stop");

        // Handle process termination
        process.on("SIGINT", () => {
            console.log("Shutting down...");

            // Close file watchers
            watchers.forEach((watcher) => watcher.close());

            // Close trigger server
            triggerServer.close();

            // Kill development server
            devProcess.kill();

            process.exit(0);
        });
    } catch (error) {
        console.error("Error starting development with snapshots:", error);
    }
}

// Run the main function
devWithSnapshots();
```


#### `scripts\refresh-screenshots.js`

```javascript
import { exec } from "child_process";
import { promisify } from "util";
import path from "path";
import { fileURLToPath } from "url";

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

/**
 * Runs a screenshot manager command
 * @param {string} command The command to run
 * @param {string[]} args Additional arguments
 */
async function runScreenshotCommand(command, args = []) {
    const commandString = `node ${path.join(
        projectRoot,
        "scripts",
        "screenshot-manager.js"
    )} ${command} ${args.join(" ")}`;
    console.log(`Running: ${commandString}`);

    try {
        const { stdout, stderr } = await execAsync(commandString);
        if (stdout) console.log(stdout);
        if (stderr) console.error(stderr);
    } catch (error) {
        console.error(`Error running ${command}:`, error.message);
        throw error;
    }
}

/**
 * Refreshes screenshots by capturing, tidying, and cleaning up
 */
async function refreshScreenshots() {
    try {
        console.log("Starting screenshot refresh process...");

        // Step 1: Capture new screenshots
        console.log("\n=== Step 1: Capturing new screenshots ===");
        await runScreenshotCommand("capture");

        // Step 2: Tidy up the screenshots directory
        console.log("\n=== Step 2: Tidying up screenshots directory ===");
        await runScreenshotCommand("tidy");

        // Step 3: Clean up old screenshots (keep last 7 days)
        console.log("\n=== Step 3: Cleaning up old screenshots ===");
        await runScreenshotCommand("clean", ["7"]);

        console.log("\nScreenshot refresh process completed successfully!");
        console.log(
            "You can view the latest screenshots in the screenshot-report.html file."
        );
    } catch (error) {
        console.error("Screenshot refresh process failed:", error.message);
    }
}

// Run the refresh process
refreshScreenshots();
```


#### `scripts\screenshot-manager.js`

```javascript
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

// Configuration
const CONFIG = {
    // Screenshot directory
    screenshotsDir: path.join(projectRoot, "screenshots"),

    // Capture script path
    captureScript: path.join(projectRoot, "capture-website.js"),

    // Tidy script path
    tidyScript: path.join(projectRoot, "scripts", "tidy-screenshots.js"),

    // Commands
    commands: {
        capture: "capture",
        clean: "clean",
        compare: "compare",
        help: "help",
        schedule: "schedule",
        unschedule: "unschedule",
        tidy: "tidy",
    },

    // Default retention (days)
    defaultRetention: 7,
};

/**
 * Displays help information
 */
function showHelp() {
    console.log(`
Screenshot Manager - Utility for managing website screenshots

Usage:
  node screenshot-manager.js [command] [options]

Commands:
  capture                 Capture screenshots now
  clean [days]            Clean screenshots older than specified days (default: ${CONFIG.defaultRetention})
  compare [dir1] [dir2]   Compare two screenshot directories
  schedule [interval]     Schedule automatic captures (interval in hours, default: 24)
  unschedule              Remove scheduled captures
  tidy                    Tidy up the screenshots directory (organize and optimize)
  help                    Show this help message

Examples:
  node screenshot-manager.js capture
  node screenshot-manager.js clean 3
  node screenshot-manager.js schedule 12
  node screenshot-manager.js tidy
  `);
}

/**
 * Captures screenshots by running the capture script
 */
async function captureScreenshots() {
    try {
        console.log("Capturing screenshots...");
        await execAsync(`node ${CONFIG.captureScript}`);
        console.log("Screenshots captured successfully");
    } catch (error) {
        console.error("Error capturing screenshots:", error.message);
    }
}

/**
 * Cleans up screenshots older than the specified number of days
 * @param {number} days Number of days to keep screenshots
 */
function cleanOldScreenshots(days = CONFIG.defaultRetention) {
    try {
        const baseDir = CONFIG.screenshotsDir;
        if (!fs.existsSync(baseDir)) {
            console.log("Screenshots directory does not exist");
            return;
        }

        const now = new Date().getTime();
        const maxAge = days * 24 * 60 * 60 * 1000; // Convert days to milliseconds

        const dirs = fs
            .readdirSync(baseDir)
            .filter((name) => {
                // Only consider timestamp-formatted directories
                const fullPath = path.join(baseDir, name);
                return (
                    fs.statSync(fullPath).isDirectory() &&
                    name !== "latest" &&
                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(name)
                );
            })
            .map((name) => ({
                name,
                path: path.join(baseDir, name),
                time: fs.statSync(path.join(baseDir, name)).mtime.getTime(),
            }))
            .filter((dir) => now - dir.time > maxAge);

        if (dirs.length === 0) {
            console.log(`No screenshots older than ${days} days found`);
            return;
        }

        console.log(
            `Found ${dirs.length} screenshot directories older than ${days} days`
        );

        dirs.forEach((dir) => {
            console.log(`Removing old snapshot: ${dir.name}`);
            fs.rmSync(dir.path, { recursive: true, force: true });
        });

        console.log(`Cleaned up ${dirs.length} old screenshot directories`);
    } catch (error) {
        console.error("Error cleaning old screenshots:", error);
    }
}

/**
 * Schedules automatic screenshot captures
 * @param {number} interval Interval in hours
 */
async function scheduleCaptures(interval = 24) {
    try {
        // Check if we're on Windows
        const isWindows = process.platform === "win32";

        if (isWindows) {
            // Windows Task Scheduler
            const taskName = "RingerikeLandskapScreenshots";
            const scriptPath = path.join(
                projectRoot,
                "scripts",
                "run-capture.bat"
            );

            // Create a batch file to run the capture script
            const batchContent = `@echo off
cd ${projectRoot}
node ${CONFIG.captureScript}
`;
            fs.writeFileSync(scriptPath, batchContent);

            // Create the scheduled task
            const command = `schtasks /create /tn "${taskName}" /tr "${scriptPath}" /sc HOURLY /mo ${interval} /f`;
            await execAsync(command);

            console.log(
                `Scheduled screenshot capture every ${interval} hours using Windows Task Scheduler`
            );
        } else {
            // Unix crontab
            const cronCommand = `0 */${interval} * * * cd ${projectRoot} && node ${CONFIG.captureScript}`;

            // Get current crontab
            const { stdout: currentCrontab } = await execAsync(
                'crontab -l 2>/dev/null || echo ""'
            );

            // Check if the task is already scheduled
            if (currentCrontab.includes(CONFIG.captureScript)) {
                console.log("Screenshot capture is already scheduled");
                return;
            }

            // Add new cron job
            const newCrontab =
                currentCrontab +
                `\n# Ringerike Landskap Screenshot Capture\n${cronCommand}\n`;

            // Write to a temporary file
            const tempFile = path.join(projectRoot, "temp-crontab");
            fs.writeFileSync(tempFile, newCrontab);

            // Install new crontab
            await execAsync(`crontab ${tempFile}`);

            // Remove temporary file
            fs.unlinkSync(tempFile);

            console.log(
                `Scheduled screenshot capture every ${interval} hours using crontab`
            );
        }
    } catch (error) {
        console.error("Error scheduling captures:", error.message);
    }
}

/**
 * Removes scheduled screenshot captures
 */
async function unscheduleCaptures() {
    try {
        // Check if we're on Windows
        const isWindows = process.platform === "win32";

        if (isWindows) {
            // Windows Task Scheduler
            const taskName = "RingerikeLandskapScreenshots";

            try {
                await execAsync(`schtasks /delete /tn "${taskName}" /f`);
                console.log(
                    "Scheduled screenshot capture removed from Windows Task Scheduler"
                );
            } catch (error) {
                console.log("No scheduled task found");
            }
        } else {
            // Unix crontab
            const { stdout: currentCrontab } = await execAsync(
                'crontab -l 2>/dev/null || echo ""'
            );

            // Check if the task is scheduled
            if (!currentCrontab.includes(CONFIG.captureScript)) {
                console.log("No scheduled screenshot capture found");
                return;
            }

            // Remove the cron job
            const newCrontab = currentCrontab
                .split("\n")
                .filter(
                    (line) =>
                        !line.includes(CONFIG.captureScript) &&
                        !line.includes(
                            "# Ringerike Landskap Screenshot Capture"
                        )
                )
                .join("\n");

            // Write to a temporary file
            const tempFile = path.join(projectRoot, "temp-crontab");
            fs.writeFileSync(tempFile, newCrontab);

            // Install new crontab
            await execAsync(`crontab ${tempFile}`);

            // Remove temporary file
            fs.unlinkSync(tempFile);

            console.log("Scheduled screenshot capture removed from crontab");
        }
    } catch (error) {
        console.error("Error unscheduling captures:", error.message);
    }
}

/**
 * Tidies up the screenshots directory
 */
async function tidyScreenshots() {
    try {
        console.log("Tidying up screenshots directory...");
        await execAsync(`node ${CONFIG.tidyScript}`);
        console.log("Screenshots directory tidied up successfully");
    } catch (error) {
        console.error("Error tidying screenshots:", error.message);
    }
}

/**
 * Main function to process command line arguments
 */
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || CONFIG.commands.help;

    switch (command) {
        case CONFIG.commands.capture:
            await captureScreenshots();
            break;

        case CONFIG.commands.clean:
            const days = parseInt(args[1]) || CONFIG.defaultRetention;
            cleanOldScreenshots(days);
            break;

        case CONFIG.commands.schedule:
            const interval = parseInt(args[1]) || 24;
            await scheduleCaptures(interval);
            break;

        case CONFIG.commands.unschedule:
            await unscheduleCaptures();
            break;

        case CONFIG.commands.tidy:
            await tidyScreenshots();
            break;

        case CONFIG.commands.help:
        default:
            showHelp();
            break;
    }
}

main().catch(console.error);
```


#### `scripts\tidy-screenshots.js`

```javascript
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

// Screenshot directory
const screenshotsDir = path.join(projectRoot, "screenshots");

/**
 * Creates a .gitignore file in the screenshots directory
 */
function createGitignore() {
    const gitignorePath = path.join(screenshotsDir, ".gitignore");
    const gitignoreContent = `# Ignore all files in this directory except README.md and .gitignore
*
!README.md
!.gitignore
!.gitkeep

# Allow the latest directory but ignore its contents
!latest/
latest/*
!latest/.gitkeep

# Allow the directory structure but not the actual screenshots
!*/
*/*/
`;

    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log("Created .gitignore file in screenshots directory");
}

/**
 * Creates .gitkeep files to preserve directory structure
 */
function createGitkeepFiles() {
    // Create .gitkeep in the screenshots directory
    fs.writeFileSync(path.join(screenshotsDir, ".gitkeep"), "");

    // Create .gitkeep in the latest directory
    const latestDir = path.join(screenshotsDir, "latest");
    if (fs.existsSync(latestDir)) {
        fs.writeFileSync(path.join(latestDir, ".gitkeep"), "");

        // Create .gitkeep in viewport subdirectories
        ["mobile", "tablet", "desktop"].forEach((viewport) => {
            const viewportDir = path.join(latestDir, viewport);
            if (fs.existsSync(viewportDir)) {
                fs.writeFileSync(path.join(viewportDir, ".gitkeep"), "");
            }
        });
    }

    console.log("Created .gitkeep files to preserve directory structure");
}

/**
 * Removes the legacy backup directory
 */
function removeLegacyBackup() {
    const legacyBackupDir = path.join(screenshotsDir, "legacy-backup");

    if (fs.existsSync(legacyBackupDir)) {
        try {
            fs.rmSync(legacyBackupDir, { recursive: true, force: true });
            console.log("Removed legacy-backup directory");
        } catch (error) {
            console.error(
                "Error removing legacy-backup directory:",
                error.message
            );
        }
    } else {
        console.log("No legacy-backup directory found");
    }
}

/**
 * Removes any loose files in the screenshots directory
 */
function removeLooseFiles() {
    const files = fs.readdirSync(screenshotsDir);

    const looseFiles = files.filter((file) => {
        const filePath = path.join(screenshotsDir, file);
        const isDirectory = fs.statSync(filePath).isDirectory();

        // Keep directories with timestamp format or 'latest'
        if (isDirectory) {
            return !(
                file === "latest" ||
                /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(file)
            );
        }

        // Keep README.md, .gitignore, and .gitkeep
        if (
            [
                "README.md",
                ".gitignore",
                ".gitkeep",
                "screenshot-report.html",
            ].includes(file)
        ) {
            return false;
        }

        // All other files should be removed
        return true;
    });

    if (looseFiles.length === 0) {
        console.log("No loose files found in screenshots directory");
        return;
    }

    console.log(
        `Found ${looseFiles.length} loose files in screenshots directory`
    );

    looseFiles.forEach((file) => {
        const filePath = path.join(screenshotsDir, file);
        try {
            fs.rmSync(filePath, { force: true });
            console.log(`Removed loose file: ${file}`);
        } catch (error) {
            console.error(`Error removing ${file}:`, error.message);
        }
    });
}

/**
 * Optimizes the screenshot report by reducing image sizes
 */
function optimizeScreenshotReport() {
    const reportPath = path.join(screenshotsDir, "screenshot-report.html");

    if (!fs.existsSync(reportPath)) {
        console.log("Screenshot report not found");
        return;
    }

    try {
        let reportContent = fs.readFileSync(reportPath, "utf8");

        // Add lazy loading to images
        reportContent = reportContent.replace(
            /<img src="([^"]+)"/g,
            '<img src="$1" loading="lazy"'
        );

        // Add viewport meta tag for better mobile display if not already present
        if (!reportContent.includes("viewport")) {
            reportContent = reportContent.replace(
                "<head>",
                '<head>\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">'
            );
        }

        // Add a note about the tidied directory
        reportContent = reportContent.replace(
            '<p class="timestamp">Generated',
            '<p class="note">Screenshots directory has been tidied up for better organization.</p>\n        <p class="timestamp">Generated'
        );

        fs.writeFileSync(reportPath, reportContent);
        console.log("Optimized screenshot report");
    } catch (error) {
        console.error("Error optimizing screenshot report:", error.message);
    }
}

/**
 * Main function to tidy up the screenshots directory
 */
function tidyScreenshotsDirectory() {
    console.log("Tidying up screenshots directory...");

    // Create .gitignore file
    createGitignore();

    // Create .gitkeep files
    createGitkeepFiles();

    // Remove legacy backup
    removeLegacyBackup();

    // Remove loose files
    removeLooseFiles();

    // Optimize screenshot report
    optimizeScreenshotReport();

    console.log("Screenshots directory tidied up successfully");
}

// Run the tidy function
tidyScreenshotsDirectory();
```


#### `scripts\visualizations\check-dependencies.js`

```javascript
/**
 * Checks for dependencies required by the dependency visualization system
 * Provides helpful messages about missing dependencies and how to install them
 */
import { exec } from "child_process";
import fs from "fs";
import path from "path";
import { promisify } from "util";

const execAsync = promisify(exec);

// Required npm dependencies
const REQUIRED_PACKAGES = [
    { name: "dependency-cruiser", reason: "Core dependency analysis" },
    { name: "d3", reason: "D3-based visualizations" },
    { name: "rimraf", reason: "Safe directory cleanup" },
];

// Required executables
const REQUIRED_EXECUTABLES = [
    {
        name: "dot",
        command: process.platform === "win32" ? "where dot" : "which dot",
        installInstructions:
            "Install Graphviz from https://graphviz.org/download/ and add it to your PATH",
        reason: "SVG-based visualizations",
        optional: true,
    },
];

// Required files
const REQUIRED_FILES = [
    {
        path: "depcruise-config.cjs",
        reason: "Dependency-cruiser configuration",
    },
    {
        path: "scripts/visualizations/dependency-manager.js",
        reason: "Main visualization manager",
    },
    {
        path: "scripts/visualizations/create-flow-diagram.js",
        reason: "Flow diagram visualization",
    },
    {
        path: "scripts/visualizations/create-d3-graph.js",
        reason: "D3 graph visualization",
    },
];

async function checkNpmDependencies() {
    console.log("\nChecking required npm packages...");

    const packageJson = JSON.parse(fs.readFileSync("package.json", "utf-8"));
    const allDependencies = {
        ...(packageJson.dependencies || {}),
        ...(packageJson.devDependencies || {}),
    };

    const missing = [];

    for (const pkg of REQUIRED_PACKAGES) {
        if (!allDependencies[pkg.name]) {
            missing.push(pkg);
        } else {
            console.log(`✓ ${pkg.name} is installed`);
        }
    }

    if (missing.length > 0) {
        console.log("\n⚠️ Missing npm dependencies:");
        for (const pkg of missing) {
            console.log(`  - ${pkg.name} (${pkg.reason})`);
        }
        console.log(
            "\nRun the following command to install missing dependencies:"
        );
        console.log(
            `npm install --save-dev ${missing.map((p) => p.name).join(" ")}`
        );
    }

    return missing.length === 0;
}

async function checkExecutables() {
    console.log("\nChecking required executables...");

    const missing = [];

    for (const exe of REQUIRED_EXECUTABLES) {
        try {
            await execAsync(exe.command);
            console.log(`✓ ${exe.name} is installed`);
        } catch (error) {
            if (exe.optional) {
                console.log(
                    `⚠️ Optional executable ${exe.name} is not installed (${exe.reason})`
                );
                console.log(`  To install: ${exe.installInstructions}`);
            } else {
                missing.push(exe);
            }
        }
    }

    if (missing.length > 0) {
        console.log("\n❌ Missing required executables:");
        for (const exe of missing) {
            console.log(`  - ${exe.name} (${exe.reason})`);
            console.log(`    To install: ${exe.installInstructions}`);
        }
    }

    return missing.length === 0;
}

function checkRequiredFiles() {
    console.log("\nChecking required files...");

    const missing = [];

    for (const file of REQUIRED_FILES) {
        if (fs.existsSync(file.path)) {
            console.log(`✓ ${file.path} exists`);
        } else {
            missing.push(file);
        }
    }

    if (missing.length > 0) {
        console.log("\n❌ Missing required files:");
        for (const file of missing) {
            console.log(`  - ${file.path} (${file.reason})`);
        }
    }

    return missing.length === 0;
}

async function checkConfig() {
    console.log("\nChecking dependency-cruiser configuration...");

    try {
        if (!fs.existsSync("depcruise-config.cjs")) {
            console.log("❌ depcruise-config.cjs does not exist");
            return false;
        }

        const config = require("../../depcruise-config.cjs");

        // Check if aliases are configured
        if (
            !config.options ||
            !config.options.alias ||
            Object.keys(config.options.alias).length === 0
        ) {
            console.log("⚠️ No aliases configured in depcruise-config.cjs");
            console.log(
                "  This may cause issues with path alias resolution (@/ imports)"
            );
        } else {
            console.log("✓ Aliases configured in depcruise-config.cjs");
        }

        return true;
    } catch (error) {
        console.log("❌ Error parsing depcruise-config.cjs:", error.message);
        return false;
    }
}

async function checkDirectory() {
    console.log("\nChecking visualization directory structure...");

    const BASE_DIR = ".depcruise";
    const SUBDIRS = ["graphs", "interactive", "data"];

    if (!fs.existsSync(BASE_DIR)) {
        console.log(`⚠️ ${BASE_DIR} directory does not exist`);
        console.log(
            '  Run "npm run deps:clean" to create the directory structure'
        );
        return false;
    }

    let allValid = true;

    for (const dir of SUBDIRS) {
        const fullPath = path.join(BASE_DIR, dir);
        if (!fs.existsSync(fullPath)) {
            console.log(`⚠️ ${fullPath} directory does not exist`);
            allValid = false;
        } else {
            console.log(`✓ ${fullPath} directory exists`);
        }
    }

    if (!allValid) {
        console.log(
            '  Run "npm run deps:clean" to recreate the directory structure'
        );
    }

    return allValid;
}

async function main() {
    console.log("Checking dependency visualization system prerequisites...");

    // Run all checks
    const npmOk = await checkNpmDependencies();
    const exeOk = await checkExecutables();
    const filesOk = checkRequiredFiles();
    const configOk = await checkConfig();
    const dirOk = await checkDirectory();

    // Report results
    console.log("\n=== Summary ===");
    console.log(`npm packages: ${npmOk ? "✓ OK" : "⚠️ Issues found"}`);
    console.log(
        `Executables: ${
            exeOk ? "✓ OK" : "⚠️ Issues found (some may be optional)"
        }`
    );
    console.log(`Required files: ${filesOk ? "✓ OK" : "❌ Missing files"}`);
    console.log(
        `Configuration: ${configOk ? "✓ OK" : "❌ Configuration issues"}`
    );
    console.log(`Directory structure: ${dirOk ? "✓ OK" : "⚠️ Issues found"}`);

    // Provide next steps
    if (npmOk && exeOk && filesOk && configOk && dirOk) {
        console.log(
            "\n✅ All checks passed! The dependency visualization system is ready to use."
        );
        console.log('Run "npm run deps" to see available commands.');
    } else {
        console.log(
            "\n⚠️ Some issues were found. Please fix them before using the dependency visualization system."
        );
        console.log(
            'After fixing the issues, run "npm run deps:check" to verify the fixes.'
        );
    }
}

main().catch((error) => {
    console.error("Error checking dependencies:", error);
    process.exit(1);
});
```


#### `scripts\visualizations\check-graphviz.js`

```javascript
import { exec } from "child_process";
import fs from "fs";
import path from "path";

// Directory for messages
const OUTPUT_DIR = ".depcruise/graphs";

// Create the directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Checks if Graphviz is installed by attempting to run the dot command
 */
export function checkGraphviz() {
    return new Promise((resolve) => {
        // Use 'where dot' for Windows and 'which dot' for Unix-like systems
        const command =
            process.platform === "win32" ? "where dot" : "which dot";

        exec(command, (error) => {
            if (error) {
                console.warn(
                    "\x1b[33m%s\x1b[0m",
                    "Graphviz (dot) not found in PATH. Some dependency visualizations will be skipped."
                );
                console.warn(
                    "\x1b[33m%s\x1b[0m",
                    "Install Graphviz from https://graphviz.org/download/ and add it to your PATH to enable all visualizations."
                );

                // Create a placeholder SVG file with an informative message
                const placeholderSvg = `
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600">
    <rect width="100%" height="100%" fill="#f8f9fa"/>
    <text x="50%" y="45%" text-anchor="middle" font-family="sans-serif" font-size="24" fill="#dc3545">Graphviz not installed</text>
    <text x="50%" y="52%" text-anchor="middle" font-family="sans-serif" font-size="18" fill="#495057">Install Graphviz from graphviz.org and add it to your PATH</text>
    <text x="50%" y="58%" text-anchor="middle" font-family="sans-serif" font-size="16" fill="#6c757d">Then run 'npm run depcruise:all' again</text>
</svg>`;

                // Write placeholder SVG files
                const files = [
                    "dependency-graph.svg",
                    "hierarchical-graph.svg",
                    "circular-graph.svg",
                    "clustered-graph.svg",
                    "tech-filtered.svg",
                    "enhanced-graph.svg",
                ];

                files.forEach((file) => {
                    const filePath = path.join(OUTPUT_DIR, file);
                    fs.writeFileSync(filePath, placeholderSvg);
                    console.log(`Created placeholder for ${file}`);
                });

                resolve(false);
            } else {
                console.log(
                    "Graphviz (dot) found in PATH. All visualizations are available."
                );
                resolve(true);
            }
        });
    });
}

// If this script is run directly
if (process.argv[1] === import.meta.url) {
    checkGraphviz().then(() => {
        console.log("Graphviz check complete.");
    });
}
```


#### `scripts\visualizations\cleanup-directory.js`

```javascript
/**
 * Script to safely clean up and recreate the .depcruise directory structure
 * Used to ensure a clean state before generating new visualizations
 */
import fs from "fs";
import path from "path";
import { promisify } from "util";
import { exec } from "child_process";

const execAsync = promisify(exec);

const BASE_DIR = ".depcruise";
const SUBDIRS = ["graphs", "interactive", "data"];

async function cleanupDirectories() {
    console.log("Cleaning up .depcruise directory structure...");

    try {
        // Check if directory exists
        if (fs.existsSync(BASE_DIR)) {
            console.log(`Removing existing ${BASE_DIR} directory...`);

            // On Windows, use rimraf which handles file locks better than fs.rmSync
            if (process.platform === "win32") {
                try {
                    await execAsync(`npx rimraf ${BASE_DIR}`);
                    console.log("Directory removed successfully using rimraf");
                } catch (error) {
                    console.warn(
                        `Warning: Could not remove directory using rimraf: ${error.message}`
                    );
                    console.log("Trying alternative cleanup method...");

                    // Force close any processes that might be using files in this directory
                    // This is Windows-specific
                    try {
                        await execAsync(
                            `powershell -Command "Get-Process | Where-Object {$_.Path -like '*${BASE_DIR}*'} | Stop-Process -Force"`
                        );
                    } catch (err) {
                        // Ignore errors from this command, as it might not find any processes
                    }

                    // Try using PowerShell which might handle some Windows-specific issues better
                    try {
                        await execAsync(
                            `powershell -Command "Remove-Item -Path ${BASE_DIR} -Recurse -Force -ErrorAction SilentlyContinue"`
                        );
                        console.log(
                            "Directory removed successfully using PowerShell"
                        );
                    } catch (powerShellError) {
                        console.error(
                            `Error: Could not remove directory using PowerShell: ${powerShellError.message}`
                        );
                        console.log(
                            "Will try to work with existing directory structure..."
                        );
                    }
                }
            } else {
                // For non-Windows platforms, use standard fs.rmSync
                try {
                    fs.rmSync(BASE_DIR, { recursive: true, force: true });
                    console.log(
                        "Directory removed successfully using fs.rmSync"
                    );
                } catch (fsError) {
                    console.error(
                        `Error: Could not remove directory using fs.rmSync: ${fsError.message}`
                    );
                    console.log(
                        "Will try to work with existing directory structure..."
                    );
                }
            }
        }

        // Create base directory
        console.log(`Creating ${BASE_DIR} directory...`);
        if (!fs.existsSync(BASE_DIR)) {
            fs.mkdirSync(BASE_DIR);
        }

        // Create subdirectories
        for (const dir of SUBDIRS) {
            const fullPath = path.join(BASE_DIR, dir);
            console.log(`Creating ${fullPath} directory...`);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath);
            }
        }

        console.log("Directory structure setup completed successfully!");
    } catch (error) {
        console.error("Error during directory cleanup:", error.message);
        console.error(
            "Will attempt to continue with existing directory structure..."
        );
    }
}

// Run the cleanup process
cleanupDirectories().catch((error) => {
    console.error("Unhandled error during cleanup:", error);
    process.exit(1);
});
```


#### `scripts\visualizations\cleanup-redundant-files.js`

```javascript
/**
 * Script to clean up redundant files from the dependency visualization system
 * Removes unnecessary files created by the legacy system and the development process
 */
import fs from 'fs';
import path from 'path';

// List of redundant files and their purpose
const REDUNDANT_FILES = [
  // Legacy path fixing scripts replaced by unified dependency manager
  { path: "scripts/visualizations/fix-alias-imports.js", reason: "Functionality integrated into dependency-manager.js" },
  { path: "scripts/visualizations/fix-depcruise-paths.js", reason: "Functionality integrated into dependency-manager.js" },

  // Debug files no longer needed
  { path: ".depcruise/data/d3-data-fixed.json", reason: "Debug file not needed in production" },
  { path: ".depcruise/interactive/flow-diagram-debug.txt", reason: "Debug file not needed in production" },

  // Old visualization JSON files (data will be regenerated)
  { path: ".depcruise/data/fixed-data.json", reason: "Intermediate file no longer used" },
  { path: ".depcruise/data/dependency-debug.log", reason: "Debug file no longer needed" },

  // Obsolete scripts
  { path: "scripts/visualizations/check-usedata.js", reason: "Functionality generalized in dependency-manager.js" },

  // Backup files that might have been created during development
  { path: "scripts/visualizations/run-visualizations.js.bak", reason: "Backup file not needed" },
  { path: "scripts/visualizations/dependency-manager.js.bak", reason: "Backup file not needed" },
  { path: "depcruise-config.cjs.bak", reason: "Backup file not needed" },
];

// Start cleanup process
async function cleanupRedundantFiles() {
  console.log("Cleaning up redundant files...");

  const results = {
    removed: [],
    skipped: [],
    errors: []
  };

  // Process each file in the list
  for (const file of REDUNDANT_FILES) {
    try {
      if (fs.existsSync(file.path)) {
        console.log(`Removing: ${file.path} (${file.reason})`);
        fs.unlinkSync(file.path);
        results.removed.push(file.path);
      } else {
        console.log(`Skipping: ${file.path} (file not found)`);
        results.skipped.push(file.path);
      }
    } catch (error) {
      console.error(`Error removing ${file.path}: ${error.message}`);
      results.errors.push({ path: file.path, error: error.message });
    }
  }

  // Print summary
  console.log("\nCleanup Summary:");
  console.log(`- ${results.removed.length} files removed`);
  console.log(`- ${results.skipped.length} files not found (skipped)`);
  console.log(`- ${results.errors.length} errors encountered`);

  if (results.removed.length > 0) {
    console.log("\nRemoved files:");
    results.removed.forEach(file => console.log(`- ${file}`));
  }

  if (results.errors.length > 0) {
    console.log("\nErrors:");
    results.errors.forEach(err => console.log(`- ${err.path}: ${err.error}`));
  }

  // Save a log file with results
  try {
    const logContent = {
      timestamp: new Date().toISOString(),
      results
    };

    fs.writeFileSync('.depcruise/cleanup-log.json', JSON.stringify(logContent, null, 2));
    console.log('\nCleanup log saved to .depcruise/cleanup-log.json');
  } catch (error) {
    console.error('Error saving cleanup log:', error.message);
  }

  return results;
}

cleanupRedundantFiles()
  .then(() => {
    console.log("Cleanup process completed");

    // Self-remove option - uncomment if needed
    // If this script should remove itself after running
    /*
    try {
      const selfPath = process.argv[1];
      console.log(`Self-removing: ${selfPath}`);
      fs.unlinkSync(selfPath);
      console.log("Script self-removed successfully");
    } catch (error) {
      console.error(`Error self-removing: ${error.message}`);
    }
    */
  })
  .catch(error => {
    console.error("Unhandled error during cleanup:", error);
    process.exit(1);
  });
```


#### `scripts\visualizations\create-bubble-chart.js`

```javascript
import fs from "fs";
import path from "path";

/**
 * This script creates a bubble chart visualization of dependencies.
 * It uses D3.js to create an interactive bubble chart where each bubble
 * represents a file, and the size correlates with dependency importance.
 */

// Read dependency data from the JSON file
const DATA_FILE = ".depcruise/data/d3-data.json";
const OUTPUT_DIR = ".depcruise/interactive";
const OUTPUT_FILE = path.join(OUTPUT_DIR, "bubble-chart.html");

try {
    // Ensure the output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // Read dependency data
    const data = JSON.parse(fs.readFileSync(DATA_FILE, "utf8"));
    console.log(`Successfully read dependency data from ${DATA_FILE}`);
    console.log(
        `Data contains ${data.modules ? data.modules.length : 0} modules`
    );

    // Transform data for bubble chart
    const bubbleData = transformDataForBubbleChart(data);
    console.log(
        `Transformed data for bubble chart with ${bubbleData.length} items`
    );

    // Generate the HTML
    const html = generateHtml(bubbleData);

    // Write the HTML to a file
    fs.writeFileSync(OUTPUT_FILE, html);
    console.log(`Bubble chart visualization created at: ${OUTPUT_FILE}`);
} catch (error) {
    console.error("Error:", error.message);
    console.error(error.stack);
}

/**
 * Transforms dependency-cruiser data for bubble chart visualization
 */
function transformDataForBubbleChart(data) {
    const modules = data.modules || [];
    console.log(`Processing ${modules.length} modules for bubble chart`);

    // Extract all dependencies from modules
    const dependencies = [];
    modules.forEach((module) => {
        if (module.dependencies && Array.isArray(module.dependencies)) {
            module.dependencies.forEach((dep) => {
                dependencies.push({
                    from: module.source,
                    to: dep.resolved,
                });
            });
        }
    });

    console.log(`Extracted ${dependencies.length} dependencies from modules`);

    // Create a map to count incoming dependencies for each module
    const incomingDepsMap = new Map();
    dependencies.forEach((dep) => {
        const to = dep.to;
        incomingDepsMap.set(to, (incomingDepsMap.get(to) || 0) + 1);
    });

    // Create a map to count outgoing dependencies for each module
    const outgoingDepsMap = new Map();
    modules.forEach((module) => {
        const from = module.source;
        const outDeps = dependencies.filter((dep) => dep.from === from).length;
        outgoingDepsMap.set(from, outDeps);
    });

    // Transform modules to bubble data
    const bubbleData = modules.map((module) => {
        const source = module.source;
        const fileName = path.basename(source);
        const incomingDeps = incomingDepsMap.get(source) || 0;
        const outgoingDeps = outgoingDepsMap.get(source) || 0;

        // Determine category based on directory structure
        let category = "Other";
        if (source.includes("/components/")) {
            category = "Component";
        } else if (source.includes("/hooks/")) {
            category = "Hook";
        } else if (source.includes("/pages/")) {
            category = "Page";
        } else if (source.includes("/utils/")) {
            category = "Utility";
        } else if (source.includes("/services/")) {
            category = "Service";
        } else if (source.includes("/types/")) {
            category = "Type";
        } else if (source.includes("/contexts/")) {
            category = "Context";
        }

        return {
            name: fileName,
            fullPath: source,
            category,
            incomingDeps,
            outgoingDeps,
            value: incomingDeps + outgoingDeps + 1, // Add 1 to ensure some size for modules with no deps
        };
    });

    console.log(`Created ${bubbleData.length} bubble data items`);

    return bubbleData;
}

/**
 * Generates HTML with D3.js for bubble chart visualization
 */
function generateHtml(bubbleData) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependency Bubble Chart</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        #chart {
            width: 100%;
            height: 700px;
            border: 1px solid #eee;
            border-radius: 4px;
            position: relative;
            background-color: #fafafa;
        }
        .tooltip {
            position: absolute;
            padding: 10px;
            background: #333;
            color: white;
            border-radius: 4px;
            pointer-events: none;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .controls {
            margin: 15px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        .controls label {
            margin-right: 5px;
            font-weight: bold;
        }
        .controls select, .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .search-box {
            flex-grow: 1;
            max-width: 300px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        button:hover {
            background-color: #2980b9;
        }
        .back-button {
            margin-bottom: 20px;
            background-color: #34495e;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .legend-color {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            border-radius: 3px;
        }
        .module-count {
            color: #666;
            font-size: 14px;
            margin: 10px 0;
        }
        .bubble {
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .bubble:hover {
            opacity: 0.8;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        #status.error {
            display: block;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        #status.info {
            display: block;
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="back-button" onclick="window.location.href='../index.html'">‚Üê Back to Dashboard</button>
        <h1>Dependency Bubble Chart</h1>
        <div id="status"></div>
        <div class="module-count" id="module-count"></div>

        <div class="controls">
            <div>
                <label for="colorBy">Color by:</label>
                <select id="colorBy">
                    <option value="category">Category</option>
                    <option value="incomingDeps">Incoming Dependencies</option>
                    <option value="outgoingDeps">Outgoing Dependencies</option>
                    <option value="total">Total Dependencies</option>
                </select>
            </div>
            <div>
                <label for="categoryFilter">Filter:</label>
                <select id="categoryFilter">
                    <option value="all">All Categories</option>
                </select>
            </div>
            <div class="search-box">
                <label for="search">Search:</label>
                <input type="text" id="search" placeholder="Filter by name or path...">
            </div>
            <button id="resetFilters">Reset Filters</button>
        </div>

        <div class="legend" id="legend"></div>
        <div id="chart"></div>
    </div>

    <script>
    (function() {
        // The data for our visualization
        const data = ${JSON.stringify(bubbleData)};
        console.log("Data loaded:", data.length, "nodes");

        // Show status message if no data
        const status = document.getElementById('status');
        if (data.length === 0) {
            status.textContent = 'No dependency data available.';
            status.className = 'error';
            status.style.display = 'block';
            return;
        }

        // Track unique categories
        const categories = [...new Set(data.map(d => d.category))].sort();

        // Populate category filter
        const categoryFilter = document.getElementById('categoryFilter');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });

        // Update module count display
        const moduleCount = document.getElementById('module-count');
        moduleCount.textContent = "Showing all " + data.length + " modules";

        // Define colors based on category
        const categoryColors = {
            Component: "#4285F4", // Blue
            Hook: "#EA4335",      // Red
            Page: "#34A853",      // Green
            Utility: "#FBBC05",   // Yellow
            Service: "#8E44AD",   // Purple
            Type: "#3498db",      // Light Blue
            Context: "#e67e22",   // Orange
            Other: "#7F8C8D"      // Gray
        };

        // Create color scales for numerical values
        const blueScale = d3.scaleSequential()
            .domain([0, d3.max(data, d => d.incomingDeps) || 1])
            .interpolator(d3.interpolateBlues);

        const greenScale = d3.scaleSequential()
            .domain([0, d3.max(data, d => d.outgoingDeps) || 1])
            .interpolator(d3.interpolateGreens);

        const purpleScale = d3.scaleSequential()
            .domain([0, d3.max(data, d => d.value) || 1])
            .interpolator(d3.interpolatePurples);

        // Create tooltip
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip');

        // Function to get color based on selected property
        function getColor(d, colorBy) {
            switch (colorBy) {
                case 'category': return categoryColors[d.category] || categoryColors.Other;
                case 'incomingDeps': return blueScale(d.incomingDeps);
                case 'outgoingDeps': return greenScale(d.outgoingDeps);
                case 'total': return purpleScale(d.value);
                default: return categoryColors[d.category] || '#ccc';
            }
        }

        // Create and update the bubble chart
        function updateChart() {
            try {
                // Clear any previous status
                status.style.display = 'none';

                // Get current filter values
                const colorBy = document.getElementById('colorBy').value;
                const categoryVal = document.getElementById('categoryFilter').value;
                const searchText = document.getElementById('search').value.toLowerCase();

                // Filter data based on search and category
                const filteredData = data.filter(d => {
                    const matchesSearch = searchText === '' ||
                                         d.name.toLowerCase().includes(searchText) ||
                                         d.fullPath.toLowerCase().includes(searchText);
                    const matchesCategory = categoryVal === 'all' || d.category === categoryVal;
                    return matchesSearch && matchesCategory;
                });

                // Update count display
                moduleCount.textContent =
                    filteredData.length === data.length
                    ? "Showing all " + data.length + " modules"
                    : "Showing " + filteredData.length + " of " + data.length + " modules";

                // Display message if no data matches filters
                if (filteredData.length === 0) {
                    status.textContent = 'No modules match the current filters.';
                    status.className = 'info';
                    status.style.display = 'block';
                }

                // Get chart dimensions
                const chartElement = document.getElementById('chart');
                const width = chartElement.clientWidth;
                const height = chartElement.clientHeight;

                // Clear previous chart
                chartElement.innerHTML = '';

                // Create the SVG container
                const svg = d3.select('#chart')
                    .append('svg')
                    .attr('width', width)
                    .attr('height', height)
                    .attr('viewBox', [0, 0, width, height]);

                // Pack layout function
                const packLayout = d3.pack()
                    .size([width, height])
                    .padding(3);

                // Create hierarchy from the data
                const hierarchyData = d3.hierarchy({ children: filteredData })
                    .sum(d => d.value);

                // Apply pack layout
                const root = packLayout(hierarchyData);

                // Create bubbles
                const bubbles = svg.selectAll('.bubble')
                    .data(root.leaves())
                    .enter()
                    .append('circle')
                    .attr('class', 'bubble')
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y)
                    .attr('r', d => d.r)
                    .attr('fill', d => getColor(d.data, colorBy))
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 1)
                    .on('mouseover', function(event, d) {
                        const node = d.data;
                        tooltip.html(
                            "<strong>" + node.name + "</strong><br>" +
                            "<span style=\"opacity: 0.8\">" + node.fullPath + "</span><br>" +
                            "<span>Category: " + node.category + "</span><br>" +
                            "<span>Incoming Dependencies: " + node.incomingDeps + "</span><br>" +
                            "<span>Outgoing Dependencies: " + node.outgoingDeps + "</span><br>" +
                            "<span>Total: " + node.value + "</span>"
                        )
                        .style('left', (event.pageX + 10) + 'px')
                        .style('top', (event.pageY - 10) + 'px')
                        .style('opacity', 1);
                    })
                    .on('mouseout', function() {
                        tooltip.style('opacity', 0);
                    })
                    .on('click', function(event, d) {
                        showFileDetails(d.data);
                    });

                // Add labels to larger bubbles
                svg.selectAll('.label')
                    .data(root.leaves().filter(d => d.r > 15))
                    .enter()
                    .append('text')
                    .attr('class', 'label')
                    .attr('x', d => d.x)
                    .attr('y', d => d.y)
                    .attr('text-anchor', 'middle')
                    .attr('alignment-baseline', 'middle')
                    .style('font-size', d => Math.min(d.r / 3, 12) + 'px')
                    .style('pointer-events', 'none')
                    .style('fill', '#333')
                    .style('font-weight', '500')
                    .style('text-shadow', '0 1px 0 rgba(255,255,255,0.7)')
                    .text(d => {
                        const name = d.data.name;
                        const maxLength = Math.floor(d.r / 5);
                        return name.length > maxLength ? name.substring(0, maxLength) + '...' : name;
                    });

                // Update legend
                updateLegend(colorBy);

            } catch (error) {
                console.error("Error rendering chart:", error);
                status.textContent = 'Error rendering chart: ' + error.message;
                status.className = 'error';
                status.style.display = 'block';
            }
        }

        // Update the legend based on the current color scheme
        function updateLegend(colorBy) {
            const legendContainer = document.getElementById('legend');
            legendContainer.innerHTML = '';

            if (colorBy === 'category') {
                // Show category legend
                categories.forEach(category => {
                    const item = document.createElement('div');
                    item.className = 'legend-item';

                    const colorBox = document.createElement('div');
                    colorBox.className = 'legend-color';
                    colorBox.style.backgroundColor = categoryColors[category] || categoryColors.Other;

                    const label = document.createElement('span');
                    label.textContent = category;

                    item.appendChild(colorBox);
                    item.appendChild(label);
                    legendContainer.appendChild(item);
                });
            } else {
                // Create a gradient legend
                const width = 200;
                const height = 20;

                // Add a title based on selected color
                const title = document.createElement('div');
                title.style.width = '100%';
                title.style.marginBottom = '10px';
                title.style.fontWeight = 'bold';

                switch (colorBy) {
                    case 'incomingDeps':
                        title.textContent = 'Incoming Dependencies (blue)';
                        break;
                    case 'outgoingDeps':
                        title.textContent = 'Outgoing Dependencies (green)';
                        break;
                    case 'total':
                        title.textContent = 'Total Dependencies (purple)';
                        break;
                }

                legendContainer.appendChild(title);

                // Create SVG for gradient
                const svg = d3.select(legendContainer)
                    .append('svg')
                    .attr('width', width + 50)
                    .attr('height', height + 30);

                // Create gradient definition
                const defs = svg.append('defs');
                const gradient = defs.append('linearGradient')
                    .attr('id', 'legend-gradient')
                    .attr('x1', '0%')
                    .attr('y1', '0%')
                    .attr('x2', '100%')
                    .attr('y2', '0%');

                // Get appropriate color scale
                let colorScale;
                switch (colorBy) {
                    case 'incomingDeps': colorScale = blueScale; break;
                    case 'outgoingDeps': colorScale = greenScale; break;
                    case 'total': colorScale = purpleScale; break;
                }

                // Add color stops
                const steps = 10;
                for (let i = 0; i <= steps; i++) {
                    const offset = i / steps;
                    const value = offset * colorScale.domain()[1];
                    gradient.append('stop')
                        .attr('offset', (offset * 100) + '%')
                        .attr('stop-color', colorScale(value));
                }

                // Add gradient rectangle
                svg.append('rect')
                    .attr('x', 0)
                    .attr('y', 10)
                    .attr('width', width)
                    .attr('height', height)
                    .style('fill', 'url(#legend-gradient)');

                // Add min/max labels
                svg.append('text')
                    .attr('x', 0)
                    .attr('y', height + 25)
                    .text('0')
                    .style('font-size', '12px');

                svg.append('text')
                    .attr('x', width - 30)
                    .attr('y', height + 25)
                    .text('Max')
                    .style('font-size', '12px');
            }
        }

        // Function to show file details in a modal
        function showFileDetails(fileData) {
            // Create modal container
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.zIndex = '1000';
            modal.style.left = '50%';
            modal.style.top = '50%';
            modal.style.transform = 'translate(-50%, -50%)';
            modal.style.backgroundColor = 'white';
            modal.style.borderRadius = '8px';
            modal.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
            modal.style.width = '90%';
            modal.style.maxWidth = '600px';
            modal.style.padding = '20px';

            // Create modal content
            modal.innerHTML =
                "<h2 style=\"margin-top:0;color:#2c3e50;border-bottom:1px solid #eee;padding-bottom:10px\">" + fileData.name + "</h2>" +
                "<p><strong>Full Path:</strong> " + fileData.fullPath + "</p>" +
                "<p><strong>Category:</strong> " + fileData.category + "</p>" +
                "<p><strong>Incoming Dependencies:</strong> " + fileData.incomingDeps + "</p>" +
                "<p><strong>Outgoing Dependencies:</strong> " + fileData.outgoingDeps + "</p>" +
                "<p><strong>Total Dependencies:</strong> " + (fileData.incomingDeps + fileData.outgoingDeps) + "</p>" +
                "<div style=\"text-align:right;margin-top:20px\">" +
                "<button id=\"closeModal\" style=\"background:#3498db;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer\">Close</button>" +
                "</div>";

            // Create overlay
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.zIndex = '999';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';

            // Add to document
            document.body.appendChild(overlay);
            document.body.appendChild(modal);

            // Setup close handlers
            document.getElementById('closeModal').addEventListener('click', closeModal);
            overlay.addEventListener('click', closeModal);

            function closeModal() {
                document.body.removeChild(modal);
                document.body.removeChild(overlay);
            }
        }

        // Initialize chart
        updateChart();

        // Add event listeners to UI controls
        document.getElementById('colorBy').addEventListener('change', updateChart);
        document.getElementById('categoryFilter').addEventListener('change', updateChart);
        document.getElementById('search').addEventListener('input', updateChart);

        document.getElementById('resetFilters').addEventListener('click', function() {
            document.getElementById('colorBy').value = 'category';
            document.getElementById('categoryFilter').value = 'all';
            document.getElementById('search').value = '';
            updateChart();
        });

        // Handle window resize
        window.addEventListener('resize', debounce(updateChart, 250));

        // Debounce function to limit how often a function is called
        function debounce(fn, delay) {
            let timeout;
            return function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => fn.apply(this, arguments), delay);
            };
        }
    })();
    </script>
</body>
</html>`;
}
```


#### `scripts\visualizations\create-circle-packing.js`

```javascript
import fs from "fs";
import path from "path";
import * as d3 from "d3";

// Configuration
const DEPCRUISE_DIR = ".depcruise";
const DATA_FILE = path.join(DEPCRUISE_DIR, "data/d3-data.json");
const OUTPUT_FILE = path.join(DEPCRUISE_DIR, "interactive/circle-packing.html");

// Read the dependency data
let dependencyData;
try {
    const data = fs.readFileSync(DATA_FILE, "utf8");
    dependencyData = JSON.parse(data);
    console.log(`Successfully read dependency data from ${DATA_FILE}`);
} catch (err) {
    console.error(`Error reading dependency data: ${err.message}`);
    process.exit(1);
}

// Transform dependency data into hierarchical format for circle packing
function transformDataForCirclePacking(data) {
    // Create a hierarchical structure
    const root = {
        name: "root",
        children: [],
    };

    // Create a map to track categories and their children
    const categoryMap = new Map();

    // First pass: collect all nodes and organize by category
    data.modules.forEach((module) => {
        const source = module.source;

        // Determine category based on path
        let category = "other";
        if (source.includes("/components/")) {
            category = "components";
        } else if (source.includes("/hooks/")) {
            category = "hooks";
        } else if (source.includes("/utils/")) {
            category = "utils";
        } else if (source.includes("/pages/")) {
            category = "pages";
        } else if (source.includes("/features/")) {
            category = "features";
        } else if (source.includes("/types/")) {
            category = "types";
        } else if (source.includes("/lib/")) {
            category = "lib";
        } else if (source.includes("/assets/")) {
            category = "assets";
        } else {
            // Try to get the top-level directory
            const parts = source.split("/");
            if (parts.length > 1) {
                category = parts[0];
            }
        }

        // Get or create the category
        if (!categoryMap.has(category)) {
            const categoryNode = {
                name: category,
                children: [],
            };
            categoryMap.set(category, categoryNode);
            root.children.push(categoryNode);
        }

        // Module dependencies determine value size
        const value = module.dependencies ? module.dependencies.length + 1 : 1;

        // Get subcategory (second-level directory if available)
        let subcategory = "";
        const parts = source.split("/");
        if (parts.length > 2) {
            subcategory = parts.slice(0, 2).join("/");
        } else {
            subcategory = category;
        }

        // Add to subcategory
        const subcategoryNode = categoryMap
            .get(category)
            .children.find((node) => node.name === subcategory);
        if (subcategoryNode) {
            // Add to existing subcategory
            subcategoryNode.children.push({
                name: path.basename(source, path.extname(source)),
                fullPath: source,
                value: value,
            });
        } else {
            // Create new subcategory
            categoryMap.get(category).children.push({
                name: subcategory === category ? "main" : subcategory,
                children: [
                    {
                        name: path.basename(source, path.extname(source)),
                        fullPath: source,
                        value: value,
                    },
                ],
            });
        }
    });

    // Clean up empty categories
    root.children = root.children.filter(
        (category) => category.children.length > 0
    );

    return root;
}

const hierarchicalData = transformDataForCirclePacking(dependencyData);
console.log("Transformed data into hierarchical format for circle packing");

// Generate HTML with circle packing visualization
function generateHTML(data) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dependency Circle Packing - Ringerike Landskap</title>
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background: #f7f9fb;
      overflow: hidden;
      color: #333;
    }

    #container {
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    #header {
      padding: 15px;
      background: white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    h1 {
      font-size: 18px;
      font-weight: 500;
      color: #2c3e50;
      margin: 0;
    }

    .controls {
      display: flex;
      gap: 10px;
    }

    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
    }

    button:hover {
      background-color: #2980b9;
    }

    #visualization {
      flex: 1;
      overflow: hidden;
    }

    .tooltip {
      position: absolute;
      background: #fff;
      padding: 10px;
      border-radius: 3px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      pointer-events: none;
      opacity: 0;
      z-index: 100;
      max-width: 300px;
      transition: opacity 0.2s;
    }

    .tooltip h4 {
      margin: 0 0 5px;
      color: #2c3e50;
    }

    .tooltip p {
      margin: 0 0 3px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div id="container">
    <div id="header">
      <h1>Dependency Circle Packing</h1>
      <div class="controls">
        <button id="reset">Reset View</button>
        <button id="back-to-dashboard">Back to Dashboard</button>
      </div>
    </div>
    <div id="visualization"></div>
  </div>

  <div class="tooltip" id="tooltip"></div>

  <script>
    // Hierarchical data provided by Node.js
    const data = ${JSON.stringify(data)};

    // Create a tooltip element
    const tooltip = d3.select("#tooltip");

    // Specify the chart's dimensions
    const width = window.innerWidth;
    const height = window.innerHeight - document.getElementById("header").offsetHeight;

    // Create the color scale
    const color = d3.scaleLinear()
      .domain([0, 5])
      .range(["hsl(152,80%,80%)", "hsl(228,30%,40%)"])
      .interpolate(d3.interpolateHcl);

    // Compute the layout
    const pack = data => d3.pack()
      .size([width, height])
      .padding(3)
      (d3.hierarchy(data)
        .sum(d => d.value || 1)
        .sort((a, b) => (b.value || 0) - (a.value || 0)));

    const root = pack(data);

    // Create the SVG container
    const svg = d3.select("#visualization")
      .append("svg")
      .attr("viewBox", \`-\${width / 2} -\${height / 2} \${width} \${height}\`)
      .attr("width", width)
      .attr("height", height)
      .attr("style", \`max-width: 100%; height: 100%; display: block; background: \${color(0)}; cursor: pointer;\`);

    // Append the nodes
    const node = svg.append("g")
      .selectAll("circle")
      .data(root.descendants().slice(1))
      .join("circle")
        .attr("fill", d => d.children ? color(d.depth) : "white")
        .attr("pointer-events", d => !d.children ? "none" : null)
        .on("mouseover", function(event, d) {
          d3.select(this).attr("stroke", "#000").attr("stroke-width", "1.5px");

          // Show tooltip
          tooltip.style("opacity", 1)
            .html(() => {
              if (d.data.fullPath) {
                return \`
                  <h4>\${d.data.name}</h4>
                  <p><strong>Path:</strong> \${d.data.fullPath}</p>
                  <p><strong>Dependencies:</strong> \${d.data.value - 1}</p>
                \`;
              } else {
                return \`
                  <h4>\${d.data.name}</h4>
                  <p><strong>Files:</strong> \${d.leaves().length}</p>
                \`;
              }
            })
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        })
        .on("mouseout", function() {
          d3.select(this).attr("stroke", null);
          tooltip.style("opacity", 0);
        })
        .on("click", (event, d) => {
          if (d.data.fullPath && !d.children) {
            // Show file details in browser instead of opening in VS Code
            showFileDetails(d.data);
            event.stopPropagation();
          } else if (focus !== d) {
            zoom(event, d);
            event.stopPropagation();
          }
        });

    // Append the text labels
    const label = svg.append("g")
      .style("font", "10px sans-serif")
      .attr("pointer-events", "none")
      .attr("text-anchor", "middle")
      .selectAll("text")
      .data(root.descendants())
      .join("text")
        .style("fill-opacity", d => d.parent === root ? 1 : 0)
        .style("display", d => d.parent === root ? "inline" : "none")
        .style("font-weight", d => d.depth === 1 ? "bold" : "normal")
        .text(d => d.data.name);

    // Create the zoom behavior and zoom immediately in to the initial focus node
    svg.on("click", (event) => zoom(event, root));
    let focus = root;
    let view;
    zoomTo([focus.x, focus.y, focus.r * 2]);

    function zoomTo(v) {
      const k = width / v[2];

      view = v;

      label.attr("transform", d => \`translate(\${(d.x - v[0]) * k},\${(d.y - v[1]) * k})\`);
      node.attr("transform", d => \`translate(\${(d.x - v[0]) * k},\${(d.y - v[1]) * k})\`);
      node.attr("r", d => d.r * k);
    }

    function zoom(event, d) {
      const focus0 = focus;

      focus = d;

      const transition = svg.transition()
        .duration(event.altKey ? 7500 : 750)
        .tween("zoom", d => {
          const i = d3.interpolateZoom(view, [focus.x, focus.y, focus.r * 2]);
          return t => zoomTo(i(t));
        });

      label
        .filter(function(d) { return d.parent === focus || this.style.display === "inline"; })
        .transition(transition)
        .style("fill-opacity", d => d.parent === focus ? 1 : 0)
        .on("start", function(d) { if (d.parent === focus) this.style.display = "inline"; })
        .on("end", function(d) { if (d.parent !== focus) this.style.display = "none"; });
    }

    // Reset zoom button
    document.getElementById("reset").addEventListener("click", function() {
      zoom({}, root);
    });

    // Back to dashboard button
    document.getElementById("back-to-dashboard").addEventListener("click", function() {
      window.location.href = "../index.html";
    });

    // Resize handler
    window.addEventListener("resize", function() {
      const newWidth = window.innerWidth;
      const newHeight = window.innerHeight - document.getElementById("header").offsetHeight;

      svg.attr("viewBox", \`-\${newWidth / 2} -\${newHeight / 2} \${newWidth} \${newHeight}\`)
         .attr("width", newWidth)
         .attr("height", newHeight);

      // Re-zoom to maintain the current view
      zoomTo([focus.x, focus.y, focus.r * 2]);
    });

    // Function to show file details
    function showFileDetails(fileData) {
      // Create modal dialog
      const modal = document.createElement("div");
      modal.style.position = "fixed";
      modal.style.top = "50%";
      modal.style.left = "50%";
      modal.style.transform = "translate(-50%, -50%)";
      modal.style.backgroundColor = "white";
      modal.style.padding = "20px";
      modal.style.borderRadius = "8px";
      modal.style.boxShadow = "0 4px 12px rgba(0,0,0,0.2)";
      modal.style.zIndex = "1000";
      modal.style.maxWidth = "600px";
      modal.style.width = "90%";

      modal.innerHTML = \`
        <h2 style="margin-top:0;color:#2c3e50;border-bottom:1px solid #eee;padding-bottom:10px;margin-bottom:15px">\${fileData.name}</h2>
        <p><strong>Path:</strong> \${fileData.fullPath}</p>
        <p><strong>Dependencies:</strong> \${fileData.value - 1}</p>
        <div style="margin-top:20px;text-align:right">
          <button id="modal-close" style="background:#3498db;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer">Close</button>
        </div>
      \`;

      // Create overlay
      const overlay = document.createElement("div");
      overlay.style.position = "fixed";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.backgroundColor = "rgba(0,0,0,0.5)";
      overlay.style.zIndex = "999";

      document.body.appendChild(overlay);
      document.body.appendChild(modal);

      // Close modal on button click or overlay click
      document.getElementById("modal-close").addEventListener("click", function() {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
      });

      overlay.addEventListener("click", function() {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
      });
    }
  </script>
</body>
</html>
  `;
}

// Ensure output directory exists
try {
    const dir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
} catch (err) {
    console.error(`Error creating directory: ${err.message}`);
    process.exit(1);
}

// Write HTML file
try {
    const html = generateHTML(hierarchicalData);
    fs.writeFileSync(OUTPUT_FILE, html);
    console.log(`Circle packing visualization created at: ${OUTPUT_FILE}`);
} catch (err) {
    console.error(`Error creating visualization: ${err.message}`);
    process.exit(1);
}
```


#### `scripts\visualizations\create-d3-graph.js`

```javascript
import fs from "fs";
import path from "path";
import * as d3 from "d3";

// Configuration
const DEPCRUISE_DIR = ".depcruise";
const DATA_FILE = path.join(DEPCRUISE_DIR, "data/d3-data.json");
const OUTPUT_FILE = path.join(DEPCRUISE_DIR, "interactive/d3-graph.html");

// Read the dependency data
let dependencyData;
try {
    const data = fs.readFileSync(DATA_FILE, "utf8");
    dependencyData = JSON.parse(data);
    console.log(`Successfully read dependency data from ${DATA_FILE}`);
} catch (err) {
    console.error(`Error reading dependency data: ${err.message}`);
    process.exit(1);
}

// Transform data for D3 visualization
function transformDataForD3(data) {
    const nodes = [];
    const links = [];
    const nodeMap = new Map();

    // Process modules to create nodes
    data.modules.forEach((module) => {
        // Create a simpler ID from the source path
        const id = module.source;

        // Skip node if already added
        if (nodeMap.has(id)) return;

        // Determine node type/category based on path
        let category = "other";
        if (id.includes("/components/")) {
            category = "component";
        } else if (id.includes("/hooks/")) {
            category = "hook";
        } else if (id.includes("/utils/")) {
            category = "utility";
        } else if (id.includes("/pages/")) {
            category = "page";
        } else if (id.includes("/features/")) {
            category = "feature";
        } else if (id.includes("/types/")) {
            category = "type";
        }

        // Get the module directory for clustering
        const parts = id.split("/");
        const moduleGroup = parts.length > 1 ? parts[1] : "root";

        // Create node object
        const node = {
            id,
            name: path.basename(id, path.extname(id)),
            fullPath: module.source,
            category,
            group: moduleGroup,
            dependencyCount: module.dependencies
                ? module.dependencies.length
                : 0,
            // Include centrality metrics if available
            metrics: module.metrics || null,
        };

        // Add to nodes array and nodeMap
        nodes.push(node);
        nodeMap.set(id, node);
    });

    // Process dependencies to create links
    data.modules.forEach((module) => {
        if (!module.dependencies) return;

        const sourceId = module.source;

        module.dependencies.forEach((dep) => {
            const targetId = dep.resolved;

            // Only create links between nodes that exist
            if (nodeMap.has(sourceId) && nodeMap.has(targetId)) {
                links.push({
                    source: sourceId,
                    target: targetId,
                    type: dep.type || "unknown",
                    circular: dep.circular || false,
                });
            }
        });
    });

    return { nodes, links };
}

const graphData = transformDataForD3(dependencyData);
console.log(
    `Transformed data: ${graphData.nodes.length} nodes, ${graphData.links.length} links`
);

// Generate HTML with D3 visualization
function generateHTML(data) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>D3 Dependency Graph - Ringerike Landskap</title>
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: #f7f9fb;
      overflow: hidden;
      color: #333;
    }

    #graph-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .controls {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 10;
      background: white;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      max-width: 300px;
      opacity: 0.9;
    }

    .legend {
      position: absolute;
      bottom: 20px;
      left: 20px;
      z-index: 10;
      background: white;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      opacity: 0.9;
    }

    .legend h3 {
      margin-bottom: 10px;
      font-size: 14px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 8px;
    }

    h2 {
      margin-bottom: 15px;
      font-size: 16px;
      color: #2c3e50;
    }

    label {
      display: block;
      margin-bottom: 10px;
      font-size: 14px;
    }

    select, input {
      width: 100%;
      padding: 6px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 5px;
      font-size: 13px;
    }

    button:hover {
      background-color: #2980b9;
    }

    .tooltip {
      position: absolute;
      background: #fff;
      padding: 10px;
      border-radius: 3px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      pointer-events: none;
      opacity: 0;
      z-index: 100;
      max-width: 300px;
      transition: opacity 0.2s;
    }

    .tooltip h4 {
      margin: 0 0 5px;
      color: #2c3e50;
    }

    .tooltip p {
      margin: 0 0 3px;
      font-size: 12px;
    }

    .node {
      cursor: pointer;
    }

    .link {
      stroke-opacity: 0.4;
      fill: none;
    }

    .link.circular {
      stroke-dasharray: 5;
    }

    .nodeText {
      font-size: 10px;
      font-family: sans-serif;
      fill: #333;
      pointer-events: none;
      text-shadow: 0 1px 0 #fff, 1px 0 0 #fff, 0 -1px 0 #fff, -1px 0 0 #fff;
    }

    .hull {
      fill: rgba(200, 200, 200, 0.1);
      stroke: rgba(120, 120, 120, 0.2);
      stroke-width: 1.5px;
      stroke-linejoin: round;
    }
  </style>
</head>
<body>
  <div id="graph-container"></div>

  <div class="controls">
    <h2>Visualization Controls</h2>

    <label for="layout-type">Layout:</label>
    <select id="layout-type">
      <option value="force-directed" selected>Force Directed</option>
      <option value="clustered">Clustered</option>
      <option value="radial">Radial</option>
    </select>

    <label for="group-by">Group by:</label>
    <select id="group-by">
      <option value="none">No Grouping</option>
      <option value="category" selected>Component Type</option>
      <option value="module">Module</option>
    </select>

    <label for="filter-type">Filter by type:</label>
    <select id="filter-type">
      <option value="all">All</option>
      <option value="component">Components</option>
      <option value="hook">Hooks</option>
      <option value="utility">Utilities</option>
      <option value="page">Pages</option>
      <option value="feature">Features</option>
    </select>

    <label for="search">Search:</label>
    <input type="text" id="search" placeholder="Enter node name...">

    <button id="reset-zoom">Reset View</button>
    <button id="back-to-dashboard">Back to Dashboard</button>
  </div>

  <div class="legend">
    <h3>Node Types</h3>
    <div class="legend-item"><div class="legend-color" style="background: #4285F4;"></div> Component</div>
    <div class="legend-item"><div class="legend-color" style="background: #EA4335;"></div> Hook</div>
    <div class="legend-item"><div class="legend-color" style="background: #FBBC05;"></div> Utility</div>
    <div class="legend-item"><div class="legend-color" style="background: #34A853;"></div> Page</div>
    <div class="legend-item"><div class="legend-color" style="background: #8E44AD;"></div> Feature</div>
    <div class="legend-item"><div class="legend-color" style="background: #7F8C8D;"></div> Other</div>
  </div>

  <div class="tooltip" id="tooltip"></div>

  <script>
    // Graph data provided by Node.js
    const graphData = ${JSON.stringify(data)};

    // Create a tooltip element
    const tooltip = d3.select("#tooltip");

    // Set up the SVG
    const width = window.innerWidth;
    const height = window.innerHeight;
    const svg = d3.select("#graph-container")
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [0, 0, width, height])
      .call(d3.zoom().on("zoom", (event) => {
        g.attr("transform", event.transform);
      }));

    // Create a group for all elements
    const g = svg.append("g");

    // Create hull container (for grouping)
    const hullGroup = g.append("g").attr("class", "hulls");

    // Create links container
    const linksGroup = g.append("g").attr("class", "links");

    // Create nodes container
    const nodesGroup = g.append("g").attr("class", "nodes");

    // Define node colors by category
    const colorMap = {
      component: "#4285F4", // Blue
      hook: "#EA4335",      // Red
      utility: "#FBBC05",   // Yellow
      page: "#34A853",      // Green
      feature: "#8E44AD",   // Purple
      type: "#3498db",      // Light Blue
      other: "#7F8C8D"      // Gray
    };

    // Calculate node radius based on dependencies and importance metrics
    function calculateNodeRadius(d) {
      // If we have metrics data from our centrality calculation, use importance or impact
      if (d.metrics) {
        // Scale based on importance, with a minimum and maximum radius
        const importanceRadius = Math.max(5, Math.min(15, 5 + (d.metrics.importance * 0.5)));

        // Return slightly larger radius for key modules
        return d.metrics.isKeyModule ? importanceRadius + 2 : importanceRadius;
      }

      // Fallback to basic dependency count if metrics not available
      return Math.max(4, Math.min(8, 4 + d.dependencyCount / 2));
    }

    // Get node stroke based on whether it's a key module
    function getNodeStroke(d) {
      return d.metrics && d.metrics.isKeyModule ? "#E74C3C" : "#fff";
    }

    // Get node stroke width based on whether it's a key module
    function getNodeStrokeWidth(d) {
      return d.metrics && d.metrics.isKeyModule ? 2.5 : 1.5;
    }

    // Initialize with links as lines
    let links = linksGroup.selectAll(".link");

    // Initialize nodes as groups
    let nodes = nodesGroup.selectAll(".node");

    // Initialize hulls
    let hulls = hullGroup.selectAll(".hull");

    // Prepare data
    function prepareData() {
      // Create links
      links = linksGroup.selectAll(".link")
        .data(graphData.links)
        .join("path")
        .attr("class", d => \`link \${d.circular ? "circular" : ""}\`)
        .style("stroke", "#999")
        .style("stroke-width", 1);

      // Create nodes
      nodes = nodesGroup.selectAll(".node")
        .data(graphData.nodes)
        .join("g")
        .attr("class", "node")
        .on("mouseover", (event, d) => {
          // Build tooltip content with string concatenation instead of template literals
          let tooltipContent =
            "<h4>" + d.name + "</h4>" +
            "<p><strong>Path:</strong> " + d.fullPath + "</p>" +
            "<p><strong>Type:</strong> " + d.category + "</p>" +
            "<p><strong>Dependencies:</strong> " + d.dependencyCount + "</p>";

          // Add metrics information if available
          if (d.metrics) {
            tooltipContent +=
              "<hr style='margin: 5px 0; border-color: #ddd;'>" +
              "<p><strong>Impact Score:</strong> " + (d.metrics.impactScore ? d.metrics.impactScore.toFixed(1) : 'N/A') + "</p>" +
              "<p><strong>Incoming:</strong> " + (d.metrics.incomingDependencies || 0) + " / " +
              "<strong>Outgoing:</strong> " + (d.metrics.outgoingDependencies || 0) + "</p>" +
              "<p><strong>Stability:</strong> " + (d.metrics.stability ? (d.metrics.stability * 100).toFixed(0) + '%' : 'N/A') + "</p>" +
              (d.metrics.isKeyModule ? '<p style="color: #E74C3C"><strong>⭐ Key Module</strong></p>' : '');
          }

          // Show tooltip
          tooltip.style("opacity", 1)
            .html(tooltipContent)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        })
        .on("mouseout", () => {
          // Hide tooltip
          tooltip.style("opacity", 0);
        })
        .on("click", (event, d) => {
          // Show file details in browser instead of opening in VS Code
          showFileDetails(d);
        });

      // Clear existing elements
      nodes.selectAll("*").remove();

      // Add circles to nodes
      nodes.append("circle")
        .attr("r", calculateNodeRadius)
        .style("fill", d => colorMap[d.category] || colorMap.other)
        .style("stroke", getNodeStroke)
        .style("stroke-width", getNodeStrokeWidth);

      // Add text labels to nodes
      nodes.append("text")
        .attr("class", "nodeText")
        .text(d => d.name)
        .attr("x", 8)
        .attr("y", 3);

      return { nodes, links };
    }

    // Initialize data
    prepareData();

    // Create hulls for groups with smooth transitions
    function createHulls(groupBy) {
      // Remove existing hulls with fade out
      hullGroup.selectAll(".hull")
        .transition()
        .duration(300)
        .style("opacity", 0)
        .remove();

      if (groupBy === "none") return;

      // Group nodes
      const groups = d3.group(
        graphData.nodes,
        d => groupBy === "category" ? d.category : d.group
      );

      // Create a convex hull for each group
      groups.forEach((nodeGroup, key) => {
        if (nodeGroup.length < 2) return;

        const points = nodeGroup
          .filter(d => d.x !== undefined && d.y !== undefined)
          .map(d => [d.x, d.y]);

        if (points.length < 2) return;

        const hullData = d3.polygonHull(points);

        if (!hullData) return;

        const padded = hullData.map(point => {
          const centroid = d3.polygonCentroid(hullData);
          const angle = Math.atan2(point[1] - centroid[1], point[0] - centroid[0]);
          const padding = 30;
          return [
            point[0] + padding * Math.cos(angle),
            point[1] + padding * Math.sin(angle)
          ];
        });

        const hullPath = "M" + padded.join("L") + "Z";

        // Add hull to SVG with fade in
        hullGroup.append("path")
          .attr("d", hullPath)
          .attr("class", "hull")
          .attr("data-group", key)
          .style("opacity", 0)
          .transition()
          .duration(500)
          .style("opacity", 1);
      });
    }

    // Set up force simulation
    let simulation;

    function setupForceLayout() {
      // Stop any existing simulation
      if (simulation) simulation.stop();

      // Create new simulation
      simulation = d3.forceSimulation(graphData.nodes)
        .force("link", d3.forceLink(graphData.links)
          .id(d => d.id)
          .distance(80)
          .strength(0.2))
        .force("charge", d3.forceManyBody()
          .strength(-500)
          .distanceMax(300))
        .force("center", d3.forceCenter(width / 2, height / 2))
        .force("collision", d3.forceCollide().radius(d => calculateNodeRadius(d) + 20))
        .force("x", d3.forceX(width / 2).strength(0.05))
        .force("y", d3.forceY(height / 2).strength(0.05))
        .on("tick", forceDirectedTick)
        .alpha(1)
        .alphaDecay(0.02)
        .restart();

      return simulation;
    }

    // Add grouping force
    function setupClusteredLayout() {
      // Stop any existing simulation
      if (simulation) simulation.stop();

      // Determine the grouping property
      const groupBy = document.getElementById("group-by").value;
      const groupProperty = groupBy === "category" ? "category" : "group";

      // Create group force
      const groupForce = forceCluster()
        .groupBy(d => d[groupProperty])
        .strength(0.5);

      // Create new simulation with clustering
      simulation = d3.forceSimulation(graphData.nodes)
        .force("link", d3.forceLink(graphData.links)
          .id(d => d.id)
          .distance(50)
          .strength(0.1))
        .force("charge", d3.forceManyBody()
          .strength(-300)
          .distanceMax(300))
        .force("center", d3.forceCenter(width / 2, height / 2))
        .force("collision", d3.forceCollide().radius(d => calculateNodeRadius(d) + 15))
        .force("group", groupForce)
        .on("tick", () => {
          forceDirectedTick();
          // Update hulls after nodes have moved
          createHulls(groupBy);
        })
        .alpha(1)
        .alphaDecay(0.02)
        .restart();

      return simulation;
    }

    // Cluster force implementation
    function forceCluster() {
      let strength = 0.5;
      let nodes;
      let groupAccessor = d => d.group;

      function force(alpha) {
        // Group nodes by property
        const groups = d3.group(nodes, groupAccessor);
        const groupCenters = new Map();

        // Calculate center point for each group
        groups.forEach((nodeGroup, key) => {
          groupCenters.set(key, {
            x: d3.mean(nodeGroup, d => d.x),
            y: d3.mean(nodeGroup, d => d.y)
          });
        });

        // Apply force to each node towards its group center
        nodes.forEach(node => {
          const group = groupAccessor(node);
          const center = groupCenters.get(group);

          if (center) {
            node.vx += (center.x - node.x) * alpha * strength;
            node.vy += (center.y - node.y) * alpha * strength;
          }
        });
      }

      force.initialize = function(_nodes) {
        nodes = _nodes;
      };

      force.strength = function(_) {
        return arguments.length ? (strength = _, force) : strength;
      };

      force.groupBy = function(_) {
        return arguments.length ? (groupAccessor = _, force) : groupAccessor;
      };

      return force;
    }

    // Update positions for force-directed layout with performance optimization
    function forceDirectedTick() {
      // Batch multiple simulation steps before updating DOM
      for (let i = 0; i < 3; i++) {
        simulation.tick();
      }

      links.attr("d", d => {
        const source = d.source, target = d.target;
        return \`M\${source.x},\${source.y} \${target.x},\${target.y}\`;
      });
      nodes.attr("transform", d => \`translate(\${d.x || 0},\${d.y || 0})\`);
    }

    // Apply radial layout
    function applyRadialLayout() {
      // Stop simulation
      if (simulation) simulation.stop();

      const groupBy = document.getElementById("group-by").value;
      const groupProperty = groupBy === "category" ? "category" : "group";

      const radius = Math.min(width, height) / 2 - 150;
      const centerX = width / 2;
      const centerY = height / 2;

      // Group nodes by category or module
      const groupedNodes = d3.group(graphData.nodes, d => d[groupProperty]);
      const groups = Array.from(groupedNodes.keys());

      // Position nodes in a circle grouped by property
      graphData.nodes.forEach(node => {
        const groupIndex = groups.indexOf(node[groupProperty]);
        const angleOffset = (2 * Math.PI / groups.length) * groupIndex;
        const nodeCount = groupedNodes.get(node[groupProperty]).length;
        const nodeIndex = groupedNodes.get(node[groupProperty]).indexOf(node);

        // Position nodes in an arc within their group
        const angle = angleOffset + (nodeIndex / (nodeCount * 1.2)) * (2 * Math.PI / groups.length);

        // Vary radius slightly for better visibility
        const nodeRadius = radius * (0.7 + 0.3 * (0.5 + (nodeIndex % 3) * 0.15));

        node.x = centerX + nodeRadius * Math.cos(angle);
        node.y = centerY + nodeRadius * Math.sin(angle);
      });

      // Update node positions with transition
      nodes.transition()
        .duration(750)
        .attr("transform", d => \`translate(\${d.x},\${d.y})\`);

      // Update links
      links.transition()
        .duration(750)
        .attr("d", d => {
          const source = typeof d.source === 'object' ? d.source : graphData.nodes.find(n => n.id === d.source);
          const target = typeof d.target === 'object' ? d.target : graphData.nodes.find(n => n.id === d.target);

          if (source && target) {
            // Calculate arc path
            return generateArcPath(source, target);
          }
          return "";
        });

      // Create hulls
      createHulls(groupBy);
    }

    // Generate arc path between nodes
    function generateArcPath(source, target) {
      const dx = target.x - source.x;
      const dy = target.y - source.y;
      const dr = Math.sqrt(dx * dx + dy * dy) * 1.2;

      return \`M\${source.x},\${source.y} A\${dr},\${dr} 0 0,1 \${target.x},\${target.y}\`;
    }

    // Layout switching
    document.getElementById("layout-type").addEventListener("change", function() {
      const layoutType = this.value;

      if (layoutType === "force-directed") {
        setupForceLayout();
      } else if (layoutType === "clustered") {
        setupClusteredLayout();
      } else if (layoutType === "radial") {
        applyRadialLayout();
      }
    });

    // Group by switching
    document.getElementById("group-by").addEventListener("change", function() {
      const groupBy = this.value;
      const layoutType = document.getElementById("layout-type").value;

      if (layoutType === "clustered") {
        setupClusteredLayout();
      } else if (layoutType === "radial") {
        applyRadialLayout();
      } else {
        // Just update hulls for force-directed
        createHulls(groupBy);
      }
    });

    // Apply default layout
    setupForceLayout();

    // Filter by type
    document.getElementById("filter-type").addEventListener("change", function() {
      const filterType = this.value;

      // Show/hide nodes based on filter
      nodes.style("display", d => {
        if (filterType === "all") return "block";
        return d.category === filterType ? "block" : "none";
      });

      // Show/hide links based on node visibility
      links.style("display", d => {
        const source = typeof d.source === 'object' ? d.source.id : d.source;
        const target = typeof d.target === 'object' ? d.target.id : d.target;

        const sourceNode = graphData.nodes.find(n => n.id === source);
        const targetNode = graphData.nodes.find(n => n.id === target);

        if (!sourceNode || !targetNode) return "none";

        if (filterType === "all") return "block";
        return sourceNode.category === filterType || targetNode.category === filterType ? "block" : "none";
      });
    });

    // Search functionality
    document.getElementById("search").addEventListener("input", function() {
      const searchTerm = this.value.toLowerCase();

      if (!searchTerm) {
        // Show all nodes and links if search is empty
        nodes.style("display", "block");
        links.style("display", "block");
        return;
      }

      // Find matching nodes
      const matchingNodes = new Set();

      nodes.each(function(d) {
        const matches = d.name.toLowerCase().includes(searchTerm) ||
                       d.fullPath.toLowerCase().includes(searchTerm);

        if (matches) {
          matchingNodes.add(d.id);
          d3.select(this).style("display", "block");
        } else {
          d3.select(this).style("display", "none");
        }
      });

      // Show links connected to matching nodes
      links.style("display", d => {
        const source = typeof d.source === 'object' ? d.source.id : d.source;
        const target = typeof d.target === 'object' ? d.target.id : d.target;

        return matchingNodes.has(source) || matchingNodes.has(target) ? "block" : "none";
      });
    });

    // Reset zoom
    document.getElementById("reset-zoom").addEventListener("click", function() {
      svg.transition().duration(750).call(
        d3.zoom().transform,
        d3.zoomIdentity
      );
    });

    // Back to dashboard button
    document.getElementById("back-to-dashboard").addEventListener("click", function() {
      window.location.href = "../index.html";
    });

    // Adjust SVG size on window resize
    window.addEventListener("resize", function() {
      const newWidth = window.innerWidth;
      const newHeight = window.innerHeight;

      svg.attr("width", newWidth)
         .attr("height", newHeight);

      // Update center forces
      if (simulation) {
        simulation.force("center", d3.forceCenter(newWidth / 2, newHeight / 2))
          .force("x", d3.forceX(newWidth / 2).strength(0.05))
          .force("y", d3.forceY(newHeight / 2).strength(0.05))
          .alpha(0.3)
          .restart();
      }
    });

    // Enable drag behavior with enhanced node highlighting
    nodes.call(d3.drag()
      .on("start", (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;

        // Highlight dragged node and its connections
        nodes.select("circle").style("stroke", "#fff").style("stroke-width", 1.5);
        links.style("stroke", "#999").style("stroke-opacity", 0.4);

        d3.select(event.sourceEvent.target)
          .style("stroke", "#ff9500")
          .style("stroke-width", 3);

        links.each(function(link) {
          if (link.source.id === d.id || link.target.id === d.id) {
            d3.select(this)
              .style("stroke", "#ff9500")
              .style("stroke-opacity", 0.8)
              .style("stroke-width", 2);

            if (link.source.id === d.id) {
              nodes.filter(n => n.id === link.target.id)
                .select("circle")
                .style("stroke", "#ff9500")
                .style("stroke-width", 2);
            } else {
              nodes.filter(n => n.id === link.source.id)
                .select("circle")
                .style("stroke", "#ff9500")
                .style("stroke-width", 2);
            }
          }
        });
      })
      .on("drag", (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on("end", (event, d) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;

        // Reset highlighting with smooth transition
        nodes.select("circle")
          .transition()
          .duration(300)
          .style("stroke", "#fff")
          .style("stroke-width", 1.5);

        links.transition()
          .duration(300)
          .style("stroke", "#999")
          .style("stroke-opacity", 0.4)
          .style("stroke-width", 1);
      })
    );

    // Add click handler for node highlighting
    nodes.on("click", (event, d) => {
      // First, reset all nodes and links with transition
      nodes.select("circle")
        .transition()
        .duration(200)
        .style("stroke", "#fff")
        .style("stroke-width", 1.5);

      links.transition()
        .duration(200)
        .style("stroke", "#999")
        .style("stroke-opacity", 0.4);

      // Highlight the selected node with transition
      d3.select(event.currentTarget).select("circle")
        .transition()
        .duration(200)
        .style("stroke", "#ff9500")
        .style("stroke-width", 3);

      // Highlight connected links and nodes with transition
      links.each(function(link) {
        if (link.source.id === d.id || link.target.id === d.id) {
          d3.select(this)
            .transition()
            .duration(200)
            .style("stroke", "#ff9500")
            .style("stroke-opacity", 0.8)
            .style("stroke-width", 2);

          if (link.source.id === d.id) {
            nodes.filter(n => n.id === link.target.id)
              .select("circle")
              .transition()
              .duration(200)
              .style("stroke", "#ff9500")
              .style("stroke-width", 2);
          } else {
            nodes.filter(n => n.id === link.source.id)
              .select("circle")
              .transition()
              .duration(200)
              .style("stroke", "#ff9500")
              .style("stroke-width", 2);
          }
        }
      });

      // Prevent the click from triggering the VSCode file open
      event.stopPropagation();
    });

    // Handle file paths
    document.addEventListener('click', function(e) {
      // Find clicked link
      let target = e.target;
      while (target && target.tagName !== 'A') {
        target = target.parentNode;
        if (!target) return;
      }

      // Only process links to files
      const href = target?.getAttribute('href');
      if (!href || href.startsWith('http') || href.startsWith('#')) return;

      // Prevent default browser navigation
      e.preventDefault();

      // Extract the path
      let filePath = href;

      // Handle different path formats
      if (filePath.startsWith('src/')) {
        // Show file details in a modal instead of opening in VS Code
        showFileDetailsFromPath(filePath);
      } else {
        // For other links - attempt to open them normally
        window.open(href, '_blank');
      }
    });

    // Function to show file details
    function showFileDetails(node) {
      // Create modal dialog
      const modal = document.createElement("div");
      modal.style.position = "fixed";
      modal.style.top = "50%";
      modal.style.left = "50%";
      modal.style.transform = "translate(-50%, -50%)";
      modal.style.backgroundColor = "white";
      modal.style.padding = "20px";
      modal.style.borderRadius = "8px";
      modal.style.boxShadow = "0 4px 12px rgba(0,0,0,0.2)";
      modal.style.zIndex = "1000";
      modal.style.maxWidth = "600px";
      modal.style.width = "90%";

      modal.innerHTML = \`
        <h2 style="margin-top:0;color:#2c3e50;border-bottom:1px solid #eee;padding-bottom:10px;margin-bottom:15px">\${node.name}</h2>
        <p><strong>Path:</strong> \${node.fullPath}</p>
        <p><strong>Type:</strong> \${node.category}</p>
        <p><strong>Dependencies:</strong> \${node.dependencyCount}</p>
        <div style="margin-top:20px;text-align:right">
          <button id="modal-close" style="background:#3498db;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer">Close</button>
        </div>
      \`;

      // Create overlay
      const overlay = document.createElement("div");
      overlay.style.position = "fixed";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.backgroundColor = "rgba(0,0,0,0.5)";
      overlay.style.zIndex = "999";

      document.body.appendChild(overlay);
      document.body.appendChild(modal);

      // Close modal on button click or overlay click
      document.getElementById("modal-close").addEventListener("click", function() {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
      });

      overlay.addEventListener("click", function() {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
      });
    }

    // Function to show file details from a file path
    function showFileDetailsFromPath(filePath) {
      const node = graphData.nodes.find(n => n.fullPath === filePath);
      if (node) {
        showFileDetails(node);
      } else {
        // If node not found, show basic information
        const modal = document.createElement("div");
        modal.style.position = "fixed";
        modal.style.top = "50%";
        modal.style.left = "50%";
        modal.style.transform = "translate(-50%, -50%)";
        modal.style.backgroundColor = "white";
        modal.style.padding = "20px";
        modal.style.borderRadius = "8px";
        modal.style.boxShadow = "0 4px 12px rgba(0,0,0,0.2)";
        modal.style.zIndex = "1000";
        modal.style.maxWidth = "600px";
        modal.style.width = "90%";

        modal.innerHTML = \`
          <h2 style="margin-top:0;color:#2c3e50;border-bottom:1px solid #eee;padding-bottom:10px;margin-bottom:15px">\${path.basename(filePath)}</h2>
          <p><strong>Path:</strong> \${filePath}</p>
          <div style="margin-top:20px;text-align:right">
            <button id="modal-close" style="background:#3498db;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer">Close</button>
          </div>
        \`;

        // Create overlay
        const overlay = document.createElement("div");
        overlay.style.position = "fixed";
        overlay.style.top = "0";
        overlay.style.left = "0";
        overlay.style.width = "100%";
        overlay.style.height = "100%";
        overlay.style.backgroundColor = "rgba(0,0,0,0.5)";
        overlay.style.zIndex = "999";

        document.body.appendChild(overlay);
        document.body.appendChild(modal);

        // Close modal on button click or overlay click
        document.getElementById("modal-close").addEventListener("click", function() {
          document.body.removeChild(modal);
          document.body.removeChild(overlay);
        });

        overlay.addEventListener("click", function() {
          document.body.removeChild(modal);
          document.body.removeChild(overlay);
        });
      }
    }
  </script>
</body>
</html>
  `;
}

// Ensure output directory exists
try {
    const dir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
} catch (err) {
    console.error(`Error creating directory: ${err.message}`);
    process.exit(1);
}

// Write HTML file
try {
    const html = generateHTML(graphData);
    fs.writeFileSync(OUTPUT_FILE, html);
    console.log(`D3 visualization created at: ${OUTPUT_FILE}`);
} catch (err) {
    console.error(`Error creating visualization: ${err.message}`);
    process.exit(1);
}
```


#### `scripts\visualizations\create-dependency-dashboard.js`

```javascript
/**
 * Create Dependency Dashboard
 *
 * This script generates a dashboard for visualizing project dependencies.
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get the directory name using ESM-compatible approach
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
const DASHBOARD_DIR = path.resolve(__dirname, "../../.depcruise");
const INTERACTIVE_DIR = path.join(DASHBOARD_DIR, "interactive");
const DATA_DIR = path.join(DASHBOARD_DIR, "data");
const GRAPHS_DIR = path.join(DASHBOARD_DIR, "graphs");

/**
 * Ensure all required directories exist
 */
function ensureDirectories() {
    const dirs = [DASHBOARD_DIR, INTERACTIVE_DIR, DATA_DIR, GRAPHS_DIR];

    dirs.forEach((dir) => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`Created directory: ${dir}`);
        }
    });
}

/**
 * Create the main dashboard HTML file
 */
function createDashboardHtml() {
    const dashboardHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dependency Visualization Dashboard - Ringerike Landskap</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
    }
    header {
      background-color: #1E6435;
      color: white;
      padding: 1rem 2rem;
    }
    h1 {
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
    }
    .description {
      font-size: 1rem;
      opacity: 0.9;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    .card-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #275F47;
      color: white;
      padding: 1rem;
    }
    .card-header h2 {
      font-size: 1.2rem;
    }
    .card-body {
      padding: 1.5rem;
    }
    .card-description {
      margin-bottom: 1.5rem;
      color: #555;
    }
    .visualizations {
      margin-top: 3rem;
    }
    .section-title {
      margin-bottom: 1rem;
      border-bottom: 2px solid #1E6435;
      padding-bottom: 0.5rem;
    }
    .btn {
      display: inline-block;
      background-color: #1E6435;
      color: white;
      text-decoration: none;
      padding: 0.6rem 1.2rem;
      border-radius: 4px;
      font-weight: 500;
      transition: background-color 0.2s ease;
    }
    .btn:hover {
      background-color: #164023;
    }
    .docs-link {
      margin-top: 2rem;
      text-align: center;
      font-size: 0.9rem;
    }
    footer {
      text-align: center;
      padding: 2rem;
      color: #666;
      border-top: 1px solid #ddd;
      margin-top: 3rem;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Dependency Visualization Dashboard</h1>
    <p class="description">Ringerike Landskap Website Project</p>
  </header>

  <div class="container">
    <section>
      <p>This dashboard provides various visualizations of the project's dependencies to help understand the codebase structure and relationships between modules.</p>
    </section>

    <section class="visualizations">
      <h2 class="section-title">Interactive Visualizations</h2>
      <div class="card-grid">
        <div class="card">
          <div class="card-header">
            <h2>Flow Diagram</h2>
          </div>
          <div class="card-body">
            <p class="card-description">Visualizes the flow of dependencies between modules with a clean, directional layout.</p>
            <a href="./interactive/flow-diagram.html" class="btn">View Flow Diagram</a>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2>D3 Interactive Graph</h2>
          </div>
          <div class="card-body">
            <p class="card-description">An interactive force-directed graph allowing you to explore and reorganize the dependency network.</p>
            <a href="./interactive/d3-graph.html" class="btn">Open D3 Graph</a>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2>Circle Packing</h2>
          </div>
          <div class="card-body">
            <p class="card-description">Hierarchical view of code organization using nested circles representing directory structure.</p>
            <a href="./interactive/circle-packing.html" class="btn">View Circle Packing</a>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2>Bubble Chart</h2>
          </div>
          <div class="card-body">
            <p class="card-description">Shows modules as bubbles with size proportional to the number of dependencies.</p>
            <a href="./interactive/bubble-chart.html" class="btn">Open Bubble Chart</a>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2>Dependency Graph</h2>
          </div>
          <div class="card-body">
            <p class="card-description">Complete graph of module relationships with detailed information on dependencies.</p>
            <a href="./interactive/dependency-graph.html" class="btn">View Dependency Graph</a>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2>Validation Report</h2>
          </div>
          <div class="card-body">
            <p class="card-description">Shows any dependency validation issues according to the project's rules.</p>
            <a href="./interactive/validation.html" class="btn">View Validation</a>
          </div>
        </div>
      </div>
    </section>

    <p class="docs-link">
      <a href="../docs/dependency-visualization.md">Read the dependency visualization documentation</a>
    </p>
  </div>

  <footer>
    <p>Generated on ${new Date().toLocaleString()}</p>
  </footer>
</body>
</html>`;

    fs.writeFileSync(path.join(DASHBOARD_DIR, "index.html"), dashboardHtml);
    console.log("Dashboard HTML created successfully!");
}

/**
 * Main function to create the dashboard
 */
function main() {
    console.log("Creating dependency visualization dashboard...");

    // Ensure required directories exist
    ensureDirectories();

    // Create the dashboard HTML
    createDashboardHtml();

    console.log("Dashboard created successfully!");
}

// Run the main function
main();
```


#### `scripts\visualizations\create-flow-diagram.js`

```javascript
import fs from "fs";
import path from "path";

/**
 * This script creates a dependency flow diagram visualization.
 * It uses D3.js to create an interactive directed graph where each node
 * represents a file, and arrows show dependency relationships.
 */

// Read dependency data from the JSON file
const DATA_FILE = ".depcruise/data/d3-data.json";
const OUTPUT_DIR = ".depcruise/interactive";
const OUTPUT_FILE = path.join(OUTPUT_DIR, "flow-diagram.html");
const DEBUG_FILE = path.join(OUTPUT_DIR, "flow-diagram-debug.txt");

try {
    // Ensure the output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // Create a debug log stream
    const debugLog = fs.createWriteStream(DEBUG_FILE);

    const log = (message) => {
        console.log(message);
        debugLog.write(message + "\n");
    };

    // Read dependency data
    const data = JSON.parse(fs.readFileSync(DATA_FILE, "utf8"));
    log(`Successfully read dependency data from ${DATA_FILE}`);
    log(`Data contains ${data.modules ? data.modules.length : 0} modules`);

    // Debug: List some modules to check for path aliases
    if (data.modules && data.modules.length > 0) {
        log("\nDEBUG: First 5 modules:");
        data.modules.slice(0, 5).forEach((module) => {
            log(`- Module: ${module.source}`);
            if (module.dependencies && module.dependencies.length > 0) {
                log("  Dependencies:");
                module.dependencies.forEach((dep) => {
                    log(`  - ${dep.module} -> ${dep.resolved}`);
                });
            }
        });

        // Specifically check for hooks usage
        log("\nDEBUG: Checking for hooks usage:");
        data.modules.forEach((module) => {
            if (module.dependencies) {
                module.dependencies.forEach((dep) => {
                    if (
                        dep.module.includes("hooks") ||
                        dep.resolved?.includes("hooks")
                    ) {
                        log(
                            `${module.source} imports: ${dep.module} (resolved to: ${dep.resolved})`
                        );
                    }
                });
            }
        });

        // Check for useData.ts
        log("\nDEBUG: Checking for useData.ts:");
        const useDataModule = data.modules.find((m) =>
            m.source.includes("useData.ts")
        );
        if (useDataModule) {
            log(`Found useData.ts: ${useDataModule.source}`);
            log("Dependencies importing useData.ts:");
            data.modules.forEach((module) => {
                if (module.dependencies) {
                    module.dependencies.forEach((dep) => {
                        if (dep.resolved === useDataModule.source) {
                            log(`- ${module.source} imports useData.ts`);
                        }
                    });
                }
            });
        } else {
            log("useData.ts not found in modules!");

            // Check for any module paths that might contain useData
            log("Checking for any module path containing 'useData':");
            data.modules.forEach((module) => {
                if (module.source.toLowerCase().includes("usedata")) {
                    log(`- Found similar module: ${module.source}`);
                }
            });

            // Search for alias imports
            log("Checking for @/hooks/useData imports:");
            data.modules.forEach((module) => {
                if (module.dependencies) {
                    module.dependencies.forEach((dep) => {
                        if (dep.module.includes("@/hooks/useData")) {
                            log(
                                `- ${
                                    module.source
                                } imports @/hooks/useData as ${
                                    dep.resolved || "unresolved"
                                }`
                            );
                        }
                    });
                }
            });
        }
    }

    // Transform data for flow diagram
    const { nodes, links } = transformDataForFlowDiagram(data);
    log(`Transformed data: ${nodes.length} nodes and ${links.length} links`);

    // Generate the HTML
    const html = generateHtml(nodes, links);

    // Write the HTML to a file
    fs.writeFileSync(OUTPUT_FILE, html);
    log(`Flow diagram visualization created at: ${OUTPUT_FILE}`);
} catch (error) {
    console.error("Error:", error.message);
    console.error(error.stack);
}

/**
 * Transforms dependency-cruiser data for flow diagram visualization
 */
function transformDataForFlowDiagram(data) {
    const modules = data.modules || [];

    // Create a mapping of all modules by source path
    const moduleMap = new Map();
    modules.forEach((module) => {
        moduleMap.set(module.source, module);
    });

    // Path alias resolver - add logic to handle @ imports
    const resolveAliasedPath = (modulePath) => {
        // Check if this is a module using @/ alias
        if (modulePath.startsWith("@/")) {
            // Convert @/path to src/path
            return modulePath.replace("@/", "src/");
        }
        return modulePath;
    };

    // Create nodes
    const nodeMap = new Map();
    modules.forEach((module) => {
        const source = module.source;
        const fileName = path.basename(source);

        // Determine category based on directory structure
        let category = "Other";
        if (source.includes("/components/")) {
            category = "Component";
        } else if (source.includes("/hooks/")) {
            category = "Hook";
        } else if (source.includes("/pages/")) {
            category = "Page";
        } else if (source.includes("/utils/")) {
            category = "Utility";
        } else if (source.includes("/services/")) {
            category = "Service";
        } else if (source.includes("/types/")) {
            category = "Type";
        } else if (source.includes("/contexts/")) {
            category = "Context";
        }

        nodeMap.set(source, {
            id: source,
            name: fileName,
            fullPath: source,
            category: category,
            // We'll count these later
            incomingDeps: 0,
            outgoingDeps: 0,
        });
    });

    // Create links and count dependencies
    const links = [];
    modules.forEach((module) => {
        const source = module.source;
        const sourceNode = nodeMap.get(source);

        if (module.dependencies && Array.isArray(module.dependencies)) {
            module.dependencies.forEach((dep) => {
                let target = dep.resolved;
                const targetNode = nodeMap.get(target);

                // Try to find the module using path alias resolution if we can't find it directly
                if (!targetNode && dep.module.startsWith("@/")) {
                    // This is a module imported with @ alias
                    const potentialResolvedPath = `src${dep.module.substring(
                        1
                    )}`;
                    const potentialTargetNode = nodeMap.get(
                        potentialResolvedPath
                    );

                    if (potentialTargetNode) {
                        // We found the module using alias resolution
                        target = potentialResolvedPath;

                        // Add a console log for debugging
                        log(
                            `Resolved aliased import: ${dep.module} -> ${target}`
                        );
                    }
                }

                // Only create links where both source and target are in our node set
                const finalTargetNode = nodeMap.get(target);
                if (finalTargetNode) {
                    links.push({
                        source: source,
                        target: target,
                        type: dep.type || "regular",
                        circular: dep.circular || false,
                    });

                    // Count dependencies for each node
                    sourceNode.outgoingDeps++;
                    finalTargetNode.incomingDeps++;
                }
            });
        }
    });

    // Convert nodeMap to array
    const nodes = Array.from(nodeMap.values());

    return { nodes, links };
}

/**
 * Generates HTML with D3.js for flow diagram visualization
 */
function generateHtml(nodes, links) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependency Flow Diagram</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        #chart {
            width: 100%;
            height: 800px;
            border: 1px solid #eee;
            border-radius: 4px;
            position: relative;
            background-color: #fafafa;
        }
        .tooltip {
            position: absolute;
            padding: 10px;
            background: #333;
            color: white;
            border-radius: 4px;
            pointer-events: none;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .controls {
            margin: 15px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        .controls label {
            margin-right: 5px;
            font-weight: bold;
        }
        .controls select, .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .search-box {
            flex-grow: 1;
            max-width: 300px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        button:hover {
            background-color: #2980b9;
        }
        .back-button {
            margin-bottom: 20px;
            background-color: #34495e;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .legend-color {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            border-radius: 3px;
        }
        .module-count {
            color: #666;
            font-size: 14px;
            margin: 10px 0;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        #status.error {
            display: block;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        #status.info {
            display: block;
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .node {
            cursor: pointer;
        }
        .link {
            stroke-opacity: 0.6;
            stroke-width: 1.5px;
        }
        .node circle {
            stroke: #fff;
            stroke-width: 1.5px;
        }
        .node text {
            pointer-events: none;
            font-size: 12px;
        }
        .highlighted {
            stroke-width: 3px !important;
            stroke: #222 !important;
        }
        .highlighted-link {
            stroke-opacity: 1 !important;
            stroke-width: 3px !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="back-button" onclick="window.location.href='../index.html'">← Back to Dashboard</button>
        <h1>Dependency Flow Diagram</h1>
        <div id="status"></div>
        <div class="module-count" id="module-count"></div>

        <div class="controls">
            <div>
                <label for="layoutType">Layout:</label>
                <select id="layoutType">
                    <option value="force">Force Directed</option>
                    <option value="radial">Radial</option>
                    <option value="dagre">Hierarchical</option>
                </select>
            </div>
            <div>
                <label for="colorBy">Color by:</label>
                <select id="colorBy">
                    <option value="category">Category</option>
                    <option value="incomingDeps">Incoming Dependencies</option>
                    <option value="outgoingDeps">Outgoing Dependencies</option>
                </select>
            </div>
            <div>
                <label for="filterCategory">Filter Category:</label>
                <select id="filterCategory">
                    <option value="all">All Categories</option>
                </select>
            </div>
            <div class="search-box">
                <label for="search">Search:</label>
                <input type="text" id="search" placeholder="Filter by name or path...">
            </div>
            <button id="resetFilters">Reset Filters</button>
            <button id="resetZoom">Reset View</button>
        </div>

        <div class="legend" id="legend"></div>
        <div id="chart"></div>
    </div>

    <script>
    (function() {
        // Define data
        const nodes = ${JSON.stringify(nodes)};
        const links = ${JSON.stringify(links)};
        console.log("Data loaded:", nodes.length, "nodes and", links.length, "links");

        // Show status message if no data
        const status = document.getElementById('status');
        if (nodes.length === 0) {
            status.textContent = 'No dependency data available.';
            status.className = 'error';
            status.style.display = 'block';
            return;
        }

        // Track unique categories
        const categories = [...new Set(nodes.map(d => d.category))].sort();

        // Populate category filter
        const filterCategory = document.getElementById('filterCategory');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            filterCategory.appendChild(option);
        });

        // Update module count display
        const moduleCount = document.getElementById('module-count');
        moduleCount.textContent = "Showing all " + nodes.length + " modules and " + links.length + " dependencies";

        // Define colors based on category
        const categoryColors = {
            Component: "#4285F4", // Blue
            Hook: "#EA4335",      // Red
            Page: "#34A853",      // Green
            Utility: "#FBBC05",   // Yellow
            Service: "#8E44AD",   // Purple
            Type: "#3498db",      // Light Blue
            Context: "#e67e22",   // Orange
            Other: "#7F8C8D"      // Gray
        };

        // Create color scales for numerical values
        const blueScale = d3.scaleSequential()
            .domain([0, d3.max(nodes, d => d.incomingDeps) || 1])
            .interpolator(d3.interpolateBlues);

        const greenScale = d3.scaleSequential()
            .domain([0, d3.max(nodes, d => d.outgoingDeps) || 1])
            .interpolator(d3.interpolateGreens);

        // Create tooltip
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip');

        // Function to get color based on selected property
        function getNodeColor(d, colorBy) {
            switch (colorBy) {
                case 'category': return categoryColors[d.category] || categoryColors.Other;
                case 'incomingDeps': return blueScale(d.incomingDeps);
                case 'outgoingDeps': return greenScale(d.outgoingDeps);
                default: return categoryColors[d.category] || '#ccc';
            }
        }

        // Function to get link color - typically based on source node
        function getLinkColor(d, colorBy) {
            const sourceNode = nodes.find(n => n.id === d.source);
            if (!sourceNode) return '#999';

            // Make the link semi-transparent
            const baseColor = getNodeColor(sourceNode, colorBy);
            // Convert from hex to rgba to add transparency
            return baseColor;
        }

        // Calculate node size based on dependencies
        function getNodeRadius(d) {
            const total = d.incomingDeps + d.outgoingDeps;
            // Base radius calculation to ensure good visibility
            return Math.min(Math.sqrt(total * 2) + 5, 20);
        }

        // Create and update the diagram
        function updateDiagram() {
            try {
                // Clear any previous status
                status.style.display = 'none';

                // Get current filter values
                const layoutType = document.getElementById('layoutType').value;
                const colorBy = document.getElementById('colorBy').value;
                const categoryVal = document.getElementById('filterCategory').value;
                const searchText = document.getElementById('search').value.toLowerCase();

                // Filter nodes based on search and category
                const filteredNodes = nodes.filter(d => {
                    const matchesSearch = searchText === '' ||
                                         d.name.toLowerCase().includes(searchText) ||
                                         d.fullPath.toLowerCase().includes(searchText);
                    const matchesCategory = categoryVal === 'all' || d.category === categoryVal;
                    return matchesSearch && matchesCategory;
                });

                // Keep only links where both source and target are in the filtered nodes
                const nodeIds = new Set(filteredNodes.map(d => d.id));
                const filteredLinks = links.filter(d =>
                    nodeIds.has(d.source) && nodeIds.has(d.target)
                );

                // Update count display
                moduleCount.textContent =
                    filteredNodes.length === nodes.length
                    ? "Showing all " + nodes.length + " modules and " + links.length + " dependencies"
                    : "Showing " + filteredNodes.length + " of " + nodes.length + " modules and " +
                      filteredLinks.length + " dependencies";

                // Display message if no data matches filters
                if (filteredNodes.length === 0) {
                    status.textContent = 'No modules match the current filters.';
                    status.className = 'info';
                    status.style.display = 'block';
                    return;
                }

                // Get chart dimensions
                const chartElement = document.getElementById('chart');
                const width = chartElement.clientWidth;
                const height = chartElement.clientHeight;

                // Clear previous chart
                chartElement.innerHTML = '';

                // Create the SVG container with zoom behavior
                const svg = d3.select('#chart')
                    .append('svg')
                    .attr('width', width)
                    .attr('height', height)
                    .attr('viewBox', [0, 0, width, height]);

                // Add zoom and pan behavior
                const g = svg.append('g');

                svg.call(d3.zoom()
                    .extent([[0, 0], [width, height]])
                    .scaleExtent([0.1, 4])
                    .on('zoom', (event) => {
                        g.attr('transform', event.transform);
                    }));

                // Create a container for the links
                const linkGroup = g.append('g')
                    .attr('class', 'links');

                // Create a container for the nodes
                const nodeGroup = g.append('g')
                    .attr('class', 'nodes');

                // Create the simulation for force-directed layout
                const simulation = d3.forceSimulation(filteredNodes)
                    .force('charge', d3.forceManyBody().strength(-120))
                    .force('center', d3.forceCenter(width / 2, height / 2))
                    .force('x', d3.forceX(width / 2).strength(0.05))
                    .force('y', d3.forceY(height / 2).strength(0.05))
                    .force('link', d3.forceLink(filteredLinks)
                        .id(d => d.id)
                        .distance(80)
                        .strength(0.7));

                // Apply specific layout if not force directed
                if (layoutType === 'radial') {
                    applyRadialLayout(filteredNodes, filteredLinks, width, height);
                } else if (layoutType === 'dagre') {
                    applyDagreLayout(filteredNodes, filteredLinks, width, height);
                }

                // Create the links
                const link = linkGroup.selectAll('.link')
                    .data(filteredLinks)
                    .enter()
                    .append('line')
                    .attr('class', 'link')
                    .attr('stroke', d => getLinkColor(d, colorBy))
                    .attr('marker-end', 'url(#arrow)');

                // Define arrow markers for links
                svg.append('defs').selectAll('marker')
                    .data(['arrow'])
                    .enter().append('marker')
                    .attr('id', d => d)
                    .attr('viewBox', '0 -5 10 10')
                    .attr('refX', 25) // Offset from node radius
                    .attr('refY', 0)
                    .attr('markerWidth', 6)
                    .attr('markerHeight', 6)
                    .attr('orient', 'auto')
                    .append('path')
                    .attr('fill', '#999')
                    .attr('d', 'M0,-5L10,0L0,5');

                // Create the nodes
                const node = nodeGroup.selectAll('.node')
                    .data(filteredNodes)
                    .enter()
                    .append('g')
                    .attr('class', 'node')
                    .call(d3.drag()
                        .on('start', dragstarted)
                        .on('drag', dragged)
                        .on('end', dragended));

                // Add circles to nodes
                node.append('circle')
                    .attr('r', getNodeRadius)
                    .attr('fill', d => getNodeColor(d, colorBy))
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 1.5);

                // Add text labels to nodes
                node.append('text')
                    .attr('dx', 12)
                    .attr('dy', '.35em')
                    .text(d => d.name);

                // Add mouseover events for nodes
                node.on('mouseover', function(event, d) {
                    // Highlight the node
                    d3.select(this).select('circle').classed('highlighted', true);

                    // Highlight connected links and nodes
                    link.classed('highlighted-link', l => l.source.id === d.id || l.target.id === d.id);

                    // Show tooltip
                    tooltip.html(
                        "<strong>" + d.name + "</strong><br>" +
                        "<span style=\\"opacity: 0.8\\">" + d.fullPath + "</span><br>" +
                        "<span>Category: " + d.category + "</span><br>" +
                        "<span>Incoming Dependencies: " + d.incomingDeps + "</span><br>" +
                        "<span>Outgoing Dependencies: " + d.outgoingDeps + "</span>"
                    )
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 10) + 'px')
                    .style('opacity', 1);
                })
                .on('mouseout', function() {
                    // Remove highlight
                    d3.select(this).select('circle').classed('highlighted', false);
                    link.classed('highlighted-link', false);

                    // Hide tooltip
                    tooltip.style('opacity', 0);
                })
                .on('click', function(event, d) {
                    // Show file details in modal
                    showFileDetails(d);
                });

                // Update positions on tick
                simulation.on('tick', () => {
                    link
                        .attr('x1', d => d.source.x)
                        .attr('y1', d => d.source.y)
                        .attr('x2', d => d.target.x)
                        .attr('y2', d => d.target.y);

                    node.attr('transform', d => \`translate(\${d.x},\${d.y})\`);
                });

                // Drag functions
                function dragstarted(event, d) {
                    if (!event.active) simulation.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }

                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                }

                function dragended(event, d) {
                    if (!event.active) simulation.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }

                // Apply radial layout
                function applyRadialLayout(nodes, links, width, height) {
                    const centerX = width / 2;
                    const centerY = height / 2;
                    const radius = Math.min(width, height) / 3;

                    // Sort nodes by number of connections (most connected in the center)
                    nodes.sort((a, b) => (b.incomingDeps + b.outgoingDeps) - (a.incomingDeps + a.outgoingDeps));

                    // Position nodes in a circle, with most important in center
                    nodes.forEach((node, i) => {
                        if (i === 0) {
                            // Most connected node goes in center
                            node.x = centerX;
                            node.y = centerY;
                        } else {
                            const angle = ((i - 1) / (nodes.length - 1)) * 2 * Math.PI;
                            node.x = centerX + radius * Math.cos(angle);
                            node.y = centerY + radius * Math.sin(angle);
                        }
                    });

                    // Update simulation forces for radial layout
                    simulation.force('link', d3.forceLink(links).id(d => d.id).distance(80).strength(0.1))
                        .force('charge', d3.forceManyBody().strength(-100))
                        .force('x', null)
                        .force('y', null)
                        .force('center', null);

                    // Add positioning forces
                    nodes.forEach((node, i) => {
                        if (i > 0) {
                            node.fx = node.x;
                            node.fy = node.y;
                        }
                    });
                }

                // Apply hierarchical layout (simplified without dagre)
                function applyDagreLayout(nodes, links, width, height) {
                    // Identify root nodes (nodes with no incoming links)
                    const nodeTargets = new Set(links.map(l => l.target));
                    const rootNodes = nodes.filter(node => !nodeTargets.has(node.id));

                    // Calculate levels for each node
                    const levels = new Map();

                    // Set root nodes to level 0
                    rootNodes.forEach(node => levels.set(node.id, 0));

                    // Calculate levels for other nodes via BFS
                    let changed = true;
                    while (changed) {
                        changed = false;
                        links.forEach(link => {
                            const sourceLevel = levels.get(link.source);
                            if (sourceLevel !== undefined) {
                                const targetLevel = levels.get(link.target);
                                if (targetLevel === undefined || targetLevel <= sourceLevel) {
                                    levels.set(link.target, sourceLevel + 1);
                                    changed = true;
                                }
                            }
                        });
                    }

                    // Find the maximum level
                    const maxLevel = Math.max(...Array.from(levels.values()));

                    // Position nodes based on their level
                    const levelCounts = new Array(maxLevel + 1).fill(0);

                    nodes.forEach(node => {
                        const level = levels.get(node.id) || 0;
                        const position = levelCounts[level]++;
                        const nodesInLevel = nodes.filter(n => (levels.get(n.id) || 0) === level).length;

                        node.x = (width * (level + 0.5)) / (maxLevel + 1);
                        node.y = height * ((position + 0.5) / Math.max(nodesInLevel, 1));

                        // Fix node positions
                        node.fx = node.x;
                        node.fy = node.y;
                    });

                    // Adjust forces for hierarchical layout
                    simulation.force('link', d3.forceLink(links).id(d => d.id).distance(80).strength(0.1))
                        .force('charge', d3.forceManyBody().strength(-50))
                        .force('x', null)
                        .force('y', null)
                        .force('center', null);
                }

                // Update legend
                updateLegend(colorBy);

            } catch (error) {
                console.error("Error rendering diagram:", error);
                status.textContent = 'Error rendering diagram: ' + error.message;
                status.className = 'error';
                status.style.display = 'block';
            }
        }

        // Update the legend based on the current color scheme
        function updateLegend(colorBy) {
            const legendContainer = document.getElementById('legend');
            legendContainer.innerHTML = '';

            if (colorBy === 'category') {
                // Show category legend
                categories.forEach(category => {
                    const item = document.createElement('div');
                    item.className = 'legend-item';

                    const colorBox = document.createElement('div');
                    colorBox.className = 'legend-color';
                    colorBox.style.backgroundColor = categoryColors[category] || categoryColors.Other;

                    const label = document.createElement('span');
                    label.textContent = category;

                    item.appendChild(colorBox);
                    item.appendChild(label);
                    legendContainer.appendChild(item);
                });
            } else {
                // Create a gradient legend
                const width = 200;
                const height = 20;

                // Add a title based on selected color
                const title = document.createElement('div');
                title.style.width = '100%';
                title.style.marginBottom = '10px';
                title.style.fontWeight = 'bold';

                if (colorBy === 'incomingDeps') {
                    title.textContent = 'Incoming Dependencies (blue)';
                } else if (colorBy === 'outgoingDeps') {
                    title.textContent = 'Outgoing Dependencies (green)';
                }

                legendContainer.appendChild(title);

                // Create SVG for gradient
                const svg = d3.select(legendContainer)
                    .append('svg')
                    .attr('width', width + 50)
                    .attr('height', height + 30);

                // Create gradient definition
                const defs = svg.append('defs');
                const gradient = defs.append('linearGradient')
                    .attr('id', 'legend-gradient')
                    .attr('x1', '0%')
                    .attr('y1', '0%')
                    .attr('x2', '100%')
                    .attr('y2', '0%');

                // Get appropriate color scale
                const colorScale = colorBy === 'incomingDeps' ? blueScale : greenScale;

                // Add color stops
                const steps = 10;
                for (let i = 0; i <= steps; i++) {
                    const offset = i / steps;
                    const value = offset * colorScale.domain()[1];
                    gradient.append('stop')
                        .attr('offset', (offset * 100) + '%')
                        .attr('stop-color', colorScale(value));
                }

                // Add gradient rectangle
                svg.append('rect')
                    .attr('x', 0)
                    .attr('y', 10)
                    .attr('width', width)
                    .attr('height', height)
                    .style('fill', 'url(#legend-gradient)');

                // Add min/max labels
                svg.append('text')
                    .attr('x', 0)
                    .attr('y', height + 25)
                    .text('0')
                    .style('font-size', '12px');

                svg.append('text')
                    .attr('x', width - 30)
                    .attr('y', height + 25)
                    .text('Max')
                    .style('font-size', '12px');
            }
        }

        // Function to show file details in a modal
        function showFileDetails(fileData) {
            // Create modal container
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.zIndex = '1000';
            modal.style.left = '50%';
            modal.style.top = '50%';
            modal.style.transform = 'translate(-50%, -50%)';
            modal.style.backgroundColor = 'white';
            modal.style.borderRadius = '8px';
            modal.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
            modal.style.width = '90%';
            modal.style.maxWidth = '600px';
            modal.style.padding = '20px';

            // Create modal content
            modal.innerHTML =
                "<h2 style=\\"margin-top:0;color:#2c3e50;border-bottom:1px solid #eee;padding-bottom:10px\\">" + fileData.name + "</h2>" +
                "<p><strong>Full Path:</strong> " + fileData.fullPath + "</p>" +
                "<p><strong>Category:</strong> " + fileData.category + "</p>" +
                "<p><strong>Incoming Dependencies:</strong> " + fileData.incomingDeps + "</p>" +
                "<p><strong>Outgoing Dependencies:</strong> " + fileData.outgoingDeps + "</p>" +
                "<p><strong>Total Dependencies:</strong> " + (fileData.incomingDeps + fileData.outgoingDeps) + "</p>" +
                "<div style=\\"text-align:right;margin-top:20px\\">" +
                "<button id=\\"closeModal\\" style=\\"background:#3498db;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer\\">Close</button>" +
                "</div>";

            // Create overlay
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.zIndex = '999';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';

            // Add to document
            document.body.appendChild(overlay);
            document.body.appendChild(modal);

            // Setup close handlers
            document.getElementById('closeModal').addEventListener('click', closeModal);
            overlay.addEventListener('click', closeModal);

            function closeModal() {
                document.body.removeChild(modal);
                document.body.removeChild(overlay);
            }
        }

        // Initialize diagram
        updateDiagram();

        // Add event listeners to UI controls
        document.getElementById('layoutType').addEventListener('change', updateDiagram);
        document.getElementById('colorBy').addEventListener('change', updateDiagram);
        document.getElementById('filterCategory').addEventListener('change', updateDiagram);
        document.getElementById('search').addEventListener('input', updateDiagram);

        document.getElementById('resetFilters').addEventListener('click', function() {
            document.getElementById('layoutType').value = 'force';
            document.getElementById('colorBy').value = 'category';
            document.getElementById('filterCategory').value = 'all';
            document.getElementById('search').value = '';
            updateDiagram();
        });

        document.getElementById('resetZoom').addEventListener('click', function() {
            d3.select('#chart svg')
                .transition()
                .duration(750)
                .call(d3.zoom().transform, d3.zoomIdentity);
        });

        // Handle window resize
        window.addEventListener('resize', debounce(updateDiagram, 250));

        // Debounce function to limit how often a function is called
        function debounce(fn, delay) {
            let timeout;
            return function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => fn.apply(this, arguments), delay);
            };
        }
    })();
    </script>
</body>
</html>`;
}
```


#### `scripts\visualizations\dependency-manager.js`

```javascript
import fs from "fs";
import path from "path";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

/**
 * A unified manager for dependency visualizations.
 * This script consolidates the process of:
 * 1. Generating dependency data
 * 2. Fixing path aliases and import connections
 * 3. Creating different visualization types
 * 4. Opening the visualizations
 * 5. Analyzing dependencies for important modules
 *
 * It also incorporates functionality from depcruise:safe:
 * - Checking for Graphviz installation
 * - Running appropriate visualizations based on availability
 */

// Configuration
const CONFIG = {
    dataDir: ".depcruise/data",
    outputDir: ".depcruise/interactive",
    graphsDir: ".depcruise/graphs",
    mainDataFile: ".depcruise/data/d3-data.json",
    configFile: "depcruise-config.cjs",
    // Important modules to highlight in visualizations and ensure proper connections
    importantModules: [
        { path: "hooks/useData.ts", alias: "@/hooks/useData" },
        { path: "lib/utils.ts", alias: "@/lib/utils" },
        { path: "lib/hooks", alias: "@/lib/hooks" },
        { path: "components/shared", alias: "@/components/shared" },
        { path: "contexts", alias: "@/contexts" },
    ],
    // Commands that require Graphviz
    graphvizCommands: [
        "depcruise:svg",
        "depcruise:archi",
        "depcruise:archi-interactive",
        "depcruise:advanced-dot",
        "depcruise:hierarchical",
        "depcruise:circular",
        "depcruise:focus",
        "depcruise:clustered",
        "depcruise:interactive-svg",
        "depcruise:multi",
        "depcruise:archi-advanced",
        "depcruise:tech-filter",
    ],
    // Commands that don't require Graphviz
    nonGraphvizCommands: [
        "depcruise:html",
        "depcruise:json",
        "depcruise:interactive",
        "depcruise:d3-data",
        "depcruise:d3-graph",
        "depcruise:circle-packing",
        "depcruise:bubble-chart",
        "depcruise:flow-diagram",
    ],
    visualizations: {
        flowDiagram: {
            script: "scripts/visualizations/create-flow-diagram.js",
            output: ".depcruise/interactive/flow-diagram.html",
        },
        bubbleChart: {
            script: "scripts/visualizations/create-bubble-chart.js",
            output: ".depcruise/interactive/bubble-chart.html",
        },
        d3Graph: {
            script: "scripts/visualizations/create-d3-graph.js",
            output: ".depcruise/interactive/d3-graph.html",
        },
        circlePacking: {
            script: "scripts/visualizations/create-circle-packing.js",
            output: ".depcruise/interactive/circle-packing.html",
        },
    },
};

// Ensure all required directories exist
async function ensureDirectories() {
    console.log("Ensuring directories exist...");

    for (const dir of [CONFIG.dataDir, CONFIG.outputDir, CONFIG.graphsDir]) {
        if (!fs.existsSync(dir)) {
            console.log(`Creating directory: ${dir}`);
            fs.mkdirSync(dir, { recursive: true });
        }
    }
}

/**
 * Checks if Graphviz is installed
 */
async function checkGraphviz() {
    try {
        console.log("Checking for Graphviz installation...");

        // Use 'where dot' for Windows and 'which dot' for Unix-like systems
        const command =
            process.platform === "win32" ? "where dot" : "which dot";

        await execAsync(command);
        console.log(
            "Graphviz (dot) found in PATH. All visualizations are available."
        );
        return true;
    } catch (error) {
        console.warn(
            "\x1b[33m%s\x1b[0m",
            "Graphviz (dot) not found in PATH. Some dependency visualizations will be skipped."
        );
        console.warn(
            "\x1b[33m%s\x1b[0m",
            "Install Graphviz from https://graphviz.org/download/ and add it to your PATH to enable all visualizations."
        );

        await createPlaceholderGraphs();
        return false;
    }
}

/**
 * Creates placeholder SVG files when Graphviz is not available
 */
async function createPlaceholderGraphs() {
    const placeholderSvg = `
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600">
    <rect width="100%" height="100%" fill="#f8f9fa"/>
    <text x="50%" y="45%" text-anchor="middle" font-family="sans-serif" font-size="24" fill="#dc3545">Graphviz not installed</text>
    <text x="50%" y="52%" text-anchor="middle" font-family="sans-serif" font-size="18" fill="#495057">Install Graphviz from graphviz.org and add it to your PATH</text>
    <text x="50%" y="58%" text-anchor="middle" font-family="sans-serif" font-size="16" fill="#6c757d">Then run 'npm run deps:audit' again</text>
</svg>`;

    // Files that would normally be generated by Graphviz
    const files = [
        "dependency-graph.svg",
        "hierarchical-graph.svg",
        "circular-graph.svg",
        "clustered-graph.svg",
        "tech-filtered.svg",
        "enhanced-graph.svg",
    ];

    for (const file of files) {
        const filePath = path.join(CONFIG.graphsDir, file);
        fs.writeFileSync(filePath, placeholderSvg);
        console.log(`Created placeholder for ${file}`);
    }
}

/**
 * Runs a npm command and handles errors
 */
async function runNpmCommand(command) {
    try {
        console.log(`Running: ${command}`);
        const { stdout, stderr } = await execAsync(`npm run ${command}`);
        if (stdout) console.log(stdout);
        if (stderr) console.error(stderr);
        return true;
    } catch (error) {
        console.error(`Error running ${command}:`);
        console.error(error.message);
        return false;
    }
}

// Generate dependency data
async function generateDependencyData() {
    console.log("Generating dependency data...");

    try {
        await execAsync(
            `npx dependency-cruiser --config ${CONFIG.configFile} --include-only "^src" --output-type json src > ${CONFIG.mainDataFile}`
        );
        console.log(`Dependency data generated at: ${CONFIG.mainDataFile}`);
        return true;
    } catch (error) {
        console.error("Error generating dependency data:", error.message);
        return false;
    }
}

/**
 * Fix path aliases and ensure proper connections between modules
 */
async function fixPathAliases() {
    console.log("Fixing path aliases and module connections...");

    try {
        const data = JSON.parse(fs.readFileSync(CONFIG.mainDataFile, "utf8"));

        if (!data.modules || !Array.isArray(data.modules)) {
            console.error("Invalid data format: modules array not found");
            return false;
        }

        console.log(`Found ${data.modules.length} modules in the data`);

        // Create a map of all modules by their source path for easy lookup
        const moduleMap = new Map();
        data.modules.forEach((module) => {
            moduleMap.set(module.source, module);

            // Also map using just the filename part for more flexible matching
            const filename = path.basename(module.source);
            if (!moduleMap.has(filename)) {
                moduleMap.set(filename, module);
            }
        });

        // Track fixed imports
        let totalFixCount = 0;

        // Helper function to resolve a path from @ alias
        const resolveAliasPath = (aliasPath) => {
            if (aliasPath.startsWith("@/")) {
                // Convert @/path to src/path
                return "src" + aliasPath.substring(1);
            }
            return aliasPath;
        };

        // Process each module to fix aliased imports
        data.modules.forEach((module) => {
            if (module.dependencies) {
                // Find dependencies that use aliases
                const aliasDeps = module.dependencies.filter(
                    (dep) =>
                        dep.module.startsWith("@/") &&
                        (!dep.resolved || dep.couldNotResolve)
                );

                // Fix each alias dependency
                aliasDeps.forEach((dep) => {
                    const possibleSourcePath = resolveAliasPath(dep.module);

                    // Look for exact match
                    let targetModule = data.modules.find(
                        (m) => m.source === possibleSourcePath
                    );

                    // Look for match with .ts or .tsx extension
                    if (
                        !targetModule &&
                        !possibleSourcePath.endsWith(".ts") &&
                        !possibleSourcePath.endsWith(".tsx")
                    ) {
                        targetModule = data.modules.find(
                            (m) =>
                                m.source === `${possibleSourcePath}.ts` ||
                                m.source === `${possibleSourcePath}.tsx` ||
                                m.source === `${possibleSourcePath}/index.ts` ||
                                m.source === `${possibleSourcePath}/index.tsx`
                        );
                    }

                    if (targetModule) {
                        console.log(
                            `Fixing import in ${module.source}: ${dep.module} -> ${targetModule.source}`
                        );
                        dep.resolved = targetModule.source;
                        dep.couldNotResolve = false;
                        totalFixCount++;
                    }
                });
            }
        });

        // Check for important modules that should be tracked
        console.log("\nVerifying important module connections...");
        const importantModulesFound = [];
        const importantModulesMissing = [];

        for (const importantModule of CONFIG.importantModules) {
            // Find any module containing the important module path
            const moduleMatches = data.modules.filter((m) =>
                m.source.includes(importantModule.path)
            );

            if (moduleMatches.length > 0) {
                // Find modules importing this important module
                let importingModulesCount = 0;

                data.modules.forEach((module) => {
                    if (module.dependencies) {
                        const relevantDeps = module.dependencies.filter(
                            (dep) =>
                                dep.module.includes(importantModule.alias) ||
                                dep.module.includes(importantModule.path) ||
                                (dep.resolved &&
                                    moduleMatches.some(
                                        (m) => m.source === dep.resolved
                                    ))
                        );

                        relevantDeps.forEach((dep) => {
                            // For important modules, make sure the connection is properly established
                            const targetModule = moduleMatches[0]; // Use the first match
                            if (!dep.resolved || dep.couldNotResolve) {
                                console.log(
                                    `Fixing critical import in ${module.source}: ${dep.module} -> ${targetModule.source}`
                                );
                                dep.resolved = targetModule.source;
                                dep.couldNotResolve = false;
                                totalFixCount++;
                            }
                            importingModulesCount++;
                        });
                    }
                });

                importantModulesFound.push({
                    module: importantModule.path,
                    instances: moduleMatches.length,
                    imported_by: importingModulesCount,
                });
            } else {
                importantModulesMissing.push(importantModule.path);
            }
        }

        // Report findings
        if (importantModulesFound.length > 0) {
            console.log("\nImportant modules found:");
            importantModulesFound.forEach((m) => {
                console.log(
                    `- ${m.module}: ${m.instances} instance(s), imported by ${m.imported_by} module(s)`
                );
            });
        }

        if (importantModulesMissing.length > 0) {
            console.log("\nImportant modules not found:");
            importantModulesMissing.forEach((m) => console.log(`- ${m}`));
        }

        console.log(`\nFixed ${totalFixCount} alias imports in total`);

        // Write the fixed data back to the file
        fs.writeFileSync(CONFIG.mainDataFile, JSON.stringify(data, null, 2));
        console.log("Fixed data written back to original file");

        // Generate a report file with import statistics
        const reportFile = path.join(CONFIG.dataDir, "import-analysis.json");
        const report = {
            timestamp: new Date().toISOString(),
            total_modules: data.modules.length,
            total_fixed_imports: totalFixCount,
            important_modules_found: importantModulesFound,
            important_modules_missing: importantModulesMissing,
        };

        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
        console.log(`Import analysis report written to ${reportFile}`);

        return true;
    } catch (error) {
        console.error("Error fixing path aliases:", error.message);
        console.error(error.stack);
        return false;
    }
}

/**
 * Calculate centrality and impact metrics for each module
 * This identifies the most important modules in the codebase based on their connections
 */
async function calculateCentralityMetrics() {
    console.log("Calculating centrality and impact metrics...");

    try {
        const data = JSON.parse(fs.readFileSync(CONFIG.mainDataFile, "utf8"));

        if (!data.modules || !Array.isArray(data.modules)) {
            console.error("Invalid data format: modules array not found");
            return false;
        }

        console.log(
            `Analyzing ${data.modules.length} modules for centrality metrics...`
        );

        // Create a map of modules by source for quick lookup
        const moduleMap = new Map();
        data.modules.forEach((module) => {
            moduleMap.set(module.source, module);
        });

        // Calculate incoming dependencies (how many modules depend on this one)
        data.modules.forEach((module) => {
            // Initialize metrics
            module.metrics = module.metrics || {};
            module.metrics.incomingDependencies = 0;
            module.metrics.outgoingDependencies = 0;
        });

        // Count dependencies
        data.modules.forEach((module) => {
            if (module.dependencies && Array.isArray(module.dependencies)) {
                // Count outgoing dependencies for this module
                module.metrics.outgoingDependencies =
                    module.dependencies.length;

                // Increment incoming dependencies count for each target module
                module.dependencies.forEach((dep) => {
                    if (dep.resolved && moduleMap.has(dep.resolved)) {
                        const targetModule = moduleMap.get(dep.resolved);
                        targetModule.metrics = targetModule.metrics || {};
                        targetModule.metrics.incomingDependencies =
                            (targetModule.metrics.incomingDependencies || 0) +
                            1;
                    }
                });
            }
        });

        // Calculate additional metrics
        data.modules.forEach((module) => {
            // Stability: ratio of incoming to total dependencies (0-1)
            // 0 = completely unstable, 1 = completely stable
            const totalDeps =
                module.metrics.incomingDependencies +
                module.metrics.outgoingDependencies;

            module.metrics.stability =
                totalDeps > 0
                    ? module.metrics.incomingDependencies / totalDeps
                    : 0;

            // Importance: weighted sum of incoming dependencies and stability
            module.metrics.importance =
                module.metrics.incomingDependencies * 0.7 +
                module.metrics.stability *
                    module.metrics.incomingDependencies *
                    0.3;

            // Impact: estimate of how many modules would be affected by changes
            // Direct incoming dependencies plus second-level impact
            module.metrics.impactScore = module.metrics.incomingDependencies;
        });

        // Calculate second-level impact (modules indirectly affected by changes)
        calculateSecondLevelImpact(data.modules, moduleMap);

        // Identify the most important modules (top 10%)
        const sortedByImportance = [...data.modules].sort(
            (a, b) =>
                (b.metrics?.importance || 0) - (a.metrics?.importance || 0)
        );

        const topModulesCount = Math.max(
            1,
            Math.ceil(sortedByImportance.length * 0.1)
        );
        const topModules = sortedByImportance.slice(0, topModulesCount);

        // Mark top modules
        topModules.forEach((module) => {
            module.metrics.isKeyModule = true;
        });

        // Log insights about the most important modules
        console.log("\nTop important modules:");
        topModules.slice(0, 5).forEach((module) => {
            console.log(
                `- ${
                    module.source
                } (Importance: ${module.metrics.importance.toFixed(
                    2
                )}, Impact: ${module.metrics.impactScore})`
            );
        });

        // Write the enhanced data back to the file
        fs.writeFileSync(CONFIG.mainDataFile, JSON.stringify(data, null, 2));

        // Write a separate metrics file for easier access
        const metricsFile = path.join(CONFIG.dataDir, "module-metrics.json");
        const metricsData = data.modules.map((module) => ({
            source: module.source,
            metrics: module.metrics,
        }));

        fs.writeFileSync(metricsFile, JSON.stringify(metricsData, null, 2));
        console.log(`Module metrics written to ${metricsFile}`);

        return true;
    } catch (error) {
        console.error("Error calculating centrality metrics:", error.message);
        console.error(error.stack);
        return false;
    }
}

/**
 * Calculate second-level impact for all modules
 * This estimates the cascading effect of changes
 */
function calculateSecondLevelImpact(modules, moduleMap) {
    modules.forEach((module) => {
        // Skip if no dependencies
        if (!module.dependencies || !Array.isArray(module.dependencies)) {
            return;
        }

        // For each module this one depends on, add its impact to the second-level score
        let secondLevelImpact = 0;

        module.dependencies.forEach((dep) => {
            if (dep.resolved && moduleMap.has(dep.resolved)) {
                const targetModule = moduleMap.get(dep.resolved);
                const targetImpact =
                    targetModule.metrics?.incomingDependencies || 0;
                secondLevelImpact += targetImpact;
            }
        });

        // Add second-level impact to total impact score
        // Weight second-level impact less than direct impact
        module.metrics.secondLevelImpact = secondLevelImpact;
        module.metrics.impactScore += secondLevelImpact * 0.5;
    });
}

/**
 * Run the original 'depcruise:safe' functionality
 * Runs available visualizations based on Graphviz availability
 */
async function runSafeVisualizations() {
    console.log("Running safe visualizations...");

    try {
        // Check if Graphviz is available
        const hasGraphviz = await checkGraphviz();

        // Run commands that don't require Graphviz
        console.log("\nRunning visualizations that don't require Graphviz...");
        for (const command of CONFIG.nonGraphvizCommands) {
            await runNpmCommand(command);
        }

        // Run commands that require Graphviz if available
        if (hasGraphviz) {
            console.log("\nRunning visualizations that require Graphviz...");
            for (const command of CONFIG.graphvizCommands) {
                await runNpmCommand(command);
            }
        } else {
            console.log("\nSkipping visualizations that require Graphviz...");
            console.log("Install Graphviz to enable all visualizations.");
        }

        // Update the dashboard
        console.log("\nUpdating dashboard...");
        await runNpmCommand("depcruise:dashboard");

        console.log("\nVisualization process complete!");
        return true;
    } catch (error) {
        console.error("Error running safe visualizations:", error.message);
        return false;
    }
}

/**
 * Analyze the dependency data and check for specific files and their connections
 */
async function analyzeDependencies() {
    console.log("Analyzing dependency connections...");

    try {
        if (!fs.existsSync(CONFIG.mainDataFile)) {
            console.error(
                `Dependency data file not found: ${CONFIG.mainDataFile}`
            );
            return false;
        }

        const data = JSON.parse(fs.readFileSync(CONFIG.mainDataFile, "utf8"));

        if (!data.modules || !Array.isArray(data.modules)) {
            console.error("Invalid data format: modules array not found");
            return false;
        }

        // Collect stats on module types
        const stats = {
            total: data.modules.length,
            byType: {
                components: data.modules.filter((m) =>
                    m.source.includes("/components/")
                ).length,
                hooks: data.modules.filter((m) => m.source.includes("/hooks/"))
                    .length,
                pages: data.modules.filter((m) => m.source.includes("/pages/"))
                    .length,
                utils: data.modules.filter((m) => m.source.includes("/utils/"))
                    .length,
                contexts: data.modules.filter((m) =>
                    m.source.includes("/contexts/")
                ).length,
                other: 0,
            },
            mostDependedOn: [],
            mostDependingOn: [],
        };

        // Calculate "other" category
        stats.byType.other =
            stats.total -
            (stats.byType.components +
                stats.byType.hooks +
                stats.byType.pages +
                stats.byType.utils +
                stats.byType.contexts);

        // Count incoming and outgoing dependencies for each module
        const dependencyCounts = data.modules.map((module) => {
            // Count incoming dependencies (modules that depend on this one)
            const incomingCount = data.modules.filter(
                (m) =>
                    m.dependencies &&
                    m.dependencies.some((d) => d.resolved === module.source)
            ).length;

            // Count outgoing dependencies (modules this depends on)
            const outgoingCount = module.dependencies
                ? module.dependencies.length
                : 0;

            return {
                module: module.source,
                incoming: incomingCount,
                outgoing: outgoingCount,
                total: incomingCount + outgoingCount,
            };
        });

        // Sort by most depended on (incoming)
        stats.mostDependedOn = [...dependencyCounts]
            .sort((a, b) => b.incoming - a.incoming)
            .slice(0, 10);

        // Sort by most depending on others (outgoing)
        stats.mostDependingOn = [...dependencyCounts]
            .sort((a, b) => b.outgoing - a.outgoing)
            .slice(0, 10);

        // Generate an analysis report
        const analysisFile = path.join(
            CONFIG.dataDir,
            "dependency-analysis.json"
        );
        fs.writeFileSync(analysisFile, JSON.stringify(stats, null, 2));

        console.log("\nDependency Analysis Summary:");
        console.log(`Total modules: ${stats.total}`);
        console.log("Module types:");
        for (const [type, count] of Object.entries(stats.byType)) {
            console.log(`- ${type}: ${count}`);
        }

        console.log("\nTop 3 most depended on modules:");
        stats.mostDependedOn.slice(0, 3).forEach((item, i) => {
            console.log(
                `${i + 1}. ${path.basename(item.module)} (${
                    item.incoming
                } incoming deps)`
            );
        });

        console.log(`\nFull analysis written to ${analysisFile}`);
        return true;
    } catch (error) {
        console.error("Error analyzing dependencies:", error.message);
        return false;
    }
}

// Generate a specific visualization
async function generateVisualization(visType) {
    console.log(`Generating ${visType} visualization...`);

    const visConfig = CONFIG.visualizations[visType];
    if (!visConfig) {
        console.error(`Unknown visualization type: ${visType}`);
        return false;
    }

    try {
        await execAsync(`node ${visConfig.script}`);
        console.log(`Visualization created at: ${visConfig.output}`);
        return true;
    } catch (error) {
        console.error(
            `Error generating ${visType} visualization:`,
            error.message
        );
        return false;
    }
}

// Open a visualization in the default browser
async function openVisualization(visType) {
    console.log(`Opening ${visType} visualization...`);

    const visConfig = CONFIG.visualizations[visType];
    if (!visConfig) {
        console.error(`Unknown visualization type: ${visType}`);
        return false;
    }

    if (!fs.existsSync(visConfig.output)) {
        console.error(`Visualization file not found: ${visConfig.output}`);
        console.log(
            "Try generating the visualization first with: npm run deps generate " +
                visType
        );
        return false;
    }

    try {
        // Use PowerShell to open the file
        await execAsync(
            `powershell -Command "Start-Process (Join-Path $PWD '${visConfig.output}')"`,
            { shell: true }
        );
        console.log(`Opened ${visType} visualization in browser`);
        return true;
    } catch (error) {
        console.error(`Error opening ${visType} visualization:`, error.message);
        return false;
    }
}

// Generate all visualizations
async function generateAllVisualizations() {
    console.log("Generating all visualizations...");

    let allSuccess = true;

    for (const visType in CONFIG.visualizations) {
        const success = await generateVisualization(visType);
        if (!success) {
            allSuccess = false;
            console.warn(`Failed to generate ${visType} visualization`);
        }
    }

    return allSuccess;
}

// Create dashboard that links to all visualizations
async function createDashboard() {
    console.log("Creating dashboard...");

    try {
        await execAsync(
            "node scripts/visualizations/create-dependency-dashboard.js"
        );
        console.log("Dashboard created at .depcruise/index.html");
        return true;
    } catch (error) {
        console.error("Error creating dashboard:", error.message);
        return false;
    }
}

// Open the dashboard
async function openDashboard() {
    console.log("Opening dashboard...");

    try {
        await execAsync(
            "powershell -Command \"Start-Process (Join-Path $PWD '.depcruise/index.html')\"",
            { shell: true }
        );
        console.log("Opened dashboard in browser");
        return true;
    } catch (error) {
        console.error("Error opening dashboard:", error.message);
        return false;
    }
}

/**
 * Generate a complete dependency audit of the project's dependencies
 */
async function generateDependencyAudit() {
    console.log("Generating complete dependency audit...");

    // First, make sure we have the latest data
    await generateDependencyData();

    // Fix any path aliases and connections
    await fixPathAliases();

    // Calculate centrality metrics for all modules
    await calculateCentralityMetrics();

    // Analyze the dependency data
    await analyzeDependencies();

    // Check for Graphviz and generate visualizations accordingly
    const hasGraphviz = await checkGraphviz();

    // Generate D3-based visualizations (don't need Graphviz)
    for (const visType in CONFIG.visualizations) {
        await generateVisualization(visType);
    }

    // Generate GraphViz-based visualizations if available
    if (hasGraphviz) {
        console.log("\nGenerating Graphviz-based visualizations...");
        for (const command of CONFIG.graphvizCommands) {
            await runNpmCommand(command);
        }
    }

    // Create the dashboard
    await createDashboard();

    console.log("Dependency audit completed! View the dashboard for results.");
    return true;
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    const target = args[1];

    // Make sure directories exist
    await ensureDirectories();

    switch (command) {
        case "generate":
            if (target === "all") {
                await generateDependencyData();
                await fixPathAliases();
                await calculateCentralityMetrics();
                await generateAllVisualizations();
                await createDashboard();
            } else if (target && CONFIG.visualizations[target]) {
                await generateDependencyData();
                await fixPathAliases();
                await calculateCentralityMetrics();
                await generateVisualization(target);
            } else {
                console.error(
                    "Usage: node dependency-manager.js generate [all|flowDiagram|bubbleChart|d3Graph|circlePacking]"
                );
            }
            break;

        case "open":
            if (target === "dashboard") {
                await openDashboard();
            } else if (target && CONFIG.visualizations[target]) {
                await openVisualization(target);
            } else {
                console.error(
                    "Usage: node dependency-manager.js open [dashboard|flowDiagram|bubbleChart|d3Graph|circlePacking]"
                );
            }
            break;

        case "quick":
            if (target && CONFIG.visualizations[target]) {
                await generateDependencyData();
                await fixPathAliases();
                await calculateCentralityMetrics();
                await generateVisualization(target);
                await openVisualization(target);
            } else {
                console.error(
                    "Usage: node dependency-manager.js quick [flowDiagram|bubbleChart|d3Graph|circlePacking]"
                );
            }
            break;

        case "dashboard":
            await generateDependencyData();
            await fixPathAliases();
            await calculateCentralityMetrics();
            await analyzeDependencies();
            await generateAllVisualizations();
            await createDashboard();
            await openDashboard();
            break;

        case "analyze":
            await generateDependencyData();
            await fixPathAliases();
            await calculateCentralityMetrics();
            await analyzeDependencies();
            console.log(
                "Run 'npm run deps:all' to generate visualizations based on this analysis."
            );
            break;

        case "audit":
            await generateDependencyAudit();
            break;

        case "safe":
            // Reimplementation of the original depcruise:safe functionality
            await runSafeVisualizations();
            break;

        default:
            console.log("Dependency Visualization Manager");
            console.log("================================");
            console.log("Available commands:");
            console.log(
                "  generate all                   - Generate all visualizations and dashboard"
            );
            console.log(
                "  generate [type]                - Generate a specific visualization"
            );
            console.log(
                "  open dashboard                 - Open the dashboard"
            );
            console.log(
                "  open [type]                    - Open a specific visualization"
            );
            console.log(
                "  quick [type]                   - Generate and open a specific visualization"
            );
            console.log(
                "  dashboard                      - Generate all and open dashboard"
            );
            console.log(
                "  analyze                        - Analyze dependencies without visualizing"
            );
            console.log(
                "  audit                          - Run complete dependency audit with all visualizations"
            );
            console.log(
                "  safe                           - Original depcruise:safe functionality (Graphviz-aware)"
            );
            console.log("");
            console.log("Visualization types:");
            console.log(
                "  flowDiagram                    - Dependency flow diagram"
            );
            console.log(
                "  bubbleChart                    - Bubble chart visualization"
            );
            console.log(
                "  d3Graph                        - Interactive D3 graph"
            );
            console.log(
                "  circlePacking                  - Circle packing visualization"
            );
            break;
    }
}

main().catch((error) => {
    console.error("Unhandled error:", error);
    process.exit(1);
});
```


#### `scripts\visualizations\fix-depcruise-paths.js`

```javascript
/**
 * Fix Dependency Cruise Paths
 *
 * This script corrects paths in the dependency cruise output to ensure
 * they work correctly in the visualization dashboard.
 */

import fs from "fs";
import path from "path";

/**
 * Normalize file paths to be consistent across different operating systems
 * @param {string} filePath - The file path to normalize
 * @returns {string} The normalized file path
 */
function normalizePath(filePath) {
    return filePath.replace(/\\/g, "/");
}

/**
 * Fix paths in the specified dependency cruise JSON file
 * @param {string} jsonFilePath - Path to the dependency cruise JSON file
 */
function fixDependencyPaths(jsonFilePath) {
    try {
        // Read the file
        const jsonData = fs.readFileSync(jsonFilePath, "utf8");
        const data = JSON.parse(jsonData);

        // Fix paths in the modules array
        if (data.modules && Array.isArray(data.modules)) {
            data.modules.forEach((module) => {
                if (module.source) {
                    module.source = normalizePath(module.source);
                }

                if (module.dependencies && Array.isArray(module.dependencies)) {
                    module.dependencies.forEach((dependency) => {
                        if (dependency.resolved) {
                            dependency.resolved = normalizePath(
                                dependency.resolved
                            );
                        }
                    });
                }
            });
        }

        // Write the fixed data back to the file
        fs.writeFileSync(jsonFilePath, JSON.stringify(data, null, 2), "utf8");
        console.log(`Successfully fixed paths in ${jsonFilePath}`);
    } catch (error) {
        console.error(`Error fixing paths in ${jsonFilePath}:`, error);
        process.exit(1);
    }
}

/**
 * Main function to process command line arguments and run the path fixing
 */
function main() {
    // Get the JSON file path from command line arguments
    const jsonFilePath = process.argv[2];

    if (!jsonFilePath) {
        console.error(
            "Error: Please provide a path to the dependency cruise JSON file"
        );
        console.error(
            "Usage: node fix-depcruise-paths.js path/to/dependency-cruise-output.json"
        );
        process.exit(1);
    }

    if (!fs.existsSync(jsonFilePath)) {
        console.error(`Error: File not found: ${jsonFilePath}`);
        process.exit(1);
    }

    fixDependencyPaths(jsonFilePath);
}

// Run the main function
main();
```


#### `scripts\visualizations\fix-missing-files.js`

```javascript
import fs from "fs";
import path from "path";

// Root directory for visualizations
const DEPCRUISE_DIR = ".depcruise";

// Ensure all required directories exist
const directories = [
    path.join(DEPCRUISE_DIR, "graphs"),
    path.join(DEPCRUISE_DIR, "interactive"),
    path.join(DEPCRUISE_DIR, "data"),
];

// Create directories if they don't exist
directories.forEach((dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`Created directory: ${dir}`);
    }
});

// List of missing files in interactive directory
const interactiveFiles = [
    "d3-graph.html",
    "dependency-graph.html",
    "high-level-dependencies.html",
    "archi-interactive.html",
    "validation.html",
    "circle-packing.html",
    "bubble-chart.html",
];

// List of missing files in graphs directory
const graphsFiles = [
    "dependency-graph.svg",
    "hierarchical-graph.svg",
    "circular-graph.svg",
    "clustered-graph.svg",
    "tech-filtered.svg",
];

// List of missing files in data directory
const dataFiles = ["dependency-data.json", "d3-data.json"];

// Create placeholder HTML file
function createPlaceholderHtml(filePath, title) {
    const content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin-top: 50px;
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <p>This visualization can be generated by running the appropriate dependency-cruiser commands.</p>
        <p>Run <code>npm run depcruise:safe</code> to generate all visualizations.</p>
        <a href="../index.html" class="btn">Back to Dashboard</a>
    </div>
</body>
</html>`;

    fs.writeFileSync(filePath, content);
    console.log(`Created placeholder HTML: ${filePath}`);
}

// Create placeholder SVG file
function createPlaceholderSvg(filePath, title) {
    const content = `
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600">
    <rect width="100%" height="100%" fill="#f8f9fa"/>
    <text x="50%" y="40%" text-anchor="middle" font-family="sans-serif" font-size="24" fill="#3498db">${title}</text>
    <text x="50%" y="50%" text-anchor="middle" font-family="sans-serif" font-size="18" fill="#495057">Visualization Placeholder</text>
    <text x="50%" y="58%" text-anchor="middle" font-family="sans-serif" font-size="16" fill="#6c757d">Run 'npm run depcruise:safe' to generate all visualizations</text>
</svg>`;

    fs.writeFileSync(filePath, content);
    console.log(`Created placeholder SVG: ${filePath}`);
}

// Create placeholder JSON file
function createPlaceholderJson(filePath) {
    const content = `{
  "modules": [
    {
      "source": "src/index.tsx",
      "dependencies": [
        {
          "resolved": "src/App.tsx",
          "type": "local",
          "module": "./App"
        }
      ]
    },
    {
      "source": "src/App.tsx",
      "dependencies": []
    }
  ]
}`;

    fs.writeFileSync(filePath, content);
    console.log(`Created placeholder JSON: ${filePath}`);
}

// Generate all missing HTML files
interactiveFiles.forEach((file) => {
    const filePath = path.join(DEPCRUISE_DIR, "interactive", file);
    if (!fs.existsSync(filePath)) {
        const title =
            file
                .replace(".html", "")
                .split("-")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ") + " Visualization";
        createPlaceholderHtml(filePath, title);
    }
});

// Generate all missing SVG files
graphsFiles.forEach((file) => {
    const filePath = path.join(DEPCRUISE_DIR, "graphs", file);
    if (!fs.existsSync(filePath)) {
        const title = file
            .replace(".svg", "")
            .split("-")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
        createPlaceholderSvg(filePath, title);
    }
});

// Generate all missing JSON files
dataFiles.forEach((file) => {
    const filePath = path.join(DEPCRUISE_DIR, "data", file);
    if (!fs.existsSync(filePath)) {
        createPlaceholderJson(filePath);
    }
});

console.log("All missing files have been created!");
console.log("Now running depcruise:fix-paths to update path references...");

// Create d3-data.json with more detailed placeholder data
const d3DataPath = path.join(DEPCRUISE_DIR, "data", "d3-data.json");
if (!fs.existsSync(d3DataPath)) {
    // Generate more detailed data for d3 visualizations
    const d3Content = {
        modules: [
            {
                source: "src/index.tsx",
                dependencies: [
                    {
                        resolved: "src/App.tsx",
                        type: "local",
                        module: "./App",
                    },
                    {
                        resolved: "src/styles/global.css",
                        type: "local",
                        module: "./styles/global.css",
                    },
                ],
            },
            {
                source: "src/App.tsx",
                dependencies: [
                    {
                        resolved: "src/components/Header.tsx",
                        type: "local",
                        module: "./components/Header",
                    },
                    {
                        resolved: "src/components/Footer.tsx",
                        type: "local",
                        module: "./components/Footer",
                    },
                ],
            },
            {
                source: "src/components/Header.tsx",
                dependencies: [
                    {
                        resolved: "src/utils/helpers.ts",
                        type: "local",
                        module: "../utils/helpers",
                    },
                ],
            },
            {
                source: "src/components/Footer.tsx",
                dependencies: [],
            },
            {
                source: "src/utils/helpers.ts",
                dependencies: [],
            },
        ],
    };

    fs.writeFileSync(d3DataPath, JSON.stringify(d3Content, null, 2));
    console.log(`Created detailed d3-data.json: ${d3DataPath}`);
}
```


#### `scripts\visualizations\run-visualizations.js`

```javascript
import { exec } from "child_process";
import { checkGraphviz } from "./check-graphviz.js";

// Commands that require Graphviz (dot, circo, fdp, etc.)
const graphvizCommands = [
    "depcruise:svg",
    "depcruise:archi",
    "depcruise:archi-interactive",
    "depcruise:advanced-dot",
    "depcruise:hierarchical",
    "depcruise:circular",
    "depcruise:focus",
    "depcruise:clustered",
    "depcruise:interactive-svg",
    "depcruise:multi",
    "depcruise:archi-advanced",
    "depcruise:tech-filter",
];

// Commands that don't require Graphviz
const nonGraphvizCommands = [
    "depcruise:html",
    "depcruise:json",
    "depcruise:interactive",
    "depcruise:d3-data",
    "depcruise:d3-graph",
    "depcruise:circle-packing",
    "depcruise:bubble-chart",
    "depcruise:flow-diagram",
];

/**
 * Execute a shell command and return a promise
 */
function runCommand(command) {
    return new Promise((resolve, reject) => {
        console.log(`Running: ${command}`);
        exec(`npm run ${command}`, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error running ${command}:`);
                console.error(error.message);
                return resolve(false); // Continue with other commands
            }
            if (stdout) console.log(stdout);
            if (stderr) console.error(stderr);
            resolve(true);
        });
    });
}

/**
 * Run visualizations based on Graphviz availability
 */
async function runVisualizations() {
    try {
        console.log("Checking for Graphviz installation...");
        const hasGraphviz = await checkGraphviz();

        // Run commands that don't require Graphviz
        console.log("\nRunning visualizations that don't require Graphviz...");
        for (const command of nonGraphvizCommands) {
            await runCommand(command);
        }

        // Run commands that require Graphviz if available
        if (hasGraphviz) {
            console.log("\nRunning visualizations that require Graphviz...");
            for (const command of graphvizCommands) {
                await runCommand(command);
            }
        } else {
            console.log("\nSkipping visualizations that require Graphviz...");
            console.log("Install Graphviz to enable all visualizations.");
        }

        // Update the dashboard
        console.log("\nUpdating dashboard...");
        await runCommand("depcruise:dashboard");

        console.log("\nVisualization process complete!");
    } catch (error) {
        console.error("An error occurred:", error);
    }
}

// Run the script
runVisualizations();
```
