# 'https://chatgpt.com/c/67cda770-dc8c-8008-9cbc-7eca3b375202'

```
project-root/
└─ src/
   ├─ components/
   │   ├─ ui/
   │   │   ├─ Button.tsx
   │   │   ├─ Card.tsx
   │   │   └─ ...
   │   ├─ layout/
   │   │   ├─ Header.tsx
   │   │   ├─ Footer.tsx
   │   │   └─ MainLayout.tsx
   │   └─ shared/
   │       ├─ Carousel.tsx
   │       └─ ...
   │
   ├─ features/
   │   ├─ home/
   │   │   ├─ HomeHero.tsx
   │   │   ├─ SeasonalCarousel.tsx
   │   │   └─ ...
   │   ├─ services/
   │   │   ├─ ServicesHero.tsx
   │   │   ├─ ServiceCard.tsx
   │   │   └─ ...
   │   ├─ projects/
   │   │   ├─ ProjectsGrid.tsx
   │   │   ├─ ProjectFilter.tsx
   │   │   └─ ...
   │   ├─ about/
   │   │   ├─ AboutHero.tsx
   │   │   ├─ TeamMemberCard.tsx
   │   │   └─ ...
   │   ├─ contact/
   │   │   ├─ ContactForm.tsx
   │   │   ├─ ContactHero.tsx
   │   │   └─ ...
   │   └─ testimonials/
   │       ├─ TestimonialList.tsx
   │       └─ ...
   │
   ├─ data/
   │   ├─ services.json
   │   ├─ projects.json
   │   ├─ testimonials.json
   │   ├─ seasons.ts
   │   └─ ...
   │
   ├─ hooks/
   │   ├─ useMediaQuery.ts
   │   ├─ useScrollPosition.ts
   │   ├─ useSeasonalContent.ts
   │   └─ ...
   │
   ├─ context/
   │   ├─ SeasonalContext.tsx
   │   └─ ...
   │
   ├─ pages/               // (Next.js example)
   │   ├─ index.tsx
   │   ├─ kontakt.tsx
   │   ├─ hvem-er-vi.tsx
   │   ├─ hva.tsx
   │   ├─ prosjekter/
   │   │   ├─ index.tsx
   │   │   └─ [id].tsx
   │   ├─ tjenester/
   │   │   ├─ index.tsx
   │   │   └─ [id].tsx
   │   ├─ kundehistorier.tsx
   │   ├─ _app.tsx
   │   └─ _document.tsx
   │
   ├─ styles/
   │   ├─ globals.css
   │   └─ tailwind.css
   │
   ├─ utils/
   │   ├─ seoHelpers.ts
   │   ├─ stringHelpers.ts
   │   └─ ...
   │
   └─ types/
       ├─ Service.ts
       ├─ Project.ts
       ├─ Testimonial.ts
       └─ index.ts
```
