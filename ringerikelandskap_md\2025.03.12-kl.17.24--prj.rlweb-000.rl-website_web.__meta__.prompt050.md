the enabledragbehaviour was a great addition, please build on that:

    ### 1. Performance Optimization

    ```js
    // Current approach
    function forceDirectedTick() {
      links.attr("d", d => {
        // Generate path for every link on every tick
        const source = typeof d.source === 'object' ? d.source : graphData.nodes.find(n => n.id === d.source);
        const target = typeof d.target === 'object' ? d.target : graphData.nodes.find(n => n.id === d.target);
        if (source && target) {
          return `M${source.x},${source.y} ${target.x},${target.y}`;
        }
        return "";
      });
      nodes.attr("transform", d => `translate(${d.x || 0},${d.y || 0})`);
    }

    // Enhanced approach - simple performance optimization
    function enhancedTick() {
      // Batch multiple simulation steps before updating DOM
      for (let i = 0; i  {
        const source = d.source, target = d.target;
        return `M${source.x},${source.y} ${target.x},${target.y}`;
      });
      nodes.attr("transform", d => `translate(${d.x || 0},${d.y || 0})`);
    }
    ```

    ### 2. Enhanced Node Highlighting

    Add this to the existing code without architectural changes:

    ```js
    // Add to existing event handlers for nodes
    nodes.on("click", (event, d) => {
      // First, reset all nodes and links
      nodes.select("circle").style("stroke", "#fff").style("stroke-width", 1.5);
      links.style("stroke", "#999").style("stroke-opacity", 0.4);

      // Highlight the selected node
      d3.select(event.currentTarget).select("circle")
        .style("stroke", "#ff9500")
        .style("stroke-width", 3);

      // Highlight connected links and nodes
      links.each(function(link) {
        if (link.source.id === d.id || link.target.id === d.id) {
          d3.select(this)
            .style("stroke", "#ff9500")
            .style("stroke-opacity", 0.8)
            .style("stroke-width", 2);

          // Also highlight connected nodes
          if (link.source.id === d.id) {
            nodes.filter(n => n.id === link.target.id)
              .select("circle")
              .style("stroke", "#ff9500")
              .style("stroke-width", 2);
          } else {
            nodes.filter(n => n.id === link.source.id)
              .select("circle")
              .style("stroke", "#ff9500")
              .style("stroke-width", 2);
          }
        }
      });
    });
    ```

    ### 3. Smoother Animations

    ```js
    // Add to the existing implementation
    function createHulls(groupBy) {
      // Existing hull creation code...

      // Add transition for smooth appearance
      hullGroup.selectAll(".hull")
        .style("opacity", 0)
        .transition()
        .duration(500)
        .style("opacity", 1);
    }
    ```
