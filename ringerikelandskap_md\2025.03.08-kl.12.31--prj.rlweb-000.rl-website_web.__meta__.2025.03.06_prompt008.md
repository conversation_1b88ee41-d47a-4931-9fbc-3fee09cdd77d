please rephrase the following markdown block to match the layout of the website (attached images):

    ```
    # Context
    You are an expert UX designer. Your role is to work with the product manager and the product owner to create a UI design document. This UI design document will be in markdown format. It will be used by developers and other large language models to create the best user interface possible.

    ---

    # Inputs:
    We need to know what we’re designing. The first step in any design is to understand the user’s input. That’s what we do in the UI design doc.
    1. Product Requirements Document
    3. User Input

    ---

    # Instructions

    1. Process the product input documents if one is not provided ask for one.
    2. Ask questions about the user persona if it's unclear to you.
    3. Generate 3 options for user interface designs that might suit the persona. Don't use code this is a natural language description.
    4. Ask the product owner to confirm which one they like or amendments they have.
    5. Show the final user interface design plan that is easy to read and follow.
    6. Proceed to generate the final User Interface Design Document. Use only basic markdown.

    ---

    # Headings to be included

    **Core Components:**
    - Expansive content input area with image support
    - Interactive preview cards that users can click to edit
    - A top navigation bar for publishing controls and account management

    **Interaction Patterns:**
    - Modal dialogs for detailed content editing
    - Interactive cards that update in real time
    - Hover effects and clear visual cues for actionable items

    **Visual Design Elements & Color Scheme:**
    - Dark background with bold accent colors (e.g., electric blue or bright orange)
    - Subtle gradients and shadow effects to create depth
    - Emphasis on visual flair while maintaining clarity

    **Typography:**
    - Modern sans-serif fonts with varying weights to establish hierarchy

    **Accessibility:**
    - Options for a high-contrast mode
    - Screen reader-friendly components and distinct focus states

    ---

    ### **Option 3: Card-based Modular Layout**

    - **Layout Structure:**
      A modular, card-driven interface:
      - **Dashboard View:** A grid of cards representing each content idea (with status indicators such as draft, pending, or published)
      - **Content Creation:** A dedicated modal or separate screen for composing and editing new content

    - **Core Components:**
      - Cards for individual content pieces
      - A prominent modal for content creation and editing
      - A unified publishing section with clear account integration

    - **Interaction Patterns:**
      - Drag-and-drop organization of cards
      - Contextual menus for quick actions (e.g., edit, delete, publish)
      - Modal pop-ups for detailed content management

    - **Visual Design Elements & Color Scheme:**
      - Neutral base colors with subtle shadows and borders to differentiate cards
      - Strategic use of accent colors to indicate status (e.g., green for published, amber for drafts)
      - A balance of professional aesthetics with modern touches

    - **Typography:**
      - Professional sans-serif fonts that convey clarity and business-readiness

    - **Accessibility:**
      - Scalable text and color-blind friendly palettes
      - Intuitive navigation with clear, accessible touchpoints

    ---

    **Next Steps:**
    Please review these three options and let me know:
    - Which option best aligns with your vision for Levercast?
    - If you have any amendments or additional preferences regarding the user persona, branding, or layout.

    Once I have your feedback, I will proceed to generate the final User Interface Design Document in markdown format with the following sections:
    - Layout Structure
    - Core Components
    - Interaction Patterns
    - Visual Design Elements & Color Scheme
    - Typography
    - Accessibility

    Looking forward to your feedback!
    ```
