# Dir `docs`

*Files marked `[-]` are shown in structure but not included in content.

### File Structure

```
├── AI-README.md
├── README.md
├── rl-website-initial-notes.md
├── 00-documentation-index.md
├── 01-project-state
│   ├── 01-current-codebase-reality.md
│   ├── 02-technical-debt-inventory.md
│   └── 03-company-information.md
├── 02-codebase-map
│   ├── 01-directory-structure.md
│   ├── 02-architecture-overview.md
│   └── 03-component-hierarchy.md
├── 03-implementation-patterns
│   ├── 01-current-patterns.md
│   ├── 02-component-patterns.md
│   └── 03-target-patterns.md
├── 04-visual-systems
│   ├── 01-screenshot-infrastructure.md
│   ├── 02-design-guidelines.md
│   └── 03-styling-approach.md
└── 05-development-workflow
    ├── 01-environment-setup.md
    ├── 02-optimization-guide.md
    └── 03-accessibility-guide.md
```

#### `AI-README.md`

```markdown
# AI ASSISTANT GUIDE TO RINGERIKE LANDSKAP WEBSITE

## Table of Contents

-   [Critical Context](#critical-context-please-read-first)
-   [Project Overview](#project-overview)
-   [Key Systems](#key-systems)
-   [Documentation Structure](#documentation-structure)
-   [Known Limitations and Challenges](#known-limitations-and-challenges)
-   [Recommended Reading Order](#recommended-reading-order)
-   [Caution Areas](#caution-areas)
-   [Getting Started](#getting-started)
-   [Screenshot Reference](#screenshot-reference)
-   [Core Documentation Files](#core-documentation-files)
-   [Documentation Index](#documentation-index)

## CRITICAL CONTEXT (PLEASE READ FIRST)

This project is an in-development website for Ringerike Landskap AS that has evolved into a complex codebase. The documentation in this directory serves two purposes:

1. To accurately map the CURRENT state of the codebase (not an idealized version)
2. To establish a clear direction for future development

**See also**: [Current Codebase Reality](./01-project-state/01-current-codebase-reality.md), [Documentation Index](./00-documentation-index.md)

## PROJECT OVERVIEW

Ringerike Landskap AS is a landscaping and machine-contracting company based in Hole, Norway, offering eight core services including stonework, planting, steel installations, and retaining walls. This website is designed to showcase their services, projects, and expertise while adapting seasonally to highlight relevant offerings.

The website is built with:

-   React
-   TypeScript
-   Vite
-   Tailwind CSS
-   React Router
-   React Helmet for SEO management

**See also**: [Company Information](./01-project-state/03-company-information.md), [Initial Project Notes](./rl-website-initial-notes.md)

## KEY SYSTEMS

The project consists of four integrated systems:

1. **Website Application**: React/TypeScript site built with Vite, React Router, and Tailwind CSS
2. **Visual Documentation System**: Integrated screenshot capture system for tracking visual changes
3. **AI Development Tools**: Specialized infrastructure for AI-assisted development
4. **Content Management**: Typed content with seasonal adaptation

**See also**: [Architecture Overview](./02-codebase-map/02-architecture-overview.md), [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## DOCUMENTATION STRUCTURE

The documentation is organized into five main sections:

-   **01-project-state/**: Current state documentation

    -   [01-current-codebase-reality.md](./01-project-state/01-current-codebase-reality.md) - Start here
    -   [02-technical-debt-inventory.md](./01-project-state/02-technical-debt-inventory.md) - Issues to address
    -   [03-company-information.md](./01-project-state/03-company-information.md) - Business context

-   **02-codebase-map/**: Structure and relationships

    -   [01-directory-structure.md](./02-codebase-map/01-directory-structure.md) - File organization
    -   [02-architecture-overview.md](./02-codebase-map/02-architecture-overview.md) - System architecture
    -   [03-component-hierarchy.md](./02-codebase-map/03-component-hierarchy.md) - Component relationships

-   **03-implementation-patterns/**: Code patterns

    -   [01-current-patterns.md](./03-implementation-patterns/01-current-patterns.md) - Existing patterns
    -   [02-component-patterns.md](./03-implementation-patterns/02-component-patterns.md) - Component details
    -   [03-target-patterns.md](./03-implementation-patterns/03-target-patterns.md) - Target patterns

-   **04-visual-systems/**: Visual documentation

    -   [01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md) - Critical system
    -   [02-design-guidelines.md](./04-visual-systems/02-design-guidelines.md) - Design principles
    -   [03-styling-approach.md](./04-visual-systems/03-styling-approach.md) - CSS approach

-   **05-development-workflow/**: Development process
    -   [01-environment-setup.md](./05-development-workflow/01-environment-setup.md) - Development environment
    -   [02-optimization-guide.md](./05-development-workflow/02-optimization-guide.md) - Performance
    -   [03-accessibility-guide.md](./05-development-workflow/03-accessibility-guide.md) - Accessibility

**See also**: [Complete Documentation Index](./00-documentation-index.md)

## KNOWN LIMITATIONS AND CHALLENGES

1. **Component Structure**: Inconsistent component structures exist
2. **State Management**: The implementation uses various state management approaches
3. **SEO Optimization**: The site needs further optimization for search engines
4. **Seasonal Content**: Website content should adapt to current seasons
5. **Performance**: Several areas need performance improvements

**See also**: [Technical Debt Inventory](./01-project-state/02-technical-debt-inventory.md)

## RECOMMENDED READING ORDER

When engaging with this codebase for the first time, AI assistants should follow this reading path:

1. First, read `rl-website-initial-notes.md` to understand the complete project background, company details, and website requirements.
2. Next, read the files in the `01-project-state/` folder to understand the current state of the project
3. Continue with the `02-codebase-map/` to understand the structure
4. Then review `03-implementation-patterns/` to learn current and target patterns
5. For visual understanding, check `04-visual-systems/`
6. Finally, understand the development process from `05-development-workflow/`

**See also**: [Documentation Index](./00-documentation-index.md) for quick navigation links

## CAUTION AREAS

When modifying this codebase, be especially careful with:

1. **Screenshot generation system** - This is essential for development workflow

    - See [04-visual-systems/01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md)
    - Never modify core screenshot scripts without careful testing
    - Don't delete the `.ai-analysis` or `screenshots` directories

2. **Content data structures** - These must maintain their current shape

    - Content is defined in `src/data/*.ts` files
    - Maintain existing type structures and relationships

3. **Routing structure** - Navigation patterns should remain consistent
    - Routes are defined in App.tsx
    - Norwegian route names should be preserved

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md), [Architecture Overview](./02-codebase-map/02-architecture-overview.md)

## GETTING STARTED

For quick setup:

```bash
npm install    # Install dependencies
npm run dev    # Start development server
npm run dev:ai # Start development with auto-screenshots
```

**See also**: [Environment Setup](./05-development-workflow/01-environment-setup.md)

## SCREENSHOT REFERENCE

To view the current visual state of the website, reference the latest screenshots at:

```
/.ai-analysis/latest/
```

These images are crucial for understanding the current visual state when making changes.

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## CORE DOCUMENTATION FILES

-   **[Documentation Index](./00-documentation-index.md)**: Complete overview of all documentation
-   **[README.md](./README.md)**: Main README file for human developers
-   **[AI-README.md](./AI-README.md)**: This file, specifically for AI assistants
-   **[Initial Project Notes](./rl-website-initial-notes.md)**: Initial project documentation with company information, website purpose, SEO strategy, and detailed requirements for the website.

## DOCUMENTATION INDEX

For a complete overview of all documentation and cross-referenced topics, see the [Documentation Index](./00-documentation-index.md).
```

#### `README.md`

```markdown
# Ringerike Landskap Website Documentation

This directory contains comprehensive documentation for the Ringerike Landskap website. The documentation is organized into logical sections to provide both high-level overviews and detailed technical recommendations.

## Table of Contents

-   [Documentation Overview](#documentation-overview)
-   [Key Files](#key-files)
-   [Project Documentation Folders](#project-documentation-folders)
-   [Key Systems](#key-systems)
-   [Using This Documentation](#using-this-documentation)
    -   [For Developers](#for-developers)
    -   [For AI Assistants](#for-ai-assistants)
-   [Screenshots](#screenshots)
-   [Contributing to Documentation](#contributing-to-documentation)
-   [Documentation Index](#documentation-index)

## Documentation Overview

The documentation is structured to support both human developers and AI assistants in understanding and working with the Ringerike Landskap website codebase. We've created a [Documentation Index](./00-documentation-index.md) that provides a comprehensive overview of all documentation with topical cross-references.

## Key Files

-   **[Documentation Index](./00-documentation-index.md)**: Comprehensive index of all documentation with cross-references
-   **[AI-README.md](./AI-README.md)**: Entry point for AI assistants with comprehensive context about the project, its architecture, and development patterns.
-   **[rl-website-initial-notes.md](./rl-website-initial-notes.md)**: Initial project documentation that provides detailed information about the company, target audience, website purpose, SEO strategy, and technical requirements. This document serves as the foundational brief for the entire project.

## Project Documentation Folders

1. **01-project-state/**: Documentation about the current state of the project

    - [01-current-codebase-reality.md](./01-project-state/01-current-codebase-reality.md): Assessment of the current codebase
    - [02-technical-debt-inventory.md](./01-project-state/02-technical-debt-inventory.md): Inventory of technical debt and issues
    - [03-company-information.md](./01-project-state/03-company-information.md): Information about Ringerike Landskap

2. **02-codebase-map/**: Documentation mapping the structure and relationships in the codebase

    - [01-directory-structure.md](./02-codebase-map/01-directory-structure.md): Explanation of the directory structure
    - [02-architecture-overview.md](./02-codebase-map/02-architecture-overview.md): Overview of the architecture
    - [03-component-hierarchy.md](./02-codebase-map/03-component-hierarchy.md): Hierarchy of components

3. **03-implementation-patterns/**: Documentation of current and target coding patterns

    - [01-current-patterns.md](./03-implementation-patterns/01-current-patterns.md): Current implementation patterns
    - [02-component-patterns.md](./03-implementation-patterns/02-component-patterns.md): Component implementation details
    - [03-target-patterns.md](./03-implementation-patterns/03-target-patterns.md): Target implementation patterns

4. **04-visual-systems/**: Documentation of visual systems

    - [01-screenshot-infrastructure.md](./04-visual-systems/01-screenshot-infrastructure.md): Infrastructure for capturing screenshots
    - [02-design-guidelines.md](./04-visual-systems/02-design-guidelines.md): Design guidelines
    - [03-styling-approach.md](./04-visual-systems/03-styling-approach.md): Approach to styling

5. **05-development-workflow/**: Documentation of development processes and requirements
    - [01-environment-setup.md](./05-development-workflow/01-environment-setup.md): Setting up the development environment
    - [02-optimization-guide.md](./05-development-workflow/02-optimization-guide.md): Performance optimization strategies
    - [03-accessibility-guide.md](./05-development-workflow/03-accessibility-guide.md): Accessibility best practices

**See also**: [Complete Documentation Index](./00-documentation-index.md) for topic-based cross-references

## Key Systems

The website is built around several integrated systems:

1. **Website Application**: A React-based frontend application utilizing TypeScript, Vite, Tailwind CSS, and React Router to create a fast, responsive, and user-friendly website.

2. **Visual Documentation System**: A system for capturing and organizing screenshots of the website to provide visual reference for development and track visual changes over time.

3. **AI Development Tools**: A set of documentation and tools that help AI assistants understand and work with the codebase more effectively.

4. **Content Management**: A system for managing website content through structured data files.

**See also**: [Architecture Overview](./02-codebase-map/02-architecture-overview.md), [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## Using This Documentation

### For Developers

1. Start by reading the [Documentation Index](./00-documentation-index.md) to get an overview of all available documentation.
2. Then read the [rl-website-initial-notes.md](./rl-website-initial-notes.md) file to understand the project's background, company information, and detailed requirements.
3. Next, review the files in the `01-project-state/` folder to understand the current state of the project.
4. Use the `02-codebase-map/` folder to understand the structure and architecture of the codebase.
5. Refer to the `03-implementation-patterns/` folder for guidance on how to implement features.
6. Check the `04-visual-systems/` folder for design guidelines and styling approaches.
7. Follow the guidance in the `05-development-workflow/` folder for development processes.

### For AI Assistants

AI assistants should start with the [AI-README.md](./AI-README.md) file, which provides comprehensive context about the project, followed by [rl-website-initial-notes.md](./rl-website-initial-notes.md) for the foundational project brief. The documentation structure provides a clear path for understanding the project and its requirements.

**See also**: [AI-README Reading Order](./AI-README.md#recommended-reading-order)

## Screenshots

The website screenshots are available in three locations:

1. **Historical Screenshots**: Located in timestamped directories under `/screenshots/`
2. **Latest Screenshots**: Always available at `/screenshots/latest/` for the most current view
3. **AI Analysis Screenshots**: Available at `/.ai-analysis/latest/`

To capture new screenshots, use one of the following commands:

```bash
# Capture screenshots manually
npm run screenshots:capture

# Start development server with automatic screenshot capturing
npm run dev:ai
```

**See also**: [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

## Contributing to Documentation

When contributing to this documentation, please follow these guidelines:

1. Use clear, concise language
2. Include code examples where appropriate
3. Keep the documentation up-to-date with changes to the codebase
4. Follow the existing structure and naming conventions
5. Add appropriate cross-references to related documentation
6. Update the [Documentation Index](./00-documentation-index.md) when adding new documents

## Documentation Index

For a complete overview of all documentation with topical cross-references, see the [Documentation Index](./00-documentation-index.md).
```

#### `rl-website-initial-notes.md`

```markdown
# Ringerike Landskap

---

## Company Overview

Ringerike Landskap AS is a relatively young but growing landscaping and machine-contracting company based in Hole, Buskerud. With extensive experience in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik), they deeply understand the terrain’s unique demands, crafting outdoor areas that skillfully blend functionality, resilience, and authentic personal style. From Røyse to Hønefoss, their detailed knowledge of local conditions ensures robust foundations, effective drainage, and frost-proof solutions, whether they're building elegant stone patios (belegningsstein), durable retaining walls, vibrant gardens, or practical driveways.

What truly sets Ringerike Landskap apart are the two young, wise, and dedicated owners, who take genuine pride in their work and personally invest in each customer relationship. Known for their exceptional interpersonal skills and unwavering commitment, they consistently exceed expectations, leaving clients pleasantly surprised by the care, creativity, and precision of their craftsmanship. The owners’ diverse expertise—including professional welding and specialized use of corten steel—allows them to deliver innovative, personalized solutions that beautifully reflect client preferences and harmonize with the Norwegian climate.

---

## Team

The company consists of two owners who are young and wise, and they take actual **pride** in their work. They make it a point to themselves to **invest** in every customer they meet. Both have multiple disciplines, including one being a professional welder, making corten-steel something they intertwine in their work.

They are creative and flexible while covering a wide and flexible skillset. Whether it's crafting welcoming front yards or designing sophisticated commercial landscapes, the team ensures each solution reflects the client's preferences while honoring the Norwegian climate. They are known for shaping outdoor spaces that stand out in the local environment.

---

## Customer Approach

Clients consistently appreciate the genuine personal connection and meticulous attention provided by Ringerike Landskap. Each project begins with transparent, thoughtful dialogue where ideas, preferences, and creative possibilities are openly explored. The owners' hands-on involvement and sincere investment in every customer ensure that projects not only meet but exceed expectations, creating lasting satisfaction and often delightful surprises.

Their versatile portfolio, ranging from intimate backyard enhancements to expansive commercial landscapes, demonstrates a proven flexibility and deep commitment to quality at any scale. Customers within a 20–50 km radius can feel confident, from initial consultation through final implementation, knowing their outdoor spaces will uniquely reflect their personal vision and practical requirements.

---

## Services

The company focuses on eight core services:

1. **Kantstein** (curbstones)
2. **Ferdigplen** (ready lawn)
3. **Støttemur** (retaining walls)
4. **Hekk/beplantning** (hedges and planting)
5. **Cortenstål** (steel installations)
6. **Belegningsstein** (paving stones)
7. **Platting** (decking)
8. **Trapp/repo** (stairs and landings)

Every project showcases meticulous attention to detail—from selecting high-quality materials to matching each space with the best design strategies. By emphasizing local conditions and seasonal changes, Ringerike Landskap consistently delivers outdoor solutions that stand the test of time.

---

## Digital Presence

Ringerike Landskap AS is a forward-thinking company, actively leveraging their digital presence within a market where few landscaping companies in Norway have fully embraced technology. Recognizing this opportunity, they are currently finalizing an engaging and modern website aimed at expanding their customer reach and clearly demonstrating their unique expertise.

Ringerike Landskap's new website will serve as more than just a digital portfolio—it will be an authentic representation of how two dedicated owners personally invest in delivering exceptional landscaping solutions throughout the Ringerike region. By emphasizing their customer-centered approach, creative versatility, and consistent ability to exceed expectations, the site will effectively communicate why local residents and businesses continue to entrust their outdoor spaces to this distinctive landscaping company. The website's strategic focus on the owners' personal involvement and commitment to customer satisfaction positions Ringerike Landskap as the natural choice for anyone seeking truly personalized landscaping expertise in Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.

The website is in development as a dynamic digital showcase, spotlighting Ringerike Landskap’s specialized landscaping services for both private homes and commercial properties throughout the Ringerike region. Central to its appeal are the company’s two young owners' personalized approach, exceptional craftsmanship, and genuine commitment to customer satisfaction.

---

## Website Purpose

The new company website is designed to significantly expand their reach within the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik, and surrounding areas) by *leveraging* advanced and unique search engine optimization (SEO) techniques. More than just an online presence, the site will function as a dynamic platform, strategically built to connect with homeowners, businesses, and property developers actively searching for local landscaping services.

The website's ultimate purpose is to solidify Ringerike Landskap's position as *the* leading landscaping experts in the Ringerike region. It achieves this not only by presenting their portfolio but also by actively engaging their target audience through a customer-centric online experience and a powerful, locally-focused SEO strategy.

---

## SEO

The site’s content strategy focuses on delivering fresh, search-engine-friendly information and visually compelling examples—ranging from robust stone patios to custom corten-steel features—that resonate with homeowners, commercial property managers, and anyone seeking unique outdoor transformations. This SEO-centered approach underscores the owners’ commitment to exceptional craftsmanship, effective project management, and transparent communication, distinguishing Ringerike Landskap as the go-to resource for innovative landscape solutions in Buskerud.

Target Groups:
    - Primary: Homeowners, businesses, property developers, and local residents in Ringerike and surrounding areas seeking professional landscaping services.
    - Primary: Prospective customers looking for tailored landscaping solutions or inspiration for outdoor spaces.
    - Secondary: Users researching local landscaping solutions online or comparing contractors.
    - Secondary: Existing customers revisiting the site for additional services or to share testimonials.
    - Secondary: Existing customers reviewing projects or contacting the company.

Core Strategies:
    - Hyperlocal SEO: The website will be meticulously optimized for location-specific keywords, including variations and long-tail phrases (e.g., "anleggsgartner Røyse" "pris på støttemur Jevnaker," "cortenstål i hage Ringerike"). This ensures high visibility in search results when potential customers within their service area are seeking relevant services.
    - Technical SEO Foundation: The website's architecture is built for speed, mobile-friendliness, and optimal crawlability by search engines. Optimized images, clean code, structured data markup (Schema.org), and a clear sitemap ensure the site's content is easily
    - Optimized UI/UX: User-friendly navigation and detailed service breakdowns—covering everything from welcoming front yards to sophisticated commercial landscapes—to inspiring project galleries categorized by season and location, the site is strategically structured to enhance engagement.
    - Showcasing professionalism: Transparent communication, a genuine commitment to customer satisfaction, and thoughtful seasonal insights, the platform guides visitors effortlessly from inspiration to consultation, positioning Ringerike Landskap as the trusted local authority in outdoor design and landscape craftsmanship.
    - Authenticity: The website will authentically portray the two dedicated owners, their personal investment in each project, and their commitment to exceeding client expectations. This "people-first" approach, reinforced through testimonials, case studies, and behind-the-scenes content, builds trust and resonates with local customers, differentiating Ringerike Landskap from larger competitors.
    - Clear calls-to-action ("Book Gratis Befaring," "Få et Pristilbud"), user-friendly contact forms, and a streamlined user experience encourage inquiries and consultations.

User Interface:
    - Visual Style: Clean design with high-quality imagery showcasing landscaping work across prioritized services; green accents reflect nature and sustainability.
    - Navigation: Clear menu structure.
    - Call-to-Actions: Prominent buttons like “Book Gratis Befaring” should stand out visually to encourage conversions.
    - Project Showcase: Grid-style layout with filters for easy exploration of completed works categorized by job type or location.
    - Mobile-Friendly Features: Responsive layouts ensuring usability across all devices.

Responsive Practices:
    - Mobile-First Approach: Start with mobile layout and enhance for larger screens
    - Fluid Typography: Scale text based on screen size
    - Flexible Images: Ensure images adapt to their containers
    - Touch-Friendly UI: Larger touch targets on mobile
    - Performance Optimization: Lazy loading and optimized assets
    - Testing: Test on multiple devices and screen sizes
    - Accessibility: Ensure accessibility across all screen sizes

User Experience:
    - Browsing Services: Visitors land on the homepage or “Hva vi gjør” page to learn about the firm’s core landscaping solutions.
    - Exploring Projects: They can explore real examples under “Prosjekter,” filtering results by interest—like building a new terrace or installing a støttemur.
    - Seasonal Tips: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).
    - Users filter projects by category or location to find relevant examples.
    - Testimonials provide social proof and build trust throughout the decision-making process.
    - Requesting a Quote: When ready, they fill out the contact form or tap a “Book gratis befaring” button to schedule an on-site evaluation.
    - Personal Follow-Up: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.

Optimizations:
    - Meta Tags: Dynamic meta tags for each page
    - Structured Data: Schema.org markup for rich results
    - Semantic HTML: Proper heading structure
    - Canonical URLs: Proper URL structure

Accessibility:
    - Semantic HTML: Proper use of HTML elements
    - ARIA Attributes: For enhanced accessibility
    - Screen Reader Support: Proper labels and descriptions
    - Keyboard Navigation: All interactive elements are keyboard accessible

---

## Website Requirements

The website should be fully responsive, providing an optimal viewing experience across a wide range of devices, from mobile phones to desktop monitors. This is achieved through a combination of Tailwind CSS utility classes, custom responsive components, and media queries.

Component Architecture:
    - Well-organized component structure following a feature-based organization
    - Reusable UI components in the `ui` directory
    - Page-specific components in feature directories
    - Layout components for consistent page structure

State Management:
    - Uses React hooks for component-level state management
    - Custom hooks for reusable logic (e.g., `useMediaQuery`, `useScrollPosition`)
    - Local storage integration for persisting user preferences

Data Structure:
    - Static data files for services and projects
    - Well-defined TypeScript interfaces for type safety
    - Structured content with rich metadata

Architecture:
    - Clear separation of concerns between UI, data, and business logic
    - Component composition patterns for flexible UI building
    - Abstraction layers that separate presentation from data

Design Philosophy:
    - Focus on accessibility with proper semantic HTML and ARIA attributes
    - Performance optimization with code splitting and asset optimization
    - SEO-friendly structure with metadata and schema.org markup

Code Organization Principles:
    - High cohesion: Related functionality is grouped together
    - Low coupling: Components are independent and reusable
    - Consistent naming conventions and file structure

Seasonal Adaptation
    - Content changes based on current season
    - Affects projects, services, and UI elements
    - Automatic detection and mapping

Component Hierarchy
    - UI Components → Feature Components → Page Components
    - Composition over inheritance
    - Reusable building blocks

Data Flow
    - Static data in `/data`
    - Props down, events up
    - Context for global state
    - Custom hooks for logic

Responsive Design
    - Mobile-first approach
    - Tailwind breakpoints
    - Fluid typography
    - Adaptive layouts

Type Safety
    - TypeScript throughout
    - Strict type checking
    - Interface-driven development

Navigation Structure:
    - Home (/)
        - Hero section
        - Seasonal projects carousel
        - Service areas list
        - Seasonal services section
        - Testimonials section

    - Services (/hva-vi-gjor) # todo:change to `/hva`
        - Hero section
        - Service filtering (Category, Function, Season)
        - Service listings with details
        - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)
        - CTA section

    - Projects (/prosjekter)
        - Hero section
        - Project filtering (Category, Location, Tag, Season)
        - Project grid with details
        - Seasonal recommendations

    - About Us (/hvem-er-vi) # todo:change to `/hvem`
        - Hero section
        - Company information
        - Team members
        - Core values and benefits

    - Contact (/kontakt)
        - Hero section
        - Contact form
        - Location information
        - Service areas

Dynamic Routes:
    - Service Detail (/tjenester/:id)
        - Service description
        - Features list
        - Related projects
        - Image gallery
        - Contact CTA

    - Project Detail (/prosjekter/:id)
        - Project description
        - Project specifications
        - Materials and features
        - Related service
        - Testimonial (if available)

Additional Pages:
    - Testimonials (/kundehistorier)
        - Testimonial filtering
        - Testimonial grid
        - Testimonial categories
        - CTA section

Service Areas:
    - Røyse (Main Base)
    - Hønefoss
    - Hole kommune
    - Jevnaker
    - Sundvollen
    - Vik

Seasonal Content Adaptation:
    - Spring (Vår)
        - Focus on: Hekk og Beplantning, Ferdigplen
        - Relevant tags: beplantning, plen, hage

    - Summer (Sommer)
        - Focus on: Platting, Cortenstål, Belegningsstein
        - Relevant tags: terrasse, uteplass, innkjørsel

    - Fall (Høst)
        - Focus on: Støttemurer, Kantstein, Trapper og Repoer
        - Relevant tags: terrengforming, støttemur, trapp

    - Winter (Vinter)
        - Focus on: Planning and Design
        - Relevant tags: planlegging, design, prosjektering

Service Categories:
    - Belegningsstein
    - Cortenstål
    - Støttemurer
    - Platting
    - Ferdigplen
    - Kantstein
    - Trapper og Repoer
    - Hekk og Beplantning
```

#### `00-documentation-index.md`

```markdown
# Ringerike Landskap Documentation Index

This index provides a comprehensive overview of all documentation for the Ringerike Landskap website project. Use this as your starting point for navigating the documentation.

## Quick Links

-   [AI Assistant Guide](./AI-README.md) - Essential context for AI assistants
-   [Initial Project Notes](./rl-website-initial-notes.md) - Initial business requirements and project goals
-   [Main README](./README.md) - Overview of documentation structure

## Documentation Categories

### 01. Project State

Current reality and context of the project:

-   [01-1. Current Codebase Reality](./01-project-state/01-current-codebase-reality.md) - Honest assessment of the current codebase
-   [01-2. Technical Debt Inventory](./01-project-state/02-technical-debt-inventory.md) - Inventory of technical debt to address
-   [01-3. Company Information](./01-project-state/03-company-information.md) - Business context and company details

### 02. Codebase Map

Structure and organization of the codebase:

-   [02-1. Directory Structure](./02-codebase-map/01-directory-structure.md) - Overview of file and folder organization
-   [02-2. Architecture Overview](./02-codebase-map/02-architecture-overview.md) - Technical architecture of the application
-   [02-3. Component Hierarchy](./02-codebase-map/03-component-hierarchy.md) - Component relationships and tree structure

### 03. Implementation Patterns

Coding patterns and standards:

-   [03-1. Current Patterns](./03-implementation-patterns/01-current-patterns.md) - Existing implementation patterns
-   [03-2. Component Patterns](./03-implementation-patterns/02-component-patterns.md) - Component implementation details
-   [03-3. Target Patterns](./03-implementation-patterns/03-target-patterns.md) - Recommended patterns for future development

### 04. Visual Systems

Design and visual documentation:

-   [04-1. Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md) - **CRITICAL SYSTEM** - Screenshot capture system
-   [04-2. Design Guidelines](./04-visual-systems/02-design-guidelines.md) - Visual design principles and guidelines
-   [04-3. Styling Approach](./04-visual-systems/03-styling-approach.md) - CSS and Tailwind implementation details

### 05. Development Workflow

Development processes and tools:

-   [05-1. Environment Setup](./05-development-workflow/01-environment-setup.md) - Setting up the development environment
-   [05-2. Optimization Guide](./05-development-workflow/02-optimization-guide.md) - Performance optimization strategies
-   [05-3. Accessibility Guide](./05-development-workflow/03-accessibility-guide.md) - Accessibility best practices

## Topic Cross-References

### Component Development

-   [Component Patterns](./03-implementation-patterns/02-component-patterns.md)
-   [Component Hierarchy](./02-codebase-map/03-component-hierarchy.md)
-   [Target Patterns](./03-implementation-patterns/03-target-patterns.md)

### Styling and Design

-   [Design Guidelines](./04-visual-systems/02-design-guidelines.md)
-   [Styling Approach](./04-visual-systems/03-styling-approach.md)
-   [Component Patterns](./03-implementation-patterns/02-component-patterns.md)

### Performance

-   [Optimization Guide](./05-development-workflow/02-optimization-guide.md)
-   [Target Patterns](./03-implementation-patterns/03-target-patterns.md)
-   [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

### Business Context

-   [Company Information](./01-project-state/03-company-information.md)
-   [Initial Project Notes](./rl-website-initial-notes.md)

## Critical Systems

Special attention should be paid to these critical systems:

1. **Screenshot Infrastructure** - [Documentation](./04-visual-systems/01-screenshot-infrastructure.md)

    - Do not modify screenshot scripts without careful testing
    - Do not delete `.ai-analysis` or `screenshots` directories

2. **Norwegian Routes** - [See App.tsx]

    - Preserve Norwegian route names (/hvem-er-vi, /hva-vi-gjor, etc.)
    - Routes are critical for SEO and local branding

3. **Seasonal Adaptation** - [Design Guidelines](./04-visual-systems/02-design-guidelines.md)
    - Ensure seasonal content adaptation is properly implemented
    - Content should adapt based on current season (Vår, Sommer, Høst, Vinter)
```

#### `01-project-state\01-current-codebase-reality.md`

```markdown
# Current Codebase Reality

This document provides an honest assessment of the current state of the Ringerike Landskap website codebase. Understanding the real state of the code is essential for making effective improvements.

## Table of Contents

-   [Project Overview](#project-overview)
-   [Current Structure](#current-structure)
-   [Actual State Assessment](#actual-state-assessment)
    -   [Strengths](#strengths)
    -   [Areas Needing Improvement](#areas-needing-improvement)
-   [Critical Systems](#critical-systems)
    -   [Screenshot System](#screenshot-system)
    -   [Content Management](#content-management)
-   [Technical Debt Inventory](#technical-debt-inventory)
-   [Next Steps](#next-steps)
-   [Related Documentation](#related-documentation)

## Project Overview

The Ringerike Landskap website is a React/TypeScript application built with modern web technologies:

-   **Frontend**: React
-   **Build Tool**: Vite
-   **Styling**: Tailwind CSS
-   **Routing**: React Router
-   **TypeScript**: TypeScript

**See also**: [Architecture Overview](../02-codebase-map/02-architecture-overview.md), [Initial Project Notes](../rl-website-initial-notes.md)

## Current Structure

The codebase follows a modular structure organized by feature and responsibility:

```
src/
├── components/       # Reusable UI components
│   ├── layout/       # Layout components (Header, Footer)
│   └── ui/           # Basic UI components (Button, Card, etc.)
├── features/         # Feature-specific components
│   ├── home/         # Home page specific components
│   ├── services/     # Services page specific components
│   └── projects/     # Projects page specific components
├── hooks/            # Custom React hooks
├── pages/            # Page components
│   ├── home/
│   ├── about/
│   ├── services/
│   ├── projects/
│   └── contact/
├── data/             # Mock data and content definitions
│   ├── services.ts
│   ├── projects.ts
│   └── testimonials.ts
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
└── App.tsx           # Main application component
```

**See also**: [Directory Structure](../02-codebase-map/01-directory-structure.md), [Component Hierarchy](../02-codebase-map/03-component-hierarchy.md)

## Actual State Assessment

### Strengths

1. **Component Organization**: Generally well-organized by feature and responsibility
2. **TypeScript Implementation**: Basic type definitions are in place
3. **Routing Structure**: Clean routing implementation with React Router
4. **Styling System**: Consistent use of Tailwind CSS

### Areas Needing Improvement

1. **Inconsistent Component Patterns**: Components follow mixed patterns:

    - Some use props drilling extensively
    - Others use React context irregularly
    - Mix of functional and class-based approaches

2. **TypeScript Usage**:

    - Type definitions exist but are incomplete
    - Some components lack proper prop typing
    - `any` type is used in several places

3. **Data Management**:

    - Data is primarily stored as static TypeScript objects
    - No centralized state management approach
    - Inconsistent data fetching patterns

4. **Performance Considerations**:
    - Limited use of memoization
    - No code splitting implemented
    - Image optimization is inconsistent

**See also**: [Technical Debt Inventory](./02-technical-debt-inventory.md), [Target Patterns](../03-implementation-patterns/03-target-patterns.md)

## Critical Systems

### Screenshot System

The project has a sophisticated screenshot management system that is essential to the development workflow:

-   Captures website at various viewport sizes
-   Maintains historical screenshots in timestamped directories
-   Integrates with development workflow through `npm run dev:ai`
-   Enables AI-assisted development through visual feedback

This system is critical and must be handled carefully in any refactoring efforts.

**See also**: [Screenshot Infrastructure](../04-visual-systems/01-screenshot-infrastructure.md)

### Content Management

Content is currently managed through TypeScript files in `src/data/`:

-   Services, projects, and testimonials are defined as typed arrays
-   Images are referenced with paths to the `public` directory
-   Relationship mappings exist between service categories and projects

The content structure should be preserved even as implementation patterns are improved.

**See also**: [Company Information](./03-company-information.md)

## Technical Debt Inventory

1. **Component Refactoring Needs**:

    - Standardize component patterns
    - Improve prop typing
    - Implement proper error boundaries

2. **State Management Improvements**:

    - Implement a consistent state management approach
    - Reduce prop drilling
    - Consider context API usage or a state management library

3. **Performance Optimizations**:

    - Implement code splitting for routes
    - Add lazy loading for components
    - Optimize image loading strategy

4. **Testing Gap**:
    - No testing infrastructure currently in place
    - Critical components need unit tests
    - Visual regression testing would benefit from the screenshot system

**See also**: [Technical Debt Inventory](./02-technical-debt-inventory.md) for more detailed information

## Next Steps

The primary focus should be on:

1. Standardizing component patterns
2. Implementing a consistent state management approach
3. Enhancing TypeScript usage for better type safety
4. Leveraging the screenshot system for visual regression testing

This assessment serves as the foundation for targeted improvements while maintaining the existing functionality and visual design.

**See also**: [Target Patterns](../03-implementation-patterns/03-target-patterns.md), [Optimization Guide](../05-development-workflow/02-optimization-guide.md)

## Related Documentation

-   [Technical Debt Inventory](./02-technical-debt-inventory.md) - Detailed list of technical debt items
-   [Directory Structure](../02-codebase-map/01-directory-structure.md) - More detailed directory organization
-   [Component Hierarchy](../02-codebase-map/03-component-hierarchy.md) - Component relationships
-   [Target Patterns](../03-implementation-patterns/03-target-patterns.md) - Recommended implementation patterns
-   [Documentation Index](../00-documentation-index.md) - Complete documentation overview
```

#### `01-project-state\02-technical-debt-inventory.md`

```markdown
# Technical Debt Inventory

This document provides a comprehensive inventory of technical debt in the Ringerike Landskap website codebase. It identifies specific issues that need to be addressed as the project moves forward.

## Component Structure Issues

1. **Inconsistent Component Patterns**
   - Some components use functional patterns while others are class-based
   - Props drilling is used extensively in some areas, context in others
   - Component organization lacks consistency across features

2. **Component Props Typing**
   - Some components lack proper TypeScript interface definitions
   - Inconsistent use of default props and prop validation
   - Overuse of any type in component definitions

## State Management Concerns

1. **Mixed State Management Approaches**
   - Local state (useState) is used inconsistently
   - React Context is implemented in different ways across the codebase
   - No clear pattern for state sharing between components

2. **Prop Drilling**
   - Excessive prop drilling in nested component hierarchies
   - Missing context providers for deeply nested state requirements
   - Unclear state ownership in complex component trees

## TypeScript Implementation

1. **Type Safety Gaps**
   - Incomplete interface definitions for core data structures
   - Use of `any` type in several components and utilities
   - Missing generics in reusable components and hooks

2. **Type Organization**
   - Type definitions are scattered across the codebase
   - Inconsistent naming conventions for interfaces and types
   - Duplicate type definitions in multiple files

## Performance Optimization Needs

1. **Rendering Optimization**
   - Missing memoization for expensive calculations
   - No code splitting implemented for route-based components
   - Unnecessary re-renders in complex component trees

2. **Asset Optimization**
   - Inconsistent image optimization strategy
   - Large assets affecting initial page load
   - No lazy loading for below-the-fold images

## Testing Coverage

1. **Missing Tests**
   - No unit tests for core components
   - No integration tests for key user flows
   - Missing visual regression tests

2. **Test Infrastructure**
   - No testing framework set up
   - Missing test utilities and helpers
   - No CI/CD integration for testing

## Code Organization

1. **Directory Structure**
   - Some components are placed in inconsistent locations
   - Unclear boundaries between features/ and components/ directories
   - Utility functions not properly organized by purpose

2. **File Naming**
   - Inconsistent file naming conventions
   - Some files contain multiple components
   - Lack of index files for cleaner imports

## Accessibility Issues

1. **A11y Implementation**
   - Missing ARIA attributes on interactive elements
   - Insufficient keyboard navigation support
   - Color contrast issues in some UI components

2. **Semantic HTML**
   - Non-semantic div soup in some components
   - Improper heading hierarchy
   - Missing alt text on some images

## Action Plan

Based on this inventory, the following prioritized actions are recommended:

1. **High Priority**
   - Standardize component patterns across the codebase
   - Improve TypeScript type definitions and usage
   - Implement consistent state management approach

2. **Medium Priority**
   - Add code splitting for route-based components
   - Create test infrastructure and add tests for key components
   - Address major accessibility issues

3. **Lower Priority**
   - Refine directory structure and naming conventions
   - Optimize assets for performance
   - Clean up unused code and dependencies

This inventory should be reviewed and updated regularly as the project evolves. ```

#### `01-project-state\03-company-information.md`

```markdown
# Ringerike Landskap Company Information

## Company Overview

Ringerike Landskap AS is a relatively young but growing landscaping and machine-contracting company based in Hole, Buskerud. With extensive experience in the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik), they deeply understand the terrain's unique demands, crafting outdoor areas that skillfully blend functionality, resilience, and authentic personal style. From Røyse to Hønefoss, their detailed knowledge of local conditions ensures robust foundations, effective drainage, and frost-proof solutions, whether they're building elegant stone patios (belegningsstein), durable retaining walls, vibrant gardens, or practical driveways.

What truly sets Ringerike Landskap apart are the two young, wise, and dedicated owners, who take genuine pride in their work and personally invest in each customer relationship. Known for their exceptional interpersonal skills and unwavering commitment, they consistently exceed expectations, leaving clients pleasantly surprised by the care, creativity, and precision of their craftsmanship. The owners' diverse expertise—including professional welding and specialized use of corten steel—allows them to deliver innovative, personalized solutions that beautifully reflect client preferences and harmonize with the Norwegian climate.

## Team

The company consists of two owners who are young and wise, and they take actual **pride** in their work. They make it a point to themselves to **invest** in every customer they meet. Both have multiple disciplines, including one being a professional welder, making corten-steel something they intertwine in their work.

They are creative and flexible while covering a wide and flexible skillset. Whether it's crafting welcoming front yards or designing sophisticated commercial landscapes, the team ensures each solution reflects the client's preferences while honoring the Norwegian climate. They are known for shaping outdoor spaces that stand out in the local environment.

## Services

The company focuses on eight core services:

1. **Kantstein** (curbstones)
2. **Ferdigplen** (ready lawn)
3. **Støttemur** (retaining walls)
4. **Hekk/beplantning** (hedges and planting)
5. **Cortenstål** (steel installations)
6. **Belegningsstein** (paving stones)
7. **Platting** (decking)
8. **Trapp/repo** (stairs and landings)

Every project showcases meticulous attention to detail—from selecting high-quality materials to matching each space with the best design strategies. By emphasizing local conditions and seasonal changes, Ringerike Landskap consistently delivers outdoor solutions that stand the test of time.

## Customer Approach

Clients consistently appreciate the genuine personal connection and meticulous attention provided by Ringerike Landskap. Each project begins with transparent, thoughtful dialogue where ideas, preferences, and creative possibilities are openly explored. The owners' hands-on involvement and sincere investment in every customer ensure that projects not only meet but exceed expectations, creating lasting satisfaction and often delightful surprises.

Their versatile portfolio, ranging from intimate backyard enhancements to expansive commercial landscapes, demonstrates a proven flexibility and deep commitment to quality at any scale. Customers within a 20–50 km radius can feel confident, from initial consultation through final implementation, knowing their outdoor spaces will uniquely reflect their personal vision and practical requirements.

## Digital Presence

Ringerike Landskap AS is a forward-thinking company, actively leveraging their digital presence within a market where few landscaping companies in Norway have fully embraced technology. Recognizing this opportunity, they are currently finalizing an engaging and modern website aimed at expanding their customer reach and clearly demonstrating their unique expertise.

Ringerike Landskap's new website will serve as more than just a digital portfolio—it will be an authentic representation of how two dedicated owners personally invest in delivering exceptional landscaping solutions throughout the Ringerike region. By emphasizing their customer-centered approach, creative versatility, and consistent ability to exceed expectations, the site will effectively communicate why local residents and businesses continue to entrust their outdoor spaces to this distinctive landscaping company. The website's strategic focus on the owners' personal involvement and commitment to customer satisfaction positions Ringerike Landskap as the natural choice for anyone seeking truly personalized landscaping expertise in Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.

## Website Purpose

The new company website is designed to significantly expand their reach within the Ringerike region (Hole, Hønefoss, Jevnaker, Sundvollen, Vik, and surrounding areas) by *leveraging* advanced and unique search engine optimization (SEO) techniques. More than just an online presence, the site will function as a dynamic platform, strategically built to connect with homeowners, businesses, and property developers actively searching for local landscaping services.

The website's ultimate purpose is to solidify Ringerike Landskap's position as *the* leading landscaping experts in the Ringerike region. It achieves this not only by presenting their portfolio but also by actively engaging their target audience through a customer-centric online experience and a powerful, locally-focused SEO strategy.
```

#### `02-codebase-map\01-directory-structure.md`

```markdown
# Codebase Directory Structure

This document provides a comprehensive map of the Ringerike Landskap website codebase organization, focusing on the actual structure rather than an idealized version.

## Root Directory Structure

```
project/
├── .ai-analysis/       # AI-specific screenshots and analysis data
├── content/            # Structured content organization
│   ├── projects/
│   ├── services/
│   ├── team/
│   └── testimonials/
├── docs/               # Project documentation
├── node_modules/       # Dependencies
├── public/             # Static assets
│   └── images/         # Image assets organized by category
├── screenshots/        # Screenshot captures for visual reference
│   ├── latest/         # Always contains most recent screenshots
│   └── [timestamped]/  # Historical screenshot directories
├── scripts/            # Automation scripts
│   ├── screenshot-manager.js
│   ├── dev-with-snapshots.js
│   └── auto-snapshot.js
├── src/                # Source code
└── various config files (package.json, tsconfig.json, etc.)
```

## Source Directory Structure

```
src/
├── app/                # Core application setup
├── components/         # Reusable UI components
│   ├── layout/         # Layout components
│   │   ├── Header.tsx
│   │   └── Footer.tsx
│   └── ui/             # UI components
│       ├── Button.tsx
│       ├── Card.tsx
│       └── etc.
├── contexts/           # React context providers
├── data/               # Mock data and content
│   ├── services.ts
│   ├── projects.ts
│   ├── testimonials.ts
│   ├── team.ts
│   └── navigation.ts
├── features/           # Feature-specific components
│   ├── home/
│   ├── services/
│   └── projects/
├── hooks/              # Custom React hooks
├── lib/                # Third-party integrations
├── pages/              # Route components
│   ├── home/
│   │   └── index.tsx
│   ├── about/
│   │   └── index.tsx
│   ├── services/
│   │   ├── index.tsx
│   │   └── detail.tsx
│   ├── projects/
│   │   ├── index.tsx
│   │   └── detail.tsx
│   └── contact/
│       └── index.tsx
├── styles/             # Global styles
├── types/              # TypeScript type definitions
│   ├── services.ts
│   ├── projects.ts
│   └── common.ts
├── utils/              # Utility functions
├── App.tsx             # Main application component
├── main.tsx            # Application entry point
└── index.css           # Global CSS
```

## Key Relations Between Directories

### Content Flow

```
content/ → src/data/ → components/ → pages/
```

The content directory provides structured content which is imported into data files, then consumed by components and finally rendered in pages.

### Visual Documentation System

```
src/ → scripts/ → screenshots/ and .ai-analysis/
```

The source code is captured through screenshot scripts, which store visual output in both screenshots and .ai-analysis directories.

## Critical Directories

These directories contain essential functionality that must be handled carefully:

1. **scripts/**: Contains screenshot and development automation
2. **src/data/**: Contains all content definitions
3. **src/pages/**: Contains the routing structure
4. **.ai-analysis/**: Contains AI-specific captures and metadata

## Dependency Management

The project uses npm with these key dependencies:

- React ecosystem: react, react-dom, react-router-dom
- UI utilities: clsx, lucide-react, tailwind-merge
- Development tools: vite, typescript, eslint
- Build tools: postcss, autoprefixer, tailwindcss

## Configuration Files

- **vite.config.ts**: Configures the Vite build tool
- **tsconfig.json**: Main TypeScript configuration
- **tsconfig.app.json**: App-specific TypeScript configuration
- **tsconfig.node.json**: Node-specific TypeScript configuration
- **tailwind.config.js**: Tailwind CSS configuration
- **postcss.config.js**: PostCSS configuration
- **eslint.config.js**: ESLint configuration

## Notes on Actual Usage

The structure described above represents the actual organization, with some observations:

1. Not all directories are fully populated (some are placeholders)
2. Some components don't follow the ideal organization pattern
3. The boundaries between features/ and components/ are sometimes blurred
4. Context usage is inconsistent across the application

This map serves as a guide for navigating the codebase while acknowledging its current organizational state. ```

#### `02-codebase-map\02-architecture-overview.md`

```markdown
# Ringerike Landskap Website Architecture

This document outlines the technical architecture of the Ringerike Landskap website, providing a comprehensive overview of the codebase structure, component organization, and implementation details.

## Technology Stack

The website is built using the following technologies:

-   **React**: Frontend library for building user interfaces (v18.3)
-   **TypeScript**: Typed superset of JavaScript for improved developer experience
-   **Vite**: Build tool and development server
-   **Tailwind CSS**: Utility-first CSS framework for styling
-   **React Router**: Library for routing and navigation (v6.22)
-   **React Helmet**: Library for managing document head
-   **Lucide React**: Icon library

## Project Structure

The project follows a modular structure organized by feature and responsibility:

```
src/
├── components/       # Reusable UI components
│   ├── layout/       # Layout components (Header, Footer)
│   └── ui/           # Basic UI components (Button, Card, etc.)
├── features/         # Feature-specific components
│   ├── home/         # Home page specific components
│   ├── services/     # Services page specific components
│   └── projects/     # Projects page specific components
├── hooks/            # Custom React hooks
├── pages/            # Page components
│   ├── home/
│   ├── about/
│   ├── services/
│   ├── projects/
│   └── contact/
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
└── App.tsx           # Main application component
```

## Component Architecture

The website follows a component-based architecture with the following principles:

1. **Atomic Design**: Components are organized from small, atomic components to larger, composite components
2. **Composition Over Inheritance**: Components are composed together rather than extended
3. **Single Responsibility**: Each component has a single, well-defined responsibility
4. **Reusability**: Components are designed to be reusable across the application

### Core Components

#### Layout Components

-   **Header**: Main navigation component with responsive design
-   **Footer**: Site footer with contact information and links
-   **Container**: Wrapper component for consistent horizontal padding
-   **Layout**: Main layout component that includes Header and Footer

#### UI Components

-   **Button**: Reusable button component with variants
-   **Card**: Card component for displaying content with consistent styling
-   **Hero**: Hero section component for page headers
-   **Input**: Form input component with validation support

## Routing

The website uses React Router for navigation with the following route structure:

```jsx
<Routes>
    <Route path="/" element={<HomePage />} />
    <Route path="/hvem-er-vi" element={<AboutPage />} />
    <Route path="/hva-vi-gjor" element={<ServicesPage />} />
    <Route path="/prosjekter" element={<ProjectsPage />} />
    <Route path="/kontakt" element={<ContactPage />} />
</Routes>
```

## State Management

The website uses React's built-in state management with hooks:

-   **useState**: For component-level state
-   **useContext**: For sharing state between components
-   **useReducer**: For more complex state logic

## Data Fetching

Data fetching is handled using custom hooks that encapsulate the fetching logic:

```tsx
function useProjects() {
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        async function fetchProjects() {
            try {
                setLoading(true);
                // Fetch projects from API or mock data
                const data = await fetchProjectsData();
                setProjects(data);
            } catch (err) {
                setError(err as Error);
            } finally {
                setLoading(false);
            }
        }

        fetchProjects();
    }, []);

    return { projects, loading, error };
}
```

## Styling

The website uses Tailwind CSS for styling with a custom configuration:

```js
// tailwind.config.js
module.exports = {
    content: ["./src/**/*.{js,jsx,ts,tsx}", "./index.html"],
    theme: {
        extend: {
            colors: {
                primary: "#1e9545",
                "primary-dark": "#0d6b30",
                "primary-light": "#e6f4ea",
            },
            fontFamily: {
                sans: ["Inter", "sans-serif"],
            },
        },
    },
    plugins: [],
};
```

## Responsive Design

The website is designed to be responsive with the following breakpoints:

-   **Mobile**: < 768px
-   **Tablet**: 768px - 1023px
-   **Desktop**: ≥ 1024px

Responsive design is implemented using Tailwind's responsive utilities:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {/* Grid items */}
</div>
```

## Performance Optimization

The website implements several performance optimizations:

1. **Code Splitting**: Using React.lazy and Suspense for route-based code splitting
2. **Image Optimization**: Using responsive images and WebP format
3. **Lazy Loading**: Implementing lazy loading for below-the-fold content
4. **Memoization**: Using React.memo and useMemo for expensive computations

## Accessibility

The website follows accessibility best practices:

1. **Semantic HTML**: Using appropriate HTML elements
2. **ARIA Attributes**: Adding ARIA attributes for interactive elements
3. **Keyboard Navigation**: Ensuring keyboard accessibility
4. **Color Contrast**: Maintaining sufficient color contrast
5. **Focus Management**: Implementing proper focus states

## Deployment

The website is built using Vite and deployed as a static site:

```bash
# Build for production
npm run build

# Preview the production build
npm run preview
```

## Future Considerations

1. **State Management**: Consider implementing Redux or Context API for more complex state management
2. **API Integration**: Implement a proper API client for data fetching
3. **Testing**: Add unit and integration tests
4. **Internationalization**: Add support for multiple languages
5. **Analytics**: Implement analytics tracking

## Route Structure

The website implements a carefully planned route structure that reflects the organizational needs and user journeys:

### Main Navigation Routes

- **Home** (/)
  - Hero section
  - Seasonal projects carousel
  - Service areas list
  - Seasonal services section
  - Testimonials section

- **Services** (/hva-vi-gjor)
  - Hero section
  - Service filtering (Category, Function, Season)
  - Service listings with details
  - Benefits section (Lokalkunnskap, Tilpassede løsninger, Klimatilpasset)
  - CTA section

- **Projects** (/prosjekter)
  - Hero section
  - Project filtering (Category, Location, Tag, Season)
  - Project grid with details
  - Seasonal recommendations

- **About Us** (/hvem-er-vi)
  - Hero section
  - Company information
  - Team members
  - Core values and benefits

- **Contact** (/kontakt)
  - Hero section
  - Contact form
  - Location information
  - Service areas

### Dynamic Routes

- **Service Detail** (/tjenester/:id)
  - Service description
  - Features list
  - Related projects
  - Image gallery
  - Contact CTA

- **Project Detail** (/prosjekter/:id)
  - Project description
  - Project specifications
  - Materials and features
  - Related service
  - Testimonial (if available)

### Additional Pages

- **Testimonials** (/kundehistorier)
  - Testimonial filtering
  - Testimonial grid
  - Testimonial categories
  - CTA section

## SEO Strategy

The website employs a comprehensive SEO strategy to enhance visibility and reach in the Ringerike region:

### Target Groups

- **Primary**: Homeowners, businesses, property developers, and local residents in Ringerike and surrounding areas seeking professional landscaping services
- **Primary**: Prospective customers looking for tailored landscaping solutions or inspiration for outdoor spaces
- **Secondary**: Users researching local landscaping solutions online or comparing contractors
- **Secondary**: Existing customers revisiting the site for additional services or to share testimonials
- **Secondary**: Existing customers reviewing projects or contacting the company

### Core SEO Strategies

- **Hyperlocal SEO**: The website is meticulously optimized for location-specific keywords, including variations and long-tail phrases (e.g., "anleggsgartner Røyse" "pris på støttemur Jevnaker," "cortenstål i hage Ringerike"). This ensures high visibility in search results when potential customers within their service area are seeking relevant services.

- **Technical SEO Foundation**: The website's architecture is built for speed, mobile-friendliness, and optimal crawlability by search engines. Optimized images, clean code, structured data markup (Schema.org), and a clear sitemap ensure the site's content is easily discoverable.

- **Optimized UI/UX**: User-friendly navigation and detailed service breakdowns—covering everything from welcoming front yards to sophisticated commercial landscapes—to inspiring project galleries categorized by season and location, the site is strategically structured to enhance engagement.

- **Showcasing Professionalism**: Transparent communication, a genuine commitment to customer satisfaction, and thoughtful seasonal insights, the platform guides visitors effortlessly from inspiration to consultation, positioning Ringerike Landskap as the trusted local authority in outdoor design and landscape craftsmanship.

- **Authenticity**: The website authentically portrays the two dedicated owners, their personal investment in each project, and their commitment to exceeding client expectations. This "people-first" approach, reinforced through testimonials, case studies, and behind-the-scenes content, builds trust and resonates with local customers, differentiating Ringerike Landskap from larger competitors.

### SEO Technical Implementation

- **Meta Tags**: Dynamic meta tags for each page using React Helmet
- **Structured Data**: Schema.org markup for rich results
- **Semantic HTML**: Proper heading structure
- **Canonical URLs**: Proper URL structure
- **Performance**: Optimized loading times and Core Web Vitals metrics
- **Mobile Optimization**: Fully responsive design with mobile-friendly features
```

#### `02-codebase-map\03-component-hierarchy.md`

```markdown
# Ringerike Landskap Website Component Tree

```
App
├── Router
│   ├── Header
│   │   ├── Logo
│   │   ├── Navigation (Desktop)
│   │   └── MobileMenuToggle
│   │
│   ├── main
│   │   └── Routes
│   │       ├── HomePage (path="/")
│   │       │   ├── Hero
│   │       │   │   ├── Title
│   │       │   │   ├── Subtitle
│   │       │   │   ├── LocationBadge
│   │       │   │   └── CTAButton
│   │       │   │
│   │       │   ├── Container
│   │       │   │   ├── Grid (Projects & Service Areas)
│   │       │   │   │   ├── SeasonalProjectsCarousel
│   │       │   │   │   │   ├── ProjectCard(s)
│   │       │   │   │   │   ├── NavigationArrows
│   │       │   │   │   │   └── PaginationIndicators
│   │       │   │   │   │
│   │       │   │   │   └── ServiceAreaList
│   │       │   │   │       └── ServiceAreaItem(s)
│   │       │   │   │
│   │       │   │   └── Grid (Services & CTA)
│   │       │   │       ├── FilteredServicesSection
│   │       │   │       │   └── ServiceCard(s)
│   │       │   │       │
│   │       │   │       └── SeasonalCTA
│   │       │   │
│   │       │   └── TestimonialsSection
│   │       │       └── TestimonialCard(s)
│   │       │
│   │       ├── AboutPage (path="/hvem-er-vi")
│   │       ├── ServicesPage (path="/hva-vi-gjor")
│   │       ├── ServiceDetailPage (path="/tjenester/:id")
│   │       ├── ProjectsPage (path="/prosjekter")
│   │       ├── ProjectDetailPage (path="/prosjekter/:id")
│   │       ├── ContactPage (path="/kontakt")
│   │       └── TestimonialsPage (path="/kundehistorier")
│   │
│   └── Footer
│       ├── CompanyInfo
│       │   └── SocialLinks
│       ├── ContactInfo
│       │   ├── Address
│       │   ├── Phone
│       │   └── Email
│       ├── OpeningHours
│       ├── QuickLinks
│       └── PrivacyLinks
│
└── ReactHelmet (SEO metadata)
```

## Data Flow

```
┌─────────────────┐
│                 │
│  Mock API Data  │◄───────┐
│                 │        │
└────────┬────────┘        │
         │                 │
         ▼                 │
┌─────────────────┐        │
│                 │        │
│  Custom Hooks   │        │
│  (useData)      │        │
│                 │        │
└────────┬────────┘        │
         │                 │
         ▼                 │
┌─────────────────┐        │
│                 │        │
│  Page Component │        │
│                 │        │
└────────┬────────┘        │
         │                 │
         ▼                 │
┌─────────────────┐        │
│                 │        │
│  UI Components  │────────┘
│                 │
└─────────────────┘
```

## Rendered UI Structure (Homepage)

```
┌─────────────────────────────────────────────────────────────┐
│ Header                                                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Hero Section                                            │ │
│ │ - Title, Subtitle, CTA Button                           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌───────────────────────────┐ ┌───────────────────────────┐ │
│ │                           │ │                           │ │
│ │ Seasonal Projects         │ │ Service Areas             │ │
│ │ Carousel                  │ │                           │ │
│ │                           │ │                           │ │
│ └───────────────────────────┘ └───────────────────────────┘ │
│                                                             │
│ ┌───────────────────────────┐ ┌───────────────────────────┐ │
│ │                           │ │                           │ │
│ │ Filtered Services         │ │ Seasonal CTA              │ │
│ │                           │ │                           │ │
│ │                           │ │                           │ │
│ └───────────────────────────┘ └───────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Testimonials Section                                    │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ Footer                                                      │
└─────────────────────────────────────────────────────────────┘
```

## Responsive Layouts

### Desktop (>1024px)

```
┌─────────────────────────────────────────────────────────────┐
│ Header (Full Navigation)                                    │
├─────────────────────────────────────────────────────────────┤
│ Hero                                                        │
├───────────────────────────┬───────────────────────────────┬─┤
│ Projects (2/3)            │ Service Areas (1/3)           │ │
├───────────────────────────┼───────────────────────────────┼─┤
│ Services (2/3)            │ CTA (1/3)                     │ │
├─────────────────────────────────────────────────────────────┤
│ Testimonials                                               │
├─────────────────────────────────────────────────────────────┤
│ Footer (3 columns)                                         │
└─────────────────────────────────────────────────────────────┘
```

### Tablet (768px-1024px)

```
┌─────────────────────────────────────────────────────────────┐
│ Header (Full Navigation)                                    │
├─────────────────────────────────────────────────────────────┤
│ Hero                                                        │
├───────────────────────────┬───────────────────────────────┬─┤
│ Projects (2/3)            │ Service Areas (1/3)           │ │
├───────────────────────────┼───────────────────────────────┼─┤
│ Services (2/3)            │ CTA (1/3)                     │ │
├─────────────────────────────────────────────────────────────┤
│ Testimonials                                               │
├─────────────────────────────────────────────────────────────┤
│ Footer (2-3 columns)                                       │
└─────────────────────────────────────────────────────────────┘
```

### Mobile (<768px)

```
┌─────────────────────────────────────────────────────────────┐
│ Header (Hamburger Menu)                                     │
├─────────────────────────────────────────────────────────────┤
│ Hero                                                        │
├─────────────────────────────────────────────────────────────┤
│ Projects                                                    │
├─────────────────────────────────────────────────────────────┤
│ Service Areas                                               │
├─────────────────────────────────────────────────────────────┤
│ Services                                                    │
├─────────────────────────────────────────────────────────────┤
│ CTA                                                         │
├─────────────────────────────────────────────────────────────┤
│ Testimonials                                                │
├─────────────────────────────────────────────────────────────┤
│ Footer (Single column)                                      │
└─────────────────────────────────────────────────────────────┘
```
```

#### `03-implementation-patterns\01-current-patterns.md`

```markdown
# Current Implementation Patterns

This document outlines the current implementation patterns found in the Ringerike Landskap website codebase. Understanding these patterns is essential for maintaining consistency during development and planning improvements.

## Component Patterns

The codebase currently uses several different component implementation patterns:

### Functional Components with Props

Most components use the functional component pattern with explicit props:

```tsx
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  onClick?: () => void;
}

function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  return (
    <button
      className={`btn ${variant === 'primary' ? 'btn-primary' : variant === 'secondary' ? 'btn-secondary' : 'btn-outline'}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}

export default Button;
```

### Context Usage

Some components use React Context for state management:

```tsx
// Context definition
export const ServiceContext = createContext<{
  services: ServiceType[];
  loading: boolean;
} | undefined>(undefined);

// Provider component
export function ServiceProvider({ children }: { children: React.ReactNode }) {
  const [services] = useState<ServiceType[]>(servicesData);
  const [loading] = useState(false);

  return (
    <ServiceContext.Provider value={{ services, loading }}>
      {children}
    </ServiceContext.Provider>
  );
}

// Usage in components
function ServiceList() {
  const context = useContext(ServiceContext);

  if (!context) {
    throw new Error('ServiceList must be used within ServiceProvider');
  }

  const { services, loading } = context;

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {services.map(service => (
        <ServiceCard key={service.id} service={service} />
      ))}
    </div>
  );
}
```

### Prop Drilling

Deeply nested components often use prop drilling:

```tsx
function ProjectsPage() {
  const [filter, setFilter] = useState('all');

  return (
    <div>
      <ProjectFilter filter={filter} setFilter={setFilter} />
      <ProjectList filter={filter} />
    </div>
  );
}

function ProjectList({ filter }: { filter: string }) {
  return (
    <div>
      {projects
        .filter(project => filter === 'all' || project.category === filter)
        .map(project => (
          <ProjectItem key={project.id} project={project} />
        ))}
    </div>
  );
}
```

## Styling Patterns

### Tailwind Classes

The primary styling approach uses Tailwind CSS utility classes:

```tsx
function Card({ title, content }) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
      <p className="text-gray-600">{content}</p>
    </div>
  );
}
```

### Conditional Classes

Conditional styling is implemented using template literals or conditional expressions:

```tsx
function Alert({ type, message }) {
  return (
    <div className={`p-4 rounded ${
      type === 'error' ? 'bg-red-100 text-red-800' :
      type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
      'bg-green-100 text-green-800'
    }`}>
      {message}
    </div>
  );
}
```

## Data Management Patterns

### Static Data Files

Data is primarily managed through static TypeScript files:

```tsx
// src/data/services.ts
import { ServiceType } from '@/types';

export const services: ServiceType[] = [
  {
    id: "belegningsstein",
    title: "Belegningsstein",
    description: "Profesjonell legging av belegningsstein for innkjÃ¸rsler og gangveier.",
    image: "/images/categorized/belegg/IMG_3037.webp",
    features: [
      "Solid grunnarbeid",
      "Presis legging",
      "Flere designmuligheter"
    ]
  },
  // More services...
];
```

### Helper Functions

Helper functions for data filtering and transformation:

```tsx
// Helper function to get a service by ID
export function getServiceById(id: string): ServiceType | undefined {
  return services.find(service => service.id === id);
}

// Helper function to filter services by season
export function getSeasonalServices(): ServiceType[] {
  const currentMonth = new Date().getMonth();
  const isSummer = currentMonth >= 4 && currentMonth <= 8;

  return services.filter(service =>
    isSummer ? !service.winterOnly : !service.summerOnly
  );
}
```

## Routing Pattern

The routing structure uses React Router with nested routes:

```tsx
function App() {
  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/hvem-er-vi" element={<AboutPage />} />
            <Route path="/hva-vi-gjor" element={<ServicesPage />} />
            <Route path="/tjenester/:id" element={<ServiceDetailPage />} />
            <Route path="/prosjekter" element={<ProjectsPage />} />
            <Route path="/prosjekter/:id" element={<ProjectDetailPage />} />
            <Route path="/kontakt" element={<ContactPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}
```

## State Management Patterns

The codebase uses several approaches to state management:

1. **Component Local State**: Using `useState` for component-specific state
2. **React Context**: For sharing state between related components
3. **URL Parameters**: For page-specific state reflected in the URL
4. **Form State**: Managed within form components

No central state management library is currently in use.

## TypeScript Usage

TypeScript is used throughout the codebase with varying levels of type safety:

### Interface Definitions

```tsx
// src/types/services.ts
export interface ServiceType {
  id: string;
  title: string;
  description: string;
  image: string;
  features: string[];
  summerOnly?: boolean;
  winterOnly?: boolean;
}
```

### Type Assertions

```tsx
function getProjectImage(project: any) {
  // Using type assertion
  return (project as ProjectType).image || '/images/placeholder.webp';
}
```

### Generic Types

```tsx
function useFetch<T>(url: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Implementation...

  return { data, loading, error };
}
```

## Areas for Pattern Improvement

Based on the current implementation patterns, several areas could benefit from standardization:

1. **Component Structure**: Standardize on functional components with explicit prop interfaces
2. **State Management**: Adopt a more consistent approach (either Context API or a state management library)
3. **TypeScript Usage**: Improve type safety by reducing `any` usage and adding proper generics
4. **Styling Approach**: Standardize conditional styling with utility functions
5. **Data Fetching**: Implement consistent data fetching patterns

These improvements would enhance code quality while maintaining the existing functionality. ```

#### `03-implementation-patterns\02-component-patterns.md`

```markdown
# Component-Specific Review

This document provides a detailed analysis of key components in the Ringerike Landskap website, examining their implementation, strengths, and areas for improvement.

## Core Layout Components

### Header (`src/components/layout/Header.tsx`)

The Header component provides the main navigation for the website, with both desktop and mobile versions.

**Implementation Details:**

-   Uses React hooks (`useState`, `useEffect`) for state management
-   Implements responsive design with different layouts for mobile and desktop
-   Includes a sticky positioning with background opacity changes on scroll
-   Uses the Lucide React library for icons

**Strengths:**

-   Clean separation of mobile and desktop navigation
-   Effective use of React hooks for state management
-   Good responsive behavior with appropriate breakpoints
-   Active state clearly indicates current page

**Improvement Opportunities:**

-   The mobile menu transition could be smoother with CSS transitions
-   Consider using `useCallback` for event handlers
-   The logo sizing could be more responsive on smaller screens
-   Add ARIA attributes for better accessibility

### Footer (`src/components/layout/Footer.tsx`)

The Footer component provides contact information, opening hours, and navigation links.

**Implementation Details:**

-   Organized into multiple sections (company info, contact, opening hours)
-   Uses Lucide React icons for visual elements
-   Implements responsive grid layout
-   Dynamically calculates the current year for copyright

**Strengths:**

-   Comprehensive information architecture
-   Good use of icons to enhance readability
-   Responsive layout adapts well to different screen sizes
-   Clear organization of content sections

**Improvement Opportunities:**

-   Consider extracting sub-components for better maintainability
-   Add more structured microdata for SEO
-   Enhance social media integration
-   Implement a newsletter signup form

### Hero (`src/components/ui/Hero.tsx`)

The Hero component creates a full-width banner with background image, text, and call-to-action.

**Implementation Details:**

-   Configurable through props (height, overlay, text alignment)
-   Uses multiple background layers for visual effect
-   Implements responsive typography
-   Includes a location badge and CTA button

**Strengths:**

-   Highly customizable through props
-   Effective use of overlay gradients for text readability
-   Responsive design with appropriate text sizing
-   Good visual hierarchy with clear focal points

**Improvement Opportunities:**

-   The multiple overlay layers could be simplified for better performance
-   Consider using CSS variables for more consistent styling
-   Add animation options for more dynamic presentations
-   Implement image preloading for faster rendering

## UI Components

### ServiceCard (`src/components/ServiceCard.tsx`)

The ServiceCard component displays a service with image, title, description, and action link.

**Implementation Details:**

-   Uses background image with overlay gradient
-   Implements hover effects with scale transformation
-   Includes title, description, and action button
-   Uses React Router's Link component for navigation

**Strengths:**

-   Attractive design with good use of imagery
-   Effective hover interaction enhances user experience
-   Good text contrast against the gradient overlay
-   Clean, focused presentation of service information

**Improvement Opportunities:**

-   Add image loading optimization
-   Implement consistent aspect ratios
-   Consider adding truncation for long descriptions
-   Enhance the action button's visual prominence

### ContactForm (`src/components/ContactForm.tsx`)

The ContactForm component provides a form for users to contact the company.

**Implementation Details:**

-   Includes various input fields (name, email, phone, etc.)
-   Uses grid layout for responsive field arrangement
-   Implements focus states for form fields
-   Includes a submit button with hover effect

**Strengths:**

-   Clean layout with appropriate spacing
-   Responsive design with field reordering on smaller screens
-   Good focus states for improved usability
-   Logical grouping of related fields

**Improvement Opportunities:**

-   Add form validation with error messages
-   Implement form submission handling
-   Add loading state for the submit button
-   Include a privacy policy consent checkbox

## Feature Components

### SeasonalProjectsCarousel (`src/features/home/<USER>

This component displays a carousel of seasonal projects on the home page.

**Implementation Details:**

-   Fetches project data using a custom hook
-   Implements carousel navigation with next/previous buttons
-   Displays project cards with images and information
-   Includes loading state for data fetching

**Strengths:**

-   Dynamic content based on seasonal relevance
-   Good loading state implementation
-   Effective navigation controls
-   Responsive design adapts to different screen sizes

**Improvement Opportunities:**

-   Add keyboard navigation support
-   Implement touch swipe gestures for mobile
-   Add pagination indicators
-   Optimize image loading with lazy loading

### FilteredServicesSection (`src/features/home/<USER>

This component displays services filtered by seasonal relevance.

**Implementation Details:**

-   Fetches service data using a custom hook
-   Filters services based on seasonal relevance
-   Displays service cards in a responsive grid
-   Includes loading state for data fetching

**Strengths:**

-   Dynamic content filtering based on current season
-   Clean grid layout with appropriate spacing
-   Good loading state implementation
-   Responsive design with grid adjustments

**Improvement Opportunities:**

-   Add category filtering options
-   Implement animation for smoother transitions
-   Consider adding a "View All" option
-   Optimize image loading performance

## Page Components

### HomePage (`src/pages/home/<USER>

The HomePage component serves as the landing page for the website.

**Implementation Details:**

-   Combines multiple components (Hero, SeasonalProjectsCarousel, etc.)
-   Uses custom hooks for data fetching
-   Implements responsive grid layouts
-   Includes loading states for async content

**Strengths:**

-   Good component composition
-   Effective use of custom hooks for data fetching
-   Clean, organized structure
-   Appropriate loading states for async content

**Improvement Opportunities:**

-   Consider implementing code splitting for performance
-   Add more structured microdata for SEO
-   Implement scroll restoration for navigation
-   Add animation for page transitions

## Utility Components

### Container (`src/components/ui/Container.tsx`)

The Container component provides consistent horizontal padding and maximum width.

**Implementation Details:**

-   Accepts className prop for customization
-   Implements responsive padding
-   Sets maximum width with centered alignment
-   Uses React's children prop for content

**Strengths:**

-   Simple, reusable implementation
-   Consistent spacing across the site
-   Responsive padding adjustments
-   Easy to customize through className prop

**Improvement Opportunities:**

-   Add more size variants (narrow, wide, etc.)
-   Consider implementing vertical padding options
-   Add max-width customization through props
-   Implement background color options

## Recommendations for Component Improvements

1. **Implement Lazy Loading**

    - Add lazy loading to image components
    - Use React.lazy for code splitting
    - Implement intersection observer for below-fold content

2. **Enhance Accessibility**

    - Add ARIA attributes to interactive components
    - Implement keyboard navigation support
    - Add skip links for keyboard users
    - Ensure sufficient color contrast

3. **Optimize Performance**

    - Memoize expensive computations with useMemo
    - Use useCallback for event handlers
    - Implement virtualization for long lists
    - Optimize image loading and rendering

4. **Improve Component Architecture**

    - Extract reusable sub-components
    - Implement compound components where appropriate
    - Use context for deeply nested component trees
    - Add better TypeScript typing for props

5. **Enhance User Experience**
    - Add meaningful animations and transitions
    - Implement better loading states
    - Add error handling and recovery
    - Enhance form validation and feedback
```

#### `03-implementation-patterns\03-target-patterns.md`

```markdown
# Target Implementation Patterns

This document outlines the recommended implementation patterns for the Ringerike Landskap website codebase. These patterns should be followed for all new code and when refactoring existing code.

## Component Patterns

### Functional Components with TypeScript

All components should use functional components with explicit TypeScript interfaces for props:

```tsx
interface ButtonProps {
  /** The content to be rendered inside the button */
  children: React.ReactNode;
  /** Button variant style */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Function called when button is clicked */
  onClick?: () => void;
  /** Whether the button is disabled */
  isDisabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Button component for user interactions
 */
function Button({
  children,
  variant = 'primary',
  onClick,
  isDisabled = false,
  className = '',
}: ButtonProps) {
  const baseClasses = 'px-4 py-2 rounded transition-colors';

  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary-dark',
    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',
    outline: 'border border-primary text-primary hover:bg-primary-light'
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={onClick}
      disabled={isDisabled}
    >
      {children}
    </button>
  );
}

export default Button;
```

### Component Organization

Components should follow this organizational pattern:

1. Import statements
2. Type definitions
3. Constants and helper functions
4. Component definition
5. Export statement

Example:

```tsx
import { useState, useEffect } from 'react';
import { formatDate } from '@/utils/date';
import { ProjectType } from '@/types';

interface ProjectCardProps {
  project: ProjectType;
  isHighlighted?: boolean;
}

const MAX_DESCRIPTION_LENGTH = 150;

function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

function ProjectCard({ project, isHighlighted = false }: ProjectCardProps) {
  const formattedDate = formatDate(project.completedDate);
  const truncatedDescription = truncateText(project.description, MAX_DESCRIPTION_LENGTH);

  return (
    <div className={`card ${isHighlighted ? 'bg-primary-light' : 'bg-white'}`}>
      <img src={project.image} alt={project.title} className="card-image" />
      <div className="card-content">
        <h3 className="card-title">{project.title}</h3>
        <p className="card-description">{truncatedDescription}</p>
        <span className="card-date">{formattedDate}</span>
      </div>
    </div>
  );
}

export default ProjectCard;
```

## State Management

### React Context for Shared State

Use React Context with custom hooks for shared state:

```tsx
// context.tsx
import { createContext, useContext, useState, ReactNode } from 'react';
import { ServiceType } from '@/types';
import { services as servicesData } from '@/data/services';

interface ServicesContextType {
  services: ServiceType[];
  filteredServices: ServiceType[];
  filterServices: (category: string) => void;
  isLoading: boolean;
}

const ServicesContext = createContext<ServicesContextType | undefined>(undefined);

export function ServicesProvider({ children }: { children: ReactNode }) {
  const [services] = useState<ServiceType[]>(servicesData);
  const [filteredServices, setFilteredServices] = useState<ServiceType[]>(servicesData);
  const [isLoading, setIsLoading] = useState(false);

  function filterServices(category: string) {
    setIsLoading(true);

    try {
      if (category === 'all') {
        setFilteredServices(services);
      } else {
        setFilteredServices(services.filter(service => service.category === category));
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ServicesContext.Provider value={{
      services,
      filteredServices,
      filterServices,
      isLoading
    }}>
      {children}
    </ServicesContext.Provider>
  );
}

// Custom hook for using the context
export function useServices() {
  const context = useContext(ServicesContext);

  if (context === undefined) {
    throw new Error('useServices must be used within a ServicesProvider');
  }

  return context;
}
```

### Local Component State

For component-specific state, use the useState hook with proper typing:

```tsx
function SearchForm() {
  const [query, setQuery] = useState<string>('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Rest of the component
}
```

## Styling Patterns

### Tailwind with Composition

Use Tailwind CSS with composition utilities:

```tsx
import { clsx } from 'clsx';

interface CardProps {
  title: string;
  content: string;
  variant?: 'default' | 'highlight' | 'muted';
  className?: string;
}

function Card({ title, content, variant = 'default', className }: CardProps) {
  const baseClasses = 'rounded-lg p-6 shadow-sm';

  const variantClasses = {
    default: 'bg-white',
    highlight: 'bg-primary-light border border-primary',
    muted: 'bg-gray-50 text-gray-700'
  };

  return (
    <div className={clsx(
      baseClasses,
      variantClasses[variant],
      className
    )}>
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      <p>{content}</p>
    </div>
  );
}
```

### Responsive Design

Always implement responsive design using Tailwind's responsive modifiers:

```tsx
function ProjectGrid({ projects }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map(project => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}
```

## Data Management

### TypeScript Interfaces

Define comprehensive interfaces for all data structures:

```tsx
// types/project.ts
export interface ProjectType {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  location: string;
  completedDate: string;
  client?: string;
  features: string[];
  images?: string[];
  isFeatured?: boolean;
  seasonalRelevance?: ('summer' | 'winter' | 'spring' | 'autumn')[];
}
```

### Data Utilities

Create typed utility functions for data operations:

```tsx
// utils/projects.ts
import { ProjectType } from '@/types';
import { projects } from '@/data/projects';

/**
 * Returns a project by its ID
 */
export function getProjectById(id: string): ProjectType | undefined {
  return projects.find(project => project.id === id);
}

/**
 * Returns projects filtered by category
 */
export function getProjectsByCategory(category: string): ProjectType[] {
  if (category === 'all') return projects;
  return projects.filter(project => project.category === category);
}

/**
 * Returns featured projects
 */
export function getFeaturedProjects(limit?: number): ProjectType[] {
  const featured = projects.filter(project => project.isFeatured);
  return limit ? featured.slice(0, limit) : featured;
}

/**
 * Returns projects relevant for the current season
 */
export function getSeasonalProjects(): ProjectType[] {
  const currentMonth = new Date().getMonth();
  let currentSeason: 'winter' | 'spring' | 'summer' | 'autumn';

  if (currentMonth >= 2 && currentMonth <= 4) currentSeason = 'spring';
  else if (currentMonth >= 5 && currentMonth <= 7) currentSeason = 'summer';
  else if (currentMonth >= 8 && currentMonth <= 10) currentSeason = 'autumn';
  else currentSeason = 'winter';

  return projects.filter(project =>
    !project.seasonalRelevance ||
    project.seasonalRelevance.includes(currentSeason)
  );
}
```

## Error Handling

### Error Boundaries

Implement error boundaries for component error handling:

```tsx
import { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  fallback?: ReactNode;
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary p-4 bg-red-50 text-red-800 rounded border border-red-300">
          <h2>Something went wrong.</h2>
          <p>{this.state.error?.message || 'Unknown error'}</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### Try-Catch for Async Operations

Always use try-catch for async operations:

```tsx
async function fetchData() {
  try {
    setLoading(true);
    setError(null);

    const response = await fetch('/api/data');

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();
    setData(data);
  } catch (err) {
    setError(err instanceof Error ? err : new Error('Unknown error'));
  } finally {
    setLoading(false);
  }
}
```

## Performance Optimizations

### React.memo for Pure Components

Use React.memo for components that render often but with the same props:

```tsx
import { memo } from 'react';

interface PriceTagProps {
  amount: number;
  currency?: string;
}

function PriceTag({ amount, currency = 'NOK' }: PriceTagProps) {
  const formattedPrice = new Intl.NumberFormat('nb-NO', {
    style: 'currency',
    currency,
  }).format(amount);

  return <span className="price">{formattedPrice}</span>;
}

export default memo(PriceTag);
```

### useMemo for Expensive Calculations

Use useMemo for expensive calculations:

```tsx
import { useMemo } from 'react';

function ProjectStats({ projects }) {
  const statistics = useMemo(() => {
    return {
      total: projects.length,
      featured: projects.filter(p => p.isFeatured).length,
      byCategory: projects.reduce((acc, project) => {
        acc[project.category] = (acc[project.category] || 0) + 1;
        return acc;
      }, {})
    };
  }, [projects]);

  return (
    <div className="stats">
      <p>Total Projects: {statistics.total}</p>
      <p>Featured: {statistics.featured}</p>
      {/* Rest of the component */}
    </div>
  );
}
```

### Lazy Loading for Routes

Use React.lazy and Suspense for route-based code splitting:

```tsx
import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Loading from './components/ui/Loading';

// Lazy-loaded components
const HomePage = lazy(() => import('./pages/home'));
const AboutPage = lazy(() => import('./pages/about'));
const ServicesPage = lazy(() => import('./pages/services'));
const ProjectsPage = lazy(() => import('./pages/projects'));
const ContactPage = lazy(() => import('./pages/contact'));

function App() {
  return (
    <Router>
      <Suspense fallback={<Loading />}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/hvem-er-vi" element={<AboutPage />} />
          <Route path="/hva-vi-gjor" element={<ServicesPage />} />
          <Route path="/prosjekter" element={<ProjectsPage />} />
          <Route path="/kontakt" element={<ContactPage />} />
        </Routes>
      </Suspense>
    </Router>
  );
}
```

## Testing

### Component Tests

Write unit tests for components:

```tsx
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button component', () => {
  test('renders correctly with default props', () => {
    render(<Button>Click me</Button>);

    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary');
    expect(button).not.toBeDisabled();
  });

  test('renders with different variants', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-primary');

    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-gray-200');

    rerender(<Button variant="outline">Outline</Button>);
    expect(screen.getByRole('button')).toHaveClass('border-primary');
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('is disabled when isDisabled is true', () => {
    render(<Button isDisabled>Disabled</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

By following these patterns, we can improve code quality, maintainability, and performance while ensuring a consistent development experience. ```

#### `04-visual-systems\01-screenshot-infrastructure.md`

```markdown
# Screenshot Infrastructure

This document provides a detailed explanation of the screenshot capture system, which is a critical component of the Ringerike Landskap website development workflow.

## System Overview

The screenshot infrastructure captures and maintains visual records of the website, serving several important purposes:

1. **Visual Documentation**: Provides snapshots of the website's appearance at different stages
2. **AI-Assisted Development**: Enables AI tools to "see" the website output
3. **Visual Regression Testing**: Allows comparison of changes over time
4. **Responsive Design Verification**: Captures multiple viewport sizes

## Key Components

### Directory Structure

```
project/
├── .ai-analysis/               # AI-specific screenshots
│   ├── latest/                 # Most recent AI analysis
│   ├── snapshot-[timestamp]/   # Historical AI snapshots
│   └── metadata.json           # Snapshot tracking information
├── screenshots/                # General screenshot storage
│   ├── latest/                 # Most recent screenshots
│   ├── [YYYY-MM-DD_HH-MM-SS]/  # Timestamped screenshot sets
│   └── screenshot-report.html  # Visual report of screenshots
└── scripts/                    # Screenshot automation
    ├── screenshot-manager.js   # Main screenshot management
    ├── dev-with-snapshots.js   # Development with auto-capture
    ├── auto-snapshot.js        # Automated snapshot tool
    └── various helper scripts
```

### Core Scripts

1. **screenshot-manager.js**: Central script that handles:
   - Capturing screenshots at specified viewport sizes
   - Organizing screenshots into timestamped directories
   - Generating HTML reports
   - Cleaning up old screenshot sets

2. **dev-with-snapshots.js**: Enhanced development server that:
   - Starts the Vite development server
   - Watches for file changes
   - Automatically captures screenshots when changes occur
   - Updates the `latest` directories

3. **auto-snapshot.js**: AI-focused tool that:
   - Captures specific screenshots for AI analysis
   - Maintains the `.ai-analysis` directory
   - Tracks metadata about captures

### Capture Configuration

Screenshots are captured at these standard viewport sizes:

- **Mobile**: 375×667px
- **Tablet**: 768×1024px
- **Desktop**: 1440×900px

And for these primary pages:

- Home (/)
- About (/hvem-er-vi)
- Services (/hva-vi-gjor)
- Projects (/prosjekter)
- Contact (/kontakt)

## Usage in Development

### Standard Development Workflow

```bash
npm run dev:ai
```

This command:
1. Starts the Vite development server
2. Sets up file watchers for auto-capturing
3. Maintains the `latest` directories with current screenshots

### Manual Screenshot Capture

```bash
npm run screenshots:capture
```

This command manually captures a full set of screenshots across all configured viewports and pages.

### Screenshot Management

```bash
# View screenshot help
npm run screenshots

# Clean old screenshots (older than 7 days by default)
npm run screenshots:clean

# Refresh screenshots (capture, tidy, and clean)
npm run screenshots:refresh
```

## Integration with AI Tools

The system is designed to help AI tools (like Cursor) understand the visual output:

1. AI-specific snapshots are stored in `.ai-analysis/latest/`
2. Metadata about snapshots is maintained in `.ai-analysis/metadata.json`
3. Scripts detect changes and automatically update visual information

When working with AI assistants, they can reference:
```
/project/.ai-analysis/latest/
```

This provides consistent access to current visual state regardless of when snapshots were taken.

## Technical Implementation

The system uses:

- **Puppeteer**: For headless browser capture
- **Node.js file system APIs**: For directory management
- **Custom HTML templates**: For report generation
- **File watchers**: For detecting changes during development

## Critical Considerations

When working with the codebase:

1. **Do not modify** the core screenshot scripts without careful testing
2. **Do not delete** the `.ai-analysis` or `screenshots` directories
3. **Use the provided npm scripts** rather than calling scripts directly
4. Be aware that screenshot capture adds some overhead to development

## Extending the System

To add new pages or viewports to the capture system:

1. Edit `capture-website.js` to update the configuration
2. Add new entries to the `pages` or `viewports` arrays
3. Run `npm run screenshots:capture` to test

## Troubleshooting

If screenshots aren't being captured:

1. Ensure the development server is running
2. Check that the page is accessible at the expected URL
3. Verify there are no JavaScript errors preventing render
4. Try running `npm run screenshots:capture` manually

The screenshot infrastructure is a critical part of the development workflow and should be maintained carefully as the project evolves. ```

#### `04-visual-systems\02-design-guidelines.md`

```markdown
# Ringerike Landskap Website Design Guidelines

## Introduction

This document defines the UI and UX guidelines for the Ringerike Landskap website, ensuring a cohesive, brand-aligned, and user-friendly digital experience. It serves as an implementation blueprint for developers and designers.

## Objectives

-   Provide a structured, intuitive interface that clearly presents services and projects
-   Ensure seamless navigation with a user journey that mirrors how customers discover and engage with Ringerike Landskap
-   Maintain brand identity through consistent typography, colors, and imagery
-   Optimize for performance, accessibility, and SEO

## User Experience (UX) & Information Architecture

The UX design is clean, direct, and conversion-driven, guiding visitors toward understanding services, exploring past work, and making contact. The structure is designed to be logical and easy to navigate.

### Navigation & Site Structure

The website is structured into five core sections, accessible via the main navigation bar:

-   **Hjem** (Home) – The landing page providing a broad overview and brand introduction
-   **Hva vi gjør** (What We Do / Services) – A detailed presentation of landscaping and related services
-   **Prosjekter** (Projects) – A portfolio/gallery of past projects showcasing work quality
-   **Hvem er vi** (Who We Are / About Us) – Information about the company, team, and values
-   **Kontakt** (Contact) – Contact information and a form for inquiries or quotes

### User Journey

- **Browsing Services**: Visitors land on the homepage or "Hva vi gjør" page to learn about the firm's core landscaping solutions.
- **Exploring Projects**: They can explore real examples under "Prosjekter," filtering results by interest—like building a new terrace or installing a støttemur.
- **Seasonal Tips**: The site subtly adapts to reflect current or upcoming seasons, guiding users to relevant services (e.g., planting ideas in spring).
- **Filtering**: Users filter projects by category or location to find relevant examples.
- **Social Proof**: Testimonials provide proof and build trust throughout the decision-making process.
- **Requesting a Quote**: When ready, they fill out the contact form or tap a "Book gratis befaring" button to schedule an on-site evaluation.
- **Personal Follow-Up**: The Ringerike Landskap team personally contacts clients to discuss specific needs, ensuring straightforward, no-frills communication.

## Visual Design System

### Color Palette

The color palette is centered around greens to reflect the landscaping focus, with complementary neutrals:

-   **Primary Green**: #1e9545 (Used for primary buttons, links, and accents)
-   **Dark Green**: #0d6b30 (Used for hover states and secondary elements)
-   **Light Green**: #e6f4ea (Used for backgrounds and subtle highlights)
-   **Neutral Dark**: #333333 (Used for primary text)
-   **Neutral Mid**: #767676 (Used for secondary text)
-   **Neutral Light**: #f5f5f5 (Used for backgrounds and dividers)
-   **White**: #ffffff (Used for backgrounds and text on dark colors)

### Typography

The website uses the Inter font family throughout:

-   **Headings**: Inter Bold (700)
    -   H1: 2.5rem (40px)
    -   H2: 2rem (32px)
    -   H3: 1.5rem (24px)
    -   H4: 1.25rem (20px)
-   **Body Text**: Inter Regular (400), 1rem (16px)
-   **Small Text/Captions**: Inter Regular (400), 0.875rem (14px)
-   **Buttons/CTAs**: Inter Medium (500), 1rem (16px)

### Imagery & Icons

-   **Photography**: High-quality images of completed landscaping projects, focusing on natural elements
-   **Icons**: Simple, line-based icons for UI elements (using Lucide React library)
-   **Illustrations**: Minimal use, primarily for decorative purposes

## User Interface Elements

### Visual Style
- Clean design with high-quality imagery showcasing landscaping work across prioritized services
- Green accents reflect nature and sustainability
- Balance of white space and content for readability

### Call-to-Actions
- Prominent buttons like "Book Gratis Befaring" should stand out visually to encourage conversions
- Consistent styling for all interactive elements
- Clear visual hierarchy to guide user attention

### Project Showcase
- Grid-style layout with filters for easy exploration of completed works
- Projects categorized by job type or location
- High-quality images with consistent aspect ratios

## Seasonal Content Adaptation

The website content adapts based on the current season to highlight seasonally-relevant services and projects:

### Spring (Vår)
- **Focus on**: Hekk og Beplantning, Ferdigplen
- **Relevant tags**: beplantning, plen, hage
- **Visual cues**: Fresh greenery, new plantings, garden preparations

### Summer (Sommer)
- **Focus on**: Platting, Cortenstål, Belegningsstein
- **Relevant tags**: terrasse, uteplass, innkjørsel
- **Visual cues**: Outdoor living spaces, entertaining areas, completed projects

### Fall (Høst)
- **Focus on**: Støttemurer, Kantstein, Trapper og Repoer
- **Relevant tags**: terrengforming, støttemur, trapp
- **Visual cues**: Structural elements, terrain shaping, preparation for winter

### Winter (Vinter)
- **Focus on**: Planning and Design
- **Relevant tags**: planlegging, design, prosjektering
- **Visual cues**: Design sketches, planning elements, winter-ready landscapes

## Responsive Design Practices

- **Mobile-First Approach**: Start with mobile layout and enhance for larger screens
- **Fluid Typography**: Scale text based on screen size
- **Flexible Images**: Ensure images adapt to their containers
- **Touch-Friendly UI**: Larger touch targets on mobile
- **Performance Optimization**: Lazy loading and optimized assets
- **Testing**: Test on multiple devices and screen sizes
- **Accessibility**: Ensure accessibility across all screen sizes

## Project Categories & Locations

### Service Categories
- Belegningsstein
- Cortenstål
- Støttemurer
- Platting
- Ferdigplen
- Kantstein
- Trapper og Repoer
- Hekk og Beplantning

### Service Areas
- Røyse (Main Base)
- Hønefoss
- Hole kommune
- Jevnaker
- Sundvollen
- Vik

## Component Guidelines

### Buttons

-   **Primary Button**: Green background (#1e9545), white text, slight rounded corners (4px)
-   **Secondary Button**: White background, green border, green text
-   **Tertiary Button/Text Link**: No background, green text with subtle underline on hover

### Cards

-   **Service Cards**: Image background with gradient overlay, white text, subtle hover effect
-   **Project Cards**: Image with consistent aspect ratio, title and location information below

### Forms

-   **Input Fields**: Clear labels, light background, green focus state
-   **Validation**: Inline validation with clear error messages
-   **Submit Buttons**: Primary button style with loading state

## Responsive Design

The website follows a mobile-first approach with three main breakpoints:

-   **Mobile**: < 768px
-   **Tablet**: 768px - 1023px
-   **Desktop**: ≥ 1024px

Key responsive considerations:

-   Navigation collapses to hamburger menu on mobile
-   Grid layouts adjust from multi-column to single-column
-   Font sizes scale proportionally
-   Touch targets are appropriately sized for mobile

## Implementation Notes

-   Use Tailwind CSS for styling with custom theme configuration
-   Implement responsive images with appropriate sizing and formats
-   Ensure accessibility compliance with WCAG 2.1 standards
-   Optimize performance with lazy loading and code splitting
```

#### `04-visual-systems\03-styling-approach.md`

```markdown
# Ringerike Landskap Website Styling and Theming

## Color Palette

The website uses a custom green-focused color palette that reflects the landscaping business's connection to nature:

```
colors: {
  green: {
    50: '#f0f9f4',   // Very light green (backgrounds, hover states)
    100: '#dbf0e3',  // Light green (backgrounds, borders)
    200: '#b8e0ca',  // Light green (secondary elements)
    300: '#89c9a8',  // Medium green (accents)
    400: '#57ab83',  // Medium green (secondary buttons)
    500: '#1e9545',  // Primary green (buttons, links)
    600: '#157c3a',  // Darker green (hover states)
    700: '#116530',  // Dark green (active states)
    800: '#0e4f27',  // Very dark green (text on light backgrounds)
    900: '#0c4121',  // Darkest green (text on light backgrounds)
  },
}
```

Other colors used:

-   White (`#ffffff`) - Backgrounds, text on dark surfaces
-   Gray shades - Text, borders, subtle backgrounds
-   Black with opacity - Overlays, shadows

## Typography

The website uses a clean, modern typography system:

```
fontFamily: {
  sans: ['Inter', 'system-ui', 'sans-serif'],
}
```

Font sizes follow a hierarchical scale:

-   Headings:
    -   H1: `text-4xl sm:text-5xl lg:text-6xl` (2.25rem to 3.75rem)
    -   H2: `text-xl sm:text-2xl` (1.25rem to 1.5rem)
    -   H3: `text-lg font-semibold` (1.125rem)
-   Body: `text-base` (1rem)
-   Small text: `text-sm` (0.875rem)
-   Micro text: `text-xs` (0.75rem)

Font weights:

-   Regular: 400
-   Medium: 500
-   Semibold: 600
-   Bold: 700

## Spacing System

The website uses Tailwind's default spacing scale with consistent spacing throughout:

-   Container padding: `px-4 sm:px-6 lg:px-8`
-   Vertical section spacing: `py-8 sm:py-12`
-   Grid gaps: `gap-6 sm:gap-8`
-   Component padding: `px-4 py-3` (buttons), `p-6` (cards)

## Animations

Custom animations defined in the Tailwind config:

```
animation: {
  'fade-in': 'fadeIn 0.5s ease-out forwards',
  'slide-up': 'slideUp 0.5s ease-out forwards',
  'gradient': 'gradient 8s ease-in-out infinite',
},
keyframes: {
  fadeIn: {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
  slideUp: {
    '0%': {
      opacity: '0',
      transform: 'translateY(20px)'
    },
    '100%': {
      opacity: '1',
      transform: 'translateY(0)'
    },
  },
  gradient: {
    '0%, 100%': { backgroundPosition: '0% 50%' },
    '50%': { backgroundPosition: '100% 50%' },
  },
}
```

These animations are used for:

-   Page transitions
-   Component mounting
-   Hover effects
-   Background gradients

## UI Components

### Buttons

Primary button:

```
className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
```

Secondary button:

```
className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
```

### Cards

Service/Project card:

```
className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1"
```

### Hero Section

The Hero component uses multiple overlays for text readability:

1. Base background image with fixed attachment
2. Gradient overlay for text contrast
3. Radial gradient for corner darkening

### Navigation

Desktop navigation:

```
className="hidden md:flex md:items-center md:space-x-8"
```

Mobile navigation:

```
className="md:hidden"
```

Active link:

```
className="text-green-600"
```

Inactive link:

```
className="text-gray-700 hover:text-green-600 transition-colors"
```

## Responsive Design

The website uses a mobile-first approach with breakpoints:

-   Default: Mobile styles
-   `sm`: 640px and up
-   `md`: 768px and up
-   `lg`: 1024px and up
-   `xl`: 1280px and up

Key responsive patterns:

-   Single column on mobile, multi-column on larger screens
-   Hidden navigation on mobile, visible on desktop
-   Adjusted font sizes across breakpoints
-   Flexible spacing that increases on larger screens

## Shadows and Depth

The website uses a consistent shadow system for depth:

-   Cards: `shadow-md`
-   Hover states: `shadow-lg`
-   Buttons: `shadow-sm`
-   Header (when scrolled): `shadow-md`

## Special Effects

1. **Backdrop Blur**:

    ```
    className="bg-white/90 backdrop-blur-sm"
    ```

    Used in the header for a frosted glass effect

2. **Gradient Overlays**:

    ```
    background: linear-gradient(50deg, transparent 15%, rgba(0,0,0,0.75) 32%, rgba(0,0,0,0.6) 72%, transparent 85%)
    ```

    Used in the hero section for text readability

3. **Hover Transitions**:

    ```
    className="transition-colors hover:text-green-600"
    ```

    Smooth color changes on hover

4. **Scroll Animations**:
   Elements fade in and slide up as they enter the viewport
```

#### `05-development-workflow\01-environment-setup.md`

```markdown
# Development Environment Setup

This document provides instructions for setting up the development environment for the Ringerike Landskap website and outlines the recommended workflow.

## Prerequisites

Before starting development, ensure you have the following installed:

- **Node.js**: v18.x or later
- **npm**: v8.x or later
- **Git**: For version control
- A modern code editor (VS Code recommended)

## Initial Setup

1. **Clone the repository**:

   ```bash
   git clone [repository-url]
   cd rl-website_web/project
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Verify the installation**:

   ```bash
   npm run dev
   ```

   This should start the development server and make the website available at http://localhost:5173/.

## Development Workflows

### Standard Development

For regular development work without screenshot capture:

```bash
npm run dev
```

This starts the Vite development server with hot module replacement.

### Development with Screenshots

For development with automatic screenshot capturing:

```bash
npm run dev:ai
```

This starts the development server and sets up automatic screenshot capture. Screenshots will be captured whenever significant changes are detected and stored in both the `screenshots/latest/` and `.ai-analysis/latest/` directories.

### Manual Screenshot Management

```bash
# Capture screenshots manually
npm run screenshots:capture

# Clean up old screenshots
npm run screenshots:clean

# Refresh screenshots (capture, tidy, and clean)
npm run screenshots:refresh
```

## Working with the Codebase

### Folder Structure

When working with the codebase, follow these guidelines:

1. **Components**: Place reusable components in `src/components/`
   - UI components in `src/components/ui/`
   - Layout components in `src/components/layout/`

2. **Pages**: Place page components in `src/pages/`
   - Each page in its own directory
   - Use `index.tsx` for the main page component

3. **Feature-specific components**: Place in `src/features/[feature-name]/`

4. **Data**: Place data files in `src/data/`

5. **Types**: Place TypeScript interfaces in `src/types/`

### Coding Standards

Follow these standards when writing code:

1. **TypeScript**: Use TypeScript for all new code
   - Define interfaces for props and data structures
   - Avoid using `any` type
   - Use proper type guards

2. **Components**: Use functional components with hooks
   - Define explicit prop interfaces
   - Use destructuring for props
   - Export components as named exports

3. **Styling**: Use Tailwind CSS for styling
   - Group related classes together
   - Use clsx or tailwind-merge for conditional classes
   - Follow the existing color scheme and design system

4. **State Management**: Prefer React Context for shared state
   - Use local state for component-specific state
   - Avoid prop drilling where possible

### Git Workflow

Follow this workflow for version control:

1. Create a branch for your feature or fix
2. Make your changes, following the coding standards
3. Run tests and ensure all features work
4. Create a pull request with a clear description
5. Address review comments
6. Merge only when approved

## Build and Deployment

### Building for Production

To build the website for production:

```bash
npm run build
```

This creates optimized production files in the `dist/` directory.

### Preview Production Build

To preview the production build locally:

```bash
npm run preview
```

This serves the production build for local testing.

## Troubleshooting

### Development Server Issues

If the development server fails to start:

1. Check if another process is using port 5173
2. Try deleting `node_modules` and reinstalling dependencies
3. Check for errors in the console output

### Screenshot Capture Issues

If screenshots aren't being captured:

1. Ensure the development server is running
2. Check that Puppeteer can access the server
3. Try running screenshots manually with `npm run screenshots:capture`
4. Check the console for error messages

## AI-Assisted Development

When working with AI tools (like Cursor):

1. Make sure screenshots are up to date using `npm run screenshots:capture`
2. Reference the current visual state using the `.ai-analysis/latest/` directory
3. Provide clear context about the component or feature you're working on
4. Reference the appropriate documentation for context

This setup ensures a smooth development experience while maintaining high code quality and consistent visual output. ```

#### `05-development-workflow\02-optimization-guide.md`

```markdown
# Ringerike Landskap Website Optimization Guide

## Overview

This guide outlines the performance optimization strategies and implementation patterns for the Ringerike Landskap website to ensure fast loading times, responsive behavior, and an optimal user experience across all devices.

## Performance Goals

-   **Load Time**: Initial page load under 2 seconds on 4G connections
-   **First Contentful Paint (FCP)**: Under 1.5 seconds
-   **Time to Interactive (TTI)**: Under 3.5 seconds
-   **Cumulative Layout Shift (CLS)**: <0.1
-   **Largest Contentful Paint (LCP)**: <2.5 seconds
-   **First Input Delay (FID)**: <100ms
-   **Lighthouse Score**: 90+ in all categories (Performance, Accessibility, Best Practices, SEO)

## Asset Optimization

### Images

-   **WebP Format**: Convert all images to WebP format with fallbacks for older browsers
-   **Lazy Loading**: Implement lazy loading for images below the fold
-   **Responsive Images**: Use the `srcset` attribute to serve different image sizes based on viewport
-   **Size Attributes**: Include width and height attributes to prevent layout shifts
-   **Optimization Tool**: Use ImageOptim or similar tools for further compression
-   **CDN Integration**: Serve images through a CDN for faster delivery

### Fonts

-   **Font Display**: Use `font-display: swap` to prevent invisible text during font loading
-   **Font Subsetting**: Only include character sets that are needed
-   **WOFF2 Format**: Use WOFF2 as the primary format with fallbacks
-   **Preload Critical Fonts**: Preload fonts used in above-the-fold content
-   **System Fonts**: Use system fonts for non-branding text elements

## JavaScript Optimization

### Code Splitting

-   **Route-Based Splitting**: Split code by routes to load only what's needed
-   **Component-Level Splitting**: Lazy load heavy components that aren't immediately visible
-   **Vendor Chunking**: Separate vendor code that changes less frequently
-   **Dynamic Imports**: Use dynamic imports for conditional functionality

```javascript
// Example of lazy loading a component
const HeavyComponent = React.lazy(() => import('./HeavyComponent'));

// Usage with Suspense
<Suspense fallback={<div>Loading...</div>}>
  <HeavyComponent />
</Suspense>
```

### Bundle Size Management

-   **Tree Shaking**: Ensure unused code is eliminated
-   **Dependencies Audit**: Regularly review and minimize dependencies
-   **Bundle Analyzer**: Use Webpack Bundle Analyzer or Vite's bundle analysis tools to identify large packages
-   **Code Minification**: Ensure all production code is minified and compressed

## Component Optimization Patterns

### Responsive Optimization

- **Mobile-First Approach**: Start with mobile layout and enhance for larger screens
- **Fluid Typography**: Scale text based on screen size
- **Flexible Images**: Ensure images adapt to their containers
- **Touch-Friendly UI**: Larger touch targets on mobile
- **Performance Optimization**: Lazy loading and optimized assets
- **Testing**: Test on multiple devices and screen sizes
- **Accessibility**: Ensure accessibility across all screen sizes

### Code Organization Principles

- **High cohesion**: Related functionality is grouped together
- **Low coupling**: Components are independent and reusable
- **Consistent naming conventions and file structure**: Maintain consistency throughout the codebase

### Implementation Techniques

-   **Memoization**: Use React.memo for expensive components
-   **Virtual Lists**: For long scrollable lists, use virtual scrolling techniques
-   **State Management**: Keep state as local as possible, avoid unnecessary global state
-   **Debounce & Throttle**: Apply to frequent events like scroll and resize

```typescript
// Example of memoizing a component
const MemoizedComponent = React.memo(({ prop1, prop2 }) => {
  // Component implementation
  return <div>{/* rendered content */}</div>;
});

// Example of debouncing a search input
function SearchInput() {
  const [value, setValue] = useState('');
  const debouncedValue = useDebounce(value, 300);

  // Use debouncedValue for search operations
}
```

## Caching Strategy

-   **HTTP Caching**: Set appropriate cache-control headers
-   **Service Worker**: Use for offline functionality and caching static assets
-   **Local Storage**: Cache UI preferences and non-sensitive data
-   **Memory Caching**: Use for frequently accessed data during the session

## Rendering Strategy

-   **Static Generation**: Pre-render pages that don't need frequent updates
-   **Incremental Static Regeneration**: Update static pages in the background
-   **Client-Side Rendering**: Use for highly dynamic, personalized content

## Monitoring and Analytics

-   **Real User Monitoring (RUM)**: Track actual user experiences
-   **Core Web Vitals**: Monitor LCP, FID, and CLS
-   **Error Tracking**: Implement comprehensive error tracking
-   **Performance Budgets**: Set and maintain performance budgets

## Accessibility and Performance

-   **Keyboard Navigation**: Ensure all interactions are keyboard accessible
-   **Focus Management**: Properly manage focus for modals and dynamic content
-   **Reduced Motion**: Respect user preferences for reduced motion
-   **Color Contrast**: Maintain appropriate contrast ratios
-   **Semantic HTML**: Use correct HTML elements for better performance and accessibility

## Data Optimization

### Static Data

-   **Preload Critical Data**: Include critical data in the initial HTML
-   **Local Caching**: Cache static data that doesn't change frequently
-   **Data Bundling**: Bundle related data to reduce request overhead
-   **Data Compression**: Compress data responses

### API Optimizations

-   **Request Batching**: Combine multiple API requests where possible
-   **Data Pagination**: Paginate large data sets
-   **Query Optimization**: Only request the data that's needed
-   **Response Caching**: Cache API responses appropriately

## SEO and Performance

-   **Meta Tags**: Optimize title, description, and other meta tags using React Helmet
-   **Structured Data**: Implement Schema.org markup
-   **Semantic HTML**: Use proper HTML structure
-   **URL Structure**: Maintain clean, descriptive URLs

## Mobile Performance

-   **Touch Optimization**: Ensure touch targets are at least 44Ã—44 pixels
-   **Viewport Settings**: Configure properly for responsive design
-   **Critical CSS**: Inline critical CSS for faster rendering on mobile
-   **Reduced Network Requests**: Minimize HTTP requests for mobile

## Testing and Validation

-   **Performance Testing**: Regular performance audits with Lighthouse
-   **Cross-Browser Testing**: Test in all major browsers
-   **Device Testing**: Test across various devices and screen sizes
-   **Network Throttling**: Test under various network conditions
-   **A/B Testing**: Test performance optimizations against user metrics

## Deployment and CI/CD

-   **Automated Testing**: Include performance tests in CI pipeline
-   **Performance Budgets**: Set budgets and fail builds that exceed them
-   **Cache Invalidation**: Proper strategy for updating cached assets
-   **Progressive Rollouts**: Gradual deployment to monitor performance impact

## Vite-Specific Optimizations

Ringerike Landskap's website leverages Vite's built-in optimization features for enhanced performance:

### Build Optimization

-   **Chunking Strategy**: Configure optimal chunk sizes
-   **Module Preloading**: Preload critical modules
-   **Asset Optimization**: Optimize CSS, JS, and other assets
-   **Tree Shaking**: Remove unused code
-   **Minification**: Minify all production assets

### Development Optimization

-   **Hot Module Replacement**: Fast updates during development
-   **Fast Refresh**: Maintain component state during updates
-   **ESBuild**: Leverage fast bundling during development
```

#### `05-development-workflow\03-accessibility-guide.md`

```markdown
# Accessibility Recommendations

This document outlines specific recommendations to improve the accessibility of the Ringerike Landskap website, ensuring it's usable by people with various disabilities and meets WCAG 2.1 standards.

## Current Accessibility Assessment

Based on the analysis of the codebase and rendered website, several accessibility aspects have been identified:

-   **Semantic HTML**: Generally good use of semantic elements, but some improvements needed
-   **ARIA Attributes**: Limited implementation of ARIA roles and attributes
-   **Keyboard Navigation**: Basic functionality exists but needs enhancement
-   **Color Contrast**: Generally good, but some areas need improvement
-   **Focus Management**: Basic focus states exist but could be enhanced
-   **Screen Reader Support**: Limited explicit support for screen readers

## Semantic HTML Improvements

### Document Structure

Ensure proper heading hierarchy throughout the site:

```tsx
// Before - Incorrect heading hierarchy
<h1>Page Title</h1>
<div>
  <h3>Section Title</h3> // Skipping h2
  <p>Content</p>
</div>

// After - Correct heading hierarchy
<h1>Page Title</h1>
<div>
  <h2>Section Title</h2>
  <p>Content</p>
</div>
```

### Landmark Regions

Add proper landmark regions to improve navigation:

```tsx
// Before
<div className="header">...</div>
<div className="main-content">...</div>
<div className="footer">...</div>

// After
<header>...</header>
<main>
  <section aria-labelledby="section-title">
    <h2 id="section-title">Section Title</h2>
    ...
  </section>
</main>
<footer>...</footer>
```

## ARIA Implementation

### Interactive Elements

Add appropriate ARIA attributes to interactive elements:

```tsx
// Before - Mobile menu button
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
>
  <span className="sr-only">
    {isMenuOpen ? "Lukk meny" : "Åpne meny"}
  </span>
  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
</button>

// After - Enhanced mobile menu button
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
  aria-expanded={isMenuOpen}
  aria-controls="mobile-menu"
  aria-label={isMenuOpen ? "Lukk meny" : "Åpne meny"}
>
  <span className="sr-only">
    {isMenuOpen ? "Lukk meny" : "Åpne meny"}
  </span>
  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
</button>
```

### Form Elements

Enhance form accessibility with proper labels and ARIA attributes:

```tsx
// Before - Form field without proper label
<input
  type="email"
  placeholder="E-post"
  className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
/>

// After - Form field with proper label and ARIA
<div className="form-field">
  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
    E-post
  </label>
  <input
    id="email"
    type="email"
    placeholder="<EMAIL>"
    aria-required="true"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
</div>
```

### Dynamic Content

Add ARIA live regions for dynamic content:

```tsx
// Before - Dynamic content without ARIA
<div className="notification">
  {message && <p>{message}</p>}
</div>

// After - Dynamic content with ARIA live region
<div
  className="notification"
  aria-live="polite"
  aria-atomic="true"
>
  {message && <p>{message}</p>}
</div>
```

## Keyboard Navigation

### Skip Link

Add a skip link to bypass navigation:

```tsx
// Add at the top of the page layout
<a
  href="#main-content"
  className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-green-600 focus:shadow-md"
>
  Hopp til hovedinnhold
</a>

// Then add the corresponding ID to the main content
<main id="main-content">
  ...
</main>
```

### Focus Management

Improve focus styles for better visibility:

```css
/* Add to global CSS */
:focus {
    outline: 2px solid #1e9545;
    outline-offset: 2px;
}

/* For elements where the default outline doesn't work well */
.custom-focus:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #1e9545;
    outline: none;
}
```

### Keyboard Accessible Components

Enhance the carousel component for keyboard accessibility:

```tsx
// Add keyboard support to carousel navigation
<button
  onClick={prevSlide}
  onKeyDown={(e) => e.key === 'Enter' && prevSlide()}
  tabIndex={0}
  aria-label="Forrige prosjekt"
  className="carousel-control"
>
  <ChevronLeft className="w-6 h-6" />
</button>

<button
  onClick={nextSlide}
  onKeyDown={(e) => e.key === 'Enter' && nextSlide()}
  tabIndex={0}
  aria-label="Neste prosjekt"
  className="carousel-control"
>
  <ChevronRight className="w-6 h-6" />
</button>
```

## Color and Contrast

### Text Contrast

Ensure sufficient contrast for all text:

```tsx
// Before - Potentially low contrast
<p className="text-gray-400">Some text</p>

// After - Improved contrast
<p className="text-gray-700">Some text</p>
```

### Non-Text Contrast

Ensure interactive elements have sufficient contrast:

```tsx
// Before - Border might have insufficient contrast
<button className="border border-gray-300 text-gray-700">Button</button>

// After - Improved border contrast
<button className="border-2 border-gray-500 text-gray-700">Button</button>
```

### Color Independence

Don't rely solely on color to convey information:

```tsx
// Before - Using only color to indicate status
<div className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>

// After - Using color and text to indicate status
<div
  className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}
  aria-label={isActive ? 'Aktiv' : 'Inaktiv'}
>
  {isActive ? '✓' : '✗'}
</div>
```

## Images and Media

### Alt Text

Ensure all images have appropriate alt text:

```tsx
// Before - Missing or generic alt text
<img src="/images/projects/project1.jpg" alt="Project" />

// After - Descriptive alt text
<img
  src="/images/projects/project1.jpg"
  alt="Completed garden landscaping project in Røyse with stone pathways and native plantings"
/>

// For decorative images
<img
  src="/images/decorative/pattern.jpg"
  alt=""
  role="presentation"
/>
```

### SVG Accessibility

Enhance SVG accessibility:

```tsx
// Before - SVG without accessibility attributes
<svg width="24" height="24" viewBox="0 0 24 24">
  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
</svg>

// After - Accessible SVG
<svg
  width="24"
  height="24"
  viewBox="0 0 24 24"
  aria-hidden="true"
  focusable="false"
>
  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
</svg>
```

## Form Accessibility

### Form Validation

Implement accessible form validation:

```tsx
// Enhanced form field with validation
const FormField = ({ id, label, type, required, error, ...props }) => {
    return (
        <div className="form-field">
            <label
                htmlFor={id}
                className="block text-sm font-medium text-gray-700 mb-1"
            >
                {label} {required && <span aria-hidden="true">*</span>}
            </label>
            <input
                id={id}
                type={type}
                aria-required={required}
                aria-invalid={!!error}
                aria-describedby={error ? `${id}-error` : undefined}
                className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                    error ? "border-red-500" : "border-gray-300"
                }`}
                {...props}
            />
            {error && (
                <div
                    id={`${id}-error`}
                    className="mt-1 text-sm text-red-600"
                    role="alert"
                >
                    {error}
                </div>
            )}
        </div>
    );
};
```

### Required Fields

Clearly indicate required fields:

```tsx
// Add to the form component
<p className="text-sm text-gray-500 mb-4">
    Felt markert med <span aria-hidden="true">*</span>
    <span className="sr-only">stjerne</span> er obligatoriske
</p>
```

## Screen Reader Support

### Descriptive Link Text

Improve link text for screen readers:

```tsx
// Before - Vague link text
<Link to="/prosjekter">Les mer</Link>

// After - Descriptive link text
<Link to="/prosjekter">
  Se alle prosjekter
  <span className="sr-only"> om landskapsdesign</span>
</Link>
```

### Table Accessibility

Enhance table accessibility:

```tsx
// Accessible table structure
<table>
    <caption>Prisliste for tjenester</caption>
    <thead>
        <tr>
            <th scope="col">Tjeneste</th>
            <th scope="col">Pris (NOK)</th>
            <th scope="col">Estimert tid</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th scope="row">Hagedesign</th>
            <td>5000</td>
            <td>2 uker</td>
        </tr>
        {/* More rows */}
    </tbody>
</table>
```

## Implementation Priority

1. **High Priority (Essential for Accessibility)**

    - Add proper semantic HTML structure
    - Implement form labels and ARIA attributes
    - Ensure sufficient color contrast
    - Add alt text to all images

2. **Medium Priority (Important Improvements)**

    - Add skip links
    - Enhance keyboard navigation
    - Implement ARIA live regions
    - Improve focus management

3. **Lower Priority (Additional Enhancements)**
    - Add screen reader specific text
    - Enhance SVG accessibility
    - Implement advanced ARIA patterns for complex components

## Testing Recommendations

1. **Automated Testing**

    - Implement axe-core for automated accessibility testing
    - Add accessibility checks to CI/CD pipeline

2. **Manual Testing**

    - Test with keyboard navigation only
    - Test with screen readers (NVDA, JAWS, VoiceOver)
    - Test with high contrast mode
    - Test with text zoom (up to 200%)

3. **User Testing**
    - Conduct testing with users who have disabilities
    - Gather feedback on usability and accessibility

## Conclusion

Implementing these accessibility recommendations will significantly improve the usability of the Ringerike Landskap website for all users, including those with disabilities. The most critical improvements involve proper semantic HTML, form accessibility, and keyboard navigation, which should be prioritized in the implementation plan.

Regular accessibility testing should be conducted to ensure that the website maintains compliance with accessibility standards as new features and content are added.
```