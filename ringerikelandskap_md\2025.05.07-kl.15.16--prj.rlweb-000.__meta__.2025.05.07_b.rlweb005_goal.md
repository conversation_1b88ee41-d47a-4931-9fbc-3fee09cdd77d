
# TL;DR

Achieve comprehensive mastery of the Ringerike Landskap codebase’s structure and architecture, with detailed attention to component organization, filter logic, naming, separation of concerns, and documentation. Explicitly launch the site using required startup commands. Rigorously verify full functionality with repeated end-to-end testing via `browser-tools mcp` until flawless operation is confirmed. Ensure complete understanding for authoritative replication, extension, or adaptation.

# Objective

Analyze and assimilate all architectural principles, patterns, and recommendations specified in the Ringerike Landskap codebase filestructure-first analysis. Absorb comprehensive knowledge of the documented filestructure, directory organization, and code relationships for perfect comprehension. Rigorously study codebase analyses, structural visualizations, filtering system breakdowns, and improvement strategies. Cross-reference analytical documents, diagrams, and recommendations repeatedly to consolidate a precise mental model. Internalize layered architecture, organizing principles, and domain-driven patterns, capturing all implicit rules and best practices. Focus on enabling the ability to flawlessly replicate, extend, or adapt the codebase’s structure and architectural decisions. Ensure total understanding for safe, systematic execution of the following actions:

# Requirements

Initiate the Ringerike Landskap site by executing necessary startup commands. Use `browser-tools mcp` to verify full site functionality, meticulously testing and confirming every element operates as intended. Iterate through validation steps until operational integrity is achieved. Maintain focus on enabling authoritative replication, extension, or adaptation of the codebase with zero errors or omissions. You need to understand the practical purpose and usage of the codebase to such degree that you're able to **safely** and **systematically**:

- [Consolidate Component Organization]: Establish a consistent pattern for where components live and how they're organized.
- [Reduce Filter Logic Duplication]: Ensure all filter logic is truly centralized in the filtering.ts file.
- [Standardize Naming Conventions]: Adopt consistent naming conventions across the codebase.
- [Further Separate API and Filtering Logic]: Consider moving some of the filtering logic out of the enhanced.ts file for clearer separation of concerns.
- [Document File Structure Decisions]: Add more documentation explaining why certain files are organized the way they are.

# Process

- Analyze and assimilate all architectural principles, patterns, and recommendations specified in the Ringerike Landskap codebase filestructure-first analysis.
- Study the documented filestructure, directory organization, and code relationships for comprehensive understanding.
- Examine codebase analyses, structural visualizations, filtering system breakdowns, and improvement strategies in detail.
- Cross-reference analytical documents, diagrams, and recommendations to consolidate a precise mental model of the system.
- Internalize layered architecture, organizing principles, and domain-driven patterns, capturing all implicit rules and best practices.
- Prepare to replicate, extend, or adapt the codebase’s structure and architectural decisions flawlessly.
- Initiate the Ringerike Landskap site by executing the required startup commands.
- Verify the full functionality of the site using `browser-tools mcp`, meticulously testing and confirming that every site element operates as intended.
- Iterate validation and testing steps as needed until operational integrity is achieved.
- Ensure readiness to authoritatively replicate, extend, or adapt the codebase without errors or omissions.
- Consolidate component organization by establishing and enforcing consistent patterns for component placement and structuring.
- Centralize all filter logic in filtering.ts and eliminate filter logic duplication throughout the codebase.
- Adopt standardized naming conventions across all code files and modules.
- Separate API-related and filtering logic by moving filtering logic out of enhanced.ts to achieve clearer separation of concerns.
- Augment documentation to clearly explain file structure decisions and the rationale behind organizational choices.
- Maintain total mastery of the codebase’s filestructure, architectural patterns, and principles with detailed cross-referencing for error-free comprehension and execution.

# Goal

Intensively master the Ringerike Landskap codebase’s filestructure and architectural principles through exhaustive analysis and cross-referencing, ensuring absolute, detail-perfect comprehension. ACTUALLY START the site and validate its full functionality using `browser-tools mcp`, iteratively confirming every element works as intended. Sustain total focus on enabling flawlessly authoritative replication, extension, or adaptation of the structure by internalizing all patterns, rules, and relationships—no errors or gaps allowed. Analyze and fully assimilate the Ringerike Landskap codebase's filestructure, architectural patterns, and best practices through exhaustive study. Start the site using the required startup commands. Use `browser-tools mcp` to rigorously verify complete site functionality, repeating validation steps until flawless operation is confirmed. Ensure comprehensive mastery to enable precise replication, extension, or adaptation of the codebase, with special attention to: component organization, centralized filter logic, naming conventions, API/filter logic separation, and documentation of structural decisions.
