# Ringerike Landskap Website - Project Overview

## Project Overview

This is a modern, responsive website for Ringerike Landskap AS, a landscaping company based in Røyse, Norway. The website showcases the company's services, projects, and testimonials with a focus on local expertise in the Ringerike region.

The website is designed to be:
- Responsive and mobile-friendly
- SEO optimized for local search
- Seasonally adaptive with content that changes based on the current season
- Geographically focused on the Ringerike region

## Technical Stack

- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with custom configuration
- **Routing**: React Router
- **Animation**: Framer Motion
- **Icons**: Lucide React
- **SEO**: React Helmet for metadata management

## Project Structure

The codebase follows a well-organized structure:

```
src/
├── components/     # Reusable UI components
├── features/       # Feature-specific components
├── pages/          # Top-level page components
├── hooks/          # Custom React hooks
├── lib/            # Utilities, API functions, types
├── data/           # Static data and configuration
├── config/         # Configuration exports
├── styles/         # CSS files
├── utils/          # Utility functions
└── types/          # TypeScript type definitions
```

### Key Directories

- **components/**: Reusable UI components like Button, Container, Hero
- **features/**: Domain-specific components like ProjectCard, ServiceCard
- **pages/**: Top-level page components for each route
- **hooks/**: Custom React hooks for data fetching, UI state, etc.
- **lib/**: Utility functions, API functions, and type definitions
- **data/**: Static data files and JSON configuration
- **styles/**: CSS files for global styles and animations
- **utils/**: Utility functions for various purposes

## Key Features

### 1. Seasonal Content Adaptation

The website dynamically adapts content based on the current season:

- `getCurrentSeason()` determines the season based on month:
  - Spring (vår): March, April, May (months 2-4)
  - Summer (sommer): June, July, August (months 5-7)
  - Fall (høst): September, October, November (months 8-10)
  - Winter (vinter): December, January, February (months 11, 0, 1)

- Components that adapt to seasons:
  - `SeasonalProjectsCarousel`: Displays projects relevant to the current season
  - `FilteredServicesSection`: Shows services relevant to the current season
  - `SeasonalCTA`: Displays a call-to-action relevant to the current season

### 2. Geographic Authenticity Layer

Content is tailored to specific locations in the Ringerike region:

- Service areas include Røyse (base), Hole, Vik, Hønefoss, Sundvollen, Jevnaker, and Bærum
- Each service area has details like distance from base, description, and features
- The `ServiceAreaList` component displays these areas with their details

### 3. JSON-Driven Architecture

The project is transitioning to a centralized JSON configuration approach:

**Configuration System:**
- `site-config.json`: Global site settings (metadata, contact info, team members)
- `projects-config.json`: Project-specific settings (seasonal priorities, image mappings)

**Season-Based Project Structure:**
```
projects/
├── vaar/                # Spring projects
│   ├── project1/
│   │   ├── index.json   # Project metadata
│   │   └── images/      # Project images
├── sommer/              # Summer projects
├── hoest/               # Fall projects
└── vinter/              # Winter projects
```

**Dynamic Content Loading:**
- Uses Vite's `import.meta.glob` to dynamically load project JSON files and images
- Automatically discovers and incorporates content based on directory structure

### 4. Responsive Design

The website is designed to be responsive across different device sizes:

- Mobile-first approach with Tailwind breakpoints
- Responsive navigation with mobile menu
- Fluid typography and spacing
- Responsive grid layouts
- Touch-enabled carousels for mobile

## Component Architecture

### Layout Components

- **Header**: Navigation bar with logo, links, and mobile menu
- **Footer**: Company information, contact details, and links
- **Container**: Wrapper component for consistent padding and max-width
- **Meta**: SEO metadata using React Helmet

### UI Components

- **Hero**: Flexible hero section with background image, title, subtitle, and CTA
- **Button**: Reusable button component with variants
- **Logo**: Company logo with variants
- **ServiceAreaList**: Displays service areas with distances
- **SeasonalCTA**: Season-specific call-to-action

### Feature Components

- **ProjectCard**: Displays project information with image, title, description
- **ServiceCard**: Displays service information with image, title, description
- **TestimonialSlider**: Carousel of customer testimonials
- **FilteredServicesSection**: Displays services filtered by season
- **SeasonalProjectsCarousel**: Carousel of projects filtered by season

### Page Components

- **HomePage**: Landing page with hero, projects, services, and testimonials
- **ServicesPage**: Overview of all services
- **ServiceDetailPage**: Detailed information about a specific service
- **ProjectsPage**: Gallery of all projects with filtering
- **ProjectDetailPage**: Detailed information about a specific project
- **AboutPage**: Information about the company and team
- **ContactPage**: Contact information and form
- **TestimonialsPage**: Customer testimonials with filtering

## Data Management

### Current Approach

The project uses a hybrid approach:

1. **Static Data Files:**
   - `services.ts`: Defines services with hardcoded data
   - `testimonials.ts`: Contains customer testimonials
   - `projects.ts`: Original project data (being phased out)

2. **JSON Configuration:**
   - Project data is being migrated to JSON files in season-specific directories
   - Site configuration is centralized in JSON files

3. **Mock API Layer:**
   - API functions in `lib/api` return promises with data
   - Custom `useData` hook for data fetching with loading/error states

### Migration Progress

The project includes several migration scripts:
- `migrate.js`: Converts static project data to the season-based JSON structure
- `migrate-images.js`: Handles copying images to appropriate project directories
- `migrate.ts`: TypeScript version with enhanced type safety

## Styling System

### Tailwind Configuration

The website uses Tailwind CSS with a custom configuration:

- **Colors**: Custom green palette that reflects the landscaping business theme
- **Typography**: Inter font family with a hierarchical scale
- **Spacing**: Consistent spacing throughout
- **Animations**: Custom animations for fade-in, slide-up, and gradient effects

### UI Elements

- **Buttons**: Primary (green background), secondary (outline), and text variants
- **Cards**: Service and project cards with hover effects
- **Hero Sections**: Full-width banners with background images and overlays
- **Navigation**: Desktop and mobile variants with active state highlighting

## Build and Development

### Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the production version
- `npm run lint`: Run ESLint for code linting
- `npm run preview`: Preview the production build
- `npm run screenshot`: Generate screenshots of the website
- `npm run cleanup`: Clean up temporary files

### Environment Variables

- `.env.development`: Development environment variables
- `.env.production`: Production environment variables

## Current State and Future Direction

### Current Transition State

The project is clearly in transition from a more static, hardcoded approach to a dynamic, JSON-driven architecture:

1. **Completed:**
   - Season-based project structure
   - JSON configuration for site-wide settings
   - Dynamic project loading system
   - Image management with automatic discovery

2. **In Progress:**
   - Migration of projects from static data to JSON files
   - Backward compatibility layer for gradual transition

3. **Potential Next Steps:**
   - Extend JSON-driven approach to services and testimonials
   - Consolidate API layer to work directly with JSON files
   - Update components to consume JSON-driven data directly
   - Implement content validation for JSON files

### Benefits of the JSON-Driven Approach

1. **Improved Maintainability**: Clear separation of content and code
2. **Enhanced SEO**: Better organization of seasonal content
3. **Simplified Content Management**: Adding new content requires no code changes
4. **Better Performance**: Optimized data loading and caching
5. **Improved Developer Experience**: Clearer data structure and type safety

## Conclusion

The Ringerike Landskap website is a well-structured React application with a focus on seasonal content adaptation and local geographic relevance. The project is transitioning to a more maintainable, JSON-driven architecture that will make it easier to manage content and improve performance.

The codebase demonstrates good practices in React development, TypeScript usage, and responsive design. The seasonal content adaptation and geographic authenticity layer provide a unique user experience that is tailored to the company's local market.
