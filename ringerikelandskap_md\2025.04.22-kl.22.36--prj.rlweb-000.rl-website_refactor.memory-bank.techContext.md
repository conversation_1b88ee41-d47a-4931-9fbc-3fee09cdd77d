# Technical Context: Ringerikelandskap Website Refactor

## Technologies Used

### Core Technologies

| Technology | Version | Purpose |
|------------|---------|---------|
| React | ^18.2.0 | UI library for building component-based interfaces |
| TypeScript | ^5.0.2 | Static typing for JavaScript to improve code quality and developer experience |
| Vite | ^5.0.0 | Modern, fast build tool and development server |
| Express | ^4.21.2 | Server for production deployment |

### Key Libraries

| Library | Version | Purpose |
|---------|---------|---------|
| react-router-dom | ^6.20.0 | Client-side routing for single-page application |
| reactflow | ^11.10.1 | Interactive node-based diagrams for dependency visualization |
| tailwindcss | ^3.3.5 | Utility-first CSS framework for styling |

### Development Tools

| Tool | Version | Purpose |
|------|---------|---------|
| eslint | ^8.45.0 | Static code analysis for identifying problematic patterns |
| @typescript-eslint | ^6.0.0 | TypeScript-specific linting rules |
| @babel/parser | ^7.23.0 | For code parsing in dependency analysis |
| @babel/traverse | ^7.23.0 | For AST traversal in dependency analysis |

## Development Setup

### Environment Configuration

The project uses environment variables for configuration:
- `.env`: Base configuration shared across all environments
- `.env.development`: Development-specific configuration

Current environment variables:
- `BASE_URL`: Configures the base URL path for the application

### Build and Development Scripts

```json
{
  "scripts": {
    "dev": "vite",               // Start development server
    "build": "tsc && vite build", // Type-check and build for production
    "lint": "eslint ...",        // Lint code for issues
    "preview": "vite preview",   // Preview production build locally
    "analyze-deps": "node scripts/analyze-dependencies.js", // Analyze code dependencies
    "visualize-deps": "node scripts/visualize-dependencies.js", // Generate visualization data
    "serve": "node server.js"    // Serve production build
  }
}
```

### Development Workflow

1. **Local Development**:
   - Run `npm run dev` to start the Vite development server
   - Make code changes with hot module replacement
   - Visualize dependencies using the dependency tool

2. **Dependency Analysis**:
   - Run `npm run analyze-deps` to analyze and extract dependency information
   - Run `npm run visualize-deps` to generate visualization-ready data
   - View the visualization in the application

3. **Production Build**:
   - Run `npm run build` to create optimized production build
   - Run `npm run serve` to serve the production build locally

## Technical Constraints

### Browser Compatibility

- The application targets modern browsers with support for:
  - ES6+ features
  - CSS Grid and Flexbox
  - Modern DOM APIs

### Performance Considerations

- **Large Dependency Graphs**: The visualization must handle potentially large and complex dependency graphs efficiently
- **Responsive Interaction**: UI interactions should remain smooth even with complex visualizations
- **Initial Load Time**: Balance between code-splitting and initial load performance

### SEO and Accessibility

- Single-page application architecture requires proper handling for SEO
- Visualization interfaces pose unique accessibility challenges:
  - Color choices need to consider color vision deficiency
  - Interactive elements need keyboard accessibility
  - Complex visualizations need appropriate text alternatives

## Dependencies Management

### Direct Dependencies

- **Runtime Dependencies**: Core libraries needed for the application to run
  - React ecosystem (react, react-dom, react-router-dom)
  - Visualization tools (reactflow)
  - Server (express)

- **Development Dependencies**: Tools used during development and build
  - TypeScript compiler and types
  - Vite and plugins
  - ESLint and configurations
  - Styling tools (autoprefixer, postcss, tailwindcss)

### Dependency Analysis Tools

The project includes custom tools for dependency analysis:
- `scripts/analyze-dependencies.js`: Analyzes source code to extract dependencies
- `scripts/visualize-dependencies.js`: Processes dependencies into visualization format
- `scripts/setup-and-run.js`: Helper script for environment setup

## Tool Usage Patterns

### Vite for Development

Vite is used as the primary development and build tool, providing:
- Fast hot module replacement
- ES module-based dev server
- Efficient production builds
- TypeScript integration

Configuration in `vite.config.ts` includes:
- React plugin integration
- Build optimizations
- Environment variable handling

### Express for Production Serving

A simple Express server (`server.js`) is used for production:
- Serves static files from the `dist` directory
- Handles SPA routing by serving `index.html` for all routes
- Configurable port via environment variable

### ReactFlow for Visualization

ReactFlow is used for the interactive dependency graph:
- Custom node styling based on dependency types
- Edge styling for different relationship types
- Interactive features (pan, zoom, selection)
- Layout organization for different view types

### Static Analysis for Dependencies

Custom scripts analyze the codebase for dependencies:
- Parser extracts import/export relationships
- Detects circular dependencies
- Generates JSON data consumed by the visualization
- Data is pre-processed rather than analyzed at runtime
