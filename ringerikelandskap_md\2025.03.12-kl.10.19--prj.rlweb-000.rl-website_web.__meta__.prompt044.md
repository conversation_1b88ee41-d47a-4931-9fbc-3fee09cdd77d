how does your proposal compare to attached variation?

    /**
     * website-snapshot.js
     *
     * A single on-demand script to capture website screenshots via <PERSON><PERSON>peteer.
     * Run: node website-snapshot.js
     */

    import fs from 'fs';
    import path from 'path';
    import { fileURLToPath } from 'url';
    import puppeteer from 'puppeteer';

    /* ------------------------------------------------------------------
       1. Setup Paths & Configuration
       ------------------------------------------------------------------ */
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = __dirname; // or path.resolve(__dirname, '..') if desired

    // Where to save screenshots
    const SCREENSHOTS_DIR = path.join(projectRoot, 'screenshots');
    const LATEST_DIR = path.join(SCREENSHOTS_DIR, 'latest');

    // Pages you want to capture
    const PAGES = [
      { path: '/', name: 'home' },
      { path: '/hvem-er-vi', name: 'about' },
      { path: '/hva-vi-gjor', name: 'services' },
      { path: '/prosjekter', name: 'projects' },
      { path: '/kontakt', name: 'contact' },
    ];

    // Optional: multiple viewports if you want different device sizes
    const VIEWPORTS = [
      { width: 390, height: 844, name: 'mobile' },
      { width: 834, height: 1112, name: 'tablet' },
      { width: 1440, height: 900, name: 'desktop' },
    ];

    // The port where your dev server is running.
    // Make sure "npm run dev" (or similar) is started separately.
    const DEV_PORT = 5173;

    // If you want to wait a bit after loading each page for animations, etc.
    const CAPTURE_DELAY_MS = 2000;

    /* ------------------------------------------------------------------
       2. Directory Setup
       ------------------------------------------------------------------ */
    function ensureDirectory(dirPath) {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    }

    // Ensure base screenshot directories exist
    ensureDirectory(SCREENSHOTS_DIR);
    ensureDirectory(LATEST_DIR);

    // Create a timestamp-based folder for each run
    function createTimestampedDir() {
      const now = new Date();
      const timestamp = now
        .toISOString()
        .replace(/T/, '_')
        .replace(/\..+/, '')
        .replace(/:/g, '-');
      // e.g. "2025-03-12_12-34-56"

      const snapshotDir = path.join(SCREENSHOTS_DIR, timestamp);
      ensureDirectory(snapshotDir);
      return snapshotDir;
    }

    /* ------------------------------------------------------------------
       3. Main Capture Logic
       ------------------------------------------------------------------ */
    async function capturePage(browser, snapshotDir, pagePath, pageName, viewport) {
      const url = `http://localhost:${DEV_PORT}${pagePath}`;
      const fileSuffix = `${pageName}-${viewport.name}.png`;

      try {
        // New page in the browser
        const page = await browser.newPage();
        await page.setViewport({ width: viewport.width, height: viewport.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        // Wait for any animations or dynamic content
        await new Promise((resolve) => setTimeout(resolve, CAPTURE_DELAY_MS));

        // Build final file paths
        const viewportDir = path.join(snapshotDir, viewport.name);
        ensureDirectory(viewportDir);

        const screenshotPath = path.join(viewportDir, fileSuffix);
        await page.screenshot({ path: screenshotPath, fullPage: true });

        // Also copy to "latest/<viewport>"
        const latestViewportDir = path.join(LATEST_DIR, viewport.name);
        ensureDirectory(latestViewportDir);
        const latestScreenshotPath = path.join(latestViewportDir, fileSuffix);
        fs.copyFileSync(screenshotPath, latestScreenshotPath);

        await page.close();
        console.log(`Captured: ${pageName} (${viewport.name})`);
      } catch (error) {
        console.error(`Error capturing ${pageName} (${viewport.name}):`, error);
      }
    }

    async function captureAllPages(snapshotDir) {
      // Launch Puppeteer once
      const browser = await puppeteer.launch();

      // For each viewport, for each page
      for (const vp of VIEWPORTS) {
        for (const pg of PAGES) {
          await capturePage(browser, snapshotDir, pg.path, pg.name, vp);
        }
      }

      await browser.close();
    }

    /* ------------------------------------------------------------------
       4. Run the Script
       ------------------------------------------------------------------ */
    async function main() {
      try {
        console.log('Starting screenshot capture...');
        const snapshotDir = createTimestampedDir();
        await captureAllPages(snapshotDir);
        console.log('\nAll screenshots captured successfully!\n');
        console.log(`- Timestamped folder: ${snapshotDir}`);
        console.log(`- Latest folder:       ${LATEST_DIR}`);
      } catch (err) {
        console.error('Snapshot process failed:', err);
        process.exit(1);
      }
    }

    // Invoke main if called directly (node website-snapshot.js)
    if (import.meta.url === `file://${process.argv[1]}`) {
      main();
    }
