# Season-Based Project Structure

This directory contains the projects organized by seasons, providing a clean, intuitive, and SEO-friendly approach to managing project data.

## Directory Structure

```
projects/
├── index.ts             # Main export file
├── utils.ts             # Utility functions
├── migrate.ts           # Migration script
├── vaar/                # Spring projects
│   ├── project1/
│   │   ├── index.json   # Project metadata
│   │   └── images/      # Project images
│   └── project2/
│       └── ...
├── sommer/              # Summer projects
├── hoest/               # Fall projects
└── vinter/              # Winter projects
```

## Configuration System

The project uses a split configuration system for better maintainability:

```
src/data/config/
├── index.ts             # Main export file
├── site-config.json     # Global site configuration
└── projects-config.json # Project-specific configuration
```

### Site Configuration

`site-config.json` contains global site settings:

-   Site metadata (name, title, description, etc.)
-   Contact information
-   Team members
-   Feature descriptions
-   Business information
-   Navigation paths

### Projects Configuration

`projects-config.json` contains project-specific settings:

-   Seasonal configuration (priority categories and tags)
-   Image category mappings
-   Project categories list

## Project JSON Structure

Projects now use a simplified JSON structure:

```json
{
    "id": "unique-project-id",
    "title": "Project Title",
    "description": "Project description",
    "location": "Project Location",
    "completionDate": "Month Year",
    "category": "Project Category",
    "tags": ["tag1", "tag2", "tag3"],
    "seo": {
        "title": "SEO-optimized title",
        "description": "SEO-optimized description",
        "keywords": ["keyword1", "keyword2"]
    }
}
```

This simplified structure focuses on essential information, making it easier to maintain and update projects.

## Image Management

### Project Image Structure

Images are now managed using a directory-based approach rather than hardcoded paths in JSON files. Images are stored in two locations:

1. **Source of truth (development)**:

    ```
    src/data/projects/{season}/{project-id}/images/*.{jpg,jpeg,png,webp}
    ```

2. **Public path (for browser access)**:
    ```
    public/images/projects/{season}/{project-id}/*.{jpg,jpeg,png,webp}
    ```

The system automatically discovers all images in the project's images directory and makes them available to the project. The first image alphabetically is used as the main image.

### Adding New Images

To add new images to a project:

1. Add the image files to the project's images directory:

    ```
    src/data/projects/{season}/{project-id}/images/
    ```

2. Run the image migration script to copy the files to the public directory:

    ```
    node src/data/projects/migrate-images.js
    ```

3. The system will automatically detect and use the new images.

### Benefits of the New Image Management

1. **Simplified JSON**: Project JSON files no longer need to maintain image paths
2. **Better Organization**: All project assets are grouped with their project
3. **Automatic Discovery**: Adding new images is as simple as placing them in the right folder
4. **Simplified Maintenance**: No need to update code when adding/removing images
5. **Improved Performance**: The system can optimize image loading more effectively

## Adding a New Project

To add a new project:

1. Determine which season the project belongs to
2. Create a new directory in the appropriate season folder:
    ```
    mkdir -p src/data/projects/{season}/{project-id}/images
    ```
3. Create an `index.json` file with the project metadata using the simplified structure:
    ```json
    {
        "id": "unique-project-id",
        "title": "Project Title",
        "description": "Project description",
        "location": "Project Location",
        "completionDate": "Month Year",
        "category": "Project Category",
        "tags": ["tag1", "tag2", "tag3"],
        "seo": {
            "title": "SEO-optimized title",
            "description": "SEO-optimized description",
            "keywords": ["keyword1", "keyword2"]
        }
    }
    ```
4. Add project images to the `images/` directory

## Season Mapping

Norwegian seasons map to directory names as follows:

| Norwegian | Directory |
| --------- | --------- |
| vår       | vaar      |
| sommer    | sommer    |
| høst      | hoest     |
| vinter    | vinter    |

## Key Functions

-   `getProjectById(id)`: Get a project by ID
-   `getProjectsBySeason(season)`: Get all projects for a specific season
-   `getCurrentSeasonProjects()`: Get projects for the current season
-   `filterProjects(category, location, tag, season)`: Filter projects by various criteria

## Accessibility Considerations

All UI components, especially forms, have been updated to follow accessibility best practices:

-   Form inputs have unique `id` attributes
-   Labels are properly associated with form controls using `htmlFor`
-   Screen reader support with appropriate ARIA attributes
-   Semantic HTML elements are used throughout the application

## SEO Benefits

This structure:

-   Creates natural content clusters around seasonal terms
-   Improves topical authority for seasonal keywords
-   Enables season-specific landing pages with relevant projects
-   Facilitates better content discovery by search engines
-   Improves schema markup for projects
-   Enhanced accessibility improves search engine rankings

## Notes for Developers

-   Always check `getCurrentSeason()` before making seasonal changes
-   Use the `ProjectWithSeason` type for proper type checking
-   The `recentProjects` export is maintained for backward compatibility
-   When adding new form elements, ensure they have proper accessibility attributes
-   Maintain the simplified JSON structure for all new projects
