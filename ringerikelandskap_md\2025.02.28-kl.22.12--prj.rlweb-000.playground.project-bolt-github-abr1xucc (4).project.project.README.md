# Ringerike Landskap Website

A modern, responsive website for Ringerike Landskap AS, a landscaping company based in Røyse, Norway.

## Features

-   Modern React-based frontend with TypeScript
-   Responsive design using Tailwind CSS
-   Fast loading with Vite
-   SEO optimized with metadata
-   Image optimization with WebP format (where available)

## Getting Started

### Prerequisites

-   Node.js (v16 or higher)
-   npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
# or
yarn
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to http://localhost:5173/

## Project Structure

```
project/
├── public/             # Static assets
│   ├── images/         # Image files
│   │   ├── site/       # Site-wide images (hero, etc.)
│   │   ├── projects/   # Project images
│   │   ├── team/       # Team member images
│   │   └── ...
├── src/                # Source code
│   ├── components/     # Reusable components
│   ├── data/           # Static data (projects, services)
│   ├── hooks/          # Custom React hooks
│   ├── pages/          # Page components
│   ├── styles/         # Global styles
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── App.tsx         # Main application component
│   └── main.tsx        # Entry point
└── ...
```

## Image Optimization

The website uses WebP images where available for better performance. Currently, only some hero images have WebP versions. A future task is to convert all images to WebP format.

### TODO

-   Convert all PNG and JPG images to WebP format
-   Implement responsive images with different sizes for different devices
-   Add image lazy loading for better performance

## Development

### Available Scripts

-   `npm run dev` - Start the development server
-   `npm run build` - Build the production version
-   `npm run preview` - Preview the production build locally
-   `npm run lint` - Run ESLint to check for code issues
-   `npm run todo:convert-images` - Reminder about the image conversion task

## License

This project is proprietary and confidential.

## Contact

Ringerike Landskap AS - [ringerikelandskap.no](https://ringerikelandskap.no)
