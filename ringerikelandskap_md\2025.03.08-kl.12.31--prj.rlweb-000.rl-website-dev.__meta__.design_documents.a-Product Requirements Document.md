
1. Product Requirements Document:
- Work with an expert product manager to create your requirements. Paste this 1st prompt into your LLM and hit return.
```
# Context
You are an expert product manager your role is to work with me the product owner to create a Product Requirements Document. This document will be in markdown format and used to help other large language models understand the Product. Be concise.

# Instructions
1. Ask the product owner to explain the project idea to you
2. If they leave any details out based on the Sample PRD output, ask clarifying questions
3. Output a markdown file based on the product owner’s context and use the Sample PRD headings as a guide to the output

# Sample PRD Headings
1. Elevator Pitch – Pitch this product in one paragraph
2. This is for
3. Functional Requirements – What does it do?
4. How it Works – How will the user interact?
5. User Interface – How will it look?
```

1.1 Response to LLM Questions after First Prompt
```
Let me explain what Levercast is. It’s an application that helps entrepreneurs manage their social media posting more efficiently. Often when I’m reading an article or taking a walk, I’ll come up with an idea for a thought piece or article. I want to capture it, refine it, and then have it go out on my next post. The app has an input box where I can dump those thoughts.

The input then gets sent to an LLM that formats it based on custom template prompts I’ve set up in the application. It returns a 1st revised version of my thoughts, but it does more than that—it generates multiple versions optimized for different social platforms. For our MVP, we’ll focus on LinkedIn and Twitter posts, with plans to add more platforms later.

These generated posts will appear styled exactly as they would on their respective platforms, making it easy to preview them. I can also add images to my input, and the app will append them appropriately for each platform. I can edit any of the outputs, and when I’m satisfied, publish them directly from Levercast. We’ll use OAuth to connect Levercast with LinkedIn and Twitter accounts.

In essence, Levercast helps busy entrepreneurs capture ideas and then quickly cross multiple platforms simultaneously. The key benefit is saving time while amplifying marketing efforts. As for the user interface, while I’ll leave the specific design details to a professional designer, I know it will be approachable initially, with responsiveness to support mobile access in the future.
```

2. User Interface Design Document
- Work with an expert UX designer to figure out how you should approach designing the UI for the App. Paste this prompt in your LLM and watch the PRD come alive.
```
# Context
You are an expert UX designer. Your role is to work with the product manager and the product owner to create a UI design document. This UI design document will be in markdown format. It will be used by developers and other large language models to create the best user interface possible.

---

# Inputs:
We need to know what we’re designing. The first step in any design is to understand the user’s input. That’s what we do in the UI design doc.
1. Product Requirements Document
3. User Input

---

# Instructions

1. Process the product input documents if one is not provided ask for one.
2. Ask questions about the user persona if it's unclear to you.
3. Generate 3 options for user interface designs that might suit the persona. Don't use code this is a natural language description.
4. Ask the product owner to confirm which one they like or amendments they have.
5. Show the final user interface design plan that is easy to read and follow.
6. Proceed to generate the final User Interface Design Document. Use only basic markdown.

---

# Headings to be included

**Core Components:**
- Expansive content input area with image support
- Interactive preview cards that users can click to edit
- A top navigation bar for publishing controls and account management

**Interaction Patterns:**
- Modal dialogs for detailed content editing
- Interactive cards that update in real time
- Hover effects and clear visual cues for actionable items

**Visual Design Elements & Color Scheme:**
- Dark background with bold accent colors (e.g., electric blue or bright orange)
- Subtle gradients and shadow effects to create depth
- Emphasis on visual flair while maintaining clarity

**Typography:**
- Modern sans-serif fonts with varying weights to establish hierarchy

**Accessibility:**
- Options for a high-contrast mode
- Screen reader-friendly components and distinct focus states

---

### **Option 3: Card-based Modular Layout**

- **Layout Structure:**
  A modular, card-driven interface:
  - **Dashboard View:** A grid of cards representing each content idea (with status indicators such as draft, pending, or published)
  - **Content Creation:** A dedicated modal or separate screen for composing and editing new content

- **Core Components:**
  - Cards for individual content pieces
  - A prominent modal for content creation and editing
  - A unified publishing section with clear account integration

- **Interaction Patterns:**
  - Drag-and-drop organization of cards
  - Contextual menus for quick actions (e.g., edit, delete, publish)
  - Modal pop-ups for detailed content management

- **Visual Design Elements & Color Scheme:**
  - Neutral base colors with subtle shadows and borders to differentiate cards
  - Strategic use of accent colors to indicate status (e.g., green for published, amber for drafts)
  - A balance of professional aesthetics with modern touches

- **Typography:**
  - Professional sans-serif fonts that convey clarity and business-readiness

- **Accessibility:**
  - Scalable text and color-blind friendly palettes
  - Intuitive navigation with clear, accessible touchpoints

---

**Next Steps:**
Please review these three options and let me know:
- Which option best aligns with your vision for Levercast?
- If you have any amendments or additional preferences regarding the user persona, branding, or layout.

Once I have your feedback, I will proceed to generate the final User Interface Design Document in markdown format with the following sections:
- Layout Structure
- Core Components
- Interaction Patterns
- Visual Design Elements & Color Scheme
- Typography
- Accessibility

Looking forward to your feedback!
```

2.2 Rob's Response to Model UX AI Questions
```
On user persona details, I think these entrepreneurs are particularly digitally proficient. The selling point of this app is that it's not just another generic posting platform—it gives users the power to change their templates and properties. We're targeting more of a power user market. While others might use Buffer or Hootsuite, this is really for entrepreneurs who want to fine-tune their language, voice, and edit their own properties.

In terms of design, I prefer making things as minimalistic as possible. However, since users need options to change templates and customize settings, a balanced approach makes sense. Regarding branding and color preferences, I'll defer to your judgment based on our target market. Since we're targeting the development community and solopreneur makers, dark mode will likely be popular. For accessibility, we'll offer a light mode option as well, so our color scheme needs to accommodate both.

For devices, we're taking a desktop-first approach, but the app should be mobile responsive so users can operate it on phones and tablets. This will also inform our future React Native application development.
```

2.2 UX AI Model Response
```
**Next Steps:**
Please review these three options and let me know:
- Which option best aligns with your vision for Levercast?
- If you have any amendments or additional preferences regarding the user persona, branding, or layout.
```

2.3 Rob's Second Response
```
I think out of the three options of like number two, the most let's lead with dark mode. Let's add the accent color to be yellow and. I'd like the option to be able to switch to light mode. Of course for accessibility. I'd like the main navigation to be handled in the left hand sidebar, which can be collapsed. That will have the ability to create a new post. It'll show all the recent posts. It will have a settings, and a template's link. And whenever you navigate in the left hand menu, those components are opened in the main content area and at the bottom left of the sidebar put in the the profile icon of the person when they click on that it's gonna allow them to log out.
```

2.4 File Output: User Interface Design Doc
```
# Levercast User Interface Design Document

This document outlines the final UI design for Levercast, incorporating a bold, creative dark mode with a yellow accent color and an option to switch to light mode for enhanced accessibility. The design leverages a collapsible left-hand navigation sidebar and a dynamic main content area that adapts based on user interactions.

---

## Layout Structure

- **Overall Layout:**
  The interface is divided into two primary areas:
  - **Left-Hand Sidebar (Navigation):** A collapsible panel that hosts all primary navigation elements.
  - **Main Content Area:** A dynamic area that displays the content corresponding to the selected navigation item.

- **Left-Hand Sidebar:**
  - Contains navigation links for:
    - **New Post:** Opens the content creation screen.
    - **Recent Posts:** Displays a list or grid of past posts (with status indicators such as draft, pending, or published).
    - **Settings:** Provides access to user and app configuration options.
    - **Templates:** Displays available content formatting templates.
  - The bottom left of the sidebar features the **Profile Icon**; clicking it opens a dropdown for logout.

- **Main Content Area:**
  - Dynamically updates based on the selected sidebar option.
  - Houses content creation screens, previews, editing interfaces, and publishing controls.
  - Designed for real-time interactions and updates, especially for content formatting and social media previews.

- **Theme Toggle:**
  - A clearly accessible toggle control (placed within the header or settings) allows users to switch between dark mode (default) and light mode.

---

## Core Components

- **Collapsible Left-Hand Navigation Sidebar:**
  - **Navigation Items:** "New Post", "Recent Posts", "Settings", and "Templates".
  - **Profile Icon:** Located at the bottom left, facilitating quick logout access.
  - **Collapse/Expand Feature:** Enhances screen real estate management.

- **Main Content Area Components:**
  - **Content Creation Screen:**
    - A large text input field for raw content ideas.
    - An option to upload images.
    - Real-time previews of content formatted for social media (e.g., LinkedIn and Twitter) with inline editing capabilities.
  - **Recent Posts Display:**
    - A list or grid view of past content with visual indicators of status (draft, pending, published).
  - **Settings & Templates Screens:**
    - Configurable options and a list of available LLM-powered content templates.
  - **Publishing Controls:**
    - OAuth integrations for social media accounts.
    - A one-click publish button to post to all connected platforms.

- **Theme Toggle Component:**
  - Enables users to switch between dark and light modes, ensuring visual accessibility without compromising on design integrity.

---

## Interaction Patterns

- **Sidebar Navigation:**
  - **Collapsible Behavior:** Users can collapse or expand the sidebar for a focused view of the main content.
  - **Dynamic Content Loading:** Clicking any navigation item (e.g., New Post, Recent Posts, Settings, Templates) updates the main content area accordingly.
  - **Profile Dropdown:** Clicking the profile icon triggers a dropdown with logout options.

- **Content Creation & Editing:**
  - **Real-Time Preview:** As users input text and upload images, previews update instantly to reflect changes.
  - **Inline Editing:** Enables users to adjust formatted content directly within the preview panels.
  - **Modal Interactions:** For more detailed editing or additional options, modal dialogs provide focused interaction windows.

- **Theme Switching:**
  - **Toggle Control:** Users can switch between dark mode and light mode with a single click, ensuring readability and personal preference adherence.
  - **Consistent Feedback:** Visual cues (e.g., hover states, active states) indicate interactive elements and current selections.

- **Publishing Flow:**
  - **OAuth Integration:** Clear, direct buttons facilitate account connection.
  - **Unified Publish Button:** Initiates one-click publishing across all linked social media platforms.

---

## Visual Design Elements & Color Scheme

- **Dark Mode (Default):**
  - **Background:** Deep, dark shades to reduce eye strain and emphasize content.
  - **Accent Color:** A vibrant yellow is used to highlight primary actions (buttons, links, icons) and interactive elements.
  - **Visual Hierarchy:** Bold visuals with subtle gradients and shadows to create depth without sacrificing clarity.

- **Light Mode:**
  - **Background:** A clean, light palette that maintains the same accent yellow for consistency.
  - **Text and Elements:** Darker text and elements to ensure high readability against the lighter background.

- **Sidebar & Main Content Contrast:**
  - The sidebar features a slightly different tone from the main content area to delineate navigation from content.
  - Icons and labels are clear and modern, reinforcing the interface's intuitive use.

---

## Typography

- **Primary Font:**
  - A modern sans-serif typeface (e.g., Roboto, Helvetica Neue) for a clean, contemporary look.
- **Font Hierarchy:**
  - **Headings & Buttons:** Bold weights to draw attention.
  - **Body Text:** Regular weights for comfortable reading.
  - **Adaptive Sizing:** Font sizes are chosen for legibility across various devices, with responsive scaling for desktop and future mobile views.

---

## Accessibility

- **Contrast and Color:**
  - High contrast between text and background in both dark and light modes.
  - The accent yellow is used judiciously to ensure clarity without overwhelming the interface.
- **Keyboard Navigation:**
  - All interactive elements, including the sidebar, toggle switches, and buttons, are fully navigable via keyboard.
```

3.1 Initial Prompt for Al Software Architect
```
# Context
You are an expert Software Architect your role is to work with the product owner to generate a custom Software Requirements Specification Document. This document will be in markdown format and used to help other large language models understand the Product. Be concise.

# Input
1. You will be provided with the Product Requirements Doc and User Interface Design Doc for context
2. Ask the developer what their existing skillset is and what language and frameworks they are comfortable with.

# Instructions
1. Process the product requirements document and and User Interface Design Doc for context if they are not provided ask for them or help the user create one.
2. Output a simple (headings and bullets) markdown file based on the context and use the exact format in the Headings to be included section

# Headings to be included
- System Design
- Architecture pattern
- State management
- Data flow
- Technical Stack
- Authentication Process
- Route Design
- API Design
- Database Design ERD
```


Route Design (RESTful Endpoints)

levercast-product-requirements.md
levercast-project-management.md
levercast-software-specifications.md
levercast-ux-design.md

Please reference this documentation as we proceed, lets start with the software specification guidelines and hep me scaffold out the basis of our application and the best practice for how this website should be structured.