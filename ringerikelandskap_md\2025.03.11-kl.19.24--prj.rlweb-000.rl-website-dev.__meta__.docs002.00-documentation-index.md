# Ringerike Landskap Documentation Index

This index provides a comprehensive overview of all documentation for the Ringerike Landskap website project. Use this as your starting point for navigating the documentation.

## Quick Links

-   [AI Assistant Guide](./AI-README.md) - Essential context for AI assistants
-   [Initial Project Notes](./rl-website-initial-notes.md) - Initial business requirements and project goals
-   [Main README](./README.md) - Overview of documentation structure

## Documentation Categories

### 01. Project State

Current reality and context of the project:

-   [01-1. Current Codebase Reality](./01-project-state/01-current-codebase-reality.md) - Honest assessment of the current codebase
-   [01-2. Technical Debt Inventory](./01-project-state/02-technical-debt-inventory.md) - Inventory of technical debt to address
-   [01-3. Company Information](./01-project-state/03-company-information.md) - Business context and company details

### 02. Codebase Map

Structure and organization of the codebase:

-   [02-1. Directory Structure](./02-codebase-map/01-directory-structure.md) - Overview of file and folder organization
-   [02-2. Architecture Overview](./02-codebase-map/02-architecture-overview.md) - Technical architecture of the application
-   [02-3. Component Hierarchy](./02-codebase-map/03-component-hierarchy.md) - Component relationships and tree structure

### 03. Implementation Patterns

Coding patterns and standards:

-   [03-1. Current Patterns](./03-implementation-patterns/01-current-patterns.md) - Existing implementation patterns
-   [03-2. Component Patterns](./03-implementation-patterns/02-component-patterns.md) - Component implementation details
-   [03-3. Target Patterns](./03-implementation-patterns/03-target-patterns.md) - Recommended patterns for future development

### 04. Visual Systems

Design and visual documentation:

-   [04-1. Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md) - **CRITICAL SYSTEM** - Screenshot capture system
-   [04-2. Design Guidelines](./04-visual-systems/02-design-guidelines.md) - Visual design principles and guidelines
-   [04-3. Styling Approach](./04-visual-systems/03-styling-approach.md) - CSS and Tailwind implementation details

### 05. Development Workflow

Development processes and tools:

-   [05-1. Environment Setup](./05-development-workflow/01-environment-setup.md) - Setting up the development environment
-   [05-2. Optimization Guide](./05-development-workflow/02-optimization-guide.md) - Performance optimization strategies
-   [05-3. Accessibility Guide](./05-development-workflow/03-accessibility-guide.md) - Accessibility best practices

## Topic Cross-References

### Component Development

-   [Component Patterns](./03-implementation-patterns/02-component-patterns.md)
-   [Component Hierarchy](./02-codebase-map/03-component-hierarchy.md)
-   [Target Patterns](./03-implementation-patterns/03-target-patterns.md)

### Styling and Design

-   [Design Guidelines](./04-visual-systems/02-design-guidelines.md)
-   [Styling Approach](./04-visual-systems/03-styling-approach.md)
-   [Component Patterns](./03-implementation-patterns/02-component-patterns.md)

### Performance

-   [Optimization Guide](./05-development-workflow/02-optimization-guide.md)
-   [Target Patterns](./03-implementation-patterns/03-target-patterns.md)
-   [Screenshot Infrastructure](./04-visual-systems/01-screenshot-infrastructure.md)

### Business Context

-   [Company Information](./01-project-state/03-company-information.md)
-   [Initial Project Notes](./rl-website-initial-notes.md)

## Critical Systems

Special attention should be paid to these critical systems:

1. **Screenshot Infrastructure** - [Documentation](./04-visual-systems/01-screenshot-infrastructure.md)

    - Do not modify screenshot scripts without careful testing
    - Do not delete `.ai-analysis` or `screenshots` directories

2. **Norwegian Routes** - [See App.tsx]

    - Preserve Norwegian route names (/hvem-er-vi, /hva-vi-gjor, etc.)
    - Routes are critical for SEO and local branding

3. **Seasonal Adaptation** - [Design Guidelines](./04-visual-systems/02-design-guidelines.md)
    - Ensure seasonal content adaptation is properly implemented
    - Content should adapt based on current season (Vår, Sommer, Høst, Vinter)
