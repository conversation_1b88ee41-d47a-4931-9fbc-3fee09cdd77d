# 4. Tech Context - Ringerike Landskap Website

## Core Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Frontend Framework** | React | 18.3.1 | Component-based UI rendering |
| **Language** | TypeScript | ^5.2.2 | Type-safe JavaScript development |
| **Build Tool** | Vite | ^5.0.10 | Fast development server and optimized builds |
| **Styling** | Tailwind CSS | ^3.4.1 | Utility-first CSS framework |
| **Routing** | React Router | ^6.22.0 | Client-side routing and navigation |
| **Animation** | Framer Motion | ^11.0.3 | Animation and transitions |
| **Icons** | Lucide React | ^0.309.0 | SVG icon components |
| **SEO** | React Helmet | ^6.1.0 | Metadata and document head management |

## Development Environment

| Tool | Purpose |
|------|---------|
| **Node.js** | JavaScript runtime environment |
| **npm** | Package management |
| **ESLint** | Code linting and quality assurance |
| **PostCSS** | CSS processing for Tailwind |
| **TypeScript** | Static type checking and IDE support |

## Key Technical Constraints

1. **SEO Requirements**:
   - Must maintain proper heading hierarchy and semantic HTML
   - Requires meta tags and structured data for local SEO
   - Geographic location tags must be preserved across content

2. **Performance Constraints**:
   - Target initial load under 2 seconds on 4G connections
   - Mobile-first responsive design required for all components
   - Image optimization crucial due to high visual content

3. **Browser Compatibility**:
   - Support for modern browsers (last 2 versions)
   - Progressive enhancement for older browsers
   - WebP format with appropriate fallbacks

4. **Content Management**:
   - JSON-driven architecture for content separation
   - Directory-based content organization by season
   - Dynamic content discovery via `import.meta.glob`

## Project Structure Convention

```
src/
├── components/     # Reusable UI components
│   ├── layout/     # Structural layout components
│   ├── seo/        # SEO-related components
│   └── ui/         # Generic UI elements
├── features/       # Feature-specific components
│   ├── home/       # Homepage-specific components
│   ├── projects/   # Project-related components
│   ├── services/   # Service-related components
│   └── testimonials/ # Testimonial components
├── pages/          # Top-level page components
├── hooks/          # Custom React hooks
├── lib/            # Utilities, API, types
├── data/           # Static data and config
│   ├── config/     # Configuration JSON
│   └── projects/   # Project data by season
├── config/         # Config exports
├── content/        # Content definitions
├── styles/         # CSS files
├── utils/          # Utility functions
└── types/          # TypeScript type definitions
```

## Essential Type Definitions

The codebase relies on several key TypeScript interfaces:

```typescript
// Season type with Norwegian season names
type Season = 'vaar' | 'sommer' | 'hoest' | 'vinter';

// Project interface
interface Project {
  id: string;
  title: string;
  description: string;
  season: Season;
  location: string;
  images: string[];
  featured: boolean;
  completionDate: string;
  services: string[];
}

// Service interface
interface Service {
  id: string;
  title: string;
  description: string;
  seasons: Season[];
  icon: string;
  image: string;
  relatedProjects: string[];
}

// Service Area interface
interface ServiceArea {
  name: string;
  distance: number; // in km
  description: string;
  features: string[];
  isPrimary: boolean;
}

// Testimonial interface
interface Testimonial {
  id: string;
  author: string;
  location: string;
  text: string;
  rating: number;
  date: string;
  projectId?: string;
  serviceId?: string;
}
```

## Core Technical Utilities

1. **Season Detection**:
   ```typescript
   function getCurrentSeason(): Season {
     const month = new Date().getMonth();
     if (month >= 2 && month <= 4) return 'vaar';     // Spring (Mar-May)
     if (month >= 5 && month <= 7) return 'sommer';   // Summer (Jun-Aug)
     if (month >= 8 && month <= 10) return 'hoest';   // Fall (Sep-Nov)
     return 'vinter';                                 // Winter (Dec-Feb)
   }
   ```

2. **Dynamic Content Loading**:
   ```typescript
   // Load all project JSON files by season
   const projectModules = import.meta.glob('./projects/**/*.json', { eager: true });
   
   // Process and normalize project data
   const projects = Object.entries(projectModules).map(([path, module]) => {
     const season = path.includes('/vaar/') ? 'vaar' : 
                   path.includes('/sommer/') ? 'sommer' :
                   path.includes('/hoest/') ? 'hoest' : 'vinter';
     return { ...module.default, season };
   });
   ```

3. **Geographic Filtering**:
   ```typescript
   function filterByLocation(items: any[], location: string): any[] {
     return items.filter(item => 
       item.location?.toLowerCase() === location.toLowerCase()
     );
   }
   ```

## Build and Deployment Pipeline

1. **Development Workflow**:
   - `npm run dev`: Start Vite development server
   - `npm run lint`: Run ESLint for code quality
   - `npm run type-check`: Verify TypeScript types
   - `npm run screenshot`: Generate reference screenshots

2. **Build Process**:
   - `npm run build`: Create production build
     - TypeScript compilation
     - Tailwind CSS processing with purge
     - Asset optimization
     - Code splitting and bundling

3. **Environment Configuration**:
   - `.env.development`: Development settings
   - `.env.production`: Production settings

## Technical Debt and Transition State

The codebase is currently transitioning from hardcoded data to a more maintainable JSON-driven architecture:

1. **Current Technical Debt**:
   - Mixed data sources (JSON files and hardcoded data)
   - Incomplete typing in some areas
   - Some components tightly coupled to data structure
   - Inconsistent loading patterns across different content types

2. **Transition Strategy**:
   - Migration scripts to convert hardcoded data to JSON
   - Backward compatibility layer for gradual transition
   - Progressive typing improvement
   - Refactoring components to consume API layer

## Design System and UI Constraints

1. **Color Palette**:
   - Primary: Custom green palette (#2D5F4D, #1A3C30)
   - Secondary: Earth tones for seasonal contrast
   - Accents: Seasonal color highlights

2. **Typography**:
   - Primary Font: Inter (sans-serif)
   - Heading Scale: 4xl (36px) → xl (20px)
   - Body: base (16px)
   - Mobile Adjustments: Fluid typography scaling

3. **Component Variants**:
   - Buttons: Primary (filled), Secondary (outline), Text
   - Cards: Service, Project, Testimonial
   - Hero Sections: Full-width with overlay

4. **Responsive Breakpoints**:
   - Mobile: < 640px
   - Tablet: 640px - 1024px
   - Desktop: > 1024px
