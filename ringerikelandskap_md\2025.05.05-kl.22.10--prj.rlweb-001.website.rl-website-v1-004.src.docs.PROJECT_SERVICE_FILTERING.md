# Project-Service Relationship Filtering

This document explains the consolidation of project-service relationship filtering in the Ringerike Landskap website codebase.

## Overview

To improve maintainability and prevent code duplication, we have consolidated all project-service relationship filtering logic into the central filtering utilities module: `src/lib/utils/filtering.ts`. This module now serves as the single source of truth for all filtering operations throughout the application.

## Changes Made

1. Added new utility functions to handle project-service relationships:
   - `projectMatchesService`: Checks if a project matches a service
   - `getProjectsForService`: Gets projects that match a specific service
   - `getServicesWithProjects`: Gets services that have matching projects
   - `getUniqueProjectLocations`: Gets unique locations from projects filtered by service

2. Updated components to use the centralized utilities:
   - `ProjectGrid`: Now uses `filterProjects` instead of custom filtering logic
   - `Projects`: Now uses the new utility functions for all filtering operations

3. Deprecated old filtering code:
   - Marked `src/data/projects.ts` as deprecated
   - Removed duplicate filtering logic from components

4. Fixed React hooks order in the ProjectDetail component:
   - Ensured hooks are always called in the same order
   - Moved conditional logic inside hook callbacks
   - Moved error and loading state handling after all hooks have been called

## Benefits

- **Reduced Code Duplication**: Eliminated duplicate string normalization and comparison logic
- **Consistent Behavior**: All components now use the same filtering logic
- **Improved Maintainability**: Changes to filtering logic only need to be made in one place
- **Better Type Safety**: All filtering functions are properly typed
- **Cleaner Components**: Components are now focused on presentation rather than filtering logic

## New Utility Functions

### `projectMatchesService`

Checks if a project matches a service based on normalized string comparison of the project category and service title.

```typescript
const matches = projectMatchesService(project, service);
```

### `getProjectsForService`

Gets projects that match a specific service, with optional location filtering.

```typescript
const filteredProjects = getProjectsForService(
  projects,
  serviceId,
  services,
  location
);
```

### `getServicesWithProjects`

Gets services that have at least one matching project, with optional location filtering.

```typescript
const servicesWithProjects = getServicesWithProjects(
  services,
  projects,
  location
);
```

### `getUniqueProjectLocations`

Gets unique locations from projects, optionally filtered by service.

```typescript
const locations = getUniqueProjectLocations(
  projects,
  serviceId,
  services
);
```

## Usage Example

```tsx
import {
  getProjectsForService,
  getServicesWithProjects,
  getUniqueProjectLocations
} from '@/lib/utils/filtering';
import { useData } from '@/lib/hooks';
import { getProjects, getServices } from '@/lib/api';

const ProjectsComponent = () => {
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

  // Fetch data
  const { data: projects } = useData(getProjects, []);
  const { data: services } = useData(getServices, []);

  // Get unique locations based on selected service
  const locations = useMemo(() => {
    if (!projects || !services) return [];
    return getUniqueProjectLocations(projects, selectedService, services);
  }, [projects, services, selectedService]);

  // Get services that have projects
  const servicesWithProjects = useMemo(() => {
    if (!projects || !services) return [];
    return getServicesWithProjects(services, projects, selectedLocation);
  }, [projects, services, selectedLocation]);

  // Filter projects based on selected filters
  const filteredProjects = useMemo(() => {
    if (!projects || !services) return [];
    if (selectedService) {
      return getProjectsForService(
        projects,
        selectedService,
        services,
        selectedLocation
      );
    } else {
      return filterProjects(projects, null, selectedLocation);
    }
  }, [projects, services, selectedService, selectedLocation]);

  // Component rendering...
};
```

## React Hooks Best Practices

When using React hooks with filtering utilities, follow these best practices:

1. **Always Call Hooks in the Same Order**:
   - Don't call hooks conditionally
   - Don't call hooks in loops or nested functions
   - Always call all hooks at the top level of your component

2. **Move Conditional Logic Inside Hook Callbacks**:
   - Instead of conditionally calling a hook, always call the hook and handle the condition inside the callback:
   ```tsx
   // Good
   const { data } = useData(() => {
     if (condition) {
       return fetchData();
     }
     return Promise.resolve(null);
   }, [condition]);

   // Bad
   let data;
   if (condition) {
     const result = useData(() => fetchData(), []);
     data = result.data;
   }
   ```

3. **Handle Loading and Error States After All Hooks**:
   - Make sure all hooks are called before any early returns
   - Handle loading and error states after all hooks have been called

4. **Use Proper Dependencies**:
   - Include all values from the component scope that are used inside the hook callback
   - Use optional chaining for potentially undefined values in dependencies

## Future Improvements

- Add more specialized filtering functions for common use cases
- Implement caching for expensive filtering operations
- Add unit tests for filtering functions
- Add ESLint rules to enforce React hooks best practices
