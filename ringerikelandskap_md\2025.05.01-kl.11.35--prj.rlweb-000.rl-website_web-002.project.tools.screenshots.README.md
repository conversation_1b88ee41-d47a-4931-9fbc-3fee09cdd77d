# Screenshot Management System

This directory contains tools and utilities for capturing, managing, and analyzing screenshots of the Ringerike Landskap website.

## Directory Structure

```
tools/screenshots/
├── config/                  # Configuration files
│   └── screenshot-config.json
├── outputs/                 # Generated outputs
│   ├── captured/            # Captured screenshots
│   │   ├── YYYY-MM-DD_HH-MM-SS/  # Timestamped directories
│   │   └── latest/          # Latest screenshots
│   ├── reports/             # HTML reports
│   │   └── screenshot-report.html
│   ├── ai/                  # AI analysis data
│   │   ├── latest/          # Latest AI analysis
│   │   ├── snapshot-TIMESTAMP/  # Historical AI snapshots
│   │   └── metadata.json    # AI snapshot metadata
│   └── .gitkeep
├── scripts/                 # Scripts for capturing and managing screenshots
│   ├── capture.js           # Core screenshot capture script
│   ├── manage.js            # Screenshot management utilities
│   ├── dev-snapshots.js     # Development server with auto-snapshots
│   └── run-capture.bat      # Windows batch script for capturing
└── README.md                # This documentation file
```

## Configuration

The screenshot system is configured in `config/screenshot-config.json` with the following settings:

- **port**: Development server port (default: 5173)
- **captureDelay**: Delay before capturing screenshots in milliseconds (default: 2000)
- **maxSnapshots**: Maximum number of snapshot directories to keep (default: 10)
- **pages**: List of pages to capture, with path and name
- **viewports**: List of viewport sizes to capture (mobile, tablet, desktop)
- **aiViewport**: Viewport size for AI analysis (default: desktop)

## Usage

### Capturing Screenshots

```bash
# Capture screenshots
npm run screenshots:capture

# Capture screenshots for AI analysis
npm run ai:snapshot
```

### Managing Screenshots

```bash
# Clean up old screenshots
npm run screenshots:clean

# Tidy screenshot directory
npm run screenshots:tidy

# Refresh screenshots (capture, tidy, and clean)
npm run screenshots:refresh
```

### Development with Auto-Snapshots

```bash
# Start development server with auto-snapshots
npm run dev:ai
```

## Outputs

### Captured Screenshots

Screenshots are organized by:
1. Timestamp directory (e.g., `2025-04-19_15-18-45/`)
2. Viewport size (mobile, tablet, desktop)
3. Page name (home, about, services, projects, contact)

The `latest/` directory always contains the most recent screenshots.

### Reports

The `screenshot-report.html` file provides an interactive interface for viewing and comparing screenshots.

### AI Analysis

The AI analysis directory contains:
- Snapshots of the website for AI analysis
- Metadata about the snapshots
- AI-generated summaries

## Adding New Pages or Viewports

To add new pages or viewport sizes:

1. Edit `config/screenshot-config.json`
2. Add new entries to the `pages` or `viewports` arrays
3. Run `npm run screenshots:capture` to generate new screenshots

## Version Control

The screenshots directory is configured with a `.gitignore` file to prevent screenshots from being committed to the repository. Only the directory structure and configuration files are preserved in version control.
