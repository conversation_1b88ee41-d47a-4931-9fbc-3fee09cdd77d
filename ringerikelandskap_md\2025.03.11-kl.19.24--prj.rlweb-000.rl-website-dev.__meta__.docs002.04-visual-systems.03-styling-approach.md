# Ringerike Landskap Website Styling and Theming

## Color Palette

The website uses a custom green-focused color palette that reflects the landscaping business's connection to nature:

```
colors: {
  green: {
    50: '#f0f9f4',   // Very light green (backgrounds, hover states)
    100: '#dbf0e3',  // Light green (backgrounds, borders)
    200: '#b8e0ca',  // Light green (secondary elements)
    300: '#89c9a8',  // Medium green (accents)
    400: '#57ab83',  // Medium green (secondary buttons)
    500: '#1e9545',  // Primary green (buttons, links)
    600: '#157c3a',  // Darker green (hover states)
    700: '#116530',  // Dark green (active states)
    800: '#0e4f27',  // Very dark green (text on light backgrounds)
    900: '#0c4121',  // Darkest green (text on light backgrounds)
  },
}
```

Other colors used:

-   White (`#ffffff`) - Backgrounds, text on dark surfaces
-   Gray shades - Text, borders, subtle backgrounds
-   Black with opacity - Overlays, shadows

## Typography

The website uses a clean, modern typography system:

```
fontFamily: {
  sans: ['Inter', 'system-ui', 'sans-serif'],
}
```

Font sizes follow a hierarchical scale:

-   Headings:
    -   H1: `text-4xl sm:text-5xl lg:text-6xl` (2.25rem to 3.75rem)
    -   H2: `text-xl sm:text-2xl` (1.25rem to 1.5rem)
    -   H3: `text-lg font-semibold` (1.125rem)
-   Body: `text-base` (1rem)
-   Small text: `text-sm` (0.875rem)
-   Micro text: `text-xs` (0.75rem)

Font weights:

-   Regular: 400
-   Medium: 500
-   Semibold: 600
-   Bold: 700

## Spacing System

The website uses Tailwind's default spacing scale with consistent spacing throughout:

-   Container padding: `px-4 sm:px-6 lg:px-8`
-   Vertical section spacing: `py-8 sm:py-12`
-   Grid gaps: `gap-6 sm:gap-8`
-   Component padding: `px-4 py-3` (buttons), `p-6` (cards)

## Animations

Custom animations defined in the Tailwind config:

```
animation: {
  'fade-in': 'fadeIn 0.5s ease-out forwards',
  'slide-up': 'slideUp 0.5s ease-out forwards',
  'gradient': 'gradient 8s ease-in-out infinite',
},
keyframes: {
  fadeIn: {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
  slideUp: {
    '0%': {
      opacity: '0',
      transform: 'translateY(20px)'
    },
    '100%': {
      opacity: '1',
      transform: 'translateY(0)'
    },
  },
  gradient: {
    '0%, 100%': { backgroundPosition: '0% 50%' },
    '50%': { backgroundPosition: '100% 50%' },
  },
}
```

These animations are used for:

-   Page transitions
-   Component mounting
-   Hover effects
-   Background gradients

## UI Components

### Buttons

Primary button:

```
className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
```

Secondary button:

```
className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
```

### Cards

Service/Project card:

```
className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1"
```

### Hero Section

The Hero component uses multiple overlays for text readability:

1. Base background image with fixed attachment
2. Gradient overlay for text contrast
3. Radial gradient for corner darkening

### Navigation

Desktop navigation:

```
className="hidden md:flex md:items-center md:space-x-8"
```

Mobile navigation:

```
className="md:hidden"
```

Active link:

```
className="text-green-600"
```

Inactive link:

```
className="text-gray-700 hover:text-green-600 transition-colors"
```

## Responsive Design

The website uses a mobile-first approach with breakpoints:

-   Default: Mobile styles
-   `sm`: 640px and up
-   `md`: 768px and up
-   `lg`: 1024px and up
-   `xl`: 1280px and up

Key responsive patterns:

-   Single column on mobile, multi-column on larger screens
-   Hidden navigation on mobile, visible on desktop
-   Adjusted font sizes across breakpoints
-   Flexible spacing that increases on larger screens

## Shadows and Depth

The website uses a consistent shadow system for depth:

-   Cards: `shadow-md`
-   Hover states: `shadow-lg`
-   Buttons: `shadow-sm`
-   Header (when scrolled): `shadow-md`

## Special Effects

1. **Backdrop Blur**:

    ```
    className="bg-white/90 backdrop-blur-sm"
    ```

    Used in the header for a frosted glass effect

2. **Gradient Overlays**:

    ```
    background: linear-gradient(50deg, transparent 15%, rgba(0,0,0,0.75) 32%, rgba(0,0,0,0.6) 72%, transparent 85%)
    ```

    Used in the hero section for text readability

3. **Hover Transitions**:

    ```
    className="transition-colors hover:text-green-600"
    ```

    Smooth color changes on hover

4. **Scroll Animations**:
   Elements fade in and slide up as they enter the viewport
