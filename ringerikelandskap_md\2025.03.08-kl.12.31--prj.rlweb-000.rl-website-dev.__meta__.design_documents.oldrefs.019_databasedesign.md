
# Database Design ERD
- **Possible Entities** (if using a backend database):
  1. **Services**
     - `id` (PK)
     - `name`
     - `description`
     - `season` (optional)
     - `images`
  2. **Projects**
     - `id` (PK)
     - `title`
     - `location`
     - `description`
     - `service_ids` (multiple or a join table)
     - `images`
     - `season`
  3. **Testimonials**
     - `id` (PK)
     - `client_name`
     - `rating`
     - `comment`
     - `project_id` (FK)
  4. **Inquiries** (contact form submissions)
     - `id` (PK)
     - `name`
     - `email`
     - `phone`
     - `message`
     - `timestamp`
- **Relationships**:
  - A project can reference one or many services (many-to-many with a join table if needed).
  - Testimonials can be linked to projects (one-to-many relationship).
  - Inquiries are standalone records tied to potential leads or existing customers.

