# Responsive Design

## Overview

The Ringerike Landskap website is fully responsive, providing an optimal viewing experience across a wide range of devices, from mobile phones to desktop monitors. This is achieved through a combination of Tailwind CSS utility classes, custom responsive components, and media queries.

## Responsive Framework

### Tailwind CSS Breakpoints

The project uses Tailwind CSS's responsive breakpoints:

- **sm**: 640px and up
- **md**: 768px and up
- **lg**: 1024px and up
- **xl**: 1280px and up
- **2xl**: 1536px and up

These breakpoints are used throughout the application to create responsive layouts:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Content */}
</div>
```

### Container Component

The Container component provides consistent width constraints and padding across different screen sizes:

```jsx
<Container size="lg" className="py-8 sm:py-12">
  {children}
</Container>
```

The Container component applies different max-widths based on the `size` prop:

```typescript
const sizes = {
  sm: 'max-w-3xl',
  md: 'max-w-5xl',
  lg: 'max-w-7xl',
  xl: 'max-w-[90rem]'
};
```

## Responsive Layouts

### Grid Layouts

Grid layouts adapt to different screen sizes:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Grid items */}
</div>
```

This creates:
- A single column on mobile
- Two columns on medium screens
- Three columns on large screens

### Flex Layouts

Flex layouts are used for more dynamic arrangements:

```jsx
<div className="flex flex-col sm:flex-row gap-4 justify-center">
  <Button to="/kontakt" size="lg" className="group">
    Book gratis befaring
  </Button>
  <Button
    to="/hva-vi-gjor"
    variant="outline"
    size="lg"
    className="bg-white/10 backdrop-blur-sm border-white text-white hover:bg-white/20"
  >
    Se våre tjenester
  </Button>
</div>
```

This creates:
- A vertical stack on mobile
- A horizontal row on small screens and up

## Responsive Components

### Hero Component

The Hero component adapts its height and content layout based on screen size:

```jsx
<section
  className="relative min-h-[480px] sm:min-h-[580px] flex items-center justify-center text-white overflow-hidden"
  // ...
>
  {/* Content */}
</section>
```

- Minimum height of 480px on mobile
- Minimum height of 580px on small screens and up

### ProjectCard Component

The ProjectCard component adjusts its height based on screen size:

```jsx
<div className="h-[350px] md:h-[400px] lg:h-[450px]">
  <ProjectCard 
    project={project}
    onProjectClick={handleProjectClick}
    showTestimonial={true}
  />
</div>
```

- 350px height on mobile
- 400px height on medium screens
- 450px height on large screens

### Header Component

The Header component includes a responsive navigation menu:

```jsx
{/* Desktop navigation */}
<nav className="hidden md:flex md:items-center md:space-x-8">
  {/* Navigation items */}
</nav>

{/* Mobile menu button */}
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
  aria-expanded={isMenuOpen}
  aria-controls="mobile-menu"
>
  {/* Button content */}
</button>

{/* Mobile menu */}
{isMenuOpen && (
  <div className="md:hidden" id="mobile-menu">
    {/* Mobile menu content */}
  </div>
)}
```

- Hidden desktop navigation and visible mobile menu button on mobile
- Visible desktop navigation and hidden mobile menu button on medium screens and up

## Responsive Typography

Typography scales based on screen size:

```jsx
<h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 tracking-tight">
  {title}
</h1>

<p className="text-lg sm:text-xl max-w-2xl mx-auto leading-relaxed mb-8 text-gray-100">
  {subtitle}
</p>
```

- Smaller font sizes on mobile
- Larger font sizes on larger screens
- Adjusted margins and padding

## Responsive Images

Images adapt to their containers:

```jsx
<img
  src={project.image}
  alt={project.title}
  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
  loading="lazy"
/>
```

- `w-full h-full object-cover` ensures the image fills its container while maintaining aspect ratio
- `loading="lazy"` defers loading of off-screen images

## Responsive UI Elements

### Buttons

Buttons adjust their size based on screen size:

```jsx
<Button
  to="/kontakt"
  variant="primary"
  size="lg"
  className="px-8 py-3"
>
  Kontakt oss for gratis befaring
</Button>
```

The size prop maps to different padding and font sizes:

```typescript
const sizeClasses = {
  sm: "text-sm py-1.5 px-3",
  md: "text-base py-2 px-4",
  lg: "text-lg py-2.5 px-5"
};
```

### Forms

Form elements are responsive:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <input
    type="text"
    placeholder="Navn"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
  <input
    type="text"
    placeholder="Budsjett"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
</div>
```

- Single column on mobile
- Two columns on medium screens and up

## Media Queries

Custom media queries are used for more specific responsive behavior:

```typescript
// Custom hook for media queries
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(() => 
    window.matchMedia(query).matches
  );

  useEffect(() => {
    const media = window.matchMedia(query);
    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);
    
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
};
```

This hook is used in components like TestimonialsSlider to adjust the number of visible testimonials:

```jsx
useEffect(() => {
  const handleResize = () => {
    if (window.innerWidth >= 1024) {
      setItemsPerView(3);
    } else if (window.innerWidth >= 768) {
      setItemsPerView(2);
    } else {
      setItemsPerView(1);
    }
  };

  // Set initial value
  handleResize();

  // Add event listener
  window.addEventListener('resize', handleResize);

  // Clean up
  return () => window.removeEventListener('resize', handleResize);
}, []);
```

## Responsive Best Practices

1. **Mobile-First Approach**: Start with mobile layout and enhance for larger screens
2. **Fluid Typography**: Scale text based on screen size
3. **Flexible Images**: Ensure images adapt to their containers
4. **Touch-Friendly UI**: Larger touch targets on mobile
5. **Performance Optimization**: Lazy loading and optimized assets
6. **Testing**: Test on multiple devices and screen sizes
7. **Accessibility**: Ensure accessibility across all screen sizes