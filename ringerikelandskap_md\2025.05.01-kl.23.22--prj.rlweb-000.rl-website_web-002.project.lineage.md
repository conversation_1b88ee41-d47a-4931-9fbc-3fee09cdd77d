# Ringerike Landskap Website - Project Lineage

## Core Architecture

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **State Management**: React hooks (no Redux/Context)

## Project Structure

```
website/
├── public/           # Static assets (images)
└── src/              # Source code
    ├── app/          # Application root shell and router
    ├── sections/     # Chronologically ordered, self-contained sections
    │   ├── 10-home/  # Home page and related components
    │   ├── 20-about/ # About page and related components
    │   ├── 30-services/ # Services pages and related components
    │   ├── 40-projects/ # Projects pages and related components
    │   ├── 50-testimonials/ # Testimonials pages and related components
    │   └── 60-contact/ # Contact page and related components
    ├── ui/           # Global, atomic UI components (Button, Hero, Intersection, etc.)
    ├── layout/       # Shared layout components
    ├── data/         # Static data (services, projects, testimonials)
    ├── lib/          # Utilities, API, hooks
    ├── types/        # TypeScript definitions
    └── hooks/        # Custom React hooks
```

## Key Components

1. **Header**: Main navigation component with responsive mobile menu (in `layout/Header.tsx`)
2. **Hero**: Flexible hero section with background image, title, subtitle, and CTA (in `ui/Hero.tsx`)
3. **ServiceAreaList**: Displays service areas with location information (in `ui/ServiceAreaList.tsx`)
4. **SeasonalCTA**: Context-aware call-to-action that changes based on current season (in `ui/SeasonalCTA.tsx`)
5. **ProjectCard**: Display projects in card format (in `sections/40-projects/ProjectCard.tsx`)
6. **TestimonialsSection**: Displays testimonials with filtering (in `sections/50-testimonials/TestimonialsSection.tsx`)
7. **FilteredServicesSection**: Shows services filtered by season or category (in `sections/10-home/FilteredServicesSection.tsx`)
8. **SeasonalProjectsCarousel**: Displays seasonal projects in a carousel (in `sections/10-home/SeasonalProjectsCarousel.tsx`)

## Data Architecture

- **Static Data Files**: `services.ts`, `projects.ts`, `testimonials.ts`
- **API Layer**: Simulated API in `lib/api/index.ts` with filtering capabilities
- **Data Fetching**: Custom `useData` hook for loading states and error handling

## Page Structure

1. **Home** (`/`): Located in `sections/10-home/index.tsx` - Hero, seasonal projects carousel, service areas, filtered services
2. **About** (`/hvem`): Located in `sections/20-about/index.tsx` - Company information and team
3. **Services** (`/hva`): Located in `sections/30-services/index.tsx` - Services overview with filtering options
   - **Service Detail** (`/hva/:id`): Located in `sections/30-services/detail.tsx` - Individual service details
4. **Projects** (`/prosjekter`): Located in `sections/40-projects/index.tsx` - Project gallery with filtering by category/location
   - **Project Detail** (`/prosjekter/:id`): Located in `sections/40-projects/detail.tsx` - Individual project details
5. **Testimonials** (`/tilbakemeldinger`): Located in `sections/50-testimonials/index.tsx` - Customer testimonials with filtering
6. **Contact** (`/kontakt`): Located in `sections/60-contact/index.tsx` - Contact form and location information

## Seasonal Adaptation

The website adapts content based on the current season (vår, sommer, høst, vinter):

- Seasonal projects are highlighted
- Service recommendations change
- CTAs and tips are season-specific
- Content is filtered automatically based on relevance to current season

## Key Design Patterns

1. **Component Composition**: UI built from small, reusable components
2. **Responsive Design**: Mobile-first approach with Tailwind breakpoints
3. **Progressive Enhancement**: Loading states for all data-dependent components
4. **Semantic HTML**: Proper use of HTML5 elements and ARIA attributes
5. **Conditional Rendering**: Components adapt based on props and state

## SEO Implementation

- Meta component for page-specific metadata
- Structured data with schema.org markup
- Testimonials schema for rich results
- Proper heading hierarchy and semantic HTML

## Local Context

- Focus on Ringerike region in Norway
- Service areas include Røyse, Hole, Vik, Hønefoss, Sundvollen, Jevnaker
- Content emphasizes local knowledge of terrain and climate
- Norwegian language throughout the site

## Technical Insights

1. The project uses a custom hook pattern for data fetching (see `lib/api/index.ts`)
2. No global state management - relies on component composition and prop passing
3. Tailwind for styling with minimal custom CSS
4. Seasonal detection based on date to customize content (see `lib/utils/seasonal.ts`)
5. Image optimization with WebP format (see `lib/utils/images.ts`)
6. Chronological organization of sections with numbered prefixes (10-home, 20-about, etc.)
7. Self-contained sections with their own components
8. Shared UI components in the `ui` directory
9. Consistent use of relative imports for clarity and maintainability

## Essential Dependencies

- react
- react-dom
- react-router-dom
- react-helmet-async (for SEO)
- tailwindcss
- lucide-react (for icons)
- clsx/tailwind-merge (for conditional classes)

## Deployment Workflow

1. Development in `website/` directory
2. Build process outputs to `dist/` directory
3. Production files deployed to `www/` directory
4. Separate environments for development, staging, and production

## Recent Restructuring

The project has recently undergone a significant restructuring to improve organization and maintainability:

1. **Chronological Organization**: Sections are now prefixed with numbers (10-home, 20-about, etc.) to indicate their order in the user journey.
2. **Self-Contained Sections**: Each section now contains all the components specific to that section.
3. **Shared Components**: Common UI elements are now in the `ui` directory, layout components in the `layout` directory.
4. **Consistent Imports**: The project now uses relative imports consistently for clarity and maintainability.
5. **Consolidated UI Components**: All UI components have been consolidated into the `ui` directory, removing redundancy between `components/ui` and `ui`.
6. **Organized Form Components**: Form-related components are now in a dedicated `ui/Form` directory.
7. **Consolidated Utility Files**: Hooks, types, and utilities have been consolidated into their respective directories with proper index files.

This restructuring has resolved several issues:
- Inconsistent page organization (some in directories, some at root level)
- Duplicate files (e.g., both `pages/Services.tsx` and `pages/services/index.tsx`)
- Mixed import patterns (both relative and alias imports)
- Scattered feature components
- Redundant component organization
- Fragmented utility functions and hooks

## Conclusion

This document captures the essential architecture and patterns of the Ringerike Landskap website, providing a blueprint for understanding, maintaining, and extending the project while preserving its core functionality and design principles.
