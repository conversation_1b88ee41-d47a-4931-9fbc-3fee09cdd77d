# Dir `prj`

*Files marked `[-]` are shown in structure but not included in content.*

### File Structure

```
├── .gitignore
├── prj_ringerikelandskap_py.sublime-project
├── prj_ringerikelandskap_py.sublime-workspace [-]
├── prj_ringerikelandskap_web.sublime-project
├── prj_ringerikelandskap_web.sublime-workspace [-]
├── memory-bank
│   ├── 1-projectbrief.md
│   ├── 2-productContext.md
│   ├── 3-systemPatterns.md
│   ├── 4-techContext.md
│   ├── 5-activeContext.md
│   ├── 6-progress.md
│   └── memory-bank.md
├── playground
│   ├── dependency-visualizer (1).zip [-]
│   ├── dependency-visualizer.zip [-]
│   ├── project-bolt-github-abr1xucc (1).zip [-]
│   ├── project-bolt-github-abr1xucc (2).zip [-]
│   ├── project-bolt-github-abr1xucc (3).zip [-]
│   ├── project-bolt-github-abr1xucc (4).zip [-]
│   ├── project-bolt-github-abr1xucc (5).zip [-]
│   ├── project-bolt-github-abr1xucc (6).zip [-]
│   ├── project-bolt-github-abr1xucc.zip [-]
│   ├── project-bolt-github-pchmwwml (1).zip [-]
│   ├── project-bolt-github-pchmwwml (10).zip [-]
│   ├── project-bolt-github-pchmwwml (11).zip [-]
│   ├── project-bolt-github-pchmwwml (2).zip [-]
│   ├── project-bolt-github-pchmwwml (3).zip [-]
│   ├── project-bolt-github-pchmwwml (4).zip [-]
│   ├── project-bolt-github-pchmwwml (5).zip [-]
│   ├── project-bolt-github-pchmwwml (6).zip [-]
│   ├── project-bolt-github-pchmwwml (7).zip [-]
│   ├── project-bolt-github-pchmwwml (8).zip [-]
│   ├── project-bolt-github-pchmwwml (9).zip [-]
│   ├── project-bolt-github-pchmwwml.zip [-]
│   ├── project-bolt-sb1-e3eu5q7o.zip [-]
│   ├── project-bolt-sb1-nmxbjhlf.zip [-]
│   ├── project-bolt-sb1-tycakpvx.zip [-]
│   ├── project-bolt-github-abr1xucc (4)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── TODO.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── philosophy.txt
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── techstack.md
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── project
│   │       │   ├── .gitignore
│   │       │   ├── .gitkeep
│   │       │   ├── README.md
│   │       │   ├── TODO.md
│   │       │   ├── eslint.config.js
│   │       │   ├── index.html
│   │       │   ├── package.json
│   │       │   ├── philosophy.txt
│   │       │   ├── postcss.config.js
│   │       │   ├── tailwind.config.js
│   │       │   ├── techstack.md
│   │       │   ├── tsconfig.app.json
│   │       │   ├── tsconfig.json
│   │       │   ├── tsconfig.node.json
│   │       │   ├── vite.config.ts
│   │       │   ├── public
│   │       │   │   ├── robots.txt
│   │       │   │   ├── site.webmanifest
│   │       │   │   ├── sitemap.xml
│   │       │   │   └── images
│   │       │   │       ├── metadata.json
│   │       │   │       └── categorized
│   │       │   │           ├── hero-prosjekter.HEIC [-]
│   │       │   │           ├── hero-residential-backyard.jpeg [-]
│   │       │   │           └── kantstein
│   │       │   │               └── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │   ├── scripts
│   │       │   │   └── cleanup.js
│   │       │   └── src
│   │       │       ├── App.tsx
│   │       │       ├── index.css
│   │       │       ├── index.html
│   │       │       ├── main.tsx
│   │       │       ├── src.md
│   │       │       ├── vite-env.d.ts
│   │       │       ├── app
│   │       │       │   ├── layout.tsx
│   │       │       │   └── page.tsx
│   │       │       ├── components
│   │       │       │   ├── ContactForm.tsx
│   │       │       │   ├── Navbar.tsx
│   │       │       │   ├── ServiceCard.tsx
│   │       │       │   ├── components.md
│   │       │       │   ├── index.ts
│   │       │       │   ├── (sections)
│   │       │       │   │   ├── coverage-area
│   │       │       │   │   │   └── CoverageArea.tsx
│   │       │       │   │   ├── hero
│   │       │       │   │   │   └── Hero.tsx
│   │       │       │   │   ├── projects
│   │       │       │   │   │   ├── ProjectCard.tsx
│   │       │       │   │   │   └── ProjectsCarousel.tsx
│   │       │       │   │   ├── seasonal-planning
│   │       │       │   │   │   └── SeasonalPlanning.tsx
│   │       │       │   │   ├── services
│   │       │       │   │   │   ├── ServiceCard.tsx
│   │       │       │   │   │   ├── ServiceFeature.tsx
│   │       │       │   │   │   └── Services.tsx
│   │       │       │   │   └── testimonials
│   │       │       │   │       └── Testimonials.tsx
│   │       │       │   ├── common
│   │       │       │   │   ├── Button.tsx
│   │       │       │   │   ├── Container.tsx
│   │       │       │   │   ├── Hero.tsx
│   │       │       │   │   ├── ImageGallery.tsx
│   │       │       │   │   ├── LocalExpertise.tsx
│   │       │       │   │   ├── LocalServiceArea.tsx
│   │       │       │   │   ├── Logo.tsx
│   │       │       │   │   ├── SeasonalCTA.tsx
│   │       │       │   │   ├── ServiceAreaList.tsx
│   │       │       │   │   └── WeatherAdaptedServices.tsx
│   │       │       │   ├── contact
│   │       │       │   │   └── ContactForm.tsx
│   │       │       │   ├── layout
│   │       │       │   │   ├── Footer.tsx
│   │       │       │   │   ├── Hero.tsx
│   │       │       │   │   ├── Layout.tsx
│   │       │       │   │   ├── Meta.tsx
│   │       │       │   │   └── Navbar.tsx
│   │       │       │   ├── local
│   │       │       │   │   ├── SeasonalGuide.tsx
│   │       │       │   │   ├── ServiceAreaMap.tsx
│   │       │       │   │   └── WeatherNotice.tsx
│   │       │       │   ├── projects
│   │       │       │   │   ├── ProjectCard.tsx
│   │       │       │   │   ├── ProjectFilter.tsx
│   │       │       │   │   ├── ProjectGallery.tsx
│   │       │       │   │   └── ProjectGrid.tsx
│   │       │       │   ├── seo
│   │       │       │   │   └── TestimonialsSchema.tsx
│   │       │       │   ├── services
│   │       │       │   │   ├── Gallery.tsx
│   │       │       │   │   └── ServiceCard.tsx
│   │       │       │   ├── shared
│   │       │       │   │   ├── ErrorBoundary.tsx
│   │       │       │   │   ├── Elements
│   │       │       │   │   │   ├── Card.tsx
│   │       │       │   │   │   ├── Icon.tsx
│   │       │       │   │   │   ├── Image.tsx
│   │       │       │   │   │   ├── Link.tsx
│   │       │       │   │   │   ├── Loading.tsx
│   │       │       │   │   │   ├── index.ts
│   │       │       │   │   │   └── Form
│   │       │       │   │   │       ├── Input.tsx
│   │       │       │   │   │       ├── Select.tsx
│   │       │       │   │   │       ├── Textarea.tsx
│   │       │       │   │   │       └── index.ts
│   │       │       │   │   └── Layout
│   │       │       │   │       ├── Layout.tsx
│   │       │       │   │       └── index.ts
│   │       │       │   └── ui
│   │       │       │       ├── Button.tsx
│   │       │       │       ├── Container.tsx
│   │       │       │       ├── Hero.tsx
│   │       │       │       ├── Intersection.tsx
│   │       │       │       ├── Logo.tsx
│   │       │       │       ├── Notifications.tsx
│   │       │       │       ├── Skeleton.tsx
│   │       │       │       └── Transition.tsx
│   │       │       ├── config
│   │       │       │   ├── images.ts
│   │       │       │   ├── routes.ts
│   │       │       │   └── site.ts
│   │       │       ├── content
│   │       │       │   ├── index.ts
│   │       │       │   ├── services.json
│   │       │       │   ├── team.json
│   │       │       │   ├── locations
│   │       │       │   │   └── index.ts
│   │       │       │   ├── projects
│   │       │       │   │   └── index.ts
│   │       │       │   ├── services
│   │       │       │   │   └── index.ts
│   │       │       │   ├── team
│   │       │       │   │   └── index.ts
│   │       │       │   └── testimonials
│   │       │       │       └── index.ts
│   │       │       ├── data
│   │       │       │   ├── navigation.ts
│   │       │       │   ├── projects.ts
│   │       │       │   ├── services.ts
│   │       │       │   ├── team.ts
│   │       │       │   └── testimonials.ts
│   │       │       ├── features
│   │       │       │   ├── home.tsx
│   │       │       │   ├── projects.tsx
│   │       │       │   ├── services.tsx
│   │       │       │   ├── team.tsx
│   │       │       │   └── testimonials.tsx
│   │       │       ├── lib
│   │       │       │   ├── constants.ts
│   │       │       │   ├── content.ts
│   │       │       │   ├── hooks.ts
│   │       │       │   ├── index.ts
│   │       │       │   ├── types.ts
│   │       │       │   ├── utils.ts
│   │       │       │   ├── config
│   │       │       │   │   ├── images.ts
│   │       │       │   │   ├── index.ts
│   │       │       │   │   ├── paths.ts
│   │       │       │   │   └── site.ts
│   │       │       │   ├── constants
│   │       │       │   │   └── index.ts
│   │       │       │   ├── context
│   │       │       │   │   └── AppContext.tsx
│   │       │       │   ├── hooks
│   │       │       │   │   ├── images.ts
│   │       │       │   │   ├── index.ts
│   │       │       │   │   ├── useAnalytics.ts
│   │       │       │   │   ├── useDebounce.ts
│   │       │       │   │   ├── useEventListener.ts
│   │       │       │   │   ├── useForm.ts
│   │       │       │   │   ├── useFormField.ts
│   │       │       │   │   ├── useIntersectionObserver.ts
│   │       │       │   │   ├── useLocalStorage.ts
│   │       │       │   │   └── useMediaQuery.ts
│   │       │       │   ├── types
│   │       │       │   │   ├── common.ts
│   │       │       │   │   ├── components.ts
│   │       │       │   │   ├── content.ts
│   │       │       │   │   └── index.ts
│   │       │       │   └── utils
│   │       │       │       ├── analytics.ts
│   │       │       │       ├── date.ts
│   │       │       │       ├── images.ts
│   │       │       │       ├── index.ts
│   │       │       │       ├── seo.ts
│   │       │       │       └── validation.ts
│   │       │       ├── pages
│   │       │       │   ├── AboutUs.tsx
│   │       │       │   ├── Home.tsx
│   │       │       │   ├── ProjectDetail.tsx
│   │       │       │   ├── Projects.tsx
│   │       │       │   ├── ServiceDetail.tsx
│   │       │       │   ├── Services.tsx
│   │       │       │   └── TestimonialsPage.tsx
│   │       │       ├── styles
│   │       │       │   ├── animations.css
│   │       │       │   ├── base.css
│   │       │       │   └── utilities.css
│   │       │       └── ui
│   │       │           └── index.tsx
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   ├── sitemap.xml
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── hero-residential-backyard.jpeg [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       └── Transition.tsx
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   └── testimonials.tsx
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── Home.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   └── TestimonialsPage.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           └── ui
│   │               └── index.tsx
│   ├── project-bolt-github-abr1xucc (5)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── TODO.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── techstack.md
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   ├── sitemap.xml
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── hero-residential-backyard.jpeg [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── forms.tsx
│   │           │   ├── index.ts
│   │           │   ├── projects.tsx
│   │           │   ├── sections.tsx
│   │           │   ├── services.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   ├── Navbar.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── index.ts
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── Home.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   └── TestimonialsPage.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           └── ui
│   │               └── index.tsx
│   ├── project-bolt-github-abr1xucc (6)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── TODO.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── philosophy.txt
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── techstack.md
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   ├── sitemap.xml
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── hero-residential-backyard.jpeg [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── SeasonalCTA.tsx
│   │           │       ├── ServiceAreaList.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── ProjectsCarousel.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── AverageRating.tsx
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── TestimonialsSection.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── Home.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   ├── TestimonialsPage.tsx
│   │           │   ├── about
│   │           │   │   └── index.tsx
│   │           │   ├── contact
│   │           │   │   └── index.tsx
│   │           │   ├── home
│   │           │   │   └── index.tsx
│   │           │   ├── projects
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── services
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   └── testimonials
│   │           │       └── index.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           ├── ui
│   │           │   └── index.tsx
│   │           └── utils
│   │               └── imageLoader.ts
│   ├── project-bolt-github-pchmwwml (10)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── data-structure.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── .vite
│   │       │   └── deps
│   │       │       ├── _metadata.json
│   │       │       ├── chunk-32E4H3EV.js
│   │       │       ├── chunk-32E4H3EV.js.map
│   │       │       ├── chunk-G3PMV62Z.js
│   │       │       ├── chunk-G3PMV62Z.js.map
│   │       │       ├── chunk-TNTPHDQH.js
│   │       │       ├── chunk-TNTPHDQH.js.map
│   │       │       ├── clsx.js
│   │       │       ├── clsx.js.map
│   │       │       ├── lucide-react.js
│   │       │       ├── lucide-react.js.map
│   │       │       ├── package.json
│   │       │       ├── react-dom_client.js
│   │       │       ├── react-dom_client.js.map
│   │       │       ├── react-router-dom.js
│   │       │       ├── react-router-dom.js.map
│   │       │       ├── react.js
│   │       │       ├── react.js.map
│   │       │       ├── tailwind-merge.js
│   │       │       └── tailwind-merge.js.map
│   │       ├── docs
│   │       │   ├── README.md
│   │       │   ├── architecture.md
│   │       │   ├── company-background.md
│   │       │   ├── components.md
│   │       │   ├── hooks-and-utils.md
│   │       │   ├── responsive-design.md
│   │       │   ├── seasonal-adaptation.md
│   │       │   ├── sitemap.md
│   │       │   ├── techstack.md
│   │       │   └── tldr.md
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── SeasonalCTA.tsx
│   │           │       ├── ServiceAreaList.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── home
│   │           │   │   ├── FilteredServicesSection.tsx
│   │           │   │   ├── SeasonalProjectsCarousel.tsx
│   │           │   │   ├── SeasonalServicesSection.tsx
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── ProjectsCarousel.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── AverageRating.tsx
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── TestimonialsSection.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── hooks
│   │           │   └── useData.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── api
│   │           │   │   └── index.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   ├── TestimonialsPage.tsx
│   │           │   ├── about
│   │           │   │   └── index.tsx
│   │           │   ├── contact
│   │           │   │   └── index.tsx
│   │           │   ├── home
│   │           │   │   └── index.tsx
│   │           │   ├── projects
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── services
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   └── testimonials
│   │           │       └── index.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           └── utils
│   │               └── imageLoader.ts
│   ├── project-bolt-github-pchmwwml (11)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── data-structure.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── docs
│   │       │   ├── README.md
│   │       │   ├── architecture.md
│   │       │   ├── components.md
│   │       │   ├── hooks-and-utils.md
│   │       │   ├── responsive-design.md
│   │       │   ├── seasonal-adaptation.md
│   │       │   ├── sitemap.md
│   │       │   ├── techstack.md
│   │       │   └── tldr.md
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── SeasonalCTA.tsx
│   │           │       ├── ServiceAreaList.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── home
│   │           │   │   ├── FilteredServicesSection.tsx
│   │           │   │   ├── SeasonalProjectsCarousel.tsx
│   │           │   │   ├── SeasonalServicesSection.tsx
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── ProjectsCarousel.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── AverageRating.tsx
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── TestimonialsSection.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── hooks
│   │           │   └── useData.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── api
│   │           │   │   └── index.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── about
│   │           │   │   └── index.tsx
│   │           │   ├── contact
│   │           │   │   └── index.tsx
│   │           │   ├── home
│   │           │   │   └── index.tsx
│   │           │   ├── projects
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── services
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   └── testimonials
│   │           │       └── index.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           └── utils
│   │               └── imageLoader.ts
│   ├── project-bolt-github-pchmwwml (6)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── .gitkeep copy
│   │       ├── README.md
│   │       ├── architecture.md
│   │       ├── components.md
│   │       ├── data-structure.md
│   │       ├── eslint.config.js
│   │       ├── hooks-and-utils.md
│   │       ├── index.html
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── postcss.config.js
│   │       ├── responsive-design.md
│   │       ├── seasonal-adaptation.md
│   │       ├── sitemap.md
│   │       ├── tailwind.config.js
│   │       ├── techstack.md
│   │       ├── tldr.md
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── vite.config.ts
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── hero-prosjekter.HEIC [-]
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── SeasonalCTA.tsx
│   │           │       ├── ServiceAreaList.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── home
│   │           │   │   ├── SeasonalProjectsCarousel.tsx
│   │           │   │   └── SeasonalServicesSection.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── ProjectsCarousel.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── AverageRating.tsx
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── TestimonialsSection.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── Home.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   ├── TestimonialsPage.tsx
│   │           │   ├── about
│   │           │   │   └── index.tsx
│   │           │   ├── contact
│   │           │   │   └── index.tsx
│   │           │   ├── home
│   │           │   │   └── index.tsx
│   │           │   ├── projects
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── services
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   └── testimonials
│   │           │       └── index.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           ├── ui
│   │           │   └── index.tsx
│   │           └── utils
│   │               └── imageLoader.ts
│   ├── project-bolt-github-pchmwwml (9)
│   │   └── project
│   │       ├── .gitignore
│   │       ├── .gitkeep
│   │       ├── README.md
│   │       ├── eslint.config.js
│   │       ├── index.html
│   │       ├── move_files.ps1
│   │       ├── package-lock.json
│   │       ├── package.json
│   │       ├── postcss.config.js
│   │       ├── tailwind.config.js
│   │       ├── tsconfig.app.json
│   │       ├── tsconfig.json
│   │       ├── tsconfig.node.json
│   │       ├── update_hero_imports.ps1
│   │       ├── update_imports.ps1
│   │       ├── vite.config.ts
│   │       ├── __discarded__
│   │       │   ├── components
│   │       │   │   ├── ServiceCard.tsx
│   │       │   │   ├── common
│   │       │   │   │   ├── Button.tsx
│   │       │   │   │   ├── Container.tsx
│   │       │   │   │   ├── Hero.tsx
│   │       │   │   │   ├── ImageGallery.tsx
│   │       │   │   │   ├── LocalExpertise.tsx
│   │       │   │   │   ├── LocalServiceArea.tsx
│   │       │   │   │   ├── Logo.tsx
│   │       │   │   │   ├── SeasonalCTA.tsx
│   │       │   │   │   ├── ServiceAreaList.tsx
│   │       │   │   │   └── WeatherAdaptedServices.tsx
│   │       │   │   ├── layout
│   │       │   │   │   └── Hero.tsx
│   │       │   │   └── shared
│   │       │   │       └── Layout
│   │       │   │           ├── Layout.tsx
│   │       │   │           └── index.ts
│   │       │   ├── features
│   │       │   │   ├── projects
│   │       │   │   │   └── ProjectCard.tsx
│   │       │   │   └── services
│   │       │   │       └── ServiceCard.tsx
│   │       │   └── pages
│   │       │       ├── about
│   │       │       │   └── index.tsx
│   │       │       ├── projects
│   │       │       │   └── index.tsx
│   │       │       └── services
│   │       │           └── index.tsx
│   │       ├── docs
│   │       │   ├── README.md
│   │       │   ├── architecture.md
│   │       │   ├── components.md
│   │       │   ├── data-structure.md
│   │       │   ├── hooks-and-utils.md
│   │       │   ├── responsive-design.md
│   │       │   ├── seasonal-adaptation.md
│   │       │   ├── sitemap.md
│   │       │   ├── techstack.md
│   │       │   └── tldr.md
│   │       ├── public
│   │       │   ├── robots.txt
│   │       │   ├── site.webmanifest
│   │       │   └── images
│   │       │       ├── metadata.json
│   │       │       ├── categorized
│   │       │       │   ├── belegg
│   │       │       │   │   ├── IMG_0035.webp
│   │       │       │   │   ├── IMG_0085.webp
│   │       │       │   │   ├── IMG_0121.webp
│   │       │       │   │   ├── IMG_0129.webp
│   │       │       │   │   ├── IMG_0208.webp
│   │       │       │   │   ├── IMG_0451.webp
│   │       │       │   │   ├── IMG_0453.webp
│   │       │       │   │   ├── IMG_0715.webp
│   │       │       │   │   ├── IMG_0717.webp
│   │       │       │   │   ├── IMG_1935.webp
│   │       │       │   │   ├── IMG_2941.webp
│   │       │       │   │   ├── IMG_3001.webp
│   │       │       │   │   ├── IMG_3021.webp
│   │       │       │   │   ├── IMG_3023.webp
│   │       │       │   │   ├── IMG_3033.webp
│   │       │       │   │   ├── IMG_3034.webp
│   │       │       │   │   ├── IMG_3035.webp
│   │       │       │   │   ├── IMG_3036.webp
│   │       │       │   │   ├── IMG_3037.webp
│   │       │       │   │   ├── IMG_3084.webp
│   │       │       │   │   ├── IMG_3133.webp
│   │       │       │   │   ├── IMG_4080.webp
│   │       │       │   │   ├── IMG_4305.webp
│   │       │       │   │   ├── IMG_4547.webp
│   │       │       │   │   ├── IMG_4586.webp
│   │       │       │   │   ├── IMG_4644.webp
│   │       │       │   │   ├── IMG_4996.webp
│   │       │       │   │   ├── IMG_4997.webp
│   │       │       │   │   ├── IMG_5278.webp
│   │       │       │   │   ├── IMG_5279.webp
│   │       │       │   │   └── IMG_5280.webp
│   │       │       │   ├── ferdigplen
│   │       │       │   │   ├── IMG_0071.webp
│   │       │       │   │   └── IMG_1912.webp
│   │       │       │   ├── hekk
│   │       │       │   │   ├── IMG_0167.webp
│   │       │       │   │   ├── IMG_1841.webp
│   │       │       │   │   ├── IMG_2370.webp
│   │       │       │   │   ├── IMG_2371.webp
│   │       │       │   │   ├── IMG_3077.webp
│   │       │       │   │   └── hekk_20.webp
│   │       │       │   ├── kantstein
│   │       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │       │   │   ├── IMG_0066.webp
│   │       │       │   │   ├── IMG_0364.webp
│   │       │       │   │   ├── IMG_0369.webp
│   │       │       │   │   ├── IMG_0427.webp
│   │       │       │   │   ├── IMG_0429.webp
│   │       │       │   │   ├── IMG_0445.webp
│   │       │       │   │   ├── IMG_0716.webp
│   │       │       │   │   ├── IMG_2955.webp
│   │       │       │   │   ├── IMG_4683.webp
│   │       │       │   │   └── IMG_4991.webp
│   │       │       │   ├── platting
│   │       │       │   │   ├── IMG_3251.webp
│   │       │       │   │   └── IMG_4188.webp
│   │       │       │   ├── stål
│   │       │       │   │   ├── IMG_0068.webp
│   │       │       │   │   ├── IMG_0069.webp
│   │       │       │   │   ├── IMG_1916.webp
│   │       │       │   │   ├── IMG_1917.webp
│   │       │       │   │   ├── IMG_1918.webp
│   │       │       │   │   ├── IMG_2441.webp
│   │       │       │   │   ├── IMG_3599.webp
│   │       │       │   │   ├── IMG_3602.webp
│   │       │       │   │   ├── IMG_3829.webp
│   │       │       │   │   ├── IMG_3832.webp
│   │       │       │   │   ├── IMG_3844.webp
│   │       │       │   │   ├── IMG_3845.webp
│   │       │       │   │   ├── IMG_3847.webp
│   │       │       │   │   ├── IMG_3848.webp
│   │       │       │   │   ├── IMG_3966.webp
│   │       │       │   │   ├── IMG_3969.webp
│   │       │       │   │   ├── IMG_4030.webp
│   │       │       │   │   ├── IMG_4083.webp
│   │       │       │   │   ├── IMG_4086.webp
│   │       │       │   │   ├── IMG_4536.webp
│   │       │       │   │   └── IMG_5346.webp
│   │       │       │   ├── støttemur
│   │       │       │   │   ├── IMG_0144.webp
│   │       │       │   │   ├── IMG_0318.webp
│   │       │       │   │   ├── IMG_0324.webp
│   │       │       │   │   ├── IMG_0325.webp
│   │       │       │   │   ├── IMG_0452.webp
│   │       │       │   │   ├── IMG_0932.webp
│   │       │       │   │   ├── IMG_0985.webp
│   │       │       │   │   ├── IMG_0986.webp
│   │       │       │   │   ├── IMG_0987.webp
│   │       │       │   │   ├── IMG_1132.webp
│   │       │       │   │   ├── IMG_1134.webp
│   │       │       │   │   ├── IMG_1140.webp
│   │       │       │   │   ├── IMG_2032.webp
│   │       │       │   │   ├── IMG_2083.webp
│   │       │       │   │   ├── IMG_2274.webp
│   │       │       │   │   ├── IMG_2522.webp
│   │       │       │   │   ├── IMG_2523.webp
│   │       │       │   │   ├── IMG_2855(1).webp
│   │       │       │   │   ├── IMG_2855.webp
│   │       │       │   │   ├── IMG_2859.webp
│   │       │       │   │   ├── IMG_2861.webp
│   │       │       │   │   ├── IMG_2891.webp
│   │       │       │   │   ├── IMG_2920.webp
│   │       │       │   │   ├── IMG_2921.webp
│   │       │       │   │   ├── IMG_2951.webp
│   │       │       │   │   ├── IMG_3007.webp
│   │       │       │   │   ├── IMG_3151.webp
│   │       │       │   │   ├── IMG_3269.webp
│   │       │       │   │   ├── IMG_3271.webp
│   │       │       │   │   ├── IMG_3369.webp
│   │       │       │   │   ├── IMG_4090.webp
│   │       │       │   │   ├── IMG_4150.webp
│   │       │       │   │   ├── IMG_4151.webp
│   │       │       │   │   ├── IMG_4153.webp
│   │       │       │   │   └── IMG_4154.webp
│   │       │       │   └── trapp-repo
│   │       │       │       ├── IMG_0295.webp
│   │       │       │       ├── IMG_0401.webp
│   │       │       │       ├── IMG_0448.webp
│   │       │       │       ├── IMG_0449.webp
│   │       │       │       ├── IMG_0450.webp
│   │       │       │       ├── IMG_1081.webp
│   │       │       │       ├── IMG_1735.webp
│   │       │       │       ├── IMG_1782.webp
│   │       │       │       ├── IMG_2095.webp
│   │       │       │       ├── IMG_2097.webp
│   │       │       │       ├── IMG_2807.webp
│   │       │       │       ├── IMG_3086.webp
│   │       │       │       ├── IMG_3132.webp
│   │       │       │       ├── IMG_3838.webp
│   │       │       │       ├── IMG_3939.webp
│   │       │       │       ├── IMG_4111.webp
│   │       │       │       ├── IMG_4516.webp
│   │       │       │       ├── IMG_4551.webp
│   │       │       │       ├── IMG_5317.webp
│   │       │       │       └── image4.webp
│   │       │       ├── site
│   │       │       │   ├── hero-corten-steel.webp
│   │       │       │   ├── hero-granite.webp
│   │       │       │   ├── hero-grass.webp
│   │       │       │   ├── hero-grass2.webp
│   │       │       │   ├── hero-illustrative.webp
│   │       │       │   ├── hero-main.webp
│   │       │       │   ├── hero-prosjekter.webp
│   │       │       │   └── hero-ringerike.webp
│   │       │       └── team
│   │       │           ├── firma.webp
│   │       │           ├── jan.webp
│   │       │           └── kim.webp
│   │       ├── scripts
│   │       │   └── cleanup.js
│   │       └── src
│   │           ├── App.tsx
│   │           ├── index.css
│   │           ├── index.html
│   │           ├── main.tsx
│   │           ├── src.md
│   │           ├── vite-env.d.ts
│   │           ├── app
│   │           │   ├── layout.tsx
│   │           │   └── page.tsx
│   │           ├── components
│   │           │   ├── ContactForm.tsx
│   │           │   ├── Navbar.tsx
│   │           │   ├── ServiceCard.tsx
│   │           │   ├── index.ts
│   │           │   ├── (sections)
│   │           │   │   ├── coverage-area
│   │           │   │   │   └── CoverageArea.tsx
│   │           │   │   ├── hero
│   │           │   │   │   └── Hero.tsx
│   │           │   │   ├── projects
│   │           │   │   │   ├── ProjectCard.tsx
│   │           │   │   │   └── ProjectsCarousel.tsx
│   │           │   │   ├── seasonal-planning
│   │           │   │   │   └── SeasonalPlanning.tsx
│   │           │   │   ├── services
│   │           │   │   │   ├── ServiceCard.tsx
│   │           │   │   │   ├── ServiceFeature.tsx
│   │           │   │   │   └── Services.tsx
│   │           │   │   └── testimonials
│   │           │   │       └── Testimonials.tsx
│   │           │   ├── common
│   │           │   │   ├── Button.tsx
│   │           │   │   ├── Container.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── ImageGallery.tsx
│   │           │   │   ├── LocalExpertise.tsx
│   │           │   │   ├── LocalServiceArea.tsx
│   │           │   │   ├── Logo.tsx
│   │           │   │   ├── SeasonalCTA.tsx
│   │           │   │   ├── ServiceAreaList.tsx
│   │           │   │   └── WeatherAdaptedServices.tsx
│   │           │   ├── contact
│   │           │   │   └── ContactForm.tsx
│   │           │   ├── layout
│   │           │   │   ├── Footer.tsx
│   │           │   │   ├── Header.tsx
│   │           │   │   ├── Hero.tsx
│   │           │   │   ├── Layout.tsx
│   │           │   │   ├── Meta.tsx
│   │           │   │   └── Navbar.tsx
│   │           │   ├── local
│   │           │   │   ├── SeasonalGuide.tsx
│   │           │   │   ├── ServiceAreaMap.tsx
│   │           │   │   └── WeatherNotice.tsx
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   └── ProjectGrid.tsx
│   │           │   ├── seo
│   │           │   │   └── TestimonialsSchema.tsx
│   │           │   ├── services
│   │           │   │   ├── Gallery.tsx
│   │           │   │   └── ServiceCard.tsx
│   │           │   ├── shared
│   │           │   │   ├── ErrorBoundary.tsx
│   │           │   │   ├── Elements
│   │           │   │   │   ├── Card.tsx
│   │           │   │   │   ├── Icon.tsx
│   │           │   │   │   ├── Image.tsx
│   │           │   │   │   ├── Link.tsx
│   │           │   │   │   ├── Loading.tsx
│   │           │   │   │   ├── index.ts
│   │           │   │   │   └── Form
│   │           │   │   │       ├── Input.tsx
│   │           │   │   │       ├── Select.tsx
│   │           │   │   │       ├── Textarea.tsx
│   │           │   │   │       └── index.ts
│   │           │   │   └── Layout
│   │           │   │       ├── Layout.tsx
│   │           │   │       └── index.ts
│   │           │   └── ui
│   │           │       ├── Button.tsx
│   │           │       ├── Container.tsx
│   │           │       ├── Hero.tsx
│   │           │       ├── Intersection.tsx
│   │           │       ├── Logo.tsx
│   │           │       ├── Notifications.tsx
│   │           │       ├── SeasonalCTA.tsx
│   │           │       ├── ServiceAreaList.tsx
│   │           │       ├── Skeleton.tsx
│   │           │       ├── Transition.tsx
│   │           │       └── index.ts
│   │           ├── config
│   │           │   ├── images.ts
│   │           │   ├── routes.ts
│   │           │   └── site.ts
│   │           ├── content
│   │           │   ├── index.ts
│   │           │   ├── services.json
│   │           │   ├── team.json
│   │           │   ├── locations
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   └── index.ts
│   │           │   ├── team
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       └── index.ts
│   │           ├── data
│   │           │   ├── navigation.ts
│   │           │   ├── projects.ts
│   │           │   ├── services.ts
│   │           │   ├── team.ts
│   │           │   └── testimonials.ts
│   │           ├── features
│   │           │   ├── home.tsx
│   │           │   ├── projects.tsx
│   │           │   ├── services.tsx
│   │           │   ├── team.tsx
│   │           │   ├── testimonials.tsx
│   │           │   ├── home
│   │           │   │   ├── FilteredServicesSection.tsx
│   │           │   │   ├── SeasonalProjectsCarousel.tsx
│   │           │   │   ├── SeasonalServicesSection.tsx
│   │           │   │   └── index.ts
│   │           │   ├── projects
│   │           │   │   ├── ProjectCard.tsx
│   │           │   │   ├── ProjectFilter.tsx
│   │           │   │   ├── ProjectGallery.tsx
│   │           │   │   ├── ProjectGrid.tsx
│   │           │   │   ├── ProjectsCarousel.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   ├── services
│   │           │   │   ├── ServiceCard.tsx
│   │           │   │   ├── ServiceFeature.tsx
│   │           │   │   ├── ServiceGrid.tsx
│   │           │   │   ├── data.ts
│   │           │   │   └── index.ts
│   │           │   └── testimonials
│   │           │       ├── AverageRating.tsx
│   │           │       ├── Testimonial.tsx
│   │           │       ├── TestimonialFilter.tsx
│   │           │       ├── TestimonialSlider.tsx
│   │           │       ├── TestimonialsSection.tsx
│   │           │       ├── data.ts
│   │           │       └── index.ts
│   │           ├── hooks
│   │           │   └── useData.ts
│   │           ├── lib
│   │           │   ├── constants.ts
│   │           │   ├── content.ts
│   │           │   ├── hooks.ts
│   │           │   ├── index.ts
│   │           │   ├── types.ts
│   │           │   ├── utils.ts
│   │           │   ├── api
│   │           │   │   └── index.ts
│   │           │   ├── config
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── paths.ts
│   │           │   │   └── site.ts
│   │           │   ├── constants
│   │           │   │   └── index.ts
│   │           │   ├── context
│   │           │   │   └── AppContext.tsx
│   │           │   ├── hooks
│   │           │   │   ├── images.ts
│   │           │   │   ├── index.ts
│   │           │   │   ├── useAnalytics.ts
│   │           │   │   ├── useDebounce.ts
│   │           │   │   ├── useEventListener.ts
│   │           │   │   ├── useForm.ts
│   │           │   │   ├── useFormField.ts
│   │           │   │   ├── useIntersectionObserver.ts
│   │           │   │   ├── useLocalStorage.ts
│   │           │   │   └── useMediaQuery.ts
│   │           │   ├── types
│   │           │   │   ├── common.ts
│   │           │   │   ├── components.ts
│   │           │   │   ├── content.ts
│   │           │   │   └── index.ts
│   │           │   └── utils
│   │           │       ├── analytics.ts
│   │           │       ├── date.ts
│   │           │       ├── images.ts
│   │           │       ├── index.ts
│   │           │       ├── seo.ts
│   │           │       └── validation.ts
│   │           ├── pages
│   │           │   ├── AboutUs.tsx
│   │           │   ├── Home.tsx
│   │           │   ├── ProjectDetail.tsx
│   │           │   ├── Projects.tsx
│   │           │   ├── ServiceDetail.tsx
│   │           │   ├── Services.tsx
│   │           │   ├── TestimonialsPage.tsx
│   │           │   ├── about
│   │           │   │   └── index.tsx
│   │           │   ├── contact
│   │           │   │   └── index.tsx
│   │           │   ├── home
│   │           │   │   └── index.tsx
│   │           │   ├── projects
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   ├── services
│   │           │   │   ├── detail.tsx
│   │           │   │   └── index.tsx
│   │           │   └── testimonials
│   │           │       └── index.tsx
│   │           ├── styles
│   │           │   ├── animations.css
│   │           │   ├── base.css
│   │           │   └── utilities.css
│   │           ├── ui
│   │           │   └── index.tsx
│   │           └── utils
│   │               └── imageLoader.ts
│   └── rl-website_bolt
│       ├── .cursorignore
│       ├── .cursorrules
│       ├── .gitignore
│       ├── .gitkeep
│       ├── README.md
│       ├── capture-website.js
│       ├── codebase-analysis.md
│       ├── component-tree.md
│       ├── eslint.config.js
│       ├── generate-screenshot-report.js
│       ├── index.html
│       ├── package-lock.json
│       ├── package.json
│       ├── postcss.config.js
│       ├── rendered_homepage.html
│       ├── screenshot-report.html
│       ├── styling-and-theming.md
│       ├── tailwind.config.js
│       ├── tsconfig.app.json
│       ├── tsconfig.json
│       ├── tsconfig.node.json
│       ├── vite.config.ts
│       ├── website-analysis-summary.md
│       ├── website-structure.md
│       ├── docs
│       │   ├── rl-website-01-company.md
│       │   ├── rl-website-02-requirements.md
│       │   ├── rl-website-03-uiuixdesign-small.md
│       │   ├── rl-website-04-uiuixdesign.md
│       │   ├── rl-website-05-architecture-small.md
│       │   ├── rl-website-06-architecture.md
│       │   └── rl-website-about.md
│       ├── public
│       │   ├── robots.txt
│       │   ├── site.webmanifest
│       │   ├── sitemap.xml
│       │   └── images
│       │       ├── metadata.json
│       │       ├── placeholder.html
│       │       ├── categorized
│       │       │   ├── belegg
│       │       │   │   ├── IMG_0035.webp
│       │       │   │   ├── IMG_0085.webp
│       │       │   │   ├── IMG_0121.webp
│       │       │   │   ├── IMG_0129.webp
│       │       │   │   ├── IMG_0208.webp
│       │       │   │   ├── IMG_0451.webp
│       │       │   │   ├── IMG_0453.webp
│       │       │   │   ├── IMG_0715.webp
│       │       │   │   ├── IMG_0717.webp
│       │       │   │   ├── IMG_1935.webp
│       │       │   │   ├── IMG_2941.webp
│       │       │   │   ├── IMG_3001.webp
│       │       │   │   ├── IMG_3021.webp
│       │       │   │   ├── IMG_3023.webp
│       │       │   │   ├── IMG_3033.webp
│       │       │   │   ├── IMG_3034.webp
│       │       │   │   ├── IMG_3035.webp
│       │       │   │   ├── IMG_3036.webp
│       │       │   │   ├── IMG_3037.webp
│       │       │   │   ├── IMG_3084.webp
│       │       │   │   ├── IMG_3133.webp
│       │       │   │   ├── IMG_4080.webp
│       │       │   │   ├── IMG_4305.webp
│       │       │   │   ├── IMG_4547.webp
│       │       │   │   ├── IMG_4586.webp
│       │       │   │   ├── IMG_4644.webp
│       │       │   │   ├── IMG_4996.webp
│       │       │   │   ├── IMG_4997.webp
│       │       │   │   ├── IMG_5278.webp
│       │       │   │   ├── IMG_5279.webp
│       │       │   │   └── IMG_5280.webp
│       │       │   ├── ferdigplen
│       │       │   │   ├── IMG_0071.webp
│       │       │   │   └── IMG_1912.webp
│       │       │   ├── hekk
│       │       │   │   ├── IMG_0167.webp
│       │       │   │   ├── IMG_1841.webp
│       │       │   │   ├── IMG_2370.webp
│       │       │   │   ├── IMG_2371.webp
│       │       │   │   ├── IMG_3077.webp
│       │       │   │   └── hekk_20.webp
│       │       │   ├── kantstein
│       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│       │       │   │   ├── IMG_0066.webp
│       │       │   │   ├── IMG_0364.webp
│       │       │   │   ├── IMG_0369.webp
│       │       │   │   ├── IMG_0427.webp
│       │       │   │   ├── IMG_0429.webp
│       │       │   │   ├── IMG_0445.webp
│       │       │   │   ├── IMG_0716.webp
│       │       │   │   ├── IMG_2955.webp
│       │       │   │   ├── IMG_4683.webp
│       │       │   │   └── IMG_4991.webp
│       │       │   ├── platting
│       │       │   │   ├── IMG_3251.webp
│       │       │   │   └── IMG_4188.webp
│       │       │   ├── stål
│       │       │   │   ├── IMG_0068.webp
│       │       │   │   ├── IMG_0069.webp
│       │       │   │   ├── IMG_1916.webp
│       │       │   │   ├── IMG_1917.webp
│       │       │   │   ├── IMG_1918.webp
│       │       │   │   ├── IMG_2441.webp
│       │       │   │   ├── IMG_3599.webp
│       │       │   │   ├── IMG_3602.webp
│       │       │   │   ├── IMG_3829.webp
│       │       │   │   ├── IMG_3832.webp
│       │       │   │   ├── IMG_3844.webp
│       │       │   │   ├── IMG_3845.webp
│       │       │   │   ├── IMG_3847.webp
│       │       │   │   ├── IMG_3848.webp
│       │       │   │   ├── IMG_3966.webp
│       │       │   │   ├── IMG_3969.webp
│       │       │   │   ├── IMG_4030.webp
│       │       │   │   ├── IMG_4083.webp
│       │       │   │   ├── IMG_4086.webp
│       │       │   │   ├── IMG_4536.webp
│       │       │   │   └── IMG_5346.webp
│       │       │   ├── støttemur
│       │       │   │   ├── IMG_0144.webp
│       │       │   │   ├── IMG_0318.webp
│       │       │   │   ├── IMG_0324.webp
│       │       │   │   ├── IMG_0325.webp
│       │       │   │   ├── IMG_0452.webp
│       │       │   │   ├── IMG_0932.webp
│       │       │   │   ├── IMG_0985.webp
│       │       │   │   ├── IMG_0986.webp
│       │       │   │   ├── IMG_0987.webp
│       │       │   │   ├── IMG_1132.webp
│       │       │   │   ├── IMG_1134.webp
│       │       │   │   ├── IMG_1140.webp
│       │       │   │   ├── IMG_2032.webp
│       │       │   │   ├── IMG_2083.webp
│       │       │   │   ├── IMG_2274.webp
│       │       │   │   ├── IMG_2522.webp
│       │       │   │   ├── IMG_2523.webp
│       │       │   │   ├── IMG_2855(1).webp
│       │       │   │   ├── IMG_2855.webp
│       │       │   │   ├── IMG_2859.webp
│       │       │   │   ├── IMG_2861.webp
│       │       │   │   ├── IMG_2891.webp
│       │       │   │   ├── IMG_2920.webp
│       │       │   │   ├── IMG_2921.webp
│       │       │   │   ├── IMG_2951.webp
│       │       │   │   ├── IMG_3007.webp
│       │       │   │   ├── IMG_3151.webp
│       │       │   │   ├── IMG_3269.webp
│       │       │   │   ├── IMG_3271.webp
│       │       │   │   ├── IMG_3369.webp
│       │       │   │   ├── IMG_4090.webp
│       │       │   │   ├── IMG_4150.webp
│       │       │   │   ├── IMG_4151.webp
│       │       │   │   ├── IMG_4153.webp
│       │       │   │   └── IMG_4154.webp
│       │       │   └── trapp-repo
│       │       │       ├── IMG_0295.webp
│       │       │       ├── IMG_0401.webp
│       │       │       ├── IMG_0448.webp
│       │       │       ├── IMG_0449.webp
│       │       │       ├── IMG_0450.webp
│       │       │       ├── IMG_1081.webp
│       │       │       ├── IMG_1735.webp
│       │       │       ├── IMG_1782.webp
│       │       │       ├── IMG_2095.webp
│       │       │       ├── IMG_2097.webp
│       │       │       ├── IMG_2807.webp
│       │       │       ├── IMG_3086.webp
│       │       │       ├── IMG_3132.webp
│       │       │       ├── IMG_3838.webp
│       │       │       ├── IMG_3939.webp
│       │       │       ├── IMG_4111.webp
│       │       │       ├── IMG_4516.webp
│       │       │       ├── IMG_4551.webp
│       │       │       ├── IMG_5317.webp
│       │       │       └── image4.webp
│       │       ├── raw
│       │       │   └── hero-prosjekter.HEIC [-]
│       │       ├── site
│       │       │   ├── hero-corten-steel.webp
│       │       │   ├── hero-granite.webp
│       │       │   ├── hero-grass.webp
│       │       │   ├── hero-grass2.webp
│       │       │   ├── hero-illustrative.webp
│       │       │   ├── hero-main.webp
│       │       │   ├── hero-prosjekter.webp
│       │       │   └── hero-ringerike.webp
│       │       └── team
│       │           ├── firma.webp
│       │           ├── jan.webp
│       │           └── kim.webp
│       ├── screenshots
│       │   ├── about-desktop.html
│       │   ├── about-desktop.png [-]
│       │   ├── about-mobile.html
│       │   ├── about-mobile.png [-]
│       │   ├── about-tablet.html
│       │   ├── about-tablet.png [-]
│       │   ├── contact-desktop.html
│       │   ├── contact-desktop.png [-]
│       │   ├── contact-mobile.html
│       │   ├── contact-mobile.png [-]
│       │   ├── contact-tablet.html
│       │   ├── contact-tablet.png [-]
│       │   ├── home-desktop.html
│       │   ├── home-desktop.png [-]
│       │   ├── home-mobile.html
│       │   ├── home-mobile.png [-]
│       │   ├── home-tablet.html
│       │   ├── home-tablet.png [-]
│       │   ├── projects-desktop.html
│       │   ├── projects-desktop.png [-]
│       │   ├── projects-mobile.html
│       │   ├── projects-mobile.png [-]
│       │   ├── projects-tablet.html
│       │   ├── projects-tablet.png [-]
│       │   ├── services-desktop.html
│       │   ├── services-desktop.png [-]
│       │   ├── services-mobile.html
│       │   ├── services-mobile.png [-]
│       │   ├── services-tablet.html
│       │   └── services-tablet.png [-]
│       ├── scripts
│       │   └── cleanup.js
│       └── src
│           ├── App.tsx
│           ├── index.css
│           ├── index.html
│           ├── main.tsx
│           ├── vite-env.d.ts
│           ├── components
│           │   ├── layout
│           │   │   ├── Footer.tsx
│           │   │   ├── Header.tsx
│           │   │   └── Meta.tsx
│           │   ├── projects
│           │   │   └── ProjectGallery.tsx
│           │   ├── seo
│           │   │   └── TestimonialsSchema.tsx
│           │   ├── testimonials
│           │   │   └── TestimonialComponents.tsx
│           │   └── ui
│           │       ├── Animation.tsx
│           │       ├── Button.tsx
│           │       ├── Container.tsx
│           │       ├── Hero.tsx
│           │       ├── Logo.tsx
│           │       ├── SeasonalCTA.tsx
│           │       ├── ServiceAreaList.tsx
│           │       └── index.ts
│           ├── content
│           │   ├── index.ts
│           │   ├── services
│           │   │   └── index.ts
│           │   ├── team
│           │   │   └── index.ts
│           │   └── testimonials
│           │       └── index.ts
│           ├── data
│           │   ├── projects.ts
│           │   ├── services.ts
│           │   └── testimonials.ts
│           ├── docs
│           │   └── SEO_USAGE.md
│           ├── features
│           │   ├── home
│           │   │   ├── FilteredServicesSection.tsx
│           │   │   └── SeasonalProjectsCarousel.tsx
│           │   ├── projects
│           │   │   └── ProjectCard.tsx
│           │   ├── services
│           │   │   ├── ServiceCard.tsx
│           │   │   └── data.ts
│           │   └── testimonials
│           │       ├── AverageRating.tsx
│           │       ├── Testimonial.tsx
│           │       ├── TestimonialExports.ts
│           │       ├── TestimonialFilter.tsx
│           │       ├── TestimonialSlider.tsx
│           │       └── TestimonialsSection.tsx
│           ├── hooks
│           │   ├── index.ts
│           │   ├── analytics
│           │   │   └── useAnalytics.ts
│           │   ├── data
│           │   │   ├── useData.ts
│           │   │   ├── useProjects.ts
│           │   │   └── useServices.ts
│           │   ├── form
│           │   │   └── useFormField.ts
│           │   └── ui
│           │       └── useMediaQuery.ts
│           ├── lib
│           │   ├── constants.ts
│           │   ├── utils.ts
│           │   ├── api
│           │   │   ├── apiUtils.ts
│           │   │   ├── index.ts
│           │   │   ├── projectsApi.ts
│           │   │   ├── servicesApi.ts
│           │   │   └── testimonialsApi.ts
│           │   ├── config
│           │   │   ├── images.ts
│           │   │   ├── index.ts
│           │   │   ├── paths.ts
│           │   │   └── site.ts
│           │   ├── hooks
│           │   │   ├── useAnalytics.ts
│           │   │   ├── useEventListener.ts
│           │   │   └── useMediaQuery.ts
│           │   ├── types
│           │   │   ├── content.ts
│           │   │   └── index.ts
│           │   └── utils
│           │       ├── analytics.ts
│           │       ├── dom.ts
│           │       ├── formatting.ts
│           │       ├── images.ts
│           │       ├── index.ts
│           │       ├── seasonal.ts
│           │       ├── seo.ts
│           │       └── validation.ts
│           ├── pages
│           │   ├── ProjectDetail.tsx
│           │   ├── Projects.tsx
│           │   ├── ServiceDetail.tsx
│           │   ├── Services.tsx
│           │   ├── TestimonialsPage.tsx
│           │   ├── about
│           │   │   └── index.tsx
│           │   ├── contact
│           │   │   └── index.tsx
│           │   ├── home
│           │   │   └── HomePage.tsx
│           │   ├── services
│           │   │   ├── detail.tsx
│           │   │   └── index.tsx
│           │   └── testimonials
│           │       └── index.tsx
│           ├── styles
│           │   ├── animations.css
│           │   ├── base.css
│           │   └── utilities.css
│           ├── types
│           │   ├── content.ts
│           │   └── index.ts
│           └── utils
│               ├── imageLoader.ts
│               ├── index.ts
│               ├── analytics
│               │   └── index.ts
│               ├── dom
│               │   └── index.ts
│               ├── formatting
│               │   └── index.ts
│               ├── images
│               │   └── index.ts
│               ├── seasonal
│               │   └── index.ts
│               ├── seo
│               │   └── index.ts
│               └── validation
│                   └── index.ts
├── rl-web-boldiy2
│   ├── .gitignore
│   ├── README.md
│   ├── capture-website.js
│   ├── eslint.config.js
│   ├── index.html
│   ├── package-lock.json
│   ├── package.json
│   ├── postcss.config.js
│   ├── tailwind.config.js
│   ├── tsconfig.app.json
│   ├── tsconfig.json
│   ├── tsconfig.node.json
│   ├── vite.config.ts
│   ├── public
│   │   ├── robots.txt
│   │   ├── site.webmanifest
│   │   ├── sitemap.xml
│   │   └── images
│   │       ├── metadata.json
│   │       ├── categorized
│   │       │   ├── hero-prosjekter.HEIC [-]
│   │       │   ├── belegg
│   │       │   │   ├── IMG_0035.webp
│   │       │   │   ├── IMG_0085.webp
│   │       │   │   ├── IMG_0121.webp
│   │       │   │   ├── IMG_0129.webp
│   │       │   │   ├── IMG_0208.webp
│   │       │   │   ├── IMG_0451.webp
│   │       │   │   ├── IMG_0453.webp
│   │       │   │   ├── IMG_0715.webp
│   │       │   │   ├── IMG_0717.webp
│   │       │   │   ├── IMG_1935.webp
│   │       │   │   ├── IMG_2941.webp
│   │       │   │   ├── IMG_3001.webp
│   │       │   │   ├── IMG_3021.webp
│   │       │   │   ├── IMG_3023.webp
│   │       │   │   ├── IMG_3033.webp
│   │       │   │   ├── IMG_3034.webp
│   │       │   │   ├── IMG_3035.webp
│   │       │   │   ├── IMG_3036.webp
│   │       │   │   ├── IMG_3037.webp
│   │       │   │   ├── IMG_3084.webp
│   │       │   │   ├── IMG_3133.webp
│   │       │   │   ├── IMG_4080.webp
│   │       │   │   ├── IMG_4305.webp
│   │       │   │   ├── IMG_4547.webp
│   │       │   │   ├── IMG_4586.webp
│   │       │   │   ├── IMG_4644.webp
│   │       │   │   ├── IMG_4996.webp
│   │       │   │   ├── IMG_4997.webp
│   │       │   │   ├── IMG_5278.webp
│   │       │   │   ├── IMG_5279.webp
│   │       │   │   └── IMG_5280.webp
│   │       │   ├── ferdigplen
│   │       │   │   ├── IMG_0071.webp
│   │       │   │   └── IMG_1912.webp
│   │       │   ├── hekk
│   │       │   │   ├── IMG_0167.webp
│   │       │   │   ├── IMG_1841.webp
│   │       │   │   ├── IMG_2370.webp
│   │       │   │   ├── IMG_2371.webp
│   │       │   │   ├── IMG_3077.webp
│   │       │   │   └── hekk_20.webp
│   │       │   ├── kantstein
│   │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │   │   ├── IMG_0066.webp
│   │       │   │   ├── IMG_0364.webp
│   │       │   │   ├── IMG_0369.webp
│   │       │   │   ├── IMG_0427.webp
│   │       │   │   ├── IMG_0429.webp
│   │       │   │   ├── IMG_0445.webp
│   │       │   │   ├── IMG_0716.webp
│   │       │   │   ├── IMG_2955.webp
│   │       │   │   ├── IMG_4683.webp
│   │       │   │   └── IMG_4991.webp
│   │       │   ├── platting
│   │       │   │   ├── IMG_3251.webp
│   │       │   │   └── IMG_4188.webp
│   │       │   ├── stål
│   │       │   │   ├── IMG_0068.webp
│   │       │   │   ├── IMG_0069.webp
│   │       │   │   ├── IMG_1916.webp
│   │       │   │   ├── IMG_1917.webp
│   │       │   │   ├── IMG_1918.webp
│   │       │   │   ├── IMG_2441.webp
│   │       │   │   ├── IMG_3599.webp
│   │       │   │   ├── IMG_3602.webp
│   │       │   │   ├── IMG_3829.webp
│   │       │   │   ├── IMG_3832.webp
│   │       │   │   ├── IMG_3844.webp
│   │       │   │   ├── IMG_3845.webp
│   │       │   │   ├── IMG_3847.webp
│   │       │   │   ├── IMG_3848.webp
│   │       │   │   ├── IMG_3966.webp
│   │       │   │   ├── IMG_3969.webp
│   │       │   │   ├── IMG_4030.webp
│   │       │   │   ├── IMG_4083.webp
│   │       │   │   ├── IMG_4086.webp
│   │       │   │   ├── IMG_4536.webp
│   │       │   │   └── IMG_5346.webp
│   │       │   ├── støttemur
│   │       │   │   ├── IMG_0144.webp
│   │       │   │   ├── IMG_0318.webp
│   │       │   │   ├── IMG_0324.webp
│   │       │   │   ├── IMG_0325.webp
│   │       │   │   ├── IMG_0452.webp
│   │       │   │   ├── IMG_0932.webp
│   │       │   │   ├── IMG_0985.webp
│   │       │   │   ├── IMG_0986.webp
│   │       │   │   ├── IMG_0987.webp
│   │       │   │   ├── IMG_1132.webp
│   │       │   │   ├── IMG_1134.webp
│   │       │   │   ├── IMG_1140.webp
│   │       │   │   ├── IMG_2032.webp
│   │       │   │   ├── IMG_2083.webp
│   │       │   │   ├── IMG_2274.webp
│   │       │   │   ├── IMG_2522.webp
│   │       │   │   ├── IMG_2523.webp
│   │       │   │   ├── IMG_2855(1).webp
│   │       │   │   ├── IMG_2855.webp
│   │       │   │   ├── IMG_2859.webp
│   │       │   │   ├── IMG_2861.webp
│   │       │   │   ├── IMG_2891.webp
│   │       │   │   ├── IMG_2920.webp
│   │       │   │   ├── IMG_2921.webp
│   │       │   │   ├── IMG_2951.webp
│   │       │   │   ├── IMG_3007.webp
│   │       │   │   ├── IMG_3151.webp
│   │       │   │   ├── IMG_3269.webp
│   │       │   │   ├── IMG_3271.webp
│   │       │   │   ├── IMG_3369.webp
│   │       │   │   ├── IMG_4090.webp
│   │       │   │   ├── IMG_4150.webp
│   │       │   │   ├── IMG_4151.webp
│   │       │   │   ├── IMG_4153.webp
│   │       │   │   └── IMG_4154.webp
│   │       │   └── trapp-repo
│   │       │       ├── IMG_0295.webp
│   │       │       ├── IMG_0401.webp
│   │       │       ├── IMG_0448.webp
│   │       │       ├── IMG_0449.webp
│   │       │       ├── IMG_0450.webp
│   │       │       ├── IMG_1081.webp
│   │       │       ├── IMG_1735.webp
│   │       │       ├── IMG_1782.webp
│   │       │       ├── IMG_2095.webp
│   │       │       ├── IMG_2097.webp
│   │       │       ├── IMG_2807.webp
│   │       │       ├── IMG_3086.webp
│   │       │       ├── IMG_3132.webp
│   │       │       ├── IMG_3838.webp
│   │       │       ├── IMG_3939.webp
│   │       │       ├── IMG_4111.webp
│   │       │       ├── IMG_4516.webp
│   │       │       ├── IMG_4551.webp
│   │       │       ├── IMG_5317.webp
│   │       │       └── image4.webp
│   │       ├── site
│   │       │   ├── hero-corten-steel.webp
│   │       │   ├── hero-granite.webp
│   │       │   ├── hero-grass.webp
│   │       │   ├── hero-grass2.webp
│   │       │   ├── hero-illustrative.webp
│   │       │   ├── hero-main.webp
│   │       │   ├── hero-prosjekter.webp
│   │       │   └── hero-ringerike.webp
│   │       └── team
│   │           ├── firma.webp
│   │           ├── jan.webp
│   │           └── kim.webp
│   └── src
│       ├── App.tsx
│       ├── index.css
│       ├── index.html
│       ├── main.tsx
│       ├── vite-env.d.ts
│       ├── app
│       │   ├── layout.tsx
│       │   └── page.tsx
│       ├── components
│       │   ├── ContactForm.tsx
│       │   ├── Navbar.tsx
│       │   ├── ServiceCard.tsx
│       │   ├── index.ts
│       │   ├── (sections)
│       │   │   ├── coverage-area
│       │   │   │   └── CoverageArea.tsx
│       │   │   ├── hero
│       │   │   │   └── Hero.tsx
│       │   │   ├── projects
│       │   │   │   ├── ProjectCard.tsx
│       │   │   │   └── ProjectsCarousel.tsx
│       │   │   ├── seasonal-planning
│       │   │   │   └── SeasonalPlanning.tsx
│       │   │   ├── services
│       │   │   │   ├── ServiceCard.tsx
│       │   │   │   ├── ServiceFeature.tsx
│       │   │   │   └── Services.tsx
│       │   │   └── testimonials
│       │   │       └── Testimonials.tsx
│       │   ├── common
│       │   │   ├── Button.tsx
│       │   │   ├── Container.tsx
│       │   │   ├── Hero.tsx
│       │   │   ├── ImageGallery.tsx
│       │   │   ├── LocalExpertise.tsx
│       │   │   ├── LocalServiceArea.tsx
│       │   │   ├── Logo.tsx
│       │   │   ├── SeasonalCTA.tsx
│       │   │   ├── ServiceAreaList.tsx
│       │   │   └── WeatherAdaptedServices.tsx
│       │   ├── contact
│       │   │   └── ContactForm.tsx
│       │   ├── layout
│       │   │   ├── Footer.tsx
│       │   │   ├── Header.tsx
│       │   │   ├── Hero.tsx
│       │   │   ├── Layout.tsx
│       │   │   ├── Meta.tsx
│       │   │   └── Navbar.tsx
│       │   ├── local
│       │   │   ├── SeasonalGuide.tsx
│       │   │   ├── ServiceAreaMap.tsx
│       │   │   └── WeatherNotice.tsx
│       │   ├── projects
│       │   │   ├── ProjectCard.tsx
│       │   │   ├── ProjectFilter.tsx
│       │   │   ├── ProjectGallery.tsx
│       │   │   └── ProjectGrid.tsx
│       │   ├── seo
│       │   │   └── TestimonialsSchema.tsx
│       │   ├── services
│       │   │   ├── Gallery.tsx
│       │   │   └── ServiceCard.tsx
│       │   ├── shared
│       │   │   ├── ErrorBoundary.tsx
│       │   │   ├── Elements
│       │   │   │   ├── Card.tsx
│       │   │   │   ├── Icon.tsx
│       │   │   │   ├── Image.tsx
│       │   │   │   ├── Link.tsx
│       │   │   │   ├── Loading.tsx
│       │   │   │   ├── index.ts
│       │   │   │   └── Form
│       │   │   │       ├── Input.tsx
│       │   │   │       ├── Select.tsx
│       │   │   │       ├── Textarea.tsx
│       │   │   │       └── index.ts
│       │   │   └── Layout
│       │   │       ├── Layout.tsx
│       │   │       └── index.ts
│       │   └── ui
│       │       ├── Button.tsx
│       │       ├── Container.tsx
│       │       ├── Hero.tsx
│       │       ├── Intersection.tsx
│       │       ├── Logo.tsx
│       │       ├── Notifications.tsx
│       │       ├── SeasonalCTA.tsx
│       │       ├── ServiceAreaList.tsx
│       │       ├── Skeleton.tsx
│       │       ├── Transition.tsx
│       │       └── index.ts
│       ├── config
│       │   ├── images.ts
│       │   ├── routes.ts
│       │   └── site.ts
│       ├── content
│       │   ├── index.ts
│       │   ├── services.json
│       │   ├── team.json
│       │   ├── locations
│       │   │   └── index.ts
│       │   ├── projects
│       │   │   └── index.ts
│       │   ├── services
│       │   │   └── index.ts
│       │   ├── team
│       │   │   └── index.ts
│       │   └── testimonials
│       │       └── index.ts
│       ├── data
│       │   ├── navigation.ts
│       │   ├── projects.ts
│       │   ├── services.ts
│       │   ├── team.ts
│       │   └── testimonials.ts
│       ├── features
│       │   ├── home.tsx
│       │   ├── projects.tsx
│       │   ├── services.tsx
│       │   ├── team.tsx
│       │   ├── testimonials.tsx
│       │   ├── home
│       │   │   ├── FilteredServicesSection.tsx
│       │   │   ├── SeasonalProjectsCarousel.tsx
│       │   │   ├── SeasonalServicesSection.tsx
│       │   │   └── index.ts
│       │   ├── projects
│       │   │   ├── ProjectCard.tsx
│       │   │   ├── ProjectFilter.tsx
│       │   │   ├── ProjectGallery.tsx
│       │   │   ├── ProjectGrid.tsx
│       │   │   ├── ProjectsCarousel.tsx
│       │   │   ├── data.ts
│       │   │   └── index.ts
│       │   ├── services
│       │   │   ├── ServiceCard.tsx
│       │   │   ├── ServiceFeature.tsx
│       │   │   ├── ServiceGrid.tsx
│       │   │   ├── data.ts
│       │   │   └── index.ts
│       │   └── testimonials
│       │       ├── AverageRating.tsx
│       │       ├── Testimonial.tsx
│       │       ├── TestimonialFilter.tsx
│       │       ├── TestimonialSlider.tsx
│       │       ├── TestimonialsSection.tsx
│       │       ├── data.ts
│       │       └── index.ts
│       ├── hooks
│       │   └── useData.ts
│       ├── lib
│       │   ├── constants.ts
│       │   ├── content.ts
│       │   ├── hooks.ts
│       │   ├── index.ts
│       │   ├── types.ts
│       │   ├── utils.ts
│       │   ├── api
│       │   │   └── index.ts
│       │   ├── config
│       │   │   ├── images.ts
│       │   │   ├── index.ts
│       │   │   ├── paths.ts
│       │   │   └── site.ts
│       │   ├── constants
│       │   │   ├── index.ts
│       │   │   └── locations.ts
│       │   ├── context
│       │   │   └── AppContext.tsx
│       │   ├── hooks
│       │   │   ├── images.ts
│       │   │   ├── index.ts
│       │   │   ├── useAnalytics.ts
│       │   │   ├── useDebounce.ts
│       │   │   ├── useEventListener.ts
│       │   │   ├── useForm.ts
│       │   │   ├── useFormField.ts
│       │   │   ├── useIntersectionObserver.ts
│       │   │   ├── useLocalStorage.ts
│       │   │   └── useMediaQuery.ts
│       │   ├── types
│       │   │   ├── common.ts
│       │   │   ├── components.ts
│       │   │   ├── content.ts
│       │   │   ├── index.ts
│       │   │   └── locations.ts
│       │   └── utils
│       │       ├── analytics.ts
│       │       ├── date.ts
│       │       ├── images.ts
│       │       ├── index.ts
│       │       ├── locations.ts
│       │       ├── seo.ts
│       │       └── validation.ts
│       ├── pages
│       │   ├── AboutUs.tsx
│       │   ├── ProjectDetail.tsx
│       │   ├── Projects.tsx
│       │   ├── ServiceDetail.tsx
│       │   ├── Services.tsx
│       │   ├── TestimonialsPage.tsx
│       │   ├── about
│       │   │   └── index.tsx
│       │   ├── contact
│       │   │   └── index.tsx
│       │   ├── home
│       │   │   └── index.tsx
│       │   ├── projects
│       │   │   ├── detail.tsx
│       │   │   └── index.tsx
│       │   ├── services
│       │   │   ├── detail.tsx
│       │   │   └── index.tsx
│       │   └── testimonials
│       │       └── index.tsx
│       ├── styles
│       │   ├── animations.css
│       │   ├── base.css
│       │   └── utilities.css
│       └── utils
│           └── imageLoader.ts
├── rl-website-dev
│   └── project
│       ├── .cursorignore
│       ├── .dependency-cruiser.cjs
│       ├── .gitignore
│       ├── .gitkeep
│       ├── README.md
│       ├── capture-website.js
│       ├── cleanup-duplicates.bat
│       ├── depcruise-config.cjs
│       ├── dependency-graph.svg [-]
│       ├── eslint.config.js
│       ├── index.html
│       ├── package-lock.json
│       ├── package.json
│       ├── package.json.bak
│       ├── postcss.config.js
│       ├── project.md
│       ├── reorganize-scripts.bat
│       ├── rl-website-initial-notes.md
│       ├── scripts-reorganization-summary.md
│       ├── tailwind.config.js
│       ├── tsconfig.app.json
│       ├── tsconfig.json
│       ├── tsconfig.node.json
│       ├── vite.config.ts
│       ├── .depcruise
│       │   ├── data
│       │   │   ├── d3-data.json
│       │   │   └── dependency-data.json
│       │   ├── graphs
│       │   │   ├── circular-graph.svg [-]
│       │   │   ├── clustered-graph.svg [-]
│       │   │   ├── dependency-graph.svg [-]
│       │   │   ├── hierarchical-graph.svg [-]
│       │   │   └── tech-filtered.svg [-]
│       │   └── interactive
│       │       ├── archi-interactive.html
│       │       ├── bubble-chart.html
│       │       ├── circle-packing.html
│       │       ├── d3-graph.html
│       │       ├── dependency-graph.html
│       │       ├── flow-diagram.html
│       │       ├── high-level-dependencies.html
│       │       └── validation.html
│       ├── docs
│       │   └── dependency-visualization.md
│       ├── public
│       │   ├── robots.txt
│       │   ├── site.webmanifest
│       │   ├── sitemap.xml
│       │   └── images
│       │       ├── metadata.json
│       │       ├── categorized
│       │       │   ├── hero-prosjekter.HEIC [-]
│       │       │   ├── belegg
│       │       │   │   ├── IMG_0035.webp
│       │       │   │   ├── IMG_0085.webp
│       │       │   │   ├── IMG_0121.webp
│       │       │   │   ├── IMG_0129.webp
│       │       │   │   ├── IMG_0208.webp
│       │       │   │   ├── IMG_0451.webp
│       │       │   │   ├── IMG_0453.webp
│       │       │   │   ├── IMG_0715.webp
│       │       │   │   ├── IMG_0717.webp
│       │       │   │   ├── IMG_1935.webp
│       │       │   │   ├── IMG_2941.webp
│       │       │   │   ├── IMG_3001.webp
│       │       │   │   ├── IMG_3021.webp
│       │       │   │   ├── IMG_3023.webp
│       │       │   │   ├── IMG_3033.webp
│       │       │   │   ├── IMG_3034.webp
│       │       │   │   ├── IMG_3035.webp
│       │       │   │   ├── IMG_3036.webp
│       │       │   │   ├── IMG_3037.webp
│       │       │   │   ├── IMG_3084.webp
│       │       │   │   ├── IMG_3133.webp
│       │       │   │   ├── IMG_4080.webp
│       │       │   │   ├── IMG_4305.webp
│       │       │   │   ├── IMG_4547.webp
│       │       │   │   ├── IMG_4586.webp
│       │       │   │   ├── IMG_4644.webp
│       │       │   │   ├── IMG_4996.webp
│       │       │   │   ├── IMG_4997.webp
│       │       │   │   ├── IMG_5278.webp
│       │       │   │   ├── IMG_5279.webp
│       │       │   │   └── IMG_5280.webp
│       │       │   ├── ferdigplen
│       │       │   │   ├── IMG_0071.webp
│       │       │   │   └── IMG_1912.webp
│       │       │   ├── hekk
│       │       │   │   ├── IMG_0167.webp
│       │       │   │   ├── IMG_1841.webp
│       │       │   │   ├── IMG_2370.webp
│       │       │   │   ├── IMG_2371.webp
│       │       │   │   ├── IMG_3077.webp
│       │       │   │   └── hekk_20.webp
│       │       │   ├── kantstein
│       │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│       │       │   │   ├── IMG_0066.webp
│       │       │   │   ├── IMG_0364.webp
│       │       │   │   ├── IMG_0369.webp
│       │       │   │   ├── IMG_0427.webp
│       │       │   │   ├── IMG_0429.webp
│       │       │   │   ├── IMG_0445.webp
│       │       │   │   ├── IMG_0716.webp
│       │       │   │   ├── IMG_2955.webp
│       │       │   │   ├── IMG_4683.webp
│       │       │   │   └── IMG_4991.webp
│       │       │   ├── platting
│       │       │   │   ├── IMG_3251.webp
│       │       │   │   └── IMG_4188.webp
│       │       │   ├── stål
│       │       │   │   ├── IMG_0068.webp
│       │       │   │   ├── IMG_0069.webp
│       │       │   │   ├── IMG_1916.webp
│       │       │   │   ├── IMG_1917.webp
│       │       │   │   ├── IMG_1918.webp
│       │       │   │   ├── IMG_2441.webp
│       │       │   │   ├── IMG_3599.webp
│       │       │   │   ├── IMG_3602.webp
│       │       │   │   ├── IMG_3829.webp
│       │       │   │   ├── IMG_3832.webp
│       │       │   │   ├── IMG_3844.webp
│       │       │   │   ├── IMG_3845.webp
│       │       │   │   ├── IMG_3847.webp
│       │       │   │   ├── IMG_3848.webp
│       │       │   │   ├── IMG_3966.webp
│       │       │   │   ├── IMG_3969.webp
│       │       │   │   ├── IMG_4030.webp
│       │       │   │   ├── IMG_4083.webp
│       │       │   │   ├── IMG_4086.webp
│       │       │   │   ├── IMG_4536.webp
│       │       │   │   └── IMG_5346.webp
│       │       │   ├── støttemur
│       │       │   │   ├── IMG_0144.webp
│       │       │   │   ├── IMG_0318.webp
│       │       │   │   ├── IMG_0324.webp
│       │       │   │   ├── IMG_0325.webp
│       │       │   │   ├── IMG_0452.webp
│       │       │   │   ├── IMG_0932.webp
│       │       │   │   ├── IMG_0985.webp
│       │       │   │   ├── IMG_0986.webp
│       │       │   │   ├── IMG_0987.webp
│       │       │   │   ├── IMG_1132.webp
│       │       │   │   ├── IMG_1134.webp
│       │       │   │   ├── IMG_1140.webp
│       │       │   │   ├── IMG_2032.webp
│       │       │   │   ├── IMG_2083.webp
│       │       │   │   ├── IMG_2274.webp
│       │       │   │   ├── IMG_2522.webp
│       │       │   │   ├── IMG_2523.webp
│       │       │   │   ├── IMG_2855(1).webp
│       │       │   │   ├── IMG_2855.webp
│       │       │   │   ├── IMG_2859.webp
│       │       │   │   ├── IMG_2861.webp
│       │       │   │   ├── IMG_2891.webp
│       │       │   │   ├── IMG_2920.webp
│       │       │   │   ├── IMG_2921.webp
│       │       │   │   ├── IMG_2951.webp
│       │       │   │   ├── IMG_3007.webp
│       │       │   │   ├── IMG_3151.webp
│       │       │   │   ├── IMG_3269.webp
│       │       │   │   ├── IMG_3271.webp
│       │       │   │   ├── IMG_3369.webp
│       │       │   │   ├── IMG_4090.webp
│       │       │   │   ├── IMG_4150.webp
│       │       │   │   ├── IMG_4151.webp
│       │       │   │   ├── IMG_4153.webp
│       │       │   │   └── IMG_4154.webp
│       │       │   └── trapp-repo
│       │       │       ├── IMG_0295.webp
│       │       │       ├── IMG_0401.webp
│       │       │       ├── IMG_0448.webp
│       │       │       ├── IMG_0449.webp
│       │       │       ├── IMG_0450.webp
│       │       │       ├── IMG_1081.webp
│       │       │       ├── IMG_1735.webp
│       │       │       ├── IMG_1782.webp
│       │       │       ├── IMG_2095.webp
│       │       │       ├── IMG_2097.webp
│       │       │       ├── IMG_2807.webp
│       │       │       ├── IMG_3086.webp
│       │       │       ├── IMG_3132.webp
│       │       │       ├── IMG_3838.webp
│       │       │       ├── IMG_3939.webp
│       │       │       ├── IMG_4111.webp
│       │       │       ├── IMG_4516.webp
│       │       │       ├── IMG_4551.webp
│       │       │       ├── IMG_5317.webp
│       │       │       └── image4.webp
│       │       ├── site
│       │       │   ├── hero-corten-steel.webp
│       │       │   ├── hero-granite.webp
│       │       │   ├── hero-grass.webp
│       │       │   ├── hero-grass2.webp
│       │       │   ├── hero-illustrative.webp
│       │       │   ├── hero-main.webp
│       │       │   ├── hero-prosjekter.webp
│       │       │   └── hero-ringerike.webp
│       │       └── team
│       │           ├── firma.webp
│       │           ├── jan.webp
│       │           └── kim.webp
│       ├── screenshots
│       │   ├── .gitignore
│       │   ├── .gitkeep
│       │   ├── README.md
│       │   ├── screenshot-report.html
│       │   └── latest
│       │       ├── .gitkeep
│       │       ├── desktop
│       │       │   ├── about-desktop.html
│       │       │   ├── about-desktop.png [-]
│       │       │   ├── contact-desktop.html
│       │       │   ├── contact-desktop.png [-]
│       │       │   ├── home-desktop.html
│       │       │   ├── home-desktop.png [-]
│       │       │   ├── projects-desktop.html
│       │       │   ├── projects-desktop.png [-]
│       │       │   ├── services-desktop.html
│       │       │   └── services-desktop.png [-]
│       │       ├── mobile
│       │       │   ├── about-mobile.html
│       │       │   ├── about-mobile.png [-]
│       │       │   ├── contact-mobile.html
│       │       │   ├── contact-mobile.png [-]
│       │       │   ├── home-mobile.html
│       │       │   ├── home-mobile.png [-]
│       │       │   ├── projects-mobile.html
│       │       │   ├── projects-mobile.png [-]
│       │       │   ├── services-mobile.html
│       │       │   └── services-mobile.png [-]
│       │       └── tablet
│       │           ├── about-tablet.html
│       │           ├── about-tablet.png [-]
│       │           ├── contact-tablet.html
│       │           ├── contact-tablet.png [-]
│       │           ├── home-tablet.html
│       │           ├── home-tablet.png [-]
│       │           ├── projects-tablet.html
│       │           ├── projects-tablet.png [-]
│       │           ├── services-tablet.html
│       │           └── services-tablet.png [-]
│       ├── scripts
│       │   ├── README.md
│       │   ├── run-capture.bat
│       │   ├── dependencies
│       │   │   ├── README.md
│       │   │   ├── dependency-manager.js
│       │   │   ├── visualize
│       │   │   ├── analyze
│       │   │   │   └── check-dependencies.js
│       │   │   └── utils
│       │   │       ├── check-graphviz.js
│       │   │       ├── cleanup-directory.js
│       │   │       ├── cleanup-redundant-files.js
│       │   │       ├── fix-missing-files.js
│       │   │       └── run-visualizations.js
│       │   ├── dev
│       │   │   ├── README.md
│       │   │   └── dev-with-snapshots.js
│       │   ├── screenshots
│       │   │   ├── README.md
│       │   │   ├── capture
│       │   │   └── manage
│       │   └── utils
│       │       ├── README.md
│       │       └── cleanup.js
│       └── src
│           ├── App.tsx
│           ├── index.css
│           ├── index.html
│           ├── main.tsx
│           ├── vite-env.d.ts
│           ├── components
│           │   ├── layout
│           │   │   ├── Footer.tsx
│           │   │   ├── Header.tsx
│           │   │   ├── Meta.tsx
│           │   │   └── Navbar.tsx
│           │   ├── projects
│           │   │   └── ProjectGallery.tsx
│           │   ├── seo
│           │   │   └── TestimonialsSchema.tsx
│           │   ├── shared
│           │   │   ├── Elements
│           │   │   │   ├── Card.tsx
│           │   │   │   ├── Icon.tsx
│           │   │   │   ├── Image.tsx
│           │   │   │   ├── Link.tsx
│           │   │   │   ├── Loading.tsx
│           │   │   │   └── Form
│           │   │   │       ├── Input.tsx
│           │   │   │       ├── Select.tsx
│           │   │   │       └── Textarea.tsx
│           │   │   └── Layout
│           │   │       └── Layout.tsx
│           │   ├── testimonials
│           │   │   └── index.tsx
│           │   └── ui
│           │       ├── Button.tsx
│           │       ├── Container.tsx
│           │       ├── Hero.tsx
│           │       ├── Intersection.tsx
│           │       ├── Logo.tsx
│           │       ├── Notifications.tsx
│           │       ├── SeasonalCTA.tsx
│           │       ├── ServiceAreaList.tsx
│           │       ├── Skeleton.tsx
│           │       ├── Transition.tsx
│           │       └── index.ts
│           ├── content
│           │   ├── index.ts
│           │   ├── services
│           │   │   └── index.ts
│           │   ├── team
│           │   │   └── index.ts
│           │   └── testimonials
│           │       └── index.ts
│           ├── data
│           │   ├── projects.ts
│           │   ├── services.ts
│           │   └── testimonials.ts
│           ├── docs
│           │   └── SEO_USAGE.md
│           ├── features
│           │   ├── testimonials.tsx
│           │   ├── home
│           │   │   ├── FilteredServicesSection.tsx
│           │   │   └── SeasonalProjectsCarousel.tsx
│           │   ├── projects
│           │   │   ├── ProjectCard.tsx
│           │   │   ├── ProjectFilter.tsx
│           │   │   ├── ProjectGrid.tsx
│           │   │   └── ProjectsCarousel.tsx
│           │   ├── services
│           │   │   ├── ServiceCard.tsx
│           │   │   ├── ServiceFeature.tsx
│           │   │   ├── ServiceGrid.tsx
│           │   │   └── data.ts
│           │   └── testimonials
│           │       ├── AverageRating.tsx
│           │       ├── Testimonial.tsx
│           │       ├── TestimonialFilter.tsx
│           │       ├── TestimonialSlider.tsx
│           │       ├── TestimonialsSection.tsx
│           │       └── data.ts
│           ├── hooks
│           │   └── useData.ts
│           ├── lib
│           │   ├── constants.ts
│           │   ├── hooks.ts
│           │   ├── utils.ts
│           │   ├── api
│           │   │   └── index.ts
│           │   ├── config
│           │   │   ├── images.ts
│           │   │   ├── index.ts
│           │   │   ├── paths.ts
│           │   │   └── site.ts
│           │   ├── context
│           │   │   └── AppContext.tsx
│           │   ├── hooks
│           │   │   ├── useAnalytics.ts
│           │   │   ├── useEventListener.ts
│           │   │   └── useMediaQuery.ts
│           │   ├── types
│           │   │   └── index.ts
│           │   └── utils
│           │       ├── analytics.ts
│           │       ├── dom.ts
│           │       ├── formatting.ts
│           │       ├── images.ts
│           │       ├── index.ts
│           │       ├── seasonal.ts
│           │       ├── seo.ts
│           │       └── validation.ts
│           ├── pages
│           │   ├── ProjectDetail.tsx
│           │   ├── Projects.tsx
│           │   ├── ServiceDetail.tsx
│           │   ├── Services.tsx
│           │   ├── TestimonialsPage.tsx
│           │   ├── about
│           │   │   └── index.tsx
│           │   ├── contact
│           │   │   └── index.tsx
│           │   ├── home
│           │   │   └── index.tsx
│           │   ├── services
│           │   │   ├── detail.tsx
│           │   │   └── index.tsx
│           │   └── testimonials
│           │       └── index.tsx
│           ├── styles
│           │   ├── animations.css
│           │   ├── base.css
│           │   └── utilities.css
│           ├── types
│           │   ├── content.ts
│           │   └── index.ts
│           └── utils
│               └── imageLoader.ts
├── rl-website_refactor
│   ├── .env
│   ├── .env.development
│   ├── .gitignore
│   ├── index.html
│   ├── package-lock.json
│   ├── package.json
│   ├── server.js
│   ├── vite.config.ts
│   ├── dependency-visualization
│   │   ├── dependency-data.json
│   │   └── index.html
│   ├── memory-bank
│   │   ├── activeContext.md
│   │   ├── productContext.md
│   │   ├── progress.md
│   │   ├── projectbrief.md
│   │   ├── systemPatterns.md
│   │   └── techContext.md
│   ├── public
│   │   ├── 404.html
│   │   └── dependency-visualization
│   │       └── dependency-data.json
│   ├── scripts
│   │   ├── analyze-dependencies.js
│   │   ├── setup-and-run.js
│   │   └── visualize-dependencies.js
│   └── src
│       ├── App.tsx
│       ├── index.css
│       ├── index.html
│       ├── main.tsx
│       ├── components
│       │   ├── DependencyVisualizer.tsx
│       │   └── DetailedDependencyView.tsx
│       └── pages
│           └── DependencyVisualizerPage.tsx
├── rl-website_web-new
│   ├── .cursorignore
│   ├── .cursorrules
│   ├── .env.development
│   ├── .env.local
│   ├── .env.production
│   ├── .gitignore
│   ├── README.md
│   ├── eslint.config.js
│   ├── index.html
│   ├── package-lock.json
│   ├── package.json
│   ├── postcss.config.js
│   ├── project_info.md
│   ├── rl-website_web-new.sublime-project
│   ├── rl-website_web-new.sublime-workspace [-]
│   ├── tailwind.config.js
│   ├── tsconfig.app.json
│   ├── tsconfig.json
│   ├── tsconfig.node.json
│   ├── vite.config.ts
│   ├── docs
│   │   ├── codebase-analysis.md
│   │   ├── component-tree.md
│   │   ├── styling-and-theming.md
│   │   ├── website-analysis-summary.md
│   │   └── website-structure.md
│   ├── memory-bank
│   │   ├── 0-distilledContext.md
│   │   ├── 1-projectbrief.md
│   │   ├── 2-productContext.md
│   │   ├── 3-systemPatterns.md
│   │   ├── 4-techContext.md
│   │   ├── 5-activeContext.md
│   │   ├── 6-progress.md
│   │   ├── 7-tasks.md
│   │   ├── file-structure-consolidation-plan.md
│   │   ├── file-structure-consolidation-progress.md
│   │   ├── project-consolidation-plan.md
│   │   └── publishing-preparations.md
│   ├── project_info
│   │   ├── project-direction.md
│   │   ├── project-intent.md
│   │   ├── project-notes.md
│   │   └── project_info.md
│   ├── public
│   │   ├── robots.txt
│   │   ├── site.webmanifest
│   │   ├── sitemap.xml
│   │   └── images
│   │       ├── metadata.json
│   │       ├── placeholder.html
│   │       ├── categorized
│   │       │   ├── belegg
│   │       │   │   ├── IMG_0035.webp
│   │       │   │   ├── IMG_0085.webp
│   │       │   │   ├── IMG_0121.webp
│   │       │   │   ├── IMG_0129.webp
│   │       │   │   ├── IMG_0208.webp
│   │       │   │   ├── IMG_0451.webp
│   │       │   │   ├── IMG_0453.webp
│   │       │   │   ├── IMG_0715.webp
│   │       │   │   ├── IMG_0717.webp
│   │       │   │   ├── IMG_1935.webp
│   │       │   │   ├── IMG_2941.webp
│   │       │   │   ├── IMG_3001.webp
│   │       │   │   ├── IMG_3021.webp
│   │       │   │   ├── IMG_3023.webp
│   │       │   │   ├── IMG_3033.webp
│   │       │   │   ├── IMG_3034.webp
│   │       │   │   ├── IMG_3035.webp
│   │       │   │   ├── IMG_3036.webp
│   │       │   │   ├── IMG_3037.webp
│   │       │   │   ├── IMG_3084.webp
│   │       │   │   ├── IMG_3133.webp
│   │       │   │   ├── IMG_4080.webp
│   │       │   │   ├── IMG_4305.webp
│   │       │   │   ├── IMG_4547.webp
│   │       │   │   ├── IMG_4586.webp
│   │       │   │   ├── IMG_4644.webp
│   │       │   │   ├── IMG_4996.webp
│   │       │   │   ├── IMG_4997.webp
│   │       │   │   ├── IMG_5278.webp
│   │       │   │   ├── IMG_5279.webp
│   │       │   │   └── IMG_5280.webp
│   │       │   ├── ferdigplen
│   │       │   │   ├── IMG_0071.webp
│   │       │   │   └── IMG_1912.webp
│   │       │   ├── hekk
│   │       │   │   ├── IMG_0167.webp
│   │       │   │   ├── IMG_1841.webp
│   │       │   │   ├── IMG_2370.webp
│   │       │   │   ├── IMG_2371.webp
│   │       │   │   ├── IMG_3077.webp
│   │       │   │   └── hekk_20.webp
│   │       │   ├── kantstein
│   │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │   │   ├── IMG_0066.webp
│   │       │   │   ├── IMG_0364.webp
│   │       │   │   ├── IMG_0369.webp
│   │       │   │   ├── IMG_0427.webp
│   │       │   │   ├── IMG_0429.webp
│   │       │   │   ├── IMG_0445.webp
│   │       │   │   ├── IMG_0716.webp
│   │       │   │   ├── IMG_2955.webp
│   │       │   │   ├── IMG_4683.webp
│   │       │   │   └── IMG_4991.webp
│   │       │   ├── platting
│   │       │   │   ├── IMG_3251.webp
│   │       │   │   └── IMG_4188.webp
│   │       │   ├── stål
│   │       │   │   ├── IMG_0068.webp
│   │       │   │   ├── IMG_0069.webp
│   │       │   │   ├── IMG_1916.webp
│   │       │   │   ├── IMG_1917.webp
│   │       │   │   ├── IMG_1918.webp
│   │       │   │   ├── IMG_2441.webp
│   │       │   │   ├── IMG_3599.webp
│   │       │   │   ├── IMG_3602.webp
│   │       │   │   ├── IMG_3829.webp
│   │       │   │   ├── IMG_3832.webp
│   │       │   │   ├── IMG_3844.webp
│   │       │   │   ├── IMG_3845.webp
│   │       │   │   ├── IMG_3847.webp
│   │       │   │   ├── IMG_3848.webp
│   │       │   │   ├── IMG_3966.webp
│   │       │   │   ├── IMG_3969.webp
│   │       │   │   ├── IMG_4030.webp
│   │       │   │   ├── IMG_4083.webp
│   │       │   │   ├── IMG_4086.webp
│   │       │   │   ├── IMG_4536.webp
│   │       │   │   └── IMG_5346.webp
│   │       │   ├── støttemur
│   │       │   │   ├── IMG_0144.webp
│   │       │   │   ├── IMG_0318.webp
│   │       │   │   ├── IMG_0324.webp
│   │       │   │   ├── IMG_0325.webp
│   │       │   │   ├── IMG_0452.webp
│   │       │   │   ├── IMG_0932.webp
│   │       │   │   ├── IMG_0985.webp
│   │       │   │   ├── IMG_0986.webp
│   │       │   │   ├── IMG_0987.webp
│   │       │   │   ├── IMG_1132.webp
│   │       │   │   ├── IMG_1134.webp
│   │       │   │   ├── IMG_1140.webp
│   │       │   │   ├── IMG_2032.webp
│   │       │   │   ├── IMG_2083.webp
│   │       │   │   ├── IMG_2274.webp
│   │       │   │   ├── IMG_2522.webp
│   │       │   │   ├── IMG_2523.webp
│   │       │   │   ├── IMG_2855(1).webp
│   │       │   │   ├── IMG_2855.webp
│   │       │   │   ├── IMG_2859.webp
│   │       │   │   ├── IMG_2861.webp
│   │       │   │   ├── IMG_2891.webp
│   │       │   │   ├── IMG_2920.webp
│   │       │   │   ├── IMG_2921.webp
│   │       │   │   ├── IMG_2951.webp
│   │       │   │   ├── IMG_3007.webp
│   │       │   │   ├── IMG_3151.webp
│   │       │   │   ├── IMG_3269.webp
│   │       │   │   ├── IMG_3271.webp
│   │       │   │   ├── IMG_3369.webp
│   │       │   │   ├── IMG_4090.webp
│   │       │   │   ├── IMG_4150.webp
│   │       │   │   ├── IMG_4151.webp
│   │       │   │   ├── IMG_4153.webp
│   │       │   │   └── IMG_4154.webp
│   │       │   └── trapp-repo
│   │       │       ├── IMG_0295.webp
│   │       │       ├── IMG_0401.webp
│   │       │       ├── IMG_0448.webp
│   │       │       ├── IMG_0449.webp
│   │       │       ├── IMG_0450.webp
│   │       │       ├── IMG_1081.webp
│   │       │       ├── IMG_1735.webp
│   │       │       ├── IMG_1782.webp
│   │       │       ├── IMG_2095.webp
│   │       │       ├── IMG_2097.webp
│   │       │       ├── IMG_2807.webp
│   │       │       ├── IMG_3086.webp
│   │       │       ├── IMG_3132.webp
│   │       │       ├── IMG_3838.webp
│   │       │       ├── IMG_3939.webp
│   │       │       ├── IMG_4111.webp
│   │       │       ├── IMG_4516.webp
│   │       │       ├── IMG_4551.webp
│   │       │       ├── IMG_5317.webp
│   │       │       └── image4.webp
│   │       ├── projects
│   │       │   ├── default.webp
│   │       │   ├── hoest
│   │       │   │   ├── kantstein-sundvollen
│   │       │   │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│   │       │   │   │   ├── IMG_0066.webp
│   │       │   │   │   ├── IMG_0364.webp
│   │       │   │   │   ├── IMG_0369.webp
│   │       │   │   │   ├── IMG_0427.webp
│   │       │   │   │   ├── IMG_0429.webp
│   │       │   │   │   ├── IMG_0445.webp
│   │       │   │   │   ├── IMG_0716.webp
│   │       │   │   │   ├── IMG_2955.webp
│   │       │   │   │   ├── IMG_4683.webp
│   │       │   │   │   └── IMG_4991.webp
│   │       │   │   ├── stottemur-honefoss
│   │       │   │   │   ├── IMG_0144.webp
│   │       │   │   │   ├── IMG_0318.webp
│   │       │   │   │   ├── IMG_0324.webp
│   │       │   │   │   ├── IMG_0325.webp
│   │       │   │   │   ├── IMG_0452.webp
│   │       │   │   │   ├── IMG_0932.webp
│   │       │   │   │   ├── IMG_0985.webp
│   │       │   │   │   ├── IMG_0986.webp
│   │       │   │   │   ├── IMG_0987.webp
│   │       │   │   │   ├── IMG_1132.webp
│   │       │   │   │   ├── IMG_1134.webp
│   │       │   │   │   ├── IMG_1140.webp
│   │       │   │   │   ├── IMG_2032.webp
│   │       │   │   │   ├── IMG_2083.webp
│   │       │   │   │   ├── IMG_2274.webp
│   │       │   │   │   ├── IMG_2522.webp
│   │       │   │   │   ├── IMG_2523.webp
│   │       │   │   │   ├── IMG_2855(1).webp
│   │       │   │   │   ├── IMG_2855.webp
│   │       │   │   │   ├── IMG_2859.webp
│   │       │   │   │   ├── IMG_2861.webp
│   │       │   │   │   ├── IMG_2891.webp
│   │       │   │   │   ├── IMG_2920.webp
│   │       │   │   │   ├── IMG_2921.webp
│   │       │   │   │   ├── IMG_2951.webp
│   │       │   │   │   ├── IMG_3007.webp
│   │       │   │   │   ├── IMG_3151.webp
│   │       │   │   │   ├── IMG_3269.webp
│   │       │   │   │   ├── IMG_3271.webp
│   │       │   │   │   ├── IMG_3369.webp
│   │       │   │   │   ├── IMG_4090.webp
│   │       │   │   │   ├── IMG_4150.webp
│   │       │   │   │   ├── IMG_4151.webp
│   │       │   │   │   ├── IMG_4153.webp
│   │       │   │   │   └── IMG_4154.webp
│   │       │   │   └── trapp-repo-jevnaker
│   │       │   │       └── IMG_4111.webp
│   │       │   ├── sommer
│   │       │   │   ├── belegningsstein-royse
│   │       │   │   │   └── IMG_3037.webp
│   │       │   │   ├── moderne-hage-royse
│   │       │   │   │   ├── IMG_0068.webp
│   │       │   │   │   ├── IMG_0069.webp
│   │       │   │   │   ├── IMG_1916.webp
│   │       │   │   │   ├── IMG_1917.webp
│   │       │   │   │   ├── IMG_1918.webp
│   │       │   │   │   ├── IMG_2441.webp
│   │       │   │   │   ├── IMG_3599.webp
│   │       │   │   │   ├── IMG_3602.webp
│   │       │   │   │   ├── IMG_3829.webp
│   │       │   │   │   ├── IMG_3832.webp
│   │       │   │   │   ├── IMG_3844.webp
│   │       │   │   │   ├── IMG_3845.webp
│   │       │   │   │   ├── IMG_3847.webp
│   │       │   │   │   ├── IMG_3848.webp
│   │       │   │   │   ├── IMG_3966.webp
│   │       │   │   │   ├── IMG_3969.webp
│   │       │   │   │   ├── IMG_4030.webp
│   │       │   │   │   ├── IMG_4083.webp
│   │       │   │   │   ├── IMG_4086.webp
│   │       │   │   │   ├── IMG_4536.webp
│   │       │   │   │   └── IMG_5346.webp
│   │       │   │   └── terrasse-hole
│   │       │   │       ├── IMG_3251.webp
│   │       │   │       └── IMG_4188.webp
│   │       │   └── vaar
│   │       │       ├── ferdigplen-vik
│   │       │       │   ├── IMG_0071.webp
│   │       │       │   └── IMG_1912.webp
│   │       │       └── hekk-hole
│   │       │           └── IMG_2370.webp
│   │       ├── raw
│   │       │   └── hero-prosjekter.HEIC [-]
│   │       ├── site
│   │       │   ├── hero-corten-steel.webp
│   │       │   ├── hero-granite.webp
│   │       │   ├── hero-grass.webp
│   │       │   ├── hero-grass2.webp
│   │       │   ├── hero-illustrative.webp
│   │       │   ├── hero-main.webp
│   │       │   ├── hero-prosjekter.webp
│   │       │   └── hero-ringerike.webp
│   │       └── team
│   │           ├── firma.webp
│   │           ├── jan.webp
│   │           └── kim.webp
│   ├── screenshots
│   │   └── 2025-03-19
│   │       ├── about-desktop.html
│   │       ├── about-desktop.png [-]
│   │       ├── contact-desktop.html
│   │       ├── contact-desktop.png [-]
│   │       ├── home-desktop.html
│   │       ├── home-desktop.png [-]
│   │       ├── projects-desktop.html
│   │       ├── projects-desktop.png [-]
│   │       ├── screenshot-report.html
│   │       ├── services-desktop.html
│   │       └── services-desktop.png [-]
│   ├── scripts
│   │   ├── screenshot
│   │   │   ├── README.md
│   │   │   ├── capture.js
│   │   │   ├── index.js
│   │   │   └── report.js
│   │   └── utils
│   │       └── cleanup.js
│   └── src
│       ├── App.tsx
│       ├── index.css
│       ├── index.html
│       ├── main.tsx
│       ├── vite-env.d.ts
│       ├── components
│       │   ├── layout
│       │   │   ├── Footer.tsx
│       │   │   ├── Header.tsx
│       │   │   └── Meta.tsx
│       │   ├── seo
│       │   │   └── TestimonialsSchema.tsx
│       │   ├── testimonials
│       │   │   └── TestimonialComponents.tsx
│       │   └── ui
│       │       ├── Animation.tsx
│       │       ├── Button.tsx
│       │       ├── Container.tsx
│       │       ├── Hero.tsx
│       │       ├── ImageGallery.tsx
│       │       ├── Logo.tsx
│       │       ├── SeasonalCTA.tsx
│       │       ├── ServiceAreaList.tsx
│       │       └── index.ts
│       ├── config
│       │   └── index.ts
│       ├── content
│       │   ├── index.ts
│       │   ├── services
│       │   │   └── index.ts
│       │   ├── team
│       │   │   └── index.ts
│       │   └── testimonials
│       │       └── index.ts
│       ├── data
│       │   ├── projects.ts
│       │   ├── services.ts
│       │   ├── testimonials.ts
│       │   ├── config
│       │   │   ├── index.ts
│       │   │   ├── projects-config.json
│       │   │   └── site-config.json
│       │   └── projects
│       │       ├── README.md
│       │       ├── debug.js
│       │       ├── index.ts
│       │       ├── migrate-images.js
│       │       ├── migrate-simple.js
│       │       ├── migrate.js
│       │       ├── migrate.ts
│       │       ├── projects.md
│       │       ├── utils.ts
│       │       ├── hoest
│       │       │   ├── kantstein-sundvollen
│       │       │   │   ├── index.json
│       │       │   │   └── images
│       │       │   │       ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
│       │       │   │       ├── IMG_0066.webp
│       │       │   │       ├── IMG_0364.webp
│       │       │   │       ├── IMG_0369.webp
│       │       │   │       ├── IMG_0427.webp
│       │       │   │       ├── IMG_0429.webp
│       │       │   │       ├── IMG_0445.webp
│       │       │   │       ├── IMG_0716.webp
│       │       │   │       ├── IMG_2955.webp
│       │       │   │       ├── IMG_4683.webp
│       │       │   │       └── IMG_4991.webp
│       │       │   ├── stottemur-honefoss
│       │       │   │   ├── index.json
│       │       │   │   └── images
│       │       │   │       ├── IMG_0144.webp
│       │       │   │       ├── IMG_0318.webp
│       │       │   │       ├── IMG_0324.webp
│       │       │   │       ├── IMG_0325.webp
│       │       │   │       ├── IMG_0452.webp
│       │       │   │       ├── IMG_0932.webp
│       │       │   │       ├── IMG_0985.webp
│       │       │   │       ├── IMG_0986.webp
│       │       │   │       ├── IMG_0987.webp
│       │       │   │       ├── IMG_1132.webp
│       │       │   │       ├── IMG_1134.webp
│       │       │   │       ├── IMG_1140.webp
│       │       │   │       ├── IMG_2032.webp
│       │       │   │       ├── IMG_2083.webp
│       │       │   │       ├── IMG_2274.webp
│       │       │   │       ├── IMG_2522.webp
│       │       │   │       ├── IMG_2523.webp
│       │       │   │       ├── IMG_2855(1).webp
│       │       │   │       ├── IMG_2855.webp
│       │       │   │       ├── IMG_2859.webp
│       │       │   │       ├── IMG_2861.webp
│       │       │   │       ├── IMG_2891.webp
│       │       │   │       ├── IMG_2920.webp
│       │       │   │       ├── IMG_2921.webp
│       │       │   │       ├── IMG_2951.webp
│       │       │   │       ├── IMG_3007.webp
│       │       │   │       ├── IMG_3151.webp
│       │       │   │       ├── IMG_3269.webp
│       │       │   │       ├── IMG_3271.webp
│       │       │   │       ├── IMG_3369.webp
│       │       │   │       ├── IMG_4090.webp
│       │       │   │       ├── IMG_4150.webp
│       │       │   │       ├── IMG_4151.webp
│       │       │   │       ├── IMG_4153.webp
│       │       │   │       └── IMG_4154.webp
│       │       │   └── trapp-repo-jevnaker
│       │       │       ├── index.json
│       │       │       └── images
│       │       │           └── IMG_4111.webp
│       │       ├── sommer
│       │       │   ├── belegningsstein-royse
│       │       │   │   ├── index.json
│       │       │   │   └── images
│       │       │   │       └── IMG_3037.webp
│       │       │   ├── moderne-hage-royse
│       │       │   │   ├── index.json
│       │       │   │   └── images
│       │       │   │       ├── IMG_0068.webp
│       │       │   │       ├── IMG_0069.webp
│       │       │   │       ├── IMG_1916.webp
│       │       │   │       ├── IMG_1917.webp
│       │       │   │       ├── IMG_1918.webp
│       │       │   │       ├── IMG_2441.webp
│       │       │   │       ├── IMG_3599.webp
│       │       │   │       ├── IMG_3602.webp
│       │       │   │       ├── IMG_3829.webp
│       │       │   │       ├── IMG_3832.webp
│       │       │   │       ├── IMG_3844.webp
│       │       │   │       ├── IMG_3845.webp
│       │       │   │       ├── IMG_3847.webp
│       │       │   │       ├── IMG_3848.webp
│       │       │   │       ├── IMG_3966.webp
│       │       │   │       ├── IMG_3969.webp
│       │       │   │       ├── IMG_4030.webp
│       │       │   │       ├── IMG_4083.webp
│       │       │   │       ├── IMG_4086.webp
│       │       │   │       ├── IMG_4536.webp
│       │       │   │       └── IMG_5346.webp
│       │       │   └── terrasse-hole
│       │       │       ├── index.json
│       │       │       └── images
│       │       │           ├── IMG_3251.webp
│       │       │           └── IMG_4188.webp
│       │       └── vaar
│       │           ├── ferdigplen-vik
│       │           │   ├── index.json
│       │           │   └── images
│       │           │       ├── IMG_0071.webp
│       │           │       └── IMG_1912.webp
│       │           └── hekk-hole
│       │               ├── index.json
│       │               └── images
│       │                   └── IMG_2370.webp
│       ├── docs
│       │   └── SEO_USAGE.md
│       ├── features
│       │   ├── home
│       │   │   └── FilteredServicesSection.tsx
│       │   ├── projects
│       │   │   ├── ProjectCard.tsx
│       │   │   ├── ProjectGallery.tsx
│       │   │   ├── SeasonalProjectsCarousel.tsx
│       │   │   ├── index.ts
│       │   │   ├── types.ts
│       │   │   └── utils.ts
│       │   ├── services
│       │   │   ├── ServiceCard.tsx
│       │   │   └── data.ts
│       │   └── testimonials
│       │       ├── AverageRating.tsx
│       │       ├── Testimonial.tsx
│       │       ├── TestimonialExports.ts
│       │       ├── TestimonialFilter.tsx
│       │       ├── TestimonialSlider.tsx
│       │       └── TestimonialsSection.tsx
│       ├── hooks
│       │   ├── index.ts
│       │   ├── analytics
│       │   │   └── useAnalytics.ts
│       │   ├── data
│       │   │   ├── useData.ts
│       │   │   ├── useImages.ts
│       │   │   ├── useProjects.ts
│       │   │   └── useServices.ts
│       │   ├── form
│       │   │   └── useFormField.ts
│       │   └── ui
│       │       ├── useEventListener.ts
│       │       └── useMediaQuery.ts
│       ├── lib
│       │   ├── constants.ts
│       │   ├── utils.ts
│       │   ├── api
│       │   │   ├── apiUtils.ts
│       │   │   ├── index.ts
│       │   │   ├── projectsApi.ts
│       │   │   ├── servicesApi.ts
│       │   │   └── testimonialsApi.ts
│       │   ├── config
│       │   │   ├── images.ts
│       │   │   ├── index.ts
│       │   │   ├── paths.ts
│       │   │   └── site.ts
│       │   ├── hooks
│       │   │   ├── useAnalytics.ts
│       │   │   ├── useEventListener.ts
│       │   │   └── useMediaQuery.ts
│       │   ├── types
│       │   │   ├── content.ts
│       │   │   └── index.ts
│       │   └── utils
│       │       ├── analytics.ts
│       │       ├── dom.ts
│       │       ├── dynamicImages.ts
│       │       ├── formatting.ts
│       │       ├── images.ts
│       │       ├── index.ts
│       │       ├── seasonal.ts
│       │       ├── seo.ts
│       │       └── validation.ts
│       ├── pages
│       │   ├── Debug.tsx
│       │   ├── ProjectDetail.tsx
│       │   ├── Projects.tsx
│       │   ├── ServiceDetail.tsx
│       │   ├── Services.tsx
│       │   ├── TestimonialsPage.tsx
│       │   ├── about
│       │   │   └── index.tsx
│       │   ├── contact
│       │   │   └── index.tsx
│       │   ├── home
│       │   │   └── HomePage.tsx
│       │   ├── services
│       │   │   ├── detail.tsx
│       │   │   └── index.tsx
│       │   └── testimonials
│       │       └── index.tsx
│       ├── styles
│       │   ├── animations.css
│       │   ├── base.css
│       │   └── utilities.css
│       ├── types
│       │   ├── components.ts
│       │   ├── content.ts
│       │   ├── global.d.ts
│       │   └── index.ts
│       └── utils
│           ├── imageLoader.ts
│           ├── index.ts
│           ├── analytics
│           │   └── index.ts
│           ├── dom
│           │   └── index.ts
│           ├── formatting
│           │   └── index.ts
│           ├── images
│           │   └── index.ts
│           ├── seasonal
│           │   └── index.ts
│           ├── seo
│           │   └── index.ts
│           └── validation
│               └── index.ts
└── rl-website_web.project.backup
    ├── .cursorignore
    ├── .dependency-cruiser.cjs
    ├── .gitignore
    ├── .gitkeep
    ├── README.md
    ├── capture-website.js
    ├── cleanup-duplicates.bat
    ├── depcruise-config.cjs
    ├── dependency-graph.svg [-]
    ├── eslint.config.js
    ├── index.html
    ├── package-lock.json
    ├── package.json
    ├── package.json.bak
    ├── postcss.config.js
    ├── project.md
    ├── reorganize-scripts.bat
    ├── rl-website-initial-notes.md
    ├── scripts-reorganization-summary.md
    ├── tailwind.config.js
    ├── tsconfig.app.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── vite.config.ts
    ├── .depcruise
    │   ├── data
    │   │   ├── d3-data.json
    │   │   └── dependency-data.json
    │   ├── graphs
    │   │   ├── circular-graph.svg [-]
    │   │   ├── clustered-graph.svg [-]
    │   │   ├── dependency-graph.svg [-]
    │   │   ├── hierarchical-graph.svg [-]
    │   │   └── tech-filtered.svg [-]
    │   └── interactive
    │       ├── archi-interactive.html
    │       ├── bubble-chart.html
    │       ├── circle-packing.html
    │       ├── d3-graph.html
    │       ├── dependency-graph.html
    │       ├── flow-diagram.html
    │       ├── high-level-dependencies.html
    │       └── validation.html
    ├── docs
    │   └── dependency-visualization.md
    ├── public
    │   ├── robots.txt
    │   ├── site.webmanifest
    │   ├── sitemap.xml
    │   └── images
    │       ├── metadata.json
    │       ├── categorized
    │       │   ├── hero-prosjekter.HEIC [-]
    │       │   ├── belegg
    │       │   │   ├── IMG_0035.webp
    │       │   │   ├── IMG_0085.webp
    │       │   │   ├── IMG_0121.webp
    │       │   │   ├── IMG_0129.webp
    │       │   │   ├── IMG_0208.webp
    │       │   │   ├── IMG_0451.webp
    │       │   │   ├── IMG_0453.webp
    │       │   │   ├── IMG_0715.webp
    │       │   │   ├── IMG_0717.webp
    │       │   │   ├── IMG_1935.webp
    │       │   │   ├── IMG_2941.webp
    │       │   │   ├── IMG_3001.webp
    │       │   │   ├── IMG_3021.webp
    │       │   │   ├── IMG_3023.webp
    │       │   │   ├── IMG_3033.webp
    │       │   │   ├── IMG_3034.webp
    │       │   │   ├── IMG_3035.webp
    │       │   │   ├── IMG_3036.webp
    │       │   │   ├── IMG_3037.webp
    │       │   │   ├── IMG_3084.webp
    │       │   │   ├── IMG_3133.webp
    │       │   │   ├── IMG_4080.webp
    │       │   │   ├── IMG_4305.webp
    │       │   │   ├── IMG_4547.webp
    │       │   │   ├── IMG_4586.webp
    │       │   │   ├── IMG_4644.webp
    │       │   │   ├── IMG_4996.webp
    │       │   │   ├── IMG_4997.webp
    │       │   │   ├── IMG_5278.webp
    │       │   │   ├── IMG_5279.webp
    │       │   │   └── IMG_5280.webp
    │       │   ├── ferdigplen
    │       │   │   ├── IMG_0071.webp
    │       │   │   └── IMG_1912.webp
    │       │   ├── hekk
    │       │   │   ├── IMG_0167.webp
    │       │   │   ├── IMG_1841.webp
    │       │   │   ├── IMG_2370.webp
    │       │   │   ├── IMG_2371.webp
    │       │   │   ├── IMG_3077.webp
    │       │   │   └── hekk_20.webp
    │       │   ├── kantstein
    │       │   │   ├── 71431181346__D94EC6CF-B1F5-42DF-AC20-F180C13800C7.webp
    │       │   │   ├── IMG_0066.webp
    │       │   │   ├── IMG_0364.webp
    │       │   │   ├── IMG_0369.webp
    │       │   │   ├── IMG_0427.webp
    │       │   │   ├── IMG_0429.webp
    │       │   │   ├── IMG_0445.webp
    │       │   │   ├── IMG_0716.webp
    │       │   │   ├── IMG_2955.webp
    │       │   │   ├── IMG_4683.webp
    │       │   │   └── IMG_4991.webp
    │       │   ├── platting
    │       │   │   ├── IMG_3251.webp
    │       │   │   └── IMG_4188.webp
    │       │   ├── stål
    │       │   │   ├── IMG_0068.webp
    │       │   │   ├── IMG_0069.webp
    │       │   │   ├── IMG_1916.webp
    │       │   │   ├── IMG_1917.webp
    │       │   │   ├── IMG_1918.webp
    │       │   │   ├── IMG_2441.webp
    │       │   │   ├── IMG_3599.webp
    │       │   │   ├── IMG_3602.webp
    │       │   │   ├── IMG_3829.webp
    │       │   │   ├── IMG_3832.webp
    │       │   │   ├── IMG_3844.webp
    │       │   │   ├── IMG_3845.webp
    │       │   │   ├── IMG_3847.webp
    │       │   │   ├── IMG_3848.webp
    │       │   │   ├── IMG_3966.webp
    │       │   │   ├── IMG_3969.webp
    │       │   │   ├── IMG_4030.webp
    │       │   │   ├── IMG_4083.webp
    │       │   │   ├── IMG_4086.webp
    │       │   │   ├── IMG_4536.webp
    │       │   │   └── IMG_5346.webp
    │       │   ├── støttemur
    │       │   │   ├── IMG_0144.webp
    │       │   │   ├── IMG_0318.webp
    │       │   │   ├── IMG_0324.webp
    │       │   │   ├── IMG_0325.webp
    │       │   │   ├── IMG_0452.webp
    │       │   │   ├── IMG_0932.webp
    │       │   │   ├── IMG_0985.webp
    │       │   │   ├── IMG_0986.webp
    │       │   │   ├── IMG_0987.webp
    │       │   │   ├── IMG_1132.webp
    │       │   │   ├── IMG_1134.webp
    │       │   │   ├── IMG_1140.webp
    │       │   │   ├── IMG_2032.webp
    │       │   │   ├── IMG_2083.webp
    │       │   │   ├── IMG_2274.webp
    │       │   │   ├── IMG_2522.webp
    │       │   │   ├── IMG_2523.webp
    │       │   │   ├── IMG_2855(1).webp
    │       │   │   ├── IMG_2855.webp
    │       │   │   ├── IMG_2859.webp
    │       │   │   ├── IMG_2861.webp
    │       │   │   ├── IMG_2891.webp
    │       │   │   ├── IMG_2920.webp
    │       │   │   ├── IMG_2921.webp
    │       │   │   ├── IMG_2951.webp
    │       │   │   ├── IMG_3007.webp
    │       │   │   ├── IMG_3151.webp
    │       │   │   ├── IMG_3269.webp
    │       │   │   ├── IMG_3271.webp
    │       │   │   ├── IMG_3369.webp
    │       │   │   ├── IMG_4090.webp
    │       │   │   ├── IMG_4150.webp
    │       │   │   ├── IMG_4151.webp
    │       │   │   ├── IMG_4153.webp
    │       │   │   └── IMG_4154.webp
    │       │   └── trapp-repo
    │       │       ├── IMG_0295.webp
    │       │       ├── IMG_0401.webp
    │       │       ├── IMG_0448.webp
    │       │       ├── IMG_0449.webp
    │       │       ├── IMG_0450.webp
    │       │       ├── IMG_1081.webp
    │       │       ├── IMG_1735.webp
    │       │       ├── IMG_1782.webp
    │       │       ├── IMG_2095.webp
    │       │       ├── IMG_2097.webp
    │       │       ├── IMG_2807.webp
    │       │       ├── IMG_3086.webp
    │       │       ├── IMG_3132.webp
    │       │       ├── IMG_3838.webp
    │       │       ├── IMG_3939.webp
    │       │       ├── IMG_4111.webp
    │       │       ├── IMG_4516.webp
    │       │       ├── IMG_4551.webp
    │       │       ├── IMG_5317.webp
    │       │       └── image4.webp
    │       ├── site
    │       │   ├── hero-corten-steel.webp
    │       │   ├── hero-granite.webp
    │       │   ├── hero-grass.webp
    │       │   ├── hero-grass2.webp
    │       │   ├── hero-illustrative.webp
    │       │   ├── hero-main.webp
    │       │   ├── hero-prosjekter.webp
    │       │   └── hero-ringerike.webp
    │       └── team
    │           ├── firma.webp
    │           ├── jan.webp
    │           └── kim.webp
    ├── screenshots
    │   ├── .gitignore
    │   ├── .gitkeep
    │   ├── README.md
    │   ├── screenshot-report.html
    │   └── latest
    │       ├── .gitkeep
    │       ├── desktop
    │       │   ├── about-desktop.html
    │       │   ├── about-desktop.png [-]
    │       │   ├── contact-desktop.html
    │       │   ├── contact-desktop.png [-]
    │       │   ├── home-desktop.html
    │       │   ├── home-desktop.png [-]
    │       │   ├── projects-desktop.html
    │       │   ├── projects-desktop.png [-]
    │       │   ├── services-desktop.html
    │       │   └── services-desktop.png [-]
    │       ├── mobile
    │       │   ├── about-mobile.html
    │       │   ├── about-mobile.png [-]
    │       │   ├── contact-mobile.html
    │       │   ├── contact-mobile.png [-]
    │       │   ├── home-mobile.html
    │       │   ├── home-mobile.png [-]
    │       │   ├── projects-mobile.html
    │       │   ├── projects-mobile.png [-]
    │       │   ├── services-mobile.html
    │       │   └── services-mobile.png [-]
    │       └── tablet
    │           ├── about-tablet.html
    │           ├── about-tablet.png [-]
    │           ├── contact-tablet.html
    │           ├── contact-tablet.png [-]
    │           ├── home-tablet.html
    │           ├── home-tablet.png [-]
    │           ├── projects-tablet.html
    │           ├── projects-tablet.png [-]
    │           ├── services-tablet.html
    │           └── services-tablet.png [-]
    ├── scripts
    │   ├── README.md
    │   ├── run-capture.bat
    │   ├── dependencies
    │   │   ├── README.md
    │   │   ├── dependency-manager.js
    │   │   ├── visualize
    │   │   ├── analyze
    │   │   │   └── check-dependencies.js
    │   │   └── utils
    │   │       ├── check-graphviz.js
    │   │       ├── cleanup-directory.js
    │   │       ├── cleanup-redundant-files.js
    │   │       ├── fix-missing-files.js
    │   │       └── run-visualizations.js
    │   ├── dev
    │   │   ├── README.md
    │   │   └── dev-with-snapshots.js
    │   ├── screenshots
    │   │   ├── README.md
    │   │   ├── capture
    │   │   └── manage
    │   └── utils
    │       ├── README.md
    │       └── cleanup.js
    └── src
        ├── App.tsx
        ├── index.css
        ├── index.html
        ├── main.tsx
        ├── vite-env.d.ts
        ├── components
        │   ├── layout
        │   │   ├── Footer.tsx
        │   │   ├── Header.tsx
        │   │   ├── Meta.tsx
        │   │   └── Navbar.tsx
        │   ├── projects
        │   │   └── ProjectGallery.tsx
        │   ├── seo
        │   │   └── TestimonialsSchema.tsx
        │   ├── shared
        │   │   ├── Elements
        │   │   │   ├── Card.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Image.tsx
        │   │   │   ├── Link.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   └── Form
        │   │   │       ├── Input.tsx
        │   │   │       ├── Select.tsx
        │   │   │       └── Textarea.tsx
        │   │   └── Layout
        │   │       └── Layout.tsx
        │   ├── testimonials
        │   │   └── index.tsx
        │   └── ui
        │       ├── Button.tsx
        │       ├── Container.tsx
        │       ├── Hero.tsx
        │       ├── Intersection.tsx
        │       ├── Logo.tsx
        │       ├── Notifications.tsx
        │       ├── SeasonalCTA.tsx
        │       ├── ServiceAreaList.tsx
        │       ├── Skeleton.tsx
        │       ├── Transition.tsx
        │       └── index.ts
        ├── content
        │   ├── index.ts
        │   ├── services
        │   │   └── index.ts
        │   ├── team
        │   │   └── index.ts
        │   └── testimonials
        │       └── index.ts
        ├── data
        │   ├── projects.ts
        │   ├── services.ts
        │   └── testimonials.ts
        ├── docs
        │   └── SEO_USAGE.md
        ├── features
        │   ├── testimonials.tsx
        │   ├── home
        │   │   ├── FilteredServicesSection.tsx
        │   │   └── SeasonalProjectsCarousel.tsx
        │   ├── projects
        │   │   ├── ProjectCard.tsx
        │   │   ├── ProjectFilter.tsx
        │   │   ├── ProjectGrid.tsx
        │   │   └── ProjectsCarousel.tsx
        │   ├── services
        │   │   ├── ServiceCard.tsx
        │   │   ├── ServiceFeature.tsx
        │   │   ├── ServiceGrid.tsx
        │   │   └── data.ts
        │   └── testimonials
        │       ├── AverageRating.tsx
        │       ├── Testimonial.tsx
        │       ├── TestimonialFilter.tsx
        │       ├── TestimonialSlider.tsx
        │       ├── TestimonialsSection.tsx
        │       └── data.ts
        ├── hooks
        │   └── useData.ts
        ├── lib
        │   ├── constants.ts
        │   ├── hooks.ts
        │   ├── utils.ts
        │   ├── api
        │   │   └── index.ts
        │   ├── config
        │   │   ├── images.ts
        │   │   ├── index.ts
        │   │   ├── paths.ts
        │   │   └── site.ts
        │   ├── context
        │   │   └── AppContext.tsx
        │   ├── hooks
        │   │   ├── useAnalytics.ts
        │   │   ├── useEventListener.ts
        │   │   └── useMediaQuery.ts
        │   ├── types
        │   │   └── index.ts
        │   └── utils
        │       ├── analytics.ts
        │       ├── dom.ts
        │       ├── formatting.ts
        │       ├── images.ts
        │       ├── index.ts
        │       ├── seasonal.ts
        │       ├── seo.ts
        │       └── validation.ts
        ├── pages
        │   ├── ProjectDetail.tsx
        │   ├── Projects.tsx
        │   ├── ServiceDetail.tsx
        │   ├── Services.tsx
        │   ├── TestimonialsPage.tsx
        │   ├── about
        │   │   └── index.tsx
        │   ├── contact
        │   │   └── index.tsx
        │   ├── home
        │   │   └── index.tsx
        │   ├── services
        │   │   ├── detail.tsx
        │   │   └── index.tsx
        │   └── testimonials
        │       └── index.tsx
        ├── styles
        │   ├── animations.css
        │   ├── base.css
        │   └── utilities.css
        ├── types
        │   ├── content.ts
        │   └── index.ts
        └── utils
            └── imageLoader.ts
```
