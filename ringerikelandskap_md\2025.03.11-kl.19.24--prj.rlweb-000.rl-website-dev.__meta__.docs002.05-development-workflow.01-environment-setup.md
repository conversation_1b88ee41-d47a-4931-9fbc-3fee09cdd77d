# Development Environment Setup

This document provides instructions for setting up the development environment for the Ringerike Landskap website and outlines the recommended workflow.

## Prerequisites

Before starting development, ensure you have the following installed:

- **Node.js**: v18.x or later
- **npm**: v8.x or later
- **Git**: For version control
- A modern code editor (VS Code recommended)

## Initial Setup

1. **Clone the repository**:

   ```bash
   git clone [repository-url]
   cd rl-website_web/project
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Verify the installation**:

   ```bash
   npm run dev
   ```

   This should start the development server and make the website available at http://localhost:5173/.

## Development Workflows

### Standard Development

For regular development work without screenshot capture:

```bash
npm run dev
```

This starts the Vite development server with hot module replacement.

### Development with Screenshots

For development with automatic screenshot capturing:

```bash
npm run dev:ai
```

This starts the development server and sets up automatic screenshot capture. Screenshots will be captured whenever significant changes are detected and stored in both the `screenshots/latest/` and `.ai-analysis/latest/` directories.

### Manual Screenshot Management

```bash
# Capture screenshots manually
npm run screenshots:capture

# Clean up old screenshots
npm run screenshots:clean

# Refresh screenshots (capture, tidy, and clean)
npm run screenshots:refresh
```

## Working with the Codebase

### Folder Structure

When working with the codebase, follow these guidelines:

1. **Components**: Place reusable components in `src/components/`
   - UI components in `src/components/ui/`
   - Layout components in `src/components/layout/`

2. **Pages**: Place page components in `src/pages/`
   - Each page in its own directory
   - Use `index.tsx` for the main page component

3. **Feature-specific components**: Place in `src/features/[feature-name]/`

4. **Data**: Place data files in `src/data/`

5. **Types**: Place TypeScript interfaces in `src/types/`

### Coding Standards

Follow these standards when writing code:

1. **TypeScript**: Use TypeScript for all new code
   - Define interfaces for props and data structures
   - Avoid using `any` type
   - Use proper type guards

2. **Components**: Use functional components with hooks
   - Define explicit prop interfaces
   - Use destructuring for props
   - Export components as named exports

3. **Styling**: Use Tailwind CSS for styling
   - Group related classes together
   - Use clsx or tailwind-merge for conditional classes
   - Follow the existing color scheme and design system

4. **State Management**: Prefer React Context for shared state
   - Use local state for component-specific state
   - Avoid prop drilling where possible

### Git Workflow

Follow this workflow for version control:

1. Create a branch for your feature or fix
2. Make your changes, following the coding standards
3. Run tests and ensure all features work
4. Create a pull request with a clear description
5. Address review comments
6. Merge only when approved

## Build and Deployment

### Building for Production

To build the website for production:

```bash
npm run build
```

This creates optimized production files in the `dist/` directory.

### Preview Production Build

To preview the production build locally:

```bash
npm run preview
```

This serves the production build for local testing.

## Troubleshooting

### Development Server Issues

If the development server fails to start:

1. Check if another process is using port 5173
2. Try deleting `node_modules` and reinstalling dependencies
3. Check for errors in the console output

### Screenshot Capture Issues

If screenshots aren't being captured:

1. Ensure the development server is running
2. Check that Puppeteer can access the server
3. Try running screenshots manually with `npm run screenshots:capture`
4. Check the console for error messages

## AI-Assisted Development

When working with AI tools (like Cursor):

1. Make sure screenshots are up to date using `npm run screenshots:capture`
2. Reference the current visual state using the `.ai-analysis/latest/` directory
3. Provide clear context about the component or feature you're working on
4. Reference the appropriate documentation for context

This setup ensures a smooth development experience while maintaining high code quality and consistent visual output. 