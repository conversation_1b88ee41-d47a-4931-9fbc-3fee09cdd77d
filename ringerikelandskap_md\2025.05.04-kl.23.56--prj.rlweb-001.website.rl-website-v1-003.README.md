# Ringerike Landskap Website

This repository contains the website code for Ringerike Landskap, built using React, TypeScript, and Vite.

## Table of Contents

1. [Overview](#overview)
2. [Directory Structure](#directory-structure)
3. [Development](#development)
4. [Building the Website](#building-the-website)
5. [Deployment Guide](#deployment-guide)
   - [Prerequisites](#prerequisites)
   - [Apache Server](#apache-server)
   - [Nginx Server](#nginx-server)
   - [Vercel](#vercel)
   - [Netlify](#netlify)
   - [Manual Deployment](#manual-deployment)
6. [Seasonal Content System](#seasonal-content-system)
   - [Seasonal Constants](#1-seasonal-constants-srclibconstantsseasonalts)
   - [Seasonal Utilities](#2-seasonal-utilities-srclibutilsseasonalts)
   - [Seasonal Hook](#3-seasonal-hook-srclibhooksuseseasondatats)
   - [Usage Example](#usage-example)
7. [Contact Information System](#contact-information-system)
   - [Contact Constants](#1-contact-constants-srclibconstantscontactts)
   - [Contact Types](#2-contact-types-srclibtypescontactts)
   - [Helper Functions](#3-helper-functions)
   - [Usage Example](#contact-usage-example)
8. [SEO System](#seo-system)
   - [SEO Constants](#1-seo-constants-srclibconstantsseo)
   - [SEO Components](#2-seo-components-srccomponentsseo)
   - [Usage Example](#seo-usage-example)
9. [Environment Configuration](#environment-configuration)
10. [Import Standards](#import-standards)
11. [CSS and Styling](#css-and-styling)
12. [TypeScript and ESLint Configuration](#typescript-and-eslint-configuration)
13. [Code Organization](#code-organization)
14. [Troubleshooting](#troubleshooting)
15. [Testing Deployment](#testing-deployment)

## Overview

The website is built using React, TypeScript, and Vite. It uses a component-based architecture with a focus on reusability and maintainability. Key features include:

- **Component-Based Architecture**: Organized into reusable UI components and section-specific components
- **TypeScript**: Full type safety throughout the codebase
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Seasonal Content**: Dynamic content that adapts based on the current season
- **Performance Optimized**: Lazy loading, code splitting, and optimized assets

## Directory Structure

```
website/
├── config/           # Configuration files
│   └── env/          # Environment-specific configuration files
├── public/           # Static assets
│   ├── images/       # Images (categorized, site, team)
│   ├── .htaccess     # Apache configuration for SPA routing
│   └── _redirects    # Netlify configuration for SPA routing
├── scripts/          # Utility scripts
├── src/              # Source code
│   ├── app/          # Application root shell and router
│   ├── content/      # Content data (locations, projects, services, team, testimonials)
│   ├── data/         # Static data
│   ├── docs/         # Documentation
│   ├── layout/       # Shared layout components
│   ├── lib/          # Utility logic, API layer, config
│   │   ├── api/      # API client code
│   │   ├── config/   # Configuration
│   │   ├── constants/# Application constants and data
│   │   ├── context/  # React context providers
│   │   ├── hooks/    # Custom React hooks
│   │   ├── types/    # TypeScript definitions
│   │   └── utils/    # Utility functions
│   ├── sections/     # Chronologically ordered, self-contained sections
│   │   ├── 10-home/  # Home page and related components
│   │   ├── 20-about/ # About page and related components
│   │   ├── 30-services/ # Services pages and related components
│   │   ├── 40-projects/ # Projects pages and related components
│   │   ├── 50-testimonials/ # Testimonials pages and related components
│   │   └── 60-contact/ # Contact page and related components
│   ├── styles/       # Global styles
│   └── ui/           # Global, atomic UI components
├── package.json      # Project dependencies and scripts
├── tailwind.config.js # Tailwind CSS configuration
├── tsconfig.json     # TypeScript configuration
├── vercel.json       # Vercel configuration for SPA routing
└── vite.config.ts    # Vite configuration
```

## Development

To start the development server:

```bash
npm run dev
```

This will start the development server at http://localhost:5173.

## Building the Website

Before deploying, you need to build the website for the appropriate environment:

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

The built files will be in the `dist` directory with the following structure:

```
dist/
├── assets/       # JavaScript and CSS files
├── images/       # Optimized images
├── index.html    # Main HTML file
├── robots.txt    # Robots file
└── site.webmanifest # Web app manifest
```

## Deployment Guide

This section provides instructions for deploying the Ringerike Landskap website to different hosting environments, with a focus on ensuring proper routing for a single-page application (SPA).

### Prerequisites

Before deploying, make sure you have:

- Node.js (v16 or higher)
- npm (v7 or higher)
- Access to your hosting environment

### Apache Server

For Apache servers, the website includes a `.htaccess` file in the `public` directory that will be copied to the `dist` directory during build. This file contains the necessary rewrite rules for client-side routing.

1. Upload the contents of the `dist` directory to your web server.

2. Make sure your Apache server has `mod_rewrite` enabled and that `.htaccess` files are allowed to override server configuration:

   ```apache
   <Directory /path/to/your/website>
       Options Indexes FollowSymLinks
       AllowOverride All
       Require all granted
   </Directory>
   ```

3. If you're using a subdirectory (e.g., `example.com/ringerike/`), you'll need to update the `base` property in `vite.config.ts` before building:

   ```typescript
   base: '/ringerike/',
   ```

### Nginx Server

For Nginx servers, you'll need to add the following configuration to your server block:

```nginx
server {
    listen 80;
    server_name example.com;
    root /path/to/your/website;
    index index.html;

    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        expires max;
        add_header Cache-Control "public, max-age=********";
    }

    # For client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### Vercel

The website includes a `vercel.json` file with the necessary configuration for Vercel deployment:

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Deploy to Vercel:
   ```bash
   vercel
   ```

### Netlify

The website includes a `_redirects` file in the `public` directory for Netlify deployment:

1. Create a `netlify.toml` file in the root directory:
   ```toml
   [build]
     publish = "dist"
     command = "npm run build:production"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

2. Deploy to Netlify:
   ```bash
   npx netlify-cli deploy
   ```

### Manual Deployment

For any static hosting service:

1. Build the website: `npm run build` (or `build:staging` or `build:production`)
2. Upload the contents of the `dist` directory to your hosting service

## Seasonal Content System

The website features a comprehensive seasonal content system that dynamically adapts content based on the current season. This system is organized into three main components:

### 1. Seasonal Constants (`src/lib/constants/seasonal.ts`)

Central repository for all seasonal data including:
- Season names and mappings
- Seasonal services and projects
- Month-to-season mapping

### 2. Seasonal Utilities (`src/lib/utils/seasonal.ts`)

Utility functions for working with seasonal data:
- `getCurrentSeason()`: Determines the current season based on date
- `getSeasonDisplayName()`: Formats season names for display
- `serviceMatchesSeason()`: Checks if a service is relevant for a season
- `projectMatchesSeason()`: Checks if a project is relevant for a season

### 3. Seasonal Hook (`src/lib/hooks/useSeasonalData.ts`)

React hook that provides components with seasonal data:
```typescript
const {
  currentSeason,          // Current season (vår, sommer, høst, vinter)
  currentSeasonEnglish,   // English translation (spring, summer, fall, winter)
  currentSeasonDisplay,   // Display name (Våren, Sommeren, etc.)
  seasonalCategories,     // Categories relevant to the current season
  seasonalFeatures,       // Features relevant to the current season
  isWinter, isSpring, isSummer, isFall // Boolean flags
} = useSeasonalData();
```

### Usage Example

```tsx
import { useSeasonalData } from '@/lib/hooks';

const SeasonalComponent = () => {
  const { currentSeason, currentSeasonDisplay } = useSeasonalData();

  return (
    <div>
      <h2>Current Season: {currentSeasonDisplay}</h2>
      <p>Content optimized for {currentSeason}</p>
    </div>
  );
};
```

## Contact Information System with Local SEO Focus

The website features a comprehensive contact information system that centralizes all contact-related data in one place, with a strong focus on local SEO for the Ringerike region (20-50km radius from Røyse). This system is organized into three main components:

### 1. Contact Constants (`src/lib/constants/contact.ts`)

Central repository for all contact information including:
- Phone numbers and email addresses
- Physical address information
- Team member contact details
- Social media links
- Opening hours
- Form defaults
- **SEO-optimized service areas** with location-specific descriptions targeting Ringerike region
- **Business categories** for schema.org and industry-specific search
- **Customer reviews** with location mentions for local SEO
- **Core services** with location-specific relevance
- **Owner information** for personal touch (important for local business)
- **Local landmarks and terrain features** for rich, location-specific content

### 2. Contact Types (`src/lib/types/contact.ts`)

TypeScript interfaces for contact information:
- `ContactInformation`: Main interface for all contact data
- `TeamMember`: Interface for team member information
- `Address`: Interface for address information
- `SocialMedia`: Interface for social media links
- `OpeningHours`: Interface for opening hours
- `FormDefaults`: Interface for form defaults
- `BusinessCategory`: Interface for business categories
- `ServiceArea`: Interface for service areas with SEO descriptions, landmarks, and terrain features
- `CustomerReview`: Interface for customer reviews with location mentions
- `CoreService`: Interface for core services with location relevance
- `OwnerInfo`: Interface for owner information (personal touch for local SEO)
- `LocationSeoMetadata`: Interface for location-specific SEO data

### 3. Helper Functions

Utility functions for working with contact information:
- `getPhoneLink()`: Generates a phone link with the tel: protocol
- `getEmailLink()`: Generates an email link with the mailto: protocol
- `getFullAddress()`: Returns the full address as a formatted string
- `getMainContact()`: Returns the main contact person
- `getCopyrightText()`: Returns the copyright text with the current year
- `getFormattedOpeningHours()`: Returns formatted opening hours
- `getCompanySchema()`: Returns schema.org structured data optimized for local business in Ringerike region
- `getLocationSeoMetadata()`: Returns rich SEO metadata for location-specific pages with nearby areas calculation

### Contact Usage Example with Local SEO Focus

```tsx
import {
  CONTACT_INFO,
  getPhoneLink,
  getEmailLink,
  getLocationSeoMetadata
} from '@/lib/constants/contact';

// Basic contact component
const ContactComponent = () => {
  return (
    <div>
      <h2>Kontakt oss</h2>
      <p>Telefon: <a href={getPhoneLink()}>{CONTACT_INFO.phone}</a></p>
      <p>E-post: <a href={getEmailLink()}>{CONTACT_INFO.email}</a></p>
      <p>Adresse: {CONTACT_INFO.address.street}, {CONTACT_INFO.address.postalCode} {CONTACT_INFO.address.city}</p>

      <h3>Vårt team</h3>
      {CONTACT_INFO.team.map(member => (
        <div key={member.id}>
          <h4>{member.name}</h4>
          <p>{member.title}</p>
          <p>Telefon: {member.phone}</p>
          <p>E-post: <a href={getEmailLink(member.id)}>{member.email}</a></p>
        </div>
      ))}
    </div>
  );
};

// Location-specific page with rich local SEO optimization
const LocationPage = ({ locationId }) => {
  // Get location-specific SEO metadata with rich local data
  const seoData = getLocationSeoMetadata(locationId);

  // Find the service area
  const serviceArea = CONTACT_INFO.serviceAreas.find(area => area.id === locationId);

  // Get relevant services for this location
  const relevantServices = CONTACT_INFO.coreServices
    .filter(service => service.popularIn.includes(serviceArea.name));

  return (
    <>
      {/* Apply rich SEO metadata */}
      <Meta
        title={seoData.title}
        description={seoData.description}
        keywords={seoData.keywords}
        schema={seoData.schema}
      />

      <Hero
        title={`Anleggsgartner i ${serviceArea.name}`}
        subtitle={serviceArea.description}
      />

      {/* Display location-specific services */}
      <section>
        <h2>Våre tjenester i {serviceArea.name}</h2>
        <p>Vi tilbyr følgende tjenester spesielt tilpasset forholdene i {serviceArea.name}:</p>
        <div className="services-grid">
          {relevantServices.map(service => (
            <div key={service.id} className="service-card">
              <h3>{service.name}</h3>
              <p>{service.description}</p>
              <ul>
                {service.benefits.map(benefit => (
                  <li key={benefit}>{benefit}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      {/* Local terrain features for rich content */}
      <section>
        <h2>Tilpasset {serviceArea.name}s terreng</h2>
        <p>Som lokale anleggsgartnere kjenner vi godt til de spesielle forholdene i {serviceArea.name}:</p>
        <ul>
          {serviceArea.terrainFeatures.map(feature => (
            <li key={feature}>{feature}</li>
          ))}
        </ul>
      </section>

      {/* Nearby areas for broader reach */}
      <section>
        <h2>Vi betjener også områdene rundt {serviceArea.name}</h2>
        <p>Fra vår base i {CONTACT_INFO.address.city} når vi enkelt:</p>
        <ul className="nearby-areas">
          {seoData.locationData.nearbyAreas.map(area => (
            <li key={area}>{area}</li>
          ))}
        </ul>
      </section>

      {/* Location-specific customer reviews */}
      <section>
        <h2>Hva våre kunder i {serviceArea.name} sier</h2>
        {CONTACT_INFO.reviews
          .filter(review => review.location === serviceArea.name)
          .map(review => (
            <blockquote key={review.author} className="customer-review">
              <p>{review.reviewBody}</p>
              <footer>— {review.author}, {review.location}</footer>
            </blockquote>
          ))}
      </section>

      {/* Owner information for personal touch */}
      <section>
        <h2>Personlig oppfølging fra eierne</h2>
        <div className="owners-grid">
          {CONTACT_INFO.company.owners.map(owner => (
            <div key={owner.name} className="owner-card">
              <h3>{owner.name}</h3>
              <p>{owner.description}</p>
              <p>Spesialkompetanse: {owner.expertise.join(', ')}</p>
            </div>
          ))}
        </div>
        <p>Som lokalt firma i {CONTACT_INFO.company.mainServiceArea} er vi stolte av å kunne tilby personlig oppfølging fra våre eiere på alle prosjekter i {serviceArea.name}.</p>
      </section>
    </>
  );
};
```

## SEO System with User Segment Targeting

The website features a comprehensive SEO system that centralizes all SEO-related data and components, with a focus on phrase-based keywords and user segment targeting. This system is organized into four main components:

### 1. SEO Constants (`src/lib/constants/seo.ts`)

Central repository for all SEO-related data including:
- **Phrase-based keywords** organized by category
- **Location-specific phrases** for local SEO
- **Question-based phrases** for featured snippets and voice search
- **Page-specific SEO data** (title, description, keywords, schema)
- **Utility functions** for generating SEO metadata

Key features:
- Structured keyword phrases with variations and subphrases
- Search intent categorization (informational, navigational, transactional, commercial)
- Priority-based keyword selection
- Dynamic keyword generation based on context

### 2. User Segment Constants (`src/lib/constants/user-segments.ts`)

Defines user segments for targeted SEO:
- **Homeowners**: Residential customers looking for landscaping services
- **Businesses**: Commercial customers looking for landscaping services
- **Property Developers**: Developers looking for landscaping services for new developments
- **General**: Default segment when no specific targeting is needed

Each segment includes:
- Specific needs and pain points
- Search behaviors
- Keyword phrases tailored to the segment
- Schema.org enhancements specific to the segment
- Title and description templates

### 3. SEO Components (`src/components/SEO`)

Reusable components for applying SEO metadata to pages:
- `PageSEO`: Main component for applying SEO metadata to pages with segment targeting
- `SEODebug`: Component for debugging SEO metadata in development
- Integration with the Meta component for rendering metadata

### 4. Helper Functions

Utility functions for working with SEO data:
- `generateKeywords()`: Generates keywords from phrases based on categories, location, and segment
- `generateFAQSchema()`: Generates schema.org FAQ structured data
- `getPageSeo()`: Gets SEO data for a specific page with segment targeting
- `getLocationSeo()`: Gets SEO data for a location-specific page with segment targeting
- `getSegmentSeo()`: Gets SEO data for a segment-specific page
- `getUserSegment()`: Gets a user segment by ID
- `getSegmentKeyPhrases()`: Gets keyword phrases for a segment
- `getSegmentFAQSchema()`: Gets FAQ schema for a segment

### SEO Usage Example

```tsx
import { PageSEO } from '@/components/SEO';

// Basic page with SEO
const HomePage = () => {
  return (
    <>
      {/* Apply SEO metadata from the centralized module */}
      <PageSEO pageName="home" />

      {/* Page content */}
      <div>
        <h1>Welcome to Ringerike Landskap</h1>
        {/* ... */}
      </div>
    </>
  );
};

// Segment-specific page with SEO
const HomeownersPage = () => {
  return (
    <>
      {/* Apply homeowner-specific SEO metadata */}
      <PageSEO
        pageName="services"
        segmentId="homeowners"
      />

      {/* Page content */}
      <div>
        <h1>Hageløsninger for Boligeiere</h1>
        {/* ... */}
      </div>
    </>
  );
};

// Location and segment-specific page with SEO
const LocationSegmentPage = ({ locationId, locationName, segmentId }) => {
  return (
    <>
      {/* Apply location and segment-specific SEO metadata */}
      <PageSEO
        pageName="services"
        locationId={locationId}
        segmentId={segmentId}
      />

      {/* Page content */}
      <div>
        <h1>Tjenester for {segmentDisplayName} i {locationName}</h1>
        {/* ... */}
      </div>
    </>
  );
};

// Page with debug mode enabled
const DebugPage = () => {
  return (
    <>
      {/* Apply SEO with debug mode */}
      <PageSEO
        pageName="about"
        segmentId="businesses"
        debug={true} // Shows SEO debug information in development
      />

      {/* Page content */}
      <div>
        <h1>About Our Business Services</h1>
        {/* ... */}
      </div>
    </>
  );
};
```

## Environment Configuration

The website uses environment-specific configuration files to handle different settings for development, staging, and production. These configurations are located in the `config/env` directory:

- `.env.development` - Development environment variables
- `.env.staging` - Staging environment variables
- `.env.production` - Production environment variables
- `.env.example` - Template with required variables

Key environment variables include:

- `VITE_BASE_URL` - Base URL for the website
- `VITE_API_URL` - API URL
- `VITE_ENABLE_ANALYTICS` - Whether to enable analytics
- `VITE_ANALYTICS_ID` - Analytics tracking ID
- `VITE_DEBUG` - Whether to enable debug mode
- `VITE_FEATURE_*` - Feature flags

The `.env.example` file serves as the source of truth for required variables. You can validate all environment files against it by running:

```bash
npm run validate-env
```

For more detailed information about environment configuration, see `config/env/README.md`.

## Import Standards

This project follows specific import standards to ensure code consistency and maintainability.

### Core Principles

1. **Use absolute imports with the `@/` alias**
2. **Use barrel exports (index.ts files) for module exports**
3. **Group and organize imports consistently**

### Import Pattern

```typescript
// External dependencies (alphabetical order)
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Internal imports (grouped by module, alphabetical within groups)
import { Button, Card, Container } from '@/ui';
import { useData, useMediaQuery } from '@/lib/hooks';
import { formatDate } from '@/lib/utils';
import { ProjectType } from '@/lib/types';
```

### Barrel Exports

Each directory should have an `index.ts` file that exports its contents:

```typescript
// src/ui/index.ts
export { Button } from './Button';
export { Card } from './Card';
export { Container } from './Container';
// ...
```

### Import Rules

1. **No relative imports** with `../` or `../../`
2. **No direct imports** from implementation files when a barrel file exists
   - ❌ `import { Button } from '@/ui/Button';`
   - ✅ `import { Button } from '@/ui';`
3. **Exception**: It's acceptable to use relative imports within the same directory
   - ✅ `import { SubComponent } from './SubComponent';`

## CSS and Styling

The website uses Tailwind CSS for styling. The configuration is completely self-contained within the website directory:

- `tailwind.config.js` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration
- `src/index.css` - Main CSS file that imports Tailwind and other CSS files
- `src/styles/` - Directory containing additional CSS files:
  - `base.css` - Base styles
  - `utilities.css` - Utility classes
  - `animations.css` - Animation styles

When copying the website to a new location, make sure to install all dependencies including the Tailwind plugins:

```bash
npm install
```

## TypeScript and ESLint Configuration

The project uses TypeScript and ESLint for type safety and code quality:

- `tsconfig.json` - TypeScript configuration with strict type checking
- `tsconfig.node.json` - TypeScript configuration for Node.js environment
- `eslint.config.js` - ESLint configuration using the new flat config format

## Code Organization

The codebase follows these organizational principles:

### 1. Modular Architecture
- **UI Components**: Reusable, atomic components in `src/ui/`
- **Section Components**: Page-specific components in `src/sections/`
- **Utility Modules**: Shared functionality in `src/lib/`

### 2. Data Flow
- **API Layer**: Centralized data fetching in `src/lib/api/`
- **Hooks**: Custom React hooks for data and UI state in `src/lib/hooks/`
- **Constants**: Application constants in `src/lib/constants/`

### 3. Feature Organization
- **Seasonal System**: Consolidated in dedicated modules
- **Filtering System**: Centralized filtering logic
- **Routing**: Organized by section with consistent patterns

## Troubleshooting

### 404 Errors on Page Refresh or Direct URL Access

If you're experiencing 404 errors when refreshing the page or accessing URLs directly, it's likely that the server is not properly configured to handle client-side routing.

1. Check that the `.htaccess` file (for Apache) or server configuration (for Nginx) is correctly set up.

2. Make sure the server is configured to allow `.htaccess` files to override server configuration.

### Blank Page or JavaScript Errors

If you're seeing a blank page or JavaScript errors:

1. Check the browser console for errors.

2. Verify that the `base` property in `vite.config.ts` is correctly set for your deployment environment.

3. Make sure all assets are being loaded correctly (check for 404 errors in the network tab).

## Testing Deployment

After deploying, test the following:

1. Direct URL access to different routes (e.g., `/prosjekter`, `/hva`, etc.).

2. Refreshing the page on different routes.

3. Navigation between pages using the application's links.

4. Check that all assets (images, CSS, JavaScript) are loading correctly.

If you encounter any issues, refer to the troubleshooting section or contact the development team.
