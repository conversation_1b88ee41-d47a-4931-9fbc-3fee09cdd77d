# Accessibility Recommendations

This document outlines specific recommendations to improve the accessibility of the Ringerike Landskap website, ensuring it's usable by people with various disabilities and meets WCAG 2.1 standards.

## Current Accessibility Assessment

Based on the analysis of the codebase and rendered website, several accessibility aspects have been identified:

-   **Semantic HTML**: Generally good use of semantic elements, but some improvements needed
-   **ARIA Attributes**: Limited implementation of ARIA roles and attributes
-   **Keyboard Navigation**: Basic functionality exists but needs enhancement
-   **Color Contrast**: Generally good, but some areas need improvement
-   **Focus Management**: Basic focus states exist but could be enhanced
-   **Screen Reader Support**: Limited explicit support for screen readers

## Semantic HTML Improvements

### Document Structure

Ensure proper heading hierarchy throughout the site:

```tsx
// Before - Incorrect heading hierarchy
<h1>Page Title</h1>
<div>
  <h3>Section Title</h3> // Skipping h2
  <p>Content</p>
</div>

// After - Correct heading hierarchy
<h1>Page Title</h1>
<div>
  <h2>Section Title</h2>
  <p>Content</p>
</div>
```

### Landmark Regions

Add proper landmark regions to improve navigation:

```tsx
// Before
<div className="header">...</div>
<div className="main-content">...</div>
<div className="footer">...</div>

// After
<header>...</header>
<main>
  <section aria-labelledby="section-title">
    <h2 id="section-title">Section Title</h2>
    ...
  </section>
</main>
<footer>...</footer>
```

## ARIA Implementation

### Interactive Elements

Add appropriate ARIA attributes to interactive elements:

```tsx
// Before - Mobile menu button
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
>
  <span className="sr-only">
    {isMenuOpen ? "Lukk meny" : "Åpne meny"}
  </span>
  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
</button>

// After - Enhanced mobile menu button
<button
  type="button"
  className="md:hidden rounded-md p-2 text-gray-700"
  onClick={() => setIsMenuOpen(!isMenuOpen)}
  aria-expanded={isMenuOpen}
  aria-controls="mobile-menu"
  aria-label={isMenuOpen ? "Lukk meny" : "Åpne meny"}
>
  <span className="sr-only">
    {isMenuOpen ? "Lukk meny" : "Åpne meny"}
  </span>
  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
</button>
```

### Form Elements

Enhance form accessibility with proper labels and ARIA attributes:

```tsx
// Before - Form field without proper label
<input
  type="email"
  placeholder="E-post"
  className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
/>

// After - Form field with proper label and ARIA
<div className="form-field">
  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
    E-post
  </label>
  <input
    id="email"
    type="email"
    placeholder="<EMAIL>"
    aria-required="true"
    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
  />
</div>
```

### Dynamic Content

Add ARIA live regions for dynamic content:

```tsx
// Before - Dynamic content without ARIA
<div className="notification">
  {message && <p>{message}</p>}
</div>

// After - Dynamic content with ARIA live region
<div
  className="notification"
  aria-live="polite"
  aria-atomic="true"
>
  {message && <p>{message}</p>}
</div>
```

## Keyboard Navigation

### Skip Link

Add a skip link to bypass navigation:

```tsx
// Add at the top of the page layout
<a
  href="#main-content"
  className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-green-600 focus:shadow-md"
>
  Hopp til hovedinnhold
</a>

// Then add the corresponding ID to the main content
<main id="main-content">
  ...
</main>
```

### Focus Management

Improve focus styles for better visibility:

```css
/* Add to global CSS */
:focus {
    outline: 2px solid #1e9545;
    outline-offset: 2px;
}

/* For elements where the default outline doesn't work well */
.custom-focus:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 4px #1e9545;
    outline: none;
}
```

### Keyboard Accessible Components

Enhance the carousel component for keyboard accessibility:

```tsx
// Add keyboard support to carousel navigation
<button
  onClick={prevSlide}
  onKeyDown={(e) => e.key === 'Enter' && prevSlide()}
  tabIndex={0}
  aria-label="Forrige prosjekt"
  className="carousel-control"
>
  <ChevronLeft className="w-6 h-6" />
</button>

<button
  onClick={nextSlide}
  onKeyDown={(e) => e.key === 'Enter' && nextSlide()}
  tabIndex={0}
  aria-label="Neste prosjekt"
  className="carousel-control"
>
  <ChevronRight className="w-6 h-6" />
</button>
```

## Color and Contrast

### Text Contrast

Ensure sufficient contrast for all text:

```tsx
// Before - Potentially low contrast
<p className="text-gray-400">Some text</p>

// After - Improved contrast
<p className="text-gray-700">Some text</p>
```

### Non-Text Contrast

Ensure interactive elements have sufficient contrast:

```tsx
// Before - Border might have insufficient contrast
<button className="border border-gray-300 text-gray-700">Button</button>

// After - Improved border contrast
<button className="border-2 border-gray-500 text-gray-700">Button</button>
```

### Color Independence

Don't rely solely on color to convey information:

```tsx
// Before - Using only color to indicate status
<div className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>

// After - Using color and text to indicate status
<div
  className={`status ${isActive ? 'bg-green-500' : 'bg-red-500'}`}
  aria-label={isActive ? 'Aktiv' : 'Inaktiv'}
>
  {isActive ? '✓' : '✗'}
</div>
```

## Images and Media

### Alt Text

Ensure all images have appropriate alt text:

```tsx
// Before - Missing or generic alt text
<img src="/images/projects/project1.jpg" alt="Project" />

// After - Descriptive alt text
<img
  src="/images/projects/project1.jpg"
  alt="Completed garden landscaping project in Røyse with stone pathways and native plantings"
/>

// For decorative images
<img
  src="/images/decorative/pattern.jpg"
  alt=""
  role="presentation"
/>
```

### SVG Accessibility

Enhance SVG accessibility:

```tsx
// Before - SVG without accessibility attributes
<svg width="24" height="24" viewBox="0 0 24 24">
  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
</svg>

// After - Accessible SVG
<svg
  width="24"
  height="24"
  viewBox="0 0 24 24"
  aria-hidden="true"
  focusable="false"
>
  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
</svg>
```

## Form Accessibility

### Form Validation

Implement accessible form validation:

```tsx
// Enhanced form field with validation
const FormField = ({ id, label, type, required, error, ...props }) => {
    return (
        <div className="form-field">
            <label
                htmlFor={id}
                className="block text-sm font-medium text-gray-700 mb-1"
            >
                {label} {required && <span aria-hidden="true">*</span>}
            </label>
            <input
                id={id}
                type={type}
                aria-required={required}
                aria-invalid={!!error}
                aria-describedby={error ? `${id}-error` : undefined}
                className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                    error ? "border-red-500" : "border-gray-300"
                }`}
                {...props}
            />
            {error && (
                <div
                    id={`${id}-error`}
                    className="mt-1 text-sm text-red-600"
                    role="alert"
                >
                    {error}
                </div>
            )}
        </div>
    );
};
```

### Required Fields

Clearly indicate required fields:

```tsx
// Add to the form component
<p className="text-sm text-gray-500 mb-4">
    Felt markert med <span aria-hidden="true">*</span>
    <span className="sr-only">stjerne</span> er obligatoriske
</p>
```

## Screen Reader Support

### Descriptive Link Text

Improve link text for screen readers:

```tsx
// Before - Vague link text
<Link to="/prosjekter">Les mer</Link>

// After - Descriptive link text
<Link to="/prosjekter">
  Se alle prosjekter
  <span className="sr-only"> om landskapsdesign</span>
</Link>
```

### Table Accessibility

Enhance table accessibility:

```tsx
// Accessible table structure
<table>
    <caption>Prisliste for tjenester</caption>
    <thead>
        <tr>
            <th scope="col">Tjeneste</th>
            <th scope="col">Pris (NOK)</th>
            <th scope="col">Estimert tid</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th scope="row">Hagedesign</th>
            <td>5000</td>
            <td>2 uker</td>
        </tr>
        {/* More rows */}
    </tbody>
</table>
```

## Implementation Priority

1. **High Priority (Essential for Accessibility)**

    - Add proper semantic HTML structure
    - Implement form labels and ARIA attributes
    - Ensure sufficient color contrast
    - Add alt text to all images

2. **Medium Priority (Important Improvements)**

    - Add skip links
    - Enhance keyboard navigation
    - Implement ARIA live regions
    - Improve focus management

3. **Lower Priority (Additional Enhancements)**
    - Add screen reader specific text
    - Enhance SVG accessibility
    - Implement advanced ARIA patterns for complex components

## Testing Recommendations

1. **Automated Testing**

    - Implement axe-core for automated accessibility testing
    - Add accessibility checks to CI/CD pipeline

2. **Manual Testing**

    - Test with keyboard navigation only
    - Test with screen readers (NVDA, JAWS, VoiceOver)
    - Test with high contrast mode
    - Test with text zoom (up to 200%)

3. **User Testing**
    - Conduct testing with users who have disabilities
    - Gather feedback on usability and accessibility

## Conclusion

Implementing these accessibility recommendations will significantly improve the usability of the Ringerike Landskap website for all users, including those with disabilities. The most critical improvements involve proper semantic HTML, form accessibility, and keyboard navigation, which should be prioritized in the implementation plan.

Regular accessibility testing should be conducted to ensure that the website maintains compliance with accessibility standards as new features and content are added.
