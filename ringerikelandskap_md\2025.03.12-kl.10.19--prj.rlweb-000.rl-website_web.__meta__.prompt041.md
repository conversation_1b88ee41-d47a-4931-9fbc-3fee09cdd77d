The attached scripts reside within the `script` folder of a React/TypeScript project, you should famialize yourself with it to such degree that you can explain *exactly* what it is and *exactly* what purpose it serve:

    # Dir `scripts`

    ### File Structure

    ```
    ├── auto-snapshot.js
    ├── dev-with-snapshots.js
    ├── refresh-screenshots.js
    ├── screenshot-manager.js
    ├── tidy-screenshots.js
    ├── config
    │   └── screenshot-config.js
    └── utils
        └── screenshot-utils.js
    ```


    #### `auto-snapshot.js`

    ```javascript
    import { exec } from "child_process";
    import { promisify } from "util";
    import fs from "fs";
    import path from "path";
    import { fileURLToPath } from "url";
    import puppeteer from "puppeteer";

    const execAsync = promisify(exec);

    // Get current directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = path.resolve(__dirname, "..");

    // Configuration
    const CONFIG = {
        // Development server port
        port: 5173,

        // Delay before capturing screenshots (ms)
        captureDelay: 2000,

        // AI analysis directory
        aiDir: path.join(projectRoot, ".ai-analysis"),

        // Latest screenshots directory
        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),

        // Maximum number of snapshots to keep
        maxSnapshots: 10,

        // Pages to capture
        pages: [
            { path: "/", name: "home" },
            { path: "/hvem-er-vi", name: "about" },
            { path: "/hva-vi-gjor", name: "services" },
            { path: "/prosjekter", name: "projects" },
            { path: "/kontakt", name: "contact" },
        ],

        // Viewport to use for AI analysis
        viewport: { width: 1440, height: 900, name: "desktop" },
    };

    /**
     * Ensures the AI analysis directory exists
     */
    function setupAiDirectory() {
        // Create main AI directory if it doesn't exist
        if (!fs.existsSync(CONFIG.aiDir)) {
            fs.mkdirSync(CONFIG.aiDir, { recursive: true });
        }

        // Create latest directory if it doesn't exist
        if (!fs.existsSync(CONFIG.latestDir)) {
            fs.mkdirSync(CONFIG.latestDir, { recursive: true });
        }

        // Create a metadata.json file if it doesn't exist
        const metadataPath = path.join(CONFIG.aiDir, "metadata.json");
        if (!fs.existsSync(metadataPath)) {
            fs.writeFileSync(
                metadataPath,
                JSON.stringify(
                    {
                        snapshots: [],
                        lastSnapshot: null,
                    },
                    null,
                    2
                )
            );
        }

        return metadataPath;
    }

    /**
     * Updates the metadata file with snapshot information
     */
    function updateMetadata(metadataPath, snapshotId) {
        const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf8"));

        // Add new snapshot
        metadata.snapshots.push({
            id: snapshotId,
            timestamp: new Date().toISOString(),
            pages: CONFIG.pages.map((page) => page.name),
        });

        // Keep only the most recent snapshots
        if (metadata.snapshots.length > CONFIG.maxSnapshots) {
            const removedSnapshots = metadata.snapshots.slice(
                0,
                metadata.snapshots.length - CONFIG.maxSnapshots
            );
            metadata.snapshots = metadata.snapshots.slice(
                metadata.snapshots.length - CONFIG.maxSnapshots
            );

            // Remove old snapshot directories
            removedSnapshots.forEach((snapshot) => {
                const snapshotDir = path.join(CONFIG.aiDir, snapshot.id);
                if (fs.existsSync(snapshotDir)) {
                    fs.rmSync(snapshotDir, { recursive: true, force: true });
                }
            });
        }

        // Update last snapshot
        metadata.lastSnapshot = snapshotId;

        // Write updated metadata
        fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

        return metadata;
    }

    /**
     * Captures a screenshot of a page
     */
    async function capturePage(page, snapshotDir) {
        const url = `http://localhost:${CONFIG.port}${page.path}`;
        const outputPath = path.join(snapshotDir, `${page.name}.png`);
        const latestOutputPath = path.join(CONFIG.latestDir, `${page.name}.png`);

        try {
            // Use puppeteer directly instead of via a temporary script
            const browser = await puppeteer.launch();
            const browserPage = await browser.newPage();

            await browserPage.setViewport({
                width: CONFIG.viewport.width,
                height: CONFIG.viewport.height,
            });

            await browserPage.goto(url, {
                waitUntil: "networkidle2",
                timeout: 10000,
            });

            // Wait for any animations
            await new Promise((resolve) =>
                setTimeout(resolve, CONFIG.captureDelay)
            );

            // Take the screenshot
            await browserPage.screenshot({
                path: outputPath,
                fullPage: true,
            });

            // Copy to latest directory
            fs.copyFileSync(outputPath, latestOutputPath);

            await browser.close();

            console.log(`Captured ${page.name} page`);
            return { outputPath, latestOutputPath };
        } catch (error) {
            console.error(`Error capturing ${page.name} page:`, error.message);
            return null;
        }
    }

    /**
     * Generates an AI-readable summary of the snapshot
     */
    function generateAiSummary(snapshotDir, metadata) {
        const summaryPath = path.join(snapshotDir, "ai-summary.md");
        const latestSummaryPath = path.join(CONFIG.latestDir, "ai-summary.md");

        const summary = `# Website Snapshot for AI Analysis

    ## Snapshot Information
    - **ID**: ${metadata.lastSnapshot}
    - **Timestamp**: ${new Date().toISOString()}
    - **Pages Captured**: ${CONFIG.pages.map((page) => page.name).join(", ")}

    ## Screenshot Paths
    ${CONFIG.pages
        .map(
            (page) =>
                `- **${page.name}**: \`${path.join(
                    snapshotDir,
                    `${page.name}.png`
                )}\``
        )
        .join("\n")}

    ## Latest Screenshot Paths (Always Current)
    ${CONFIG.pages
        .map(
            (page) =>
                `- **${page.name}**: \`${path.join(
                    CONFIG.latestDir,
                    `${page.name}.png`
                )}\``
        )
        .join("\n")}

    ## Viewport Information
    - **Width**: ${CONFIG.viewport.width}px
    - **Height**: ${CONFIG.viewport.height}px
    - **Name**: ${CONFIG.viewport.name}

    ## Usage Instructions
    These screenshots can be used by AI assistants to understand the current state of the website.
    When making code changes, reference these images to understand the visual impact of your changes.

    ## Access Paths
    To reference the specific snapshot in conversations, use:
    \`\`\`
    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${
            metadata.lastSnapshot
        }
    \`\`\`

    To always reference the latest screenshots, use:
    \`\`\`
    /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest
    \`\`\`
    `;

        // Write to snapshot directory
        fs.writeFileSync(summaryPath, summary);

        // Write to latest directory
        fs.writeFileSync(latestSummaryPath, summary);

        return { summaryPath, latestSummaryPath };
    }

    /**
     * Main function to capture snapshots for AI analysis
     */
    async function captureForAiAnalysis() {
        try {
            console.log("Capturing website snapshots for AI analysis...");

            // Setup AI directory
            const metadataPath = setupAiDirectory();

            // Create a unique ID for this snapshot
            const snapshotId = `snapshot-${Date.now()}`;
            const snapshotDir = path.join(CONFIG.aiDir, snapshotId);
            fs.mkdirSync(snapshotDir, { recursive: true });

            // Capture screenshots for each page
            for (const page of CONFIG.pages) {
                await capturePage(page, snapshotDir);
            }

            // Update metadata
            const metadata = updateMetadata(metadataPath, snapshotId);

            // Generate AI summary
            const { summaryPath, latestSummaryPath } = generateAiSummary(
                snapshotDir,
                metadata
            );

            console.log(`\nSnapshot captured successfully: ${snapshotId}`);
            console.log(`AI summary generated at: ${summaryPath}`);
            console.log(`Latest summary updated at: ${latestSummaryPath}`);

            console.log(
                `\nTo reference the specific snapshot in conversations, use:`
            );
            console.log(
                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/${metadata.lastSnapshot}`
            );

            console.log(`\nTo always reference the latest screenshots, use:`);
            console.log(
                `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
            );

            return {
                success: true,
                snapshotId,
                snapshotDir,
                latestDir: CONFIG.latestDir,
                summaryPath,
                latestSummaryPath,
            };
        } catch (error) {
            console.error("Error capturing snapshots for AI analysis:", error);
            return {
                success: false,
                error: error.message,
            };
        }
    }

    // Run the capture process
    captureForAiAnalysis();
    ```


    #### `dev-with-snapshots.js`

    ```javascript
    import { exec, spawn } from "child_process";
    import { promisify } from "util";
    import fs from "fs";
    import path from "path";
    import { fileURLToPath } from "url";
    import { createServer } from "http";

    const execAsync = promisify(exec);

    // Get current directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = path.resolve(__dirname, "..");

    // Configuration
    const CONFIG = {
        // Minimum time between snapshots (ms)
        snapshotCooldown: 30000, // 30 seconds

        // Port for the snapshot trigger server
        triggerPort: 5174,

        // Development server port
        devPort: 5173,

        // Latest screenshots directory
        latestDir: path.join(projectRoot, ".ai-analysis", "latest"),
    };

    // Track the last snapshot time
    let lastSnapshotTime = 0;

    /**
     * Starts the development server
     */
    function startDevServer() {
        console.log("Starting development server...");

        const devProcess = spawn("npm", ["run", "dev"], {
            cwd: projectRoot,
            stdio: "inherit",
            shell: true,
        });

        devProcess.on("error", (error) => {
            console.error("Error starting development server:", error);
        });

        return devProcess;
    }

    /**
     * Takes a snapshot if enough time has passed since the last one
     */
    async function takeSnapshotIfNeeded() {
        const now = Date.now();

        // Check if enough time has passed since the last snapshot
        if (now - lastSnapshotTime < CONFIG.snapshotCooldown) {
            console.log("Snapshot cooldown active, skipping...");
            return false;
        }

        // Update the last snapshot time
        lastSnapshotTime = now;

        try {
            console.log("Taking snapshot for AI analysis...");
            await execAsync(`npm run ai:snapshot`, { cwd: projectRoot });

            // Display the latest directory path for easy reference
            console.log("\n=== LATEST SCREENSHOTS READY FOR AI REFERENCE ===");
            console.log(
                `Path: /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
            );
            console.log("=================================================\n");

            return true;
        } catch (error) {
            console.error("Error taking snapshot:", error.message);
            return false;
        }
    }

    /**
     * Creates a simple HTTP server to trigger snapshots
     */
    function createTriggerServer() {
        const server = createServer(async (req, res) => {
            // Set CORS headers
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            res.setHeader("Access-Control-Allow-Headers", "Content-Type");

            // Handle preflight requests
            if (req.method === "OPTIONS") {
                res.writeHead(204);
                res.end();
                return;
            }

            // Handle snapshot trigger
            if (req.url === "/trigger-snapshot") {
                console.log("Snapshot triggered via HTTP request");

                const success = await takeSnapshotIfNeeded();

                res.writeHead(200, { "Content-Type": "application/json" });
                res.end(
                    JSON.stringify({
                        success,
                        message: success
                            ? "Snapshot taken successfully"
                            : "Snapshot skipped (cooldown active)",
                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                    })
                );
                return;
            }

            // Handle status check
            if (req.url === "/status") {
                res.writeHead(200, { "Content-Type": "application/json" });
                res.end(
                    JSON.stringify({
                        status: "running",
                        lastSnapshot: new Date(lastSnapshotTime).toISOString(),
                        cooldownActive:
                            Date.now() - lastSnapshotTime < CONFIG.snapshotCooldown,
                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                    })
                );
                return;
            }

            // Handle latest path request
            if (req.url === "/latest-path") {
                res.writeHead(200, { "Content-Type": "application/json" });
                res.end(
                    JSON.stringify({
                        latestPath: `/c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`,
                    })
                );
                return;
            }

            // Default response
            res.writeHead(404, { "Content-Type": "application/json" });
            res.end(JSON.stringify({ error: "Not found" }));
        });

        server.listen(CONFIG.triggerPort, () => {
            console.log(
                `Snapshot trigger server running at http://localhost:${CONFIG.triggerPort}`
            );
            console.log(
                `- To trigger a snapshot: http://localhost:${CONFIG.triggerPort}/trigger-snapshot`
            );
            console.log(
                `- To check status: http://localhost:${CONFIG.triggerPort}/status`
            );
            console.log(
                `- To get latest path: http://localhost:${CONFIG.triggerPort}/latest-path`
            );
        });

        return server;
    }

    /**
     * Creates a file watcher to trigger snapshots on changes
     */
    function createFileWatcher() {
        // Directories to watch
        const watchDirs = [path.join(projectRoot, "src")];

        // Extensions to watch
        const watchExtensions = [
            ".ts",
            ".tsx",
            ".js",
            ".jsx",
            ".css",
            ".scss",
            ".html",
        ];

        // Create watchers for each directory
        const watchers = watchDirs.map((dir) => {
            console.log(`Watching directory for changes: ${dir}`);

            return fs.watch(
                dir,
                { recursive: true },
                async (eventType, filename) => {
                    if (!filename) return;

                    // Check if the file extension should trigger a snapshot
                    const ext = path.extname(filename).toLowerCase();
                    if (!watchExtensions.includes(ext)) return;

                    console.log(`File changed: ${filename}`);
                    await takeSnapshotIfNeeded();
                }
            );
        });

        return watchers;
    }

    /**
     * Main function to run development with automatic snapshots
     */
    async function devWithSnapshots() {
        try {
            console.log("Starting development with automatic snapshots...");

            // Start the development server
            const devProcess = startDevServer();

            // Wait for the development server to start
            console.log("Waiting for development server to start...");
            await new Promise((resolve) => setTimeout(resolve, 5000));

            // Create the trigger server
            const triggerServer = createTriggerServer();

            // Create file watchers
            const watchers = createFileWatcher();

            // Take an initial snapshot
            console.log("Taking initial snapshot...");
            await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait for dev server to be fully ready
            await takeSnapshotIfNeeded();

            console.log("\nDevelopment with automatic snapshots is running!");
            console.log("- Development server: http://localhost:5173");
            console.log(
                "- Snapshot trigger: http://localhost:5174/trigger-snapshot"
            );
            console.log(
                "- File changes in src/ will automatically trigger snapshots"
            );
            console.log("- Latest screenshots are always available at:");
            console.log(
                `  /c%3A/Users/<USER>/Desktop/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/rl-website_web/project/.ai-analysis/latest`
            );
            console.log("- Press Ctrl+C to stop");

            // Handle process termination
            process.on("SIGINT", () => {
                console.log("Shutting down...");

                // Close file watchers
                watchers.forEach((watcher) => watcher.close());

                // Close trigger server
                triggerServer.close();

                // Kill development server
                devProcess.kill();

                process.exit(0);
            });
        } catch (error) {
            console.error("Error starting development with snapshots:", error);
        }
    }

    // Run the main function
    devWithSnapshots();
    ```


    #### `refresh-screenshots.js`

    ```javascript
    import { exec } from "child_process";
    import { promisify } from "util";
    import path from "path";
    import { fileURLToPath } from "url";

    const execAsync = promisify(exec);

    // Get current directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = path.resolve(__dirname, "..");

    /**
     * Runs a screenshot manager command
     * @param {string} command The command to run
     * @param {string[]} args Additional arguments
     */
    async function runScreenshotCommand(command, args = []) {
        const commandString = `node ${path.join(
            projectRoot,
            "scripts",
            "screenshot-manager.js"
        )} ${command} ${args.join(" ")}`;
        console.log(`Running: ${commandString}`);

        try {
            const { stdout, stderr } = await execAsync(commandString);
            if (stdout) console.log(stdout);
            if (stderr) console.error(stderr);
        } catch (error) {
            console.error(`Error running ${command}:`, error.message);
            throw error;
        }
    }

    /**
     * Refreshes screenshots by capturing, tidying, and cleaning up
     */
    async function refreshScreenshots() {
        try {
            console.log("Starting screenshot refresh process...");

            // Step 1: Capture new screenshots
            console.log("\n=== Step 1: Capturing new screenshots ===");
            await runScreenshotCommand("capture");

            // Step 2: Tidy up the screenshots directory
            console.log("\n=== Step 2: Tidying up screenshots directory ===");
            await runScreenshotCommand("tidy");

            // Step 3: Clean up old screenshots (keep last 7 days)
            console.log("\n=== Step 3: Cleaning up old screenshots ===");
            await runScreenshotCommand("clean", ["7"]);

            console.log("\nScreenshot refresh process completed successfully!");
            console.log(
                "You can view the latest screenshots in the screenshot-report.html file."
            );
        } catch (error) {
            console.error("Screenshot refresh process failed:", error.message);
        }
    }

    // Run the refresh process
    refreshScreenshots();
    ```


    #### `screenshot-manager.js`

    ```javascript
    import { exec } from "child_process";
    import { promisify } from "util";
    import fs from "fs";
    import path from "path";
    import { CONFIG, COMMANDS } from "./config/screenshot-config.js";
    import {
        captureScreenshots,
        cleanOldScreenshots,
        tidyScreenshots,
        ensureDirectories,
    } from "./utils/screenshot-utils.js";

    const execAsync = promisify(exec);

    /**
     * Displays help information
     */
    function showHelp() {
        console.log(`
    Screenshot Manager - Utility for managing website screenshots

    Usage:
      node screenshot-manager.js [command] [options]

    Commands:
      capture                 Capture screenshots now
      clean [days]           Clean screenshots older than specified days (default: ${CONFIG.defaultRetention})
      compare [dir1] [dir2]  Compare two screenshot directories
      schedule [interval]    Schedule automatic captures (interval in hours, default: 24)
      unschedule            Remove scheduled captures
      tidy                  Tidy up the screenshots directory
      help                  Show this help message

    Examples:
      node screenshot-manager.js capture
      node screenshot-manager.js clean 3
      node screenshot-manager.js schedule 12
      node screenshot-manager.js tidy
    `);
    }

    /**
     * Schedules automatic screenshot captures
     * @param {number} interval Interval in hours
     */
    async function scheduleCaptures(interval = 24) {
        try {
            const isWindows = process.platform === "win32";

            if (isWindows) {
                const scriptPath = path.join(
                    CONFIG.projectRoot,
                    "scripts",
                    "run-capture.bat"
                );
                const batchContent = `@echo off\ncd ${CONFIG.projectRoot}\nnode ${CONFIG.captureScript}\n`;
                fs.writeFileSync(scriptPath, batchContent);

                await execAsync(
                    `schtasks /create /tn "${CONFIG.windowsTaskName}" /tr "${scriptPath}" /sc HOURLY /mo ${interval} /f`
                );
                console.log(
                    `Scheduled screenshot capture every ${interval} hours using Windows Task Scheduler`
                );
            } else {
                const cronCommand = `0 */${interval} * * * cd ${CONFIG.projectRoot} && node ${CONFIG.captureScript}`;
                const { stdout: currentCrontab } = await execAsync(
                    'crontab -l 2>/dev/null || echo ""'
                );

                if (currentCrontab.includes(CONFIG.captureScript)) {
                    console.log("Screenshot capture is already scheduled");
                    return;
                }

                const newCrontab =
                    currentCrontab +
                    `\n# Ringerike Landskap Screenshot Capture\n${cronCommand}\n`;
                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");
                fs.writeFileSync(tempFile, newCrontab);
                await execAsync(`crontab ${tempFile}`);
                fs.unlinkSync(tempFile);

                console.log(
                    `Scheduled screenshot capture every ${interval} hours using crontab`
                );
            }
        } catch (error) {
            console.error("Error scheduling captures:", error.message);
        }
    }

    /**
     * Removes scheduled screenshot captures
     */
    async function unscheduleCaptures() {
        try {
            const isWindows = process.platform === "win32";

            if (isWindows) {
                try {
                    await execAsync(
                        `schtasks /delete /tn "${CONFIG.windowsTaskName}" /f`
                    );
                    console.log(
                        "Scheduled screenshot capture removed from Windows Task Scheduler"
                    );
                } catch (error) {
                    console.log("No scheduled task found");
                }
            } else {
                const { stdout: currentCrontab } = await execAsync(
                    'crontab -l 2>/dev/null || echo ""'
                );

                if (!currentCrontab.includes(CONFIG.captureScript)) {
                    console.log("No scheduled screenshot capture found");
                    return;
                }

                const newCrontab = currentCrontab
                    .split("\n")
                    .filter(
                        (line) =>
                            !line.includes(CONFIG.captureScript) &&
                            !line.includes(
                                "# Ringerike Landskap Screenshot Capture"
                            )
                    )
                    .join("\n");

                const tempFile = path.join(CONFIG.projectRoot, "temp-crontab");
                fs.writeFileSync(tempFile, newCrontab);
                await execAsync(`crontab ${tempFile}`);
                fs.unlinkSync(tempFile);

                console.log("Scheduled screenshot capture removed from crontab");
            }
        } catch (error) {
            console.error("Error removing scheduled captures:", error.message);
        }
    }

    /**
     * Main function to handle commands
     */
    async function main() {
        // Ensure directories exist
        ensureDirectories();

        const [command = COMMANDS.help, ...args] = process.argv.slice(2);

        switch (command) {
            case COMMANDS.capture:
                await captureScreenshots();
                break;

            case COMMANDS.clean:
                const days = parseInt(args[0]) || CONFIG.defaultRetention;
                await cleanOldScreenshots(days);
                break;

            case COMMANDS.schedule:
                const interval = parseInt(args[0]) || 24;
                await scheduleCaptures(interval);
                break;

            case COMMANDS.unschedule:
                await unscheduleCaptures();
                break;

            case COMMANDS.tidy:
                await tidyScreenshots();
                break;

            case COMMANDS.help:
            default:
                showHelp();
                break;
        }
    }

    main().catch((error) => {
        console.error("Error:", error);
        process.exit(1);
    });
    ```


    #### `tidy-screenshots.js`

    ```javascript
    import fs from "fs";
    import path from "path";
    import { fileURLToPath } from "url";

    // Get current directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = path.resolve(__dirname, "..");

    // Screenshot directory
    const screenshotsDir = path.join(projectRoot, "screenshots");

    /**
     * Creates a .gitignore file in the screenshots directory
     */
    function createGitignore() {
        const gitignorePath = path.join(screenshotsDir, ".gitignore");
        const gitignoreContent = `# Ignore all files in this directory except README.md and .gitignore
    *
    !README.md
    !.gitignore
    !.gitkeep

    # Allow the latest directory but ignore its contents
    !latest/
    latest/*
    !latest/.gitkeep

    # Allow the directory structure but not the actual screenshots
    !*/
    */*/
    `;

        fs.writeFileSync(gitignorePath, gitignoreContent);
        console.log("Created .gitignore file in screenshots directory");
    }

    /**
     * Creates .gitkeep files to preserve directory structure
     */
    function createGitkeepFiles() {
        // Create .gitkeep in the screenshots directory
        fs.writeFileSync(path.join(screenshotsDir, ".gitkeep"), "");

        // Create .gitkeep in the latest directory
        const latestDir = path.join(screenshotsDir, "latest");
        if (fs.existsSync(latestDir)) {
            fs.writeFileSync(path.join(latestDir, ".gitkeep"), "");

            // Create .gitkeep in viewport subdirectories
            ["mobile", "tablet", "desktop"].forEach((viewport) => {
                const viewportDir = path.join(latestDir, viewport);
                if (fs.existsSync(viewportDir)) {
                    fs.writeFileSync(path.join(viewportDir, ".gitkeep"), "");
                }
            });
        }

        console.log("Created .gitkeep files to preserve directory structure");
    }

    /**
     * Removes the legacy backup directory
     */
    function removeLegacyBackup() {
        const legacyBackupDir = path.join(screenshotsDir, "legacy-backup");

        if (fs.existsSync(legacyBackupDir)) {
            try {
                fs.rmSync(legacyBackupDir, { recursive: true, force: true });
                console.log("Removed legacy-backup directory");
            } catch (error) {
                console.error(
                    "Error removing legacy-backup directory:",
                    error.message
                );
            }
        } else {
            console.log("No legacy-backup directory found");
        }
    }

    /**
     * Removes any loose files in the screenshots directory
     */
    function removeLooseFiles() {
        const files = fs.readdirSync(screenshotsDir);

        const looseFiles = files.filter((file) => {
            const filePath = path.join(screenshotsDir, file);
            const isDirectory = fs.statSync(filePath).isDirectory();

            // Keep directories with timestamp format or 'latest'
            if (isDirectory) {
                return !(
                    file === "latest" ||
                    /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(file)
                );
            }

            // Keep README.md, .gitignore, and .gitkeep
            if (
                [
                    "README.md",
                    ".gitignore",
                    ".gitkeep",
                    "screenshot-report.html",
                ].includes(file)
            ) {
                return false;
            }

            // All other files should be removed
            return true;
        });

        if (looseFiles.length === 0) {
            console.log("No loose files found in screenshots directory");
            return;
        }

        console.log(
            `Found ${looseFiles.length} loose files in screenshots directory`
        );

        looseFiles.forEach((file) => {
            const filePath = path.join(screenshotsDir, file);
            try {
                fs.rmSync(filePath, { force: true });
                console.log(`Removed loose file: ${file}`);
            } catch (error) {
                console.error(`Error removing ${file}:`, error.message);
            }
        });
    }

    /**
     * Optimizes the screenshot report by reducing image sizes
     */
    function optimizeScreenshotReport() {
        const reportPath = path.join(screenshotsDir, "screenshot-report.html");

        if (!fs.existsSync(reportPath)) {
            console.log("Screenshot report not found");
            return;
        }

        try {
            let reportContent = fs.readFileSync(reportPath, "utf8");

            // Add lazy loading to images
            reportContent = reportContent.replace(
                /<img src="([^"]+)"/g,
                '<img src="$1" loading="lazy"'
            );

            // Add viewport meta tag for better mobile display if not already present
            if (!reportContent.includes("viewport")) {
                reportContent = reportContent.replace(
                    "<head>",
                    '<head>\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">'
                );
            }

            // Add a note about the tidied directory
            reportContent = reportContent.replace(
                '<p class="timestamp">Generated',
                '<p class="note">Screenshots directory has been tidied up for better organization.</p>\n        <p class="timestamp">Generated'
            );

            fs.writeFileSync(reportPath, reportContent);
            console.log("Optimized screenshot report");
        } catch (error) {
            console.error("Error optimizing screenshot report:", error.message);
        }
    }

    /**
     * Main function to tidy up the screenshots directory
     */
    function tidyScreenshotsDirectory() {
        console.log("Tidying up screenshots directory...");

        // Create .gitignore file
        createGitignore();

        // Create .gitkeep files
        createGitkeepFiles();

        // Remove legacy backup
        removeLegacyBackup();

        // Remove loose files
        removeLooseFiles();

        // Optimize screenshot report
        optimizeScreenshotReport();

        console.log("Screenshots directory tidied up successfully");
    }

    // Run the tidy function
    tidyScreenshotsDirectory();
    ```


    #### `config\screenshot-config.js`

    ```javascript
    import path from "path";
    import { fileURLToPath } from "url";

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const projectRoot = path.resolve(__dirname, "../..");

    export const CONFIG = {
        // Core paths
        projectRoot,
        screenshotsDir: path.join(projectRoot, "screenshots"),
        aiAnalysisDir: path.join(projectRoot, ".ai-analysis"),

        // Latest directories
        latestScreenshotsDir: path.join(projectRoot, "screenshots", "latest"),
        latestAiDir: path.join(projectRoot, ".ai-analysis", "latest"),

        // Scripts
        captureScript: path.join(projectRoot, "capture-website.js"),
        tidyScript: path.join(projectRoot, "scripts", "tidy-screenshots.js"),

        // Development settings
        devPort: 5173,
        triggerPort: 5174,
        snapshotCooldown: 30000, // 30 seconds

        // Screenshot settings
        defaultRetention: 7,
        watchExtensions: [".ts", ".tsx", ".js", ".jsx", ".css", ".scss", ".html"],

        // Task scheduling
        windowsTaskName: "RingerikeLandskapScreenshots",
        cronJobLabel: "# Ringerike Landskap Screenshot Capture",
    };

    export const COMMANDS = {
        capture: "capture",
        clean: "clean",
        compare: "compare",
        help: "help",
        schedule: "schedule",
        unschedule: "unschedule",
        tidy: "tidy",
    };
    ```


    #### `utils\screenshot-utils.js`

    ```javascript
    import { exec } from "child_process";
    import { promisify } from "util";
    import fs from "fs";
    import path from "path";
    import { CONFIG } from "../config/screenshot-config.js";

    const execAsync = promisify(exec);

    /**
     * Captures screenshots using the capture script
     */
    export async function captureScreenshots() {
        try {
            console.log("Capturing screenshots...");
            await execAsync(`node ${CONFIG.captureScript}`);
            console.log("Screenshots captured successfully");
            return true;
        } catch (error) {
            console.error("Error capturing screenshots:", error.message);
            return false;
        }
    }

    /**
     * Cleans up screenshots older than the specified number of days
     * @param {number} days Number of days to keep screenshots
     */
    export async function cleanOldScreenshots(days = CONFIG.defaultRetention) {
        try {
            const baseDir = CONFIG.screenshotsDir;
            if (!fs.existsSync(baseDir)) {
                console.log("Screenshots directory does not exist");
                return false;
            }

            const now = new Date().getTime();
            const maxAge = days * 24 * 60 * 60 * 1000;

            const dirs = fs
                .readdirSync(baseDir)
                .filter((name) => {
                    const fullPath = path.join(baseDir, name);
                    return (
                        fs.statSync(fullPath).isDirectory() &&
                        name !== "latest" &&
                        /^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$/.test(name)
                    );
                })
                .map((name) => ({
                    name,
                    path: path.join(baseDir, name),
                    time: fs.statSync(path.join(baseDir, name)).mtime.getTime(),
                }))
                .filter((dir) => now - dir.time > maxAge);

            if (dirs.length === 0) {
                console.log(`No screenshots older than ${days} days found`);
                return true;
            }

            console.log(
                `Found ${dirs.length} screenshot directories older than ${days} days`
            );

            dirs.forEach((dir) => {
                console.log(`Removing old snapshot: ${dir.name}`);
                fs.rmSync(dir.path, { recursive: true, force: true });
            });

            console.log(`Cleaned up ${dirs.length} old screenshot directories`);
            return true;
        } catch (error) {
            console.error("Error cleaning old screenshots:", error);
            return false;
        }
    }

    /**
     * Runs the tidy script to organize screenshots
     */
    export async function tidyScreenshots() {
        try {
            console.log("Tidying screenshots...");
            await execAsync(`node ${CONFIG.tidyScript}`);
            console.log("Screenshots tidied successfully");
            return true;
        } catch (error) {
            console.error("Error tidying screenshots:", error.message);
            return false;
        }
    }

    /**
     * Ensures screenshot directories exist
     */
    export function ensureDirectories() {
        const dirs = [
            CONFIG.screenshotsDir,
            CONFIG.aiAnalysisDir,
            CONFIG.latestScreenshotsDir,
            CONFIG.latestAiDir,
        ];

        dirs.forEach((dir) => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    ```
