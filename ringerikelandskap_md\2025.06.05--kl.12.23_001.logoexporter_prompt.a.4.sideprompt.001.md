Please clean up and polish this ascii representation of my ui/ux:

```
╔════════════════════════════════════════════════╗
║              Logo Exporter UI (Mobile)         ║
╠════════════════════════════════════════════════╣
║ URL                                            ║
║ ┌────────────────────────────────────────────┐ ║
║ │ https://ringerikelandskap.no/logoexporter │ ║
║ └────────────────────────────────────────────┘ ║
║                                                ║
║ Oppløsning                                     ║
║ ┌────────────────────┐                         ║
║ │ [ 2K        ▼ ]     │                         ║
║ └────────────────────┘                         ║
║                                                ║
║ Format                                         ║
║ ┌────────────────────┐                         ║
║ │ [ png       ▼ ]     │                         ║
║ └────────────────────┘                         ║
║                                                ║
║ ┌────────────────────────────────────────────┐ ║
║ │ ☑ Variation A  ─────────────┐              │ ║
║ │                │  [ Logo #1 ] │              │ ║
║ ├────────────────┴────────────┘              │ ║
║ │ ☐ Variation B  ─────────────┐              │ ║
║ │                │  [ Logo #2 ] │              │ ║
║ ├────────────────┴────────────┘              │ ║
║ │ ☐ ...                                      │ ║
║ └────────────────────────────────────────────┘ ║
║                                                ║
║ ┌────────────────────────────────────────────┐ ║
║ │         [ Process and Download ]           │ ║
║ └────────────────────────────────────────────┘ ║
╚════════════════════════════════════════════════╝
```

Here's a rough outline/draft of the idea:

* Scenario: `"I have a client that i've built a website for (https://www.ringerikelandskap.no/). i've also vectorized their logo, and the logo is available as svg in different variations, formats and styles. The client often wants different sizes and formats of their logo, i've decided to just add a simple subfolder on the client's website (e.g. "https://ringerikelandskap.no/logoexporter"). This way I can very efficiently set up something dynamic, that does exactly what the client needs, without making a mess in the existing codebase."`
* Proposed Structure: `"/logoexporter"`
* Accessible at `"https://ringerikelandskap.no/logoexporter"`
* Accepts query params:
  - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)
  - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital
  - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)
  - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)
  - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)
  - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)
