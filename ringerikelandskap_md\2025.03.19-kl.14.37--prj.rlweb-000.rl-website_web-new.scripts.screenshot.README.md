# Screenshot Utility

A utility for capturing screenshots of the Ringerike Landskap website at various viewport sizes and generating a visual report.

## Features

-   Automatically captures screenshots of all pages at multiple viewport sizes
-   Organizes screenshots in date-based directories (YYYY-MM-DD format)
-   Generates an HTML report with all screenshots organized by page and viewport
-   Supports selective capturing of specific pages and viewports
-   Provides detailed logging and error reporting
-   Automatically opens the report in your default browser
-   Smart automation with "once-per-day" option for scheduled runs

## Usage

The screenshot utility has been designed to be simple to use while providing flexibility when needed.

### Basic Usage

Capture all screenshots and generate a report:

```bash
npm run screenshot
```

This will:

1. Create a date-based directory (e.g., `screenshots/2023-09-15/`)
2. Capture screenshots of all pages at all viewport sizes in that directory
3. Generate an HTML report in the same directory
4. Automatically open the report in your default browser

### Advanced Options

You can pass options to the screenshot utility to customize its behavior:

```bash
# Only capture desktop screenshots (common use case)
npm run screenshot -- viewports=desktop

# Only capture specific pages
npm run screenshot -- pages=home,about

# Only capture specific viewports
npm run screenshot -- viewports=mobile,desktop

# Combine multiple options
npm run screenshot -- pages=home,services viewports=mobile

# Skip screenshot capture if today's screenshots already exist
npm run screenshot -- once-per-day

# Prevent automatically opening the report
npm run screenshot -- no-open

# Display help information
npm run screenshot -- help
```

### Automation Options

The screenshot utility includes features specifically designed for automated or scheduled runs:

#### Once-Per-Day Mode

```bash
npm run screenshot -- once-per-day
```

This option:

-   Checks if the current day's directory already has screenshots
-   If screenshots exist, skips capturing new ones
-   If an existing report is found, can still open it (unless `no-open` is specified)
-   Perfect for cron jobs or scheduled tasks that run multiple times per day

Example scheduled task (using Windows Task Scheduler or cron):

```
# Run every hour but only capture screenshots once per day
0 * * * * cd /path/to/project && npm run screenshot -- once-per-day viewports=desktop
```

### Available Pages

-   `home`: The homepage
-   `services`: The "What We Do" (Hva vi gjør) page
-   `projects`: The Projects (Prosjekter) page
-   `about`: The "About Us" (Hvem er vi) page
-   `contact`: The Contact (Kontakt) page

### Available Viewports

-   `mobile`: 375×667px
-   `tablet`: 768×1024px
-   `desktop`: 1440×900px

## Output

The utility creates a `screenshots` directory in the project root, with:

-   Date-based subdirectories (e.g., `screenshots/2023-09-15/`)
-   PNG screenshots of each page at each viewport size
-   HTML snapshots of the pages at the time of capture
-   An HTML report that displays all screenshots in an organized way

This date-based organization makes it easy to:

-   Track changes over time
-   Compare screenshots from different dates
-   Keep a historical record of website appearance
-   Quickly find the most recent screenshots

## Troubleshooting

-   Make sure the development server is running (`npm run dev`) before using the screenshot utility
-   If screenshots are missing content, try increasing the wait times in the `capture.js` file
-   For advanced customization, you can modify the viewport sizes and page configurations in `capture.js`
-   If the report doesn't open automatically, check that you have the `open` npm package installed
