# TL;DR

Working on a complex (`React 18/TypeScript/Vite/Tailwind CSS`) codebase (`rl-website-v1-003`) that recently migrated to a unified API/data access layer, now suffering dynamic filtering bugs for services and projects. The highest order goals are to resolve new dynamic data filtering bugs that arose from architectural improvements, prevent further codebase sprawl and duplication, and ensure that the code is both functionally correct and structurally sound for ongoing development and integration. This approach is intended to ensure maximum clarity, maintainability, and future scalability of the project.

# About

The current folder contains the two directories `rl-website-v1-002` and `rl-website-v1-003`, each of these folders contain the project/sourcecode a landingpage (currently in develpment) for a norwegian landscaping company. The codebase (website's techstack) consists of `React 18, TypeScript, Vite, and Tailwind CSS`.

# Context

The intent when migrating from `rl-website-v1-002` to `rl-website-v1-003` was to standardize all application data access through a single API layer for consistency, maintainability, and future integration, replacing direct data imports with async API calls and hooks. After performing this migration/consolidation I've notice some of issues has occurred (in `rl-website-v1-003`); specifically related to the dynamic filtering functionality (to filter or adjust what data is shown-based on services, projects and service areas against seasonal context) for "Tjenester" ("/hva") and "Prosjekter" ("/prosjekter").

```json
"meta_relationships": {
    "migration": {
        "from": "rl-website-v1-002",
        "to": "rl-website-v1-003",
        "goal": "unified API/data access layer for consistency and maintainability",
        "result": "introduced dynamic filtering bugs"
    },
    "current_state": {
        "codebase": "React 18, TypeScript, Vite, Tailwind CSS",
        "progress": "mostly complete except for data filtering bugs and final file structure consolidation"
    },
    "problems": {
        "dynamic_filtering": "bugs affecting services and projects filtering (season/context aware)",
        "complexity": "codebase sprawl, duplication, loss of clear component relationships"
    },
    "philosophy": {
        "filestructure_first": "dirtree as the source of truth for navigating/refining the codebase",
        "reasoning": "maintain clarity and control in complex SPA/frontend architectures"
    },
    "best_practices": {
        "consolidation": "reduce bloat, dedupe logic, ensure directory/file consistency",
        "mapping": "always interpret component relationships through the lens of the dirtree"
    }
}
```

# Status

The website is mostly complete, the remaining tasks consists mainly of consolidation of the project/filestructure (ensure concistency, reduce the bloat in the codebase (too many files and/or too much spread), de-duplication, etc), but **most of all, make it work (we need to make it function exactly as intended) without issues**. Although `rl-website-v1-003` has some issues which it didn't have in `rl-website-v1-002`, `rl-website-v1-003` is farther along and structurally sounder due to the refactor; fixing issues here is preferred over reverting to `rl-website-v1-003`, despite regressions.

# Challenge

Given the challenge of perpetual complexity with codebases such as these (using React 18, TypeScript, Vite, and Tailwind CSS) it can be difficult to navigate for autonomous llm-agents (ai coding-assistants such as cursor ai), which naturally leads to accidentally loosing control of how each component relates to each other - which again leads to loosing control over to codebase (chaos grows, lots of duplication, cross-references, etc).

# Constant

In order to circumvent this challenge (as stated above) it's imperative to work in a principled **filestructure first** manner. The philosophy behind this concept that the filestructure can be expressed as a dirtree, this dirtree contains the **actual "map"** for the codebase as a whole. By **rooting yourself** to this source of truth (in **any-and-all** thought processes), you can more easily keep track of everything (and how each component ties together)-because regardless of underlying complexity, you will always interpret the data through the "filter" of the **inherent filestructure (dirtree)**. It's integral to be rooted in this concept (dirtree) in order to be capable of working with complex codebases of any kind.

# Dirtree

As mentioned above, the filestructure (dirtree) represents the ultimate **truth**, and even though it doesn't directly show the intertwined relationships between themselves, they reflect enough to always be able to view the complexity through a simpler lense. The dirtree serves as the definitive reference point for understanding, navigating, and refining the codebase. This approach should be applied to maintain clarity, avert code duplication and ensure consistency.

## Examples: Dirtree

**Directories Only (`rl-website-v1-003`):**
```
   ├── config
   │   └── env
   ├── public
   │   ├── images
   │   │   ├── categorized
   │   │   │   ├── belegg
   │   │   │   ├── ferdigplen
   │   │   │   ├── hekk
   │   │   │   ├── kantstein
   │   │   │   ├── platting
   │   │   │   ├── stål
   │   │   │   ├── støttemur
   │   │   │   ├── trapp-repo
   │   │   ├── site
   │   │   └── team
   ├── scripts
   ├── src
   │   ├── app
   │   ├── content
   │   │   ├── locations
   │   │   ├── team
   │   ├── data
   │   ├── docs
   │   ├── layout
   │   ├── lib
   │   │   ├── api
   │   │   ├── config
   │   │   ├── constants
   │   │   ├── context
   │   │   ├── hooks
   │   │   ├── types
   │   │   ├── utils
   │   ├── sections
   │   │   ├── 10-home
   │   │   ├── 20-about
   │   │   ├── 30-services
   │   │   │   ├── components
   │   │   ├── 40-projects
   │   │   ├── 50-testimonials
   │   │   └── 60-contact
   │   ├── styles
   │   ├── ui
   │   │   ├── Form
```

**Specific Filetypes (json in `rl-website-v1-003`):**
```
   rl-website-v1-003/package-lock.json
   rl-website-v1-003/package.json
   rl-website-v1-003/tsconfig.json
   rl-website-v1-003/tsconfig.node.json
   rl-website-v1-003/vercel.json
```

**Specific Filetypes (js in `rl-website-v1-003`):**
```
   rl-website-v1-003/eslint.config.js
   rl-website-v1-003/postcss.config.js
   rl-website-v1-003/scripts/validate-env-files.js
```

**Comparison/Summary (current/root dirtree):**
```
   | directory             | .tsx | .ts | .js | .md | .css | .json | etc |
   | ├── rl-website-v1-002 | 48   | 41  | 4   | 4   | 4    | 5     | ... |
   | └── rl-website-v1-003 | 36   | 42  | xx  | 7   | 4    | 5     | ... |
```

**Specific Filetypes by Depth (.tsx in `rl-website-v1-003`):**
```
| Depth:0            | Depth:1 | Depth:2        | Depth:3             | Depth:4                      | Depth:5         |
| ------------------ | ------- | -------------- | ------------------- | ---------------------------- | --------------- |
| tailwind.config.js |         |                |                     |                              |                 |
|                    | src     | app            | index.tsx           |                              |                 |
|                    | src     | layout         | Footer.tsx          |                              |                 |
|                    | src     | layout         | Header.tsx          |                              |                 |
|                    | src     | layout         | Meta.tsx            |                              |                 |
|                    | src     | lib            | context             | AppContext.tsx               |                 |
|                    | src     | main.tsx       |                     |                              |                 |
|                    | src     | sections       | 10-home             | FilteredServicesSection.tsx  |                 |
|                    | src     | sections       | 10-home             | index.tsx                    |                 |
|                    | src     | sections       | 10-home             | SeasonalProjectsCarousel.tsx |                 |
|                    | src     | sections       | 20-about            | index.tsx                    |                 |
|                    | src     | sections       | 30-services         | components                   | ServiceCard.tsx |
|                    | src     | sections       | 30-services         | detail.tsx                   |                 |
|                    | src     | sections       | 30-services         | index.tsx                    |                 |
|                    | src     | sections       | 40-projects         | detail.tsx                   |                 |
|                    | src     | sections       | 40-projects         | index.tsx                    |                 |
|                    | src     | sections       | 40-projects         | ProjectCard.tsx              |                 |
|                    | src     | sections       | 40-projects         | ProjectFilter.tsx            |                 |
|                    | src     | sections       | 40-projects         | ProjectGallery.tsx           |                 |
|                    | src     | sections       | 40-projects         | ProjectGrid.tsx              |                 |
|                    | src     | sections       | 40-projects         | ProjectsCarousel.tsx         |                 |
|                    | src     | sections       | 50-testimonials     | AverageRating.tsx            |                 |
|                    | src     | sections       | 50-testimonials     | Testimonial.tsx              |                 |
|                    | src     | sections       | 50-testimonials     | TestimonialFilter.tsx        |                 |
|                    | src     | sections       | 50-testimonials     | TestimonialSlider.tsx        |                 |
|                    | src     | sections       | 50-testimonials     | TestimonialsPage.tsx         |                 |
|                    | src     | sections       | 50-testimonials     | TestimonialsSchema.tsx       |                 |
|                    | src     | sections       | 60-contact          | index.tsx                    |                 |
|                    | src     | src.dirtree.md |                     |                              |                 |
|                    | src     | ui             | Button.tsx          |                              |                 |
|                    | src     | ui             | Card.tsx            |                              |                 |
|                    | src     | ui             | Container.tsx       |                              |                 |
|                    | src     | ui             | ContentGrid.tsx     |                              |                 |
|                    | src     | ui             | Form                | Input.tsx                    |                 |
|                    | src     | ui             | Form                | Select.tsx                   |                 |
|                    | src     | ui             | Form                | Textarea.tsx                 |                 |
|                    | src     | ui             | Hero.tsx            |                              |                 |
|                    | src     | ui             | Icon.tsx            |                              |                 |
|                    | src     | ui             | Intersection.tsx    |                              |                 |
|                    | src     | ui             | Loading.tsx         |                              |                 |
|                    | src     | ui             | Logo.tsx            |                              |                 |
|                    | src     | ui             | Notifications.tsx   |                              |                 |
|                    | src     | ui             | PageSection.tsx     |                              |                 |
|                    | src     | ui             | SeasonalCTA.tsx     |                              |                 |
|                    | src     | ui             | SectionHeading.tsx  |                              |                 |
|                    | src     | ui             | ServiceAreaList.tsx |                              |                 |
|                    | src     | ui             | Skeleton.tsx        |                              |                 |
|                    | src     | ui             | Transition.tsx      |                              |                 |
```

# Prioritize

- Systematically reference and analyze the project's current file structure (dirtree) as the single source of truth for all code navigation, analysis, and proposed refactoring actions.
- When consolidating post-API data filtering logic, locate all instances (via the filesystem view) where similar or duplicate data filtering routines exist across React components and utility files.
- Safely consolidate these filtering routines into a single, well-typed, reusable TypeScript utility module (e.g., src/utils/dataFilter.ts), ensuring that all dependencies and usages are updated atomically to reference the consolidated function.
- Implement a codemod or validate via a script that deprecated/duplicate filter code and any intermediary scripts/files are removed after consolidation to prevent codebase bloat and inconsistencies.
- Mandate post-consolidation validation through end-to-end/integration tests specifically targeting filtered data flows through the API and into React state, comparing outputs before and after the change.
- Enable continuous codebase navigation and double-checking by running file-level and behavioral snapshot tests and by requiring automated reports that summarize refactored file impacts, ensuring any change consequences are visible and understood before and after merging.
- Emphasize consistency in code style and conventions by referencing a well-documented style guide stored at the project root (e.g., CODE_STYLE.md) and tasking AI assistants always to reconcile code output with this guide.
- All code analysis, navigation, and proposed changes must be grounded in an accurate, up-to-date understanding of the project’s file tree and structure.
- Coding style prioritizes clarity, simplicity, elegance, precision, and structured readability, explicitly preferring self-explanatory code over excessive comments.
- A focus is placed on identifying and safely consolidating or de-duplicating filtered data functionality post-API layer consolidation, improving maintainability and preventing unnecessary complexity.
- The process for improvements and refactoring must be methodical, predictable, and safe to avoid errors, uncontrolled complexity, or residual/dead code.
- Implementation must maintain robust debug-ability, data integrity, and should include mechanisms to visually or programmatically confirm changes.
- Only the most impactful, lowest-effort change should be made at each step, ensuring each consolidation yields discernible value with minimum risk.
- Explicit guardrails and checkpoints are neededto confirm the correctness of each change, ensuring autonomous actions can be trusted and validated.

# Guidelines

- Deeply analyze the Codebase to acquire comprehensive understanding.
- Augment current understanding by anchoring decisions to the principles of clarity, simplicity, elegance, precision, and structure.
- Prioritize coding styles that enhance readability and self-explanatory code; minimize reliance on excessive commenting.
- Identify the single most impactful element in the Codebase that, when consolidated, offers the greatest benefit in cleanup, de-duplication, or consolidation with minimal change required.
- Develop a method for applying consolidation across the Codebase in a safe, systematic, and seamless manner to ensure predictable and consistent improvements.
- Prevent the proliferation of temporary scripts/files by removing such artifacts post-consolidation.
- Enforce measures to avert uncontrolled complexity and maintain a clean project environment.
- Guarantee that refinements do not introduce errors, especially in preparation for the website launch.
- Establish guardrails that allow autonomous validation of actions and visibility into the results of codebase modifications.
- Implement steps sequentially, performing one targeted consolidation at a time, always prioritizing the action with the highest return on investment.
- Ensure all consolidation and de-duplication preserves data integrity throughout the process.
- Proceed with meticulous verification to avoid any missteps in each consolidation effort.

# Goal

Your ultimate goal is to fix the issues with `rl-website-v1-003`, and to do so you need to utilize simple and effective tools, utilities and mcp's (e.g. `browser-tools mcp`). **If** you create temporary files to perform tasks on the codebase, you need to make sure to do so in a concistent manner and to always clean up after yourself (i.e. remove unused remains/traces after successfull consolidation and testing of the website).

## Current filestructure

```
    └── dev_rl-website
        ├── rl-website-v1-002
        │   ├── config
        │   │   └── env
        │   │       ├── .env.development
        │   │       ├── .env.example
        │   │       ├── .env.production
        │   │       ├── .env.staging
        │   │       └── README.md
        │   ├── public
        │   │   ├── images
        │   │   │   ├── categorized
        │   │   │   │   ├── belegg
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5280.webp
        │   │   │   │   ├── ferdigplen
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_1912.webp
        │   │   │   │   ├── hekk
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_3077.webp
        │   │   │   │   ├── kantstein
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4991.webp
        │   │   │   │   ├── platting
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4188.webp
        │   │   │   │   ├── stål
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5346.webp
        │   │   │   │   ├── støttemur
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4154.webp
        │   │   │   │   ├── trapp-repo
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5317.webp
        │   │   │   │   ├── ... truncated for brevity
        │   │   │   ├── site
        │   │   │   │   ├── hero-corten-steel.webp
        │   │   │   │   ├── hero-granite.webp
        │   │   │   │   ├── hero-grass.webp
        │   │   │   │   ├── hero-main.webp
        │   │   │   │   ├── hero-prosjekter.webp
        │   │   │   │   └── hero-ringerike.webp
        │   │   │   └── team
        │   │   │       ├── firma.webp
        │   │   │       ├── jan.webp
        │   │   │       └── kim.webp
        │   │   ├── .htaccess
        │   │   ├── _redirects
        │   │   ├── favicon.svg
        │   │   ├── robots.txt
        │   │   ├── site.webmanifest
        │   │   ├── sitemap.xml
        │   │   └── web.config
        │   ├── scripts
        │   │   └── validate-env-files.js
        │   ├── src
        │   │   ├── app
        │   │   │   └── index.tsx
        │   │   ├── content
        │   │   │   ├── locations
        │   │   │   │   └── index.ts
        │   │   │   ├── projects
        │   │   │   │   └── index.ts
        │   │   │   ├── services
        │   │   │   │   └── index.ts
        │   │   │   ├── team
        │   │   │   │   └── index.ts
        │   │   │   ├── testimonials
        │   │   │   │   └── index.ts
        │   │   │   └── index.ts
        │   │   ├── data
        │   │   │   ├── projects.ts
        │   │   │   ├── services.ts
        │   │   │   └── testimonials.ts
        │   │   ├── docs
        │   │   │   └── SEO_USAGE.md
        │   │   ├── layout
        │   │   │   ├── Footer.tsx
        │   │   │   ├── Header.tsx
        │   │   │   ├── Meta.tsx
        │   │   │   └── index.ts
        │   │   ├── lib
        │   │   │   ├── api
        │   │   │   │   └── index.ts
        │   │   │   ├── config
        │   │   │   │   ├── images.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── paths.ts
        │   │   │   │   └── site.ts
        │   │   │   ├── context
        │   │   │   │   └── AppContext.tsx
        │   │   │   ├── hooks
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── useAnalytics.ts
        │   │   │   │   ├── useData.ts
        │   │   │   │   ├── useEventListener.ts
        │   │   │   │   └── useMediaQuery.ts
        │   │   │   ├── types
        │   │   │   │   ├── components.ts
        │   │   │   │   ├── content.ts
        │   │   │   │   └── index.ts
        │   │   │   ├── utils
        │   │   │   │   ├── analytics.ts
        │   │   │   │   ├── dom.ts
        │   │   │   │   ├── formatting.ts
        │   │   │   │   ├── images.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── paths.ts
        │   │   │   │   ├── seasonal.ts
        │   │   │   │   ├── seo.ts
        │   │   │   │   ├── strings.ts
        │   │   │   │   └── validation.ts
        │   │   │   ├── README.md
        │   │   │   ├── config.ts
        │   │   │   └── constants.ts
        │   │   ├── sections
        │   │   │   ├── 10-home
        │   │   │   │   ├── FilteredServicesSection.tsx
        │   │   │   │   ├── SeasonalProjectsCarousel.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 20-about
        │   │   │   │   └── index.tsx
        │   │   │   ├── 30-services
        │   │   │   │   ├── components
        │   │   │   │   │   ├── ServiceCard.tsx
        │   │   │   │   │   ├── ServiceFeature.tsx
        │   │   │   │   │   └── ServiceGrid.tsx
        │   │   │   │   ├── data.ts
        │   │   │   │   ├── detail.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 40-projects
        │   │   │   │   ├── ProjectCard.tsx
        │   │   │   │   ├── ProjectFilter.tsx
        │   │   │   │   ├── ProjectGallery.tsx
        │   │   │   │   ├── ProjectGrid.tsx
        │   │   │   │   ├── ProjectsCarousel.tsx
        │   │   │   │   ├── detail.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 50-testimonials
        │   │   │   │   ├── AverageRating.tsx
        │   │   │   │   ├── Testimonial.tsx
        │   │   │   │   ├── TestimonialFilter.tsx
        │   │   │   │   ├── TestimonialSlider.tsx
        │   │   │   │   ├── TestimonialsPage.tsx
        │   │   │   │   ├── TestimonialsSchema.tsx
        │   │   │   │   └── index.ts
        │   │   │   └── 60-contact
        │   │   │       └── index.tsx
        │   │   ├── styles
        │   │   │   ├── animations.css
        │   │   │   ├── base.css
        │   │   │   └── utilities.css
        │   │   ├── ui
        │   │   │   ├── Form
        │   │   │   │   ├── Input.tsx
        │   │   │   │   ├── Select.tsx
        │   │   │   │   ├── Textarea.tsx
        │   │   │   │   └── index.ts
        │   │   │   ├── Button.tsx
        │   │   │   ├── Card.tsx
        │   │   │   ├── Container.tsx
        │   │   │   ├── ContentGrid.tsx
        │   │   │   ├── Hero.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Intersection.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   ├── Logo.tsx
        │   │   │   ├── Notifications.tsx
        │   │   │   ├── PageSection.tsx
        │   │   │   ├── SeasonalCTA.tsx
        │   │   │   ├── SectionHeading.tsx
        │   │   │   ├── ServiceAreaList.tsx
        │   │   │   ├── Skeleton.tsx
        │   │   │   ├── Transition.tsx
        │   │   │   └── index.ts
        │   │   ├── index.css
        │   │   ├── main.tsx
        │   │   └── vite-env.d.ts
        │   ├── .gitignore
        │   ├── README.md
        │   ├── eslint.config.js
        │   ├── index.html
        │   ├── package-lock.json
        │   ├── package.json
        │   ├── postcss.config.js
        │   ├── tailwind.config.js
        │   ├── tsconfig.json
        │   ├── tsconfig.node.json
        │   ├── vercel.json
        │   ├── vite.config.ts
        │   └── vite.svg
        ├── rl-website-v1-003
        │   ├── config
        │   │   └── env
        │   │       ├── .env.development
        │   │       ├── .env.example
        │   │       ├── .env.production
        │   │       ├── .env.staging
        │   │       └── README.md
        │   ├── public
        │   │   ├── images
        │   │   │   ├── categorized
        │   │   │   │   ├── belegg
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5280.webp
        │   │   │   │   ├── ferdigplen
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_1912.webp
        │   │   │   │   ├── hekk
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_3077.webp
        │   │   │   │   ├── kantstein
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4991.webp
        │   │   │   │   ├── platting
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4188.webp
        │   │   │   │   ├── stål
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5346.webp
        │   │   │   │   ├── støttemur
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_4154.webp
        │   │   │   │   ├── trapp-repo
        │   │   │   │   │   ├── ... truncated for brevity
        │   │   │   │   │   └── IMG_5317.webp
        │   │   │   │   ├── ... truncated for brevity
        │   │   │   ├── site
        │   │   │   │   ├── hero-corten-steel.webp
        │   │   │   │   ├── hero-granite.webp
        │   │   │   │   ├── hero-grass.webp
        │   │   │   │   ├── hero-main.webp
        │   │   │   │   ├── hero-prosjekter.webp
        │   │   │   │   └── hero-ringerike.webp
        │   │   │   └── team
        │   │   │       ├── firma.webp
        │   │   │       ├── jan.webp
        │   │   │       └── kim.webp
        │   │   ├── .htaccess
        │   │   ├── _redirects
        │   │   ├── favicon.svg
        │   │   ├── robots.txt
        │   │   ├── site.webmanifest
        │   │   ├── sitemap.xml
        │   │   └── web.config
        │   ├── scripts
        │   │   └── validate-env-files.js
        │   ├── src
        │   │   ├── app
        │   │   │   └── index.tsx
        │   │   ├── content
        │   │   │   ├── locations
        │   │   │   │   └── index.ts
        │   │   │   ├── team
        │   │   │   │   └── index.ts
        │   │   │   └── index.ts
        │   │   ├── data
        │   │   │   ├── projects.ts
        │   │   │   ├── services.ts
        │   │   │   └── testimonials.ts
        │   │   ├── docs
        │   │   │   ├── API_MIGRATION_PLAN.md
        │   │   │   └── SEO_USAGE.md
        │   │   ├── layout
        │   │   │   ├── Footer.tsx
        │   │   │   ├── Header.tsx
        │   │   │   ├── Meta.tsx
        │   │   │   └── index.ts
        │   │   ├── lib
        │   │   │   ├── api
        │   │   │   │   ├── README.md
        │   │   │   │   ├── enhanced.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   └── sync.ts
        │   │   │   ├── config
        │   │   │   │   ├── images.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── paths.ts
        │   │   │   │   └── site.ts
        │   │   │   ├── constants
        │   │   │   │   ├── data.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   └── site.ts
        │   │   │   ├── context
        │   │   │   │   └── AppContext.tsx
        │   │   │   ├── hooks
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── useAnalytics.ts
        │   │   │   │   ├── useData.ts
        │   │   │   │   ├── useEventListener.ts
        │   │   │   │   └── useMediaQuery.ts
        │   │   │   ├── types
        │   │   │   │   ├── components.ts
        │   │   │   │   ├── content.ts
        │   │   │   │   └── index.ts
        │   │   │   ├── utils
        │   │   │   │   ├── analytics.ts
        │   │   │   │   ├── debug.ts
        │   │   │   │   ├── dom.ts
        │   │   │   │   ├── formatting.ts
        │   │   │   │   ├── images.ts
        │   │   │   │   ├── index.ts
        │   │   │   │   ├── paths.ts
        │   │   │   │   ├── seasonal.ts
        │   │   │   │   ├── seo.ts
        │   │   │   │   ├── strings.ts
        │   │   │   │   └── validation.ts
        │   │   │   ├── README.md
        │   │   │   └── config.ts
        │   │   ├── sections
        │   │   │   ├── 10-home
        │   │   │   │   ├── FilteredServicesSection.tsx
        │   │   │   │   ├── SeasonalProjectsCarousel.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 20-about
        │   │   │   │   └── index.tsx
        │   │   │   ├── 30-services
        │   │   │   │   ├── components
        │   │   │   │   │   └── ServiceCard.tsx
        │   │   │   │   ├── detail.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 40-projects
        │   │   │   │   ├── ProjectCard.tsx
        │   │   │   │   ├── ProjectFilter.tsx
        │   │   │   │   ├── ProjectGallery.tsx
        │   │   │   │   ├── ProjectGrid.tsx
        │   │   │   │   ├── ProjectsCarousel.tsx
        │   │   │   │   ├── detail.tsx
        │   │   │   │   └── index.tsx
        │   │   │   ├── 50-testimonials
        │   │   │   │   ├── AverageRating.tsx
        │   │   │   │   ├── Testimonial.tsx
        │   │   │   │   ├── TestimonialFilter.tsx
        │   │   │   │   ├── TestimonialSlider.tsx
        │   │   │   │   ├── TestimonialsPage.tsx
        │   │   │   │   ├── TestimonialsSchema.tsx
        │   │   │   │   └── index.ts
        │   │   │   └── 60-contact
        │   │   │       └── index.tsx
        │   │   ├── styles
        │   │   │   ├── animations.css
        │   │   │   ├── base.css
        │   │   │   └── utilities.css
        │   │   ├── ui
        │   │   │   ├── Form
        │   │   │   │   ├── Input.tsx
        │   │   │   │   ├── Select.tsx
        │   │   │   │   ├── Textarea.tsx
        │   │   │   │   └── index.ts
        │   │   │   ├── Button.tsx
        │   │   │   ├── Card.tsx
        │   │   │   ├── Container.tsx
        │   │   │   ├── ContentGrid.tsx
        │   │   │   ├── Hero.tsx
        │   │   │   ├── Icon.tsx
        │   │   │   ├── Intersection.tsx
        │   │   │   ├── Loading.tsx
        │   │   │   ├── Logo.tsx
        │   │   │   ├── Notifications.tsx
        │   │   │   ├── PageSection.tsx
        │   │   │   ├── SeasonalCTA.tsx
        │   │   │   ├── SectionHeading.tsx
        │   │   │   ├── ServiceAreaList.tsx
        │   │   │   ├── Skeleton.tsx
        │   │   │   ├── Transition.tsx
        │   │   │   └── index.ts
        │   │   ├── index.css
        │   │   ├── main.tsx
        │   │   └── vite-env.d.ts
        │   ├── .gitignore
        │   ├── README.md
        │   ├── eslint.config.js
        │   ├── index.html
        │   ├── package-lock.json
        │   ├── package.json
        │   ├── postcss.config.js
        │   ├── tailwind.config.js
        │   ├── tsconfig.json
        │   ├── tsconfig.node.json
        │   ├── vercel.json
        │   ├── vite.config.ts
        │   ├── vite.svg
        │   └── website.dirtree.md
        └── SCENARIO.md
```
