# Structure Map: Ringerike Landskap Website

## Current Structure Assessment

The project currently exhibits a mixed structure with elements of both feature-based and layer-based organization. There are several inconsistencies and duplications that reduce clarity and maintainability.

### Key Structural Issues

1. **Type Definition Fragmentation**
   - `/types/` and `/lib/types/` contain overlapping definitions
   - Duplicate domain models across files

2. **Front-End Layer Mixing**
   - Components exist in both `/components/` and `/features/`
   - No clear boundary between generic UI and domain components

3. **Utility Function Dispersion**
   - Utils exist in `/lib/utils/`, `/lib/utils.ts`, and `/utils/`
   - Multiple implementations of similar functionality

4. **Hook Redundancy**
   - Hooks defined in `/hooks/`, `/lib/hooks/`, and `/lib/hooks.ts`
   - Duplicate implementations like `useMediaQuery`

5. **Content Organization Inconsistency**
   - Content exists in both `/data/` and `/content/`
   - Unclear separation between static content and data models

## Target Structure

```
src/
├── api/                  # API layer (unified)
│   ├── client.ts         # API client setup
│   ├── endpoints/        # API endpoints by domain
│   └── index.ts          # Public API interface
│
├── assets/               # Static assets
│   ├── images/
│   └── icons/
│
├── config/               # Application configuration
│   ├── constants.ts      # App constants
│   ├── site.ts           # Site metadata
│   ├── paths.ts          # URL paths
│   └── index.ts
│
├── features/             # Domain-specific features
│   ├── common/           # Common UI elements
│   │   ├── components/
│   │   └── hooks/
│   │
│   ├── layout/           # Layout components
│   │   ├── components/
│   │   └── hooks/
│   │
│   ├── projects/         # Projects feature
│   │   ├── components/   # Project-specific UI
│   │   ├── hooks/        # Project-specific hooks
│   │   ├── api.ts        # Project-specific API
│   │   ├── types.ts      # Project-specific types
│   │   └── index.ts
│   │
│   ├── services/         # Services feature
│   │   └── ...
│   │
│   └── testimonials/     # Testimonials feature
│       └── ...
│
├── hooks/                # Global hooks
│   ├── domain/           # Domain-specific hooks
│   ├── ui/               # UI-related hooks
│   ├── form/             # Form-related hooks
│   ├── legacy/           # Legacy hooks (to be migrated)
│   └── index.ts
│
├── pages/                # Page components
│   ├── HomePage.tsx
│   ├── ProjectsPage.tsx
│   ├── ServicesPage.tsx
│   ├── TestimonialsPage.tsx
│   └── ...
│
├── types/                # Type definitions (unified)
│   ├── common.ts         # Shared primitive types
│   ├── components.ts     # Component props
│   ├── domain/           # Domain-specific types
│   │   ├── project.ts
│   │   ├── service.ts
│   │   └── testimonial.ts
│   └── index.ts
│
├── utils/                # Utility functions (unified)
│   ├── formatting/
│   ├── dom/
│   ├── seo/
│   ├── validation/
│   └── index.ts
│
└── App.tsx               # Main application component
```

## Delta Justification

### 1. Type Consolidation
Moving all types to a unified `/types` directory improves discoverability and eliminates duplication. Domain-specific types are grouped in subdirectories for clarity, while common types are kept at the root level.

### 2. Feature-First Organization
The `/features` directory now contains all domain-specific code, organized by business domain. Each feature encapsulates its components, hooks, and types, promoting high cohesion.

### 3. Unified API Layer
Creating a centralized `/api` directory separates data fetching from UI components and establishes a clear pattern for interacting with backend services.

### 4. Consolidated Hooks
All hooks are unified under `/hooks` with clear categorization. This eliminates duplication and standardizes hook implementation patterns.

### 5. Streamlined Configuration
Moving all configuration to `/config` creates a single source of truth for application constants and settings.

### 6. Page Component Isolation
Keeping page components in `/pages` separates routing concerns from feature implementation, making the application structure more intuitive.

## Migration Strategy

This restructuring will be implemented incrementally to minimize risk:

1. First consolidate types and utility functions
2. Next, migrate hooks to the new structure
3. Then reorganize features one at a time
4. Finally, update page components to use the new structure

Each step will be validated through automated tests and manual verification to ensure no regressions.
