

    ### File Structure

    ```
    ├── memory-bank
    │   ├── 0-distilledContext.md
    │   ├── 1-projectbrief.md
    │   ├── 2-productContext.md
    │   ├── 3-systemPatterns.md
    │   ├── 4-techContext.md
    │   ├── 5-activeContext.md
    │   ├── 6-progress.md
    │   ├── 7-tasks.md
    │   └── 8-metaPhilosophy.md
    └── project
        ├── memory-bank
        │   ├── 0-distilledContext.md
        │   ├── 1-projectbrief.md
        │   ├── 2-productContext.md
        │   ├── 3-systemPatterns.md
        │   ├── 4-techContext.md
        │   ├── 5-activeContext.md
        │   ├── 6-progress.md
        │   ├── 7-tasks.md
        │   ├── 8-metaPhilosophy.md
        │   └── index.md
        └── src
            └── memory-bank
                ├── 1-projectbrief.md
                ├── 2-contextHorizon.md
                ├── 3-fractalArchitecture.md
                ├── 4-simplificationBlueprint.md
                ├── 5-optimalFileStructure.md
                ├── 6-consolidationTracker.md
                ├── 7-implementationPlan.md
                ├── 8-entropyAudit.md
                ├── README.md
                ├── consolidation-plan.md
                ├── consolidation-summary.md
                ├── image-component-consolidation.md
                ├── master-implementation-plan.md
                ├── project-card-consolidation.md
                └── service-card-consolidation.md
    ```

    ---

    #### `memory-bank\0-distilledContext.md`

    ```markdown
        # Distilled Context: Ringerike Landskap Website

        - **Project Essence**: Digital showcase for Ringerike Landskap, a landscaping company in Norway, with hyperlocal SEO focus to connect regional customers with personalized landscaping services.
        - **Critical Constraint**: Must maintain authenticity of the two owners' personal approach while delivering a modern, responsive, and technically robust website.
        - **Current Focus**: Establish comprehensive Memory Bank for maximum codebase assimilation with root abstraction driving all understanding.
    ```

    ---

    #### `memory-bank\1-projectbrief.md`

    ```markdown
        # Project Brief: Ringerike Landskap Website

        ## Irreducible Purpose
        To create a digital showcase for Ringerike Landskap that connects local customers in the Ringerike region with the company's personalized landscaping services through an authentic representation of the owners' craftsmanship and customer-centric approach.

        ## Core Value Proposition
        A modern, responsive website that differentiates Ringerike Landskap in the local market by emphasizing the owners' personal investment in each project, their technical expertise (including specialized welding and corten steel work), and their deep understanding of the local terrain, while implementing hyperlocal SEO strategies to maximize regional visibility.

        ## Critical Constraints

        ### Technical
        - **Stack**: React 18, TypeScript, Vite, Tailwind CSS
        - **Performance**: Optimized loading times and Core Web Vitals metrics
        - **Responsive**: Mobile-first approach supporting all device sizes
        - **Accessibility**: Must follow accessibility best practices (ARIA, semantic HTML)

        ### Business
        - **Authenticity**: Must preserve and showcase the owners' genuine personal approach
        - **Regional Focus**: Hyperlocal SEO targeting the Ringerike region (Hole, HÃ¸nefoss, Jevnaker, Sundvollen, Vik)
        - **Service Presentation**: Eight core services must be clearly presented with filtering options
        - **Seasonal Adaptation**: Content must adapt based on current season

        ### User Experience
        - **Simplicity**: Clear, straightforward navigation and information architecture
        - **Call to Action**: Prominent "Book Gratis Befaring" (Free Consultation) throughout
        - **Trust Building**: Integration of customer testimonials and project showcases
        - **Filtering System**: Intuitive filtering for services and projects

        ## Project Boundaries

        ### In Scope
        - Informational website with service and project showcases
        - Contact form for customer inquiries
        - Responsive design for all device sizes
        - SEO optimization for local search terms
        - Seasonal content adaptation

        ### Out of Scope
        - E-commerce functionality
        - Customer login/accounts
        - Complex animations or 3D elements
        - CMS integration (content managed through structured data files)
        - Online booking system (contact form only)

        ## Core Success Metrics
        - Technical performance (Lighthouse scores >90)
        - Search visibility for target keywords
        - Lead generation through contact form
        - User engagement with project showcases
        - Mobile usability metrics
    ```

    ---

    #### `memory-bank\2-productContext.md`

    ```markdown
        # Product Context: Ringerike Landskap Website

        ## Target Audience & User Problems

        ### Primary Audience
        1. **Local Homeowners (20-50km radius)**
           - **Problems Solved**: Finding trusted local landscape professionals with terrain knowledge; visualizing potential outdoor solutions; efficiently communicating landscaping needs
           - **Behavioral Context**: Researching options before winter for spring projects; responding to immediate needs (drainage problems, outdoor renovations)
           - **Value Sought**: Personal connection; craftsmanship; local expertise; reliability

        2. **Property Developers & Commercial Clients**
           - **Problems Solved**: Finding contractors capable of larger-scale projects; ensuring quality consistent with property value; addressing compliance requirements
           - **Behavioral Context**: Planning phases for development projects; seeking portfolio evidence of similar work
           - **Value Sought**: Professionalism; efficiency; project management capabilities; scalable solutions

        ### Secondary Audience
        1. **Existing Customers**
           - **Problems Solved**: Finding additional services; sharing completed work with others; referring friends and family
           - **Behavioral Context**: Returning after project completion for testimonials or additional services
           - **Value Sought**: Relationship continuity; service consistency; recognition

        2. **Regional Architects & Designers**
           - **Problems Solved**: Finding implementation partners for landscape designs; connecting clients with trusted contractors
           - **Behavioral Context**: Seeking collaboration on design implementation
           - **Value Sought**: Technical capability; design sensitivity; collaborative approach

        ## External Contexts & Boundary Conditions

        ### Geographic Context
        - **Service Area**: Ringerike region - Hole, HÃ¸nefoss, Jevnaker, Sundvollen, Vik (20-50km radius)
        - **Logistical Constraints**: Travel time affects pricing and availability
        - **Local Terrain Knowledge**: Understanding of soil conditions, drainage requirements, frost mitigation specific to Ringerike

        ### Seasonal Context
        - **Norwegian Seasonal Cycle**: Service demand and type changes dramatically with seasons
        - **Planning Windows**: Customers research in winter for spring projects
        - **Implementation Timeline**: Weather constrains when services can be performed

        ### Market Context
        - **Competitive Landscape**: Few landscaping companies in the region with modern digital presence
        - **Differentiation Parameters**: Personal service, specialized welding, local knowledge
        - **Industry Standards**: Need to showcase certification, compliance with Norwegian building standards

        ### Customer Journey Context
        1. **Awareness**: Local search, referrals, seeing completed projects in neighborhood
        2. **Consideration**: Website research, photo galleries, service details
        3. **Decision**: Contact form, consultation request, direct contact
        4. **Engagement**: In-person meeting, customized proposal
        5. **Post-Project**: Testimonial opportunity, referrals, seasonal maintenance

        ## Real-World to Technical Value Connections

        | Real-World Need | Technical Implementation | Value Delivered |
        |----------------|--------------------------|-----------------|
        | Local search visibility | Hyperlocal SEO, schema markup | Customers find the company when searching for local services |
        | Project visualization | Categorized project galleries with filtering | Customers can see similar projects to what they envision |
        | Trust building | Authentic testimonials, owner profiles | Personal connection before first contact |
        | Seasonal relevance | Dynamic content adaptation based on current season | Timely, relevant service suggestions |
        | Service understanding | Clear descriptions with visual examples | Customers understand service options and applications |
        | Easy contact | Prominent CTAs, simple contact form | Reduced friction to initiate conversations |

        ## Purpose-Aligned Success Criteria

        ### Business Impact Metrics
        - **Lead Generation**: 20+ quality leads per month during peak season
        - **Geographic Penetration**: Inquiries from all targeted communities
        - **Service Distribution**: Inquiries across all eight core services
        - **Conversion Rate**: >15% conversion from website visit to contact
        - **Digital Attribution**: >40% of new business attributed to website

        ### User Experience Metrics
        - **Mobile Engagement**: Equal or better engagement metrics on mobile vs desktop
        - **Information Discovery**: >70% of users view multiple service pages
        - **Project Exploration**: >50% of users interact with filtering functions
        - **Contact Completion**: >80% contact form completion rate once started
        - **Return Visits**: >25% of visitors return within 30 days

        ### Technical Performance Metrics
        - **Page Speed**: <2s initial load on 4G connection
        - **Core Web Vitals**: All metrics in "Good" range
        - **SEO Performance**: First page rankings for all target keywords
        - **Accessibility**: WCAG AA compliance
        - **Crawlability**: 100% of pages indexed by search engines
    ```

    ---

    #### `memory-bank\3-systemPatterns.md`

    ```markdown
        # System Patterns: Ringerike Landskap Website

        ## Architectural Model

        The website follows a unified, feature-based component architecture with clear abstraction tiers, emphasizing domain cohesion, composition over inheritance, and single responsibility principles.

        ```mermaid
        graph TD
            subgraph Application
                App[App.tsx]
                Router[React Router]
            end

            subgraph "src/"
                subgraph "features/"
                    subgraph "services/"
                        ServicesUI[ui/]
                        ServicesLogic[logic/]
                        ServicesData[data/]
                        ServicesHooks[hooks/]
                        ServicesTypes[types/]
                    end

                    subgraph "projects/"
                        ProjectsUI[ui/]
                        ProjectsLogic[logic/]
                        ProjectsData[data/]
                        ProjectsHooks[hooks/]
                        ProjectsTypes[types/]
                    end

                    subgraph "testimonials/"
                        TestimonialsUI[ui/]
                        TestimonialsLogic[logic/]
                        TestimonialsData[data/]
                        TestimonialsHooks[hooks/]
                        TestimonialsTypes[types/]
                    end
                end

                subgraph "ui/"
                    CoreUI[Shared UI Components]
                    Primitives[UI Primitives]
                end

                subgraph "layout/"
                    Header[Header]
                    Footer[Footer]
                    Container[Container]
                end

                subgraph "pages/"
                    PageComponents[Route-Based Pages]
                end

                subgraph "utils/"
                    Helpers[Helper Functions]
                    GlobalHooks[Global Hooks]
                end

                subgraph "types/"
                    GlobalTypes[Shared Type Definitions]
                end
            end

            App --> Router
            Router --> PageComponents
            PageComponents --> "features/"
            PageComponents --> "layout/"
            "features/" --> "ui/"
            "features/" --> "utils/"
            "layout/" --> "ui/"
        ```

        This structure directly mirrors domain value flows, removes redundant layers, and ensures that additions or modifications to any feature can be done in one place, maximizing maintainability, developer clarity, and adaptability to change.

        ## Core Architectural Patterns

        ### Feature-Based Domain Organization Pattern

        The codebase implements a unified, feature-based organization with clear abstraction tiers within each domain:

        1. **Feature Modules** (`/src/features/`)
           - Each business domain has a dedicated directory that contains all related components
           - Complete vertical slices of functionality from UI to data
           - Examples: `/features/services/`, `/features/projects/`, `/features/testimonials/`
           - Pattern: Domain cohesion, self-contained functionality

        2. **Feature Internal Structure**
           - Each feature is organized by abstraction tiers:
             - `/ui/`: Feature-specific UI components
             - `/logic/`: Business logic and processing
             - `/data/`: Domain data handling
             - `/hooks/`: Custom hooks specific to feature
             - `/types/`: Type definitions for feature
           - Pattern: Consistent internal organization, clear abstraction boundaries

        3. **Shared UI Components** (`/src/ui/`)
           - Global, reusable UI primitives used across features
           - No business logic, focused on presentation
           - Examples: Button, Card, Input, Modal
           - Pattern: DRY implementation, consistent design system

        4. **Shared Layout Components** (`/src/layout/`)
           - Global structure components for page organization
           - Examples: Header, Footer, Container, Grid
           - Pattern: Consistent page structure, layout abstraction

        5. **Page Components** (`/src/pages/`)
           - Route-based components that compose features
           - Minimal logic, focused on feature orchestration
           - Examples: HomePage, ServicesPage, ProjectsPage
           - Pattern: Routing integration, feature composition

        ### Data Flow Pattern

        Data flows through the application in a consistent, unidirectional pattern:

        ```mermaid
        flowchart LR
            subgraph Static
                SD[Static Data Files]
            end

            subgraph Custom
                CH[Custom Hooks]
            end

            subgraph Component
                P[Props]
                S[State]
                JSX[Rendering]
            end

            SD --> CH
            CH --> P
            P --> JSX
            S --> JSX
        ```

        1. **Static Data Source Pattern**
           - TypeScript files containing structured data
           - Strong typing with interfaces
           - Utility functions for data access and filtering
           - Located in: `/src/data/`

        2. **Custom Hook Abstraction Pattern**
           - Business logic encapsulated in custom hooks
           - Data transformation, filtering, and state management
           - Consistent naming convention: `use[Feature]`
           - Located in: `/src/hooks/`

        3. **Props Down, Events Up Pattern**
           - Parent components pass data down via props
           - Child components communicate up via callbacks
           - Minimal prop drilling (max 2-3 levels)
           - Type-safe prop interfaces for components

        ### Filtering System Pattern

        A core architectural pattern implementing a consistent filtering system across multiple content types:

        ```mermaid
        flowchart TD
            subgraph FilterHook[useFilteredData Hook]
                FilterState[Filter State]
                FilterFunctions[Filter Functions]
                MemoizedResults[Memoized Results]
            end

            subgraph UI
                FilterControls[Filter Controls]
                FilterDisplay[Filter Count Display]
                FilteredContent[Filtered Content]
            end

            Data[Raw Data] --> FilterHook
            FilterControls --> FilterState
            FilterState --> MemoizedResults
            FilterFunctions --> MemoizedResults
            MemoizedResults --> FilteredContent
            FilterState --> FilterDisplay
        ```

        1. **Filter Hook Pattern**
           - Core abstraction: `useFilteredData` custom hook
           - Consistent filter state management
           - Memoized filter execution for performance
           - Reused across services, projects, and testimonials

        2. **Filter UI Pattern**
           - Composable filter control components
           - Consistent UX across different filterable content
           - Visual indicators for active filters
           - Reset capability for filter state

        ### Responsive Design Pattern

        The responsive approach follows a mobile-first pattern with breakpoints using Tailwind CSS:

        1. **Container Pattern**
           - Consistent max-width and padding at each breakpoint
           - Centered content with responsive margins
           - Implementation: `Container` component

        2. **Responsive Grid Pattern**
           - Column count increases at larger breakpoints
           - Consistent gap spacing at all sizes
           - Implementation: Tailwind's grid utilities

        3. **Stack Pattern**
           - Vertical spacing that adjusts at breakpoints
           - Implementation: Flex or grid with gap utilities

        4. **Adaptive Typography Pattern**
           - Font sizes that scale with viewport
           - Heading hierarchy maintained across breakpoints
           - Implementation: Tailwind's responsive text utilities

        ### Seasonal Adaptation Pattern

        The website implements a seasonal adaptation system that affects content presentation based on the current or upcoming season:

        ```mermaid
        flowchart LR
            subgraph SeasonDetection
                CurrentDate[Current Date] --> SeasonLogic[Season Logic]
                SeasonLogic --> CurrentSeason[Current Season]
                SeasonLogic --> UpcomingSeason[Upcoming Season]
            end

            subgraph ContentAdaptation
                CurrentSeason --> ServicePriority[Service Priority]
                CurrentSeason --> ProjectSelection[Project Selection]
                CurrentSeason --> SeasonalCTA[Seasonal CTAs]
                UpcomingSeason --> PlanningPrompts[Planning Prompts]
            end

            subgraph UIAdaptation
                CurrentSeason --> ColorAccents[Color Accents]
                CurrentSeason --> ImageSelection[Image Selection]
                CurrentSeason --> SeasonalCopy[Seasonal Copy]
            end
        ```

        1. **Season Detection Pattern**
           - Date-based logic to determine current season
           - Anticipatory logic for upcoming season
           - Configurable season boundaries

        2. **Content Prioritization Pattern**
           - Season-appropriate services highlighted
           - Project examples filtered by relevance to season
           - Seasonal testimonials featured

        3. **UI Adaptation Pattern**
           - Subtle visual cues reflecting season
           - Seasonal accent colors
           - Relevant imagery prioritized

        ## Critical Interaction Flows

        ### User Journey: Service Discovery to Contact

        ```mermaid
        sequenceDiagram
            actor User
            participant HP as HomePage
            participant SP as ServicesPage
            participant SDP as ServiceDetailPage
            participant PP as ProjectsPage
            participant CP as ContactPage

            User->>HP: Visits website
            HP->>User: Shows seasonal services
            User->>SP: Explores services
            SP->>User: Shows all services
            User->>SP: Applies filters
            SP->>User: Shows filtered services
            User->>SDP: Selects specific service
            SDP->>User: Shows service details
            SDP->>User: Shows related projects
            User->>PP: Explores related projects
            PP->>User: Shows filtered projects
            User->>CP: Clicks "Book Gratis Befaring"
            CP->>User: Shows contact form
            User->>CP: Submits inquiry
            CP->>User: Confirms submission
        ```

        ### Content Navigation Pattern

        The website uses a consistent navigation structure that emphasizes exploration:

        1. **Cross-Content Referencing Pattern**
           - Services link to relevant projects
           - Projects link back to associated services
           - Testimonials link to related projects
           - All paths lead to contact opportunities

        2. **Hierarchical Navigation Pattern**
           - Primary navigation: Main sections
           - Secondary navigation: Filter systems
           - Tertiary navigation: Related content links

        3. **Call to Action Pattern**
           - Primary CTA: "Book Gratis Befaring"
           - Secondary CTAs: Explore related content
           - Persistent CTAs in strategic locations

        ## Component Dependencies

        ```mermaid
        graph TD
            subgraph Core
                App --> Router
                Router --> Pages
            end

            subgraph "src/pages"
                HomePage
                ServicesPage
                ProjectsPage
                AboutPage
                ContactPage
            end

            subgraph "src/features/services"
                ServiceList[ui/ServiceList]
                ServiceDetail[ui/ServiceDetail]
                ServiceFilter[ui/ServiceFilter]
                ServicesHooks[hooks/useFilteredServices]
                ServicesData[data/services.ts]
            end

            subgraph "src/features/projects"
                ProjectGrid[ui/ProjectGrid]
                ProjectDetail[ui/ProjectDetail]
                ProjectFilter[ui/ProjectFilter]
                ProjectsHooks[hooks/useFilteredProjects]
                ProjectsData[data/projects.ts]
            end

            subgraph "src/ui"
                Button
                Card
                Hero
                Input
            end

            subgraph "src/layout"
                Header
                Footer
                Container
            end

            HomePage --> ServiceList
            HomePage --> ProjectGrid
            ServicesPage --> ServiceList
            ServicesPage --> ServiceFilter
            ProjectsPage --> ProjectGrid
            ProjectsPage --> ProjectFilter

            ServiceList --> ServicesHooks
            ServiceList --> ServicesData
            ServiceList --> Card

            ProjectGrid --> ProjectsHooks
            ProjectGrid --> ProjectsData
            ProjectGrid --> Card

            Pages --> Header
            Pages --> Footer
            Pages --> Container
        ```

        This dependency graph illustrates how the unified feature-based structure creates clear, predictable relationships between components while reducing cross-cutting dependencies and duplication.

        ## Design Patterns & Architecture Decisions

        ### Component Design Decisions

        1. **Composition Over Inheritance**
           - Components built through composition of smaller components
           - Flexible component combinations through props
           - Higher maintainability and reusability

        2. **Controlled vs. Uncontrolled Components**
           - Form elements use controlled pattern
           - State lifted to parent components where needed
           - Explicit data flow and predictable behavior

        3. **Container/Presenter Pattern**
           - Separation of data fetching from presentation
           - Logic components wrap presentation components
           - Improved testability and separation of concerns

        ### Component Consolidation Strategy

        The following strategy addresses component duplication and establishes a unified approach to component design:

        #### UI Component Duplication Assessment

        Based on analysis, these areas require consolidation:

        1. **Button Components**
           - Create unified Button in `src/ui/button/`
           - Implement consistent prop interface:
           ```typescript
           interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
             variant?: 'primary' | 'secondary' | 'text' | 'outlined';
             size?: 'small' | 'medium' | 'large';
             fullWidth?: boolean;
             icon?: React.ReactNode;
             iconPosition?: 'left' | 'right';
           }
           ```
           - Use composition pattern for extensibility

        2. **Card Components**
           - Create base Card in `src/ui/card/`
           - Implement composable sub-components:
           ```tsx
           <Card>
             <Card.Image src="..." alt="..." />
             <Card.Content>
               <Card.Title>Title here</Card.Title>
               <Card.Description>Description here</Card.Description>
             </Card.Content>
             <Card.Footer>
               <Button>Action</Button>
             </Card.Footer>
           </Card>
           ```
           - Allow feature-specific extensions

        3. **Form Components**
           - Create unified form components in `src/ui/form/`
           - Ensure consistency in validation and error handling
           - Standardize accessibility attributes

        #### Feature-Specific Components

        Organize feature components by domain:

        1. **Services Feature**
           - Move service-specific components to `src/features/services/ui/`
           - Extract common patterns while preserving domain-specific functionality

        2. **Projects Feature**
           - Define project-specific components that extend shared components
           - Maintain consistent patterns across features

        3. **Testimonials Feature**
           - Define testimonial-specific components using the same patterns
           - Ensure consistency with other feature modules

        #### Implementation Sequence

        To minimize risk, follow this sequence:
        1. Foundation components (Button, Card, Container)
        2. Form components (Input, Select, etc.)
        3. Layout components (Header, Footer)
        4. Feature components (by domain)
        5. Page components (composed of features)

        ### File Structure Consolidation

        The file structure will be reorganized to a domain-driven organization with clear separation of concerns:

        ```
        src/
        ├── features/ # Business domain modules
        │   ├── services/ # Services feature
        │   │   ├── ui/ # Service-specific UI components
        │   │   ├── logic/ # Service business logic
        │   │   ├── data/ # Service data (from static files)
        │   │   ├── hooks/ # Service-specific custom hooks
        │   │   └── types/ # Service type definitions
        │   ├── projects/ # Projects feature
        │   │   ├── ui/ # Project-specific UI components
        │   │   ├── logic/ # Project business logic
        │   │   ├── data/ # Project data
        │   │   ├── hooks/ # Project-specific hooks
        │   │   └── types/ # Project type definitions
        │   └── testimonials/ # Testimonials feature
        │       ├── ui/ # Testimonial-specific UI components
        │       ├── logic/ # Testimonial business logic
        │       ├── data/ # Testimonial data
        │       ├── hooks/ # Testimonial-specific hooks
        │       └── types/ # Testimonial type definitions
        ├── ui/ # Shared UI primitives
        ├── layout/ # Global layout components
        ├── pages/ # Route components using features
        ├── shared/ # Cross-cutting concerns
        │   ├── hooks/ # Shared hooks
        │   ├── utils/ # Shared utilities
        │   └── types/ # Shared type definitions
        ├── assets/ # Static assets
        └── config/ # Application configuration
        ```

        Implementation will proceed in systematic phases:
        1. Preparation: Create new structure and backup original
        2. Shared Components: Migrate UI primitives first
        3. Feature Organization: Migrate feature by feature
        4. Shared Utilities: Consolidate hooks and utilities
        5. Page Components: Update to use new structure
        6. Testing: Comprehensive validation at each phase

        ### State Management Decisions

        1. **Hook-Based State Management**
           - React hooks for component-level state
           - Custom hooks for shared state logic
           - No global state management library needed

        2. **Prop Drilling Limitations**
           - Maximum 2-3 levels of prop drilling
           - Component composition to avoid excessive drilling
           - Props carefully typed and documented

        ### Performance Optimization Decisions

        1. **Memoization Strategy**
           - React.memo for pure components
           - useMemo for expensive calculations
           - Carefully controlled dependencies

        2. **Lazy Loading Pattern**
           - React.lazy for code splitting
           - Route-based code splitting
           - Image lazy loading

        3. **Rendering Optimization**
           - Virtualization for long lists
           - Pagination for large data sets
           - Throttled event handlers
    ```

    ---

    #### `memory-bank\4-techContext.md`

    ```markdown
        # Technology Context: Ringerike Landskap Website

        ## Tech Stack Foundation

        The website is built on a modern React-based stack optimized for performance, developer experience, and maintainability:

        | Technology | Version | Purpose | Key Features Used |
        |------------|---------|---------|-------------------|
        | React | 18.3.1 | UI library | Hooks, Suspense, Memo |
        | TypeScript | 5.5.3 | Type safety | Strict mode, interfaces, types |
        | Vite | 5.4.2 | Build tool | HMR, optimized builds, dev server |
        | Tailwind CSS | 3.4.1 | Styling | Utility classes, responsive design |
        | React Router | 6.22.3 | Routing | Route definitions, dynamic routes |
        | React Helmet | 6.1.0 | SEO | Document head management |
        | Framer Motion | 12.5.0 | Animations | Transitions, gestures |
        | Lucide React | 0.344.0 | Icons | UI enhancement |
        | ESLint | 9.9.1 | Code quality | Static analysis, formatting rules |

        ## Development Environment

        ### Local Environment Requirements

        ```
        Node.js: v18+
        npm: v9+ (included with Node.js)
        Git: Any recent version
        VSCode: Recommended IDE with extensions:
          - ESLint
          - Tailwind CSS IntelliSense
          - TypeScript support
        ```

        ### Key Commands

        ```bash
        # Initial setup
        npm install

        # Development with hot reload
        npm run dev

        # Development with screenshot capture
        npm run dev:ai

        # Build for production
        npm run build

        # Preview production build
        npm run preview

        # Linting
        npm run lint

        # Screenshot management
        npm run screenshots # Manage screenshots
        npm run screenshots:capture # Capture screenshots
        npm run screenshots:clean # Clean screenshots
        ```

        ### Directory Structure

        The codebase follows a unified, feature-based organization that groups related functionality by domain:

        ```
        src/
        ├── features/ # Feature modules (domain-based organization)
        │   ├── services/ # Services feature
        │   │   ├── ui/ # Service-specific UI components
        │   │   ├── logic/ # Service business logic
        │   │   ├── data/ # Service data management
        │   │   ├── hooks/ # Service-specific custom hooks
        │   │   └── types/ # Service type definitions
        │   ├── projects/ # Projects feature
        │   │   ├── ui/ # Project-specific UI components
        │   │   ├── logic/ # Project business logic
        │   │   ├── data/ # Project data management
        │   │   ├── hooks/ # Project-specific custom hooks
        │   │   └── types/ # Project type definitions
        │   └── testimonials/ # Testimonials feature
        │       ├── ui/ # Testimonial-specific UI components
        │       ├── logic/ # Testimonial business logic
        │       ├── data/ # Testimonial data management
        │       ├── hooks/ # Testimonial-specific custom hooks
        │       └── types/ # Testimonial type definitions
        ├── ui/ # Shared UI primitives
        ├── layout/ # Global layout components
        ├── pages/ # Page components (routes)
        ├── utils/ # Shared utility functions
        ├── types/ # Global TypeScript type definitions
        └── styles/ # Global styles
        ```

        This structure directly mirrors domain value flows and ensures that additions or modifications to any feature can be done in one place. It eliminates redundant components (such as duplicate Button/Hero components) and groups all related code for a specific business domain in a single location.

        ## Technical Dependencies

        ### Critical Frontend Dependencies

        1. **React Ecosystem**
           - React DOM: DOM rendering and manipulation
           - React Router: Client-side routing
           - React Helmet: Document head management for SEO

        2. **UI Enhancement Libraries**
           - Framer Motion: Animation library for smooth transitions
           - Lucide React: Icon library for consistent visual elements
           - clsx/tailwind-merge: Utility for conditional class composition

        3. **Development Tooling**
           - Vite: Build tool and development server
           - TypeScript: Strong typing for JavaScript
           - ESLint: Code quality and style enforcement
           - Autoprefixer/PostCSS: CSS processing

        ### Core Browser Dependencies

        1. **Modern JavaScript Support**
           - ES6+ features: Promises, async/await, modules
           - DOM API: querySelector, addEventListener, etc.

        2. **Modern CSS Support**
           - Flexbox and Grid for layout
           - CSS Variables for theming
           - Media queries for responsive design

        3. **Performance APIs**
           - Intersection Observer: Lazy loading
           - Fetch API: Data fetching
           - Web Storage: Local data persistence

        ## Infrastructure & Deployment

        ### Hosting Requirements

        - Static site hosting capability
        - HTTPS support
        - Custom domain configuration
        - CDN integration (optional but recommended)

        ### Build Pipeline

        ```
        Source Code → TypeScript Compilation → Vite Build → Asset Optimization → Deployment
        ```

        ### Deployment Process

        1. **Pre-build Checks**
           - TypeScript type checking
           - ESLint validation
           - Dependency verification

        2. **Build Process**
           - Code transpilation and bundling
           - CSS optimization
           - Asset optimization (images, etc.)

        3. **Post-build Steps**
           - Generate sitemap
           - Create robots.txt
           - Verify build artifacts

        ### Performance Optimizations

        - Code splitting by route
        - CSS minification and purging
        - Script loading optimization

        ## Dynamic Image Loading System

        The website implements a directory-based dynamic image loading system to improve maintainability and scalability.

        ### Image Directory Structure

        Images are organized by business domain:

        ```
        src/
        └── assets/
            └── images/
                ├── services/
                │   ├── belegningsstein/
                │   ├── cortenstaal/
                │   ├── stottemurer/
                │   └── ...
                ├── projects/
                │   ├── residential/
                │   ├── commercial/
                │   └── ...
                └── testimonials/
        ```

        ### Image Loading Implementation

        The system uses Vite's `import.meta.glob` for dynamic imports:

        ```typescript
        // Type definitions
        type ImageDomain = 'services' | 'projects' | 'testimonials';
        type ImageCategory = {
          services: 'belegningsstein' | 'cortenstaal' | 'stottemurer' | 'platting' | 'ferdigplen' | 'kantstein' | 'trapp-repo' | 'hekk';
          projects: 'residential' | 'commercial';
          testimonials: 'featured' | 'regular';
        }

        // Dynamic image loading utility
        export function getImage<D extends ImageDomain>(
          domain: D,
          category: ImageCategory[D],
          imageName: string
        ): () => Promise<string> {
          // Implementation using import.meta.glob
        }
        ```

        ### Image Component

        A reusable Image component provides built-in error handling and fallbacks:

        ```typescript
        interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
          domain?: 'services' | 'projects' | 'testimonials';
          fallbackSrc?: string;
        }

        const Image = ({ src, alt, domain, fallbackSrc, ...props }) => {
          // Implementation with error handling
        };
        ```

        ### Benefits

        1. **Improved Maintainability**: Adding new images doesn't require code changes
        2. **Better Error Handling**: Consistent fallback strategy for missing images
        3. **Type Safety**: TypeScript ensures correct domain/category usage
        4. **Performance**: Proper lazy loading and optimization
        5. **Accessibility**: Standardized alt text and loading indicators

        ## Browser Compatibility

        ### Target Browsers

        - Chrome/Edge: Latest 2 versions
        - Firefox: Latest 2 versions
        - Safari: Latest 2 versions
        - iOS Safari: Latest 2 versions
        - Android Chrome: Latest 2 versions

        ### Polyfill Strategy

        - Minimal polyfills for modern browsers
        - Focus on essential functionality
        - Progressive enhancement approach

        ## Data Architecture

        ### Data Sources

        - Static TypeScript files containing structured data
        - No backend API requirements
        - Flat file structure for simplicity and performance

        ### Data Models

        **Service**
        ```typescript
        interface ServiceType {
          id: string;
          title: string;
          description: string;
          image: string;
          features: string[];
          imageCategory: string;
        }
        ```

        **Project**
        ```typescript
        interface ProjectType {
          id: string;
          title: string;
          description: string;
          category: string;
          location: string;
          tags: string[];
          images: string[];
          serviceId?: string;
          season: 'Vår' | 'Sommer' | 'Høst' | 'Vinter';
        }
        ```

        **Testimonial**
        ```typescript
        interface TestimonialType {
          id: string;
          name: string;
          location: string;
          text: string;
          rating: number;
          projectId?: string;
          category?: string;
          image?: string;
        }
        ```

        ### Data Flow

        1. **Import Pattern**
           Static data files imported into components

        2. **Transform Pattern**
           Data transformed by hooks for filtering and display

        3. **Render Pattern**
           Transformed data passed to components via props

        ## Technical Constraints & Challenges

        ### Performance Constraints

        - **Initial Load Target**: < 2s on 4G connection
        - **Time to Interactive**: < 3s on mid-range devices
        - **Core Web Vitals**: All in "Good" range

        ### Responsive Design Constraints

        - **Screen Sizes**: Support from 320px to 1920px+
        - **Touch Support**: Full functionality on touch devices
        - **Responsive Images**: Appropriate sizing for all devices

        ### Accessibility Requirements

        - **WCAG Compliance**: AA level
        - **Semantic HTML**: Proper use of HTML elements
        - **Keyboard Navigation**: Full functionality without mouse
        - **Screen Reader Support**: All content accessible

        ### SEO Requirements

        - **Meta Tags**: Dynamic meta tags for all pages
        - **Structured Data**: Schema.org markup
        - **Performance**: Speed as an SEO factor
        - **Mobile Friendly**: Mobile-first indexing compatibility

        ## Development Workflows

        ### Feature Development

        1. Create components and types
        2. Implement core functionality
        3. Connect with data sources
        4. Style with Tailwind CSS
        5. Implement tests and validation
        6. Review and refine

        ### Screenshot Infrastructure

        The project includes a custom screenshot capture system:

        1. **Automatic Capture**
           - On-demand screenshots during development
           - Command: `npm run screenshots:capture`

        2. **Screenshot Management**
           - Organization by timestamp and route
           - Storage in `/screenshots/` directory

        3. **Analysis Tooling**
           - Visual comparison capabilities
           - Temporal tracking of UI changes

        ## Knowledge Domain Requirements

        ### Frontend Development

        - **React Knowledge**: Hooks, components, context
        - **TypeScript**: Interfaces, types, generics
        - **Modern CSS**: Tailwind, Flexbox, Grid
        - **Performance**: Code splitting, optimization

        ### SEO Expertise

        - **Technical SEO**: Meta tags, structured data
        - **Local SEO**: Geotargeting, region-specific optimization

        ### Responsive Design

        - **Mobile-First**: Development approach
        - **Fluid Design**: Adaptable layouts and typography

        ### Web Accessibility

        - **ARIA**: Proper use of ARIA attributes
        - **Focus Management**: Keyboard navigation
        - **Screen Readers**: Compatible content structure
    ```

    ---

    #### `memory-bank\5-activeContext.md`

    ```markdown
        # Active Context: Ringerike Landskap Website

        ## Current Analysis Phase

        **Phase: Initial Memory Bank Setup**
        **Focus: Comprehensive Codebase Assimilation**
        **Date: 2025-04-27**

        The current phase involves establishing the Memory Bank as a cognitive framework for understanding the Ringerike Landskap website project. We are implementing the Memory Bank structure as both lens and ledger to enable persistent maximum actionable value through complexity reduction.

        ## Working Theories

        ### Theory 1: Unified Feature-Based Organization Maximizes Maintainability
        The codebase should adopt a unified, feature-based file structure that groups all related components, logic, data, and utilities for each business domain under distinct, top-level 'features/' directories. Within each feature, further organization by abstraction tiers creates clear boundaries and responsibilities.

        **Evidence Supporting:**
        - Domain cohesion reduces cognitive load by keeping related code together
        - Abstraction tiers within features create clear responsibility boundaries
        - Eliminates duplication by consolidating shared primitives in global ui/ and layout/ directories
        - Directly mirrors domain value flows for intuitive navigation

        **Questions Remaining:**
        - How to handle cross-feature shared code most effectively?
        - What naming conventions ensure consistency across feature modules?

        ### Theory 2: Custom Hook Pattern Central to State Management
        The project leverages custom hooks extensively rather than a global state management system, with particular focus on the filtering system implemented through the `useFilteredData` hook.

        **Evidence Supporting:**
        - Filtering system documented in README as core feature
        - Data files structured to support filtering by multiple criteria
        - Component exports suggest hook consumption pattern

        **Questions Remaining:**
        - Are there performance implications at scale?
        - Does this approach adequately support future state management needs?

        ### Theory 3: Seasonal Adaptation as Core Architectural Feature
        The seasonal adaptation system appears to be a defining architectural feature that influences content selection, UI presentation, and user journeys.

        **Evidence Supporting:**
        - Season-specific service and project organization in data models
        - Extensive documentation of seasonal adaptation in requirements
        - Interface for marking content with seasonal relevance

        **Questions Remaining:**
        - How is the current season detection implemented?
        - What UI elements adapt beyond content selection?

        ## Key Findings Mapped to Structure

        ### Codebase Architecture
        - **Finding:** The project uses a React-based architecture with TypeScript, Vite and Tailwind CSS, requiring a unified feature-based organization that eliminates redundant layers and mirrors domain value flows.
        - **Location:** File structure should follow `/src/features/[domain]/[tier]/` pattern with shared components in global directories
        - **Relevance:** Updated architecture in `3-systemPatterns.md` provides clear guidance for this organization pattern

        ### Content Organization
        - **Finding:** Content is managed through structured TypeScript files rather than a CMS, providing strong typing and integration with the component system.
        - **Location:** `/src/data` contains services.ts, projects.ts, testimonials.ts
        - **Relevance:** Supports the data architecture described in `4-techContext.md`

        ### SEO Implementation
        - **Finding:** The project uses React Helmet for SEO with a focus on hyperlocal optimization targeting the Ringerike region.
        - **Location:** SEO components likely in layout components
        - **Relevance:** Directly supports the regional focus constraint in `1-projectbrief.md`

        ### User Experience Design
        - **Finding:** The UX emphasizes clear navigation paths between services, projects, and contact opportunities, with seasonal adaptation influencing the user journey.
        - **Location:** Navigation components, page structure, filtering system
        - **Relevance:** Aligns with cross-content referencing pattern in `3-systemPatterns.md`

        ## Decisions & Insights

        ### Memory Bank Consolidation Decision
        We've implemented and consolidated the Memory Bank structure with 9 core files (0-8) and removed additional redundant files. The structure has been streamlined while maintaining the abstraction hierarchy.

        **Rationale:**
        - The project's complexity warrants the full structure but not fragmentation
        - Clear abstraction levels from purpose to action
        - Core files now contain all relevant information without duplication
        - Stronger connections between related information

        **Implementation:**
        1. ✅ Integrated file structure consolidation plan into `3-systemPatterns.md`
        2. ✅ Integrated component consolidation analysis into `3-systemPatterns.md`
        3. ✅ Integrated dynamic image loading system into `4-techContext.md`
        4. ✅ Marked redundant files as deprecated and ready for removal

        ### Architectural Insight: Feature-Specific vs. Shared Functionality
        The unified feature-based structure creates a clear distinction between feature-specific code (contained within feature directories) and shared code (in global directories). This distinction helps manage cross-cutting concerns more effectively.

        **Implications:**
        - Feature-specific hooks like `useFilteredServices` should live in their respective feature module
        - Shared UI primitives consolidated in a single global location eliminates duplication
        - Clear organization reduces the need for complex state management solutions
        - Enables easier maintenance and onboarding by making the domain model explicit in the directory structure

        ### Technical Insight: Dynamic Image Loading System
        The static hardcoded image handling should be transformed into a dynamic, directory-driven system that is more robust and scalable.

        **Identified Need:**
        - Current image handling relies on static imports and hardcoded paths
        - No consistent error handling for missing images
        - Limited scalability when adding new project or service images
        - Potential maintenance overhead with current approach

        **Proposed Solution:**
        - Implement directory-based dynamic image loading using Vite's import.meta.glob
        - Create standardized image directory structure mapping to business domains
        - Refactor data files to reference image identifiers or paths dynamically
        - Add error handling and fallback images for robustness

        **Implications:**
        - Significantly reduced code maintenance for image handling
        - More scalable system for adding new media assets
        - Better error resilience for media content
        - Maintains alignment with performance constraints in `1-projectbrief.md`
        - Supports SEO goals through proper image optimization and alt text

        ### Technical Insight: Performance Optimization Strategy
        Beyond image handling, the project employs multiple complementary strategies for performance optimization:
        - Memoization for expensive computations
        - Code splitting via React.lazy for route-based loading
        - Static data structure for minimal runtime overhead

        **Implications:**
        - Aligns with performance constraints in `1-projectbrief.md`
        - Provides foundation for meeting Core Web Vitals targets
        - Supports SEO goals through performance as ranking factor

        ## Open Questions & Next Steps

        ### Questions for Further Investigation
        1. How is the seasonal adaptation specifically implemented?
        2. What testing strategies are employed?
        3. How are accessibility requirements verified?
        4. What is the current state of the project's completion?
        5. How are images currently loaded and managed throughout the system?
        6. Are there existing image optimization strategies in place?
        7. Are there any technical debt items already identified?

        ### Next Steps for Memory Bank Development
        1. **Review Project Structure:** Analyze actual component directory structure for alignment with architectural documentation
        2. **Examine Hooks Implementation:** Look at custom hook implementations, especially filtering system
        3. **Analyze Component Patterns:** Review component patterns and identify potential improvements
        4. **Investigate SEO Implementation:** Examine how hyperlocal SEO is implemented
        5. **Update Progress Tracking:** Complete `6-progress.md` with findings
        6. **Define Tasks:** Create `7-tasks.md` with potential improvements

        ### Next Steps for Codebase Understanding
        1. **Core Component Analysis:** Examine key UI and layout components
        2. **Data Structure Deep-Dive:** Analyze the complete data structures and relationships
        3. **Hook Implementation Review:** Study custom hook implementations
        4. **Routing Structure Analysis:** Map the complete routing structure
        5. **SEO Implementation Analysis:** Examine metadata and structured data
    ```

    ---

    #### `memory-bank\6-progress.md`

    ```markdown
        # Progress Tracking: Ringerike Landskap Website

        ## Understanding Coverage Map

        | Area | Coverage | Confidence | Sources | Notes |
        |------|----------|------------|---------|-------|
        | **Project Purpose** | 95% | High | README, Init notes, Architecture docs | Clear understanding of core purpose and constraints |
        | **Architecture** | 80% | Medium | Architecture overview, App.tsx, directory structure | Good theoretical understanding, needs component verification |
        | **Component System** | 70% | Medium | Component exports, README, architecture docs | Structure understood, implementation details needed |
        | **Routing** | 85% | High | App.tsx, architecture overview | Clear route structure, dynamic route resolution understood |
        | **Data Model** | 75% | Medium | services.ts, data structure docs | Core models identified, relationship mapping incomplete |
        | **State Management** | 60% | Medium-Low | README reference to hooks | Hook approach identified, implementation details needed |
        | **Styling System** | 65% | Medium | package.json, architecture docs | Tailwind implementation identified, customization unknown |
        | **SEO Strategy** | 90% | High | Documentation, requirements | Comprehensive understanding of hyperlocal approach |
        | **Performance Optimization** | 60% | Medium | Architecture docs, README | Strategies identified, implementation details needed |
        | **Filtering System** | 75% | Medium | README, service data | Core concept clear, implementation details needed |
        | **Seasonal Adaptation** | 70% | Medium | Documentation, service/project data | Concept understood, implementation mechanism unknown |

        ## System Health Assessment

        ### Code Organization Health

        | Component | Status | Entropy Level | Issues | Opportunities |
        |-----------|--------|---------------|--------|--------------|
        | **Directory Structure** | ðŸŸ¡ Adequate | Medium | Potential redundancy | Implement domain-driven feature organization |
        | **Component Hierarchy** | ðŸŸ  Needs Improvement | Medium-High | Likely duplication | Systematically analyze and refactor for unified component library |
        | **Naming Conventions** | ðŸŸ¡ Adequate | Medium | Potential inconsistency | Establish consistent naming across refactored components |
        | **Type Definitions** | ðŸŸ¡ Adequate | Medium | Potential gaps | Enhance with comprehensive typing during refactoring |
        | **Import Structure** | ðŸŸ  Needs Improvement | Medium-High | Potential complexity | Simplify through domain-based organization |
        | **Redundancy Management** | ðŸ”´ Poor | High | Likely significant duplication | Systematic analysis and iterative refactoring required |
        | **Modularity** | ðŸŸ¡ Adequate | Medium | Boundaries could be clearer | Improve through domain-driven organization |

        ### Technical Debt Inventory

        | Area | Severity | Description | Impact | Remediation |
        |------|----------|-------------|--------|------------|
        | **Component Duplication** | High | Likely duplicate UI components across features | Maintenance burden, inconsistency | Systematic analysis and unified component library |
        | **Path Refactoring** | Low | Two route references need updating (todo comments identified) | Minor maintenance issue | Update `/hva-vi-gjor` to `/hva`, `/hvem-er-vi` to `/hvem` |
        | **Unknown Testing Coverage** | Medium | No tests identified in initial examination | Potential maintenance risk | Implement automated testing with refactoring |
        | **Accessibility Verification** | Medium | WCAG compliance stated as goal, verification unclear | Potential user experience issue | Implement accessibility audit and remediation |
        | **SEO Implementation Verification** | Low | Strategy well-documented, implementation unverified | Potential marketing impact | Verify meta tag implementation and structured data |
        | **Image Loading System** | Medium | Static hardcoded image references | Scalability and maintenance issues | Implement dynamic directory-based image loading |
        | **Code Organization** | High | Suboptimal organization reducing maintainability | Increased development cost | Incremental refactoring to domain-driven structure |

        ### Performance Metrics

        | Metric | Target | Current | Status | Notes |
        |--------|--------|---------|--------|-------|
        | **Page Load Time** | <2s | Unknown | ðŸŸ¡ Unverified | Need measurement |
        | **Time to Interactive** | <3s | Unknown | ðŸŸ¡ Unverified | Need measurement |
        | **Core Web Vitals** | All "Good" | Unknown | ðŸŸ¡ Unverified | Need measurement |
        | **Bundle Size** | <250KB | Unknown | ðŸŸ¡ Unverified | Need measurement |
        | **Image Optimization** | WebP format | Partial | ðŸŸ¡ Adequate | Some WebP usage observed, need comprehensive check |

        ### Understanding Gaps

        1. **Code Duplication Assessment**
           - Extent of component duplication across features
           - Patterns of repeated logic in hooks and utilities
           - Redundant styling approaches
           - Opportunities for abstraction and consolidation

        2. **Implementation of Seasonal Adaptation**
           - How is current season detected?
           - Which UI elements adapt to seasons?
           - How is seasonal content prioritization implemented?

        3. **Filtering System Implementation**
           - Structure of useFilteredData hook
           - Performance optimization techniques
           - Filter UI implementation details

        4. **Testing Strategy**
           - What testing approach is used?
           - Current test coverage?
           - Test locations in codebase?

        5. **Refactoring Risk Assessment**
           - Critical path components that require careful handling
           - Integration points between features
           - Side effects of restructuring
           - Validation strategies for refactoring steps

        6. **Accessibility Implementation**
           - ARIA attribute usage
           - Keyboard navigation implementation
           - Screen reader compatibility

        7. **SEO Technical Implementation**
           - Meta tag generation methodology
           - Structured data implementation
           - Local SEO optimization techniques

        ## Entropy Tracking

        ### Low Entropy Areas
        - **Project Purpose**: Clear definition, well-documented
        - **Architecture Pattern**: Consistent feature-based organization
        - **Routing Structure**: Straightforward implementation with React Router
        - **Data Structure**: Well-defined interfaces with clear relationships

        ### Medium Entropy Areas
        - **Component Implementation**: Details of component pattern implementation
        - **Hook Implementation**: Custom hook implementation details
        - **Styling System**: Tailwind customization and organization

        ### Unknown/Potential High Entropy Areas
        - **Testing System**: Not identified in initial examination
        - **Accessibility Implementation**: Stated as requirement but implementation unknown
        - **Development Workflow**: Practical implementation of stated processes

        ## Evolution Timeline

        | Date | Milestone | Key Changes | Status |
        |------|-----------|------------|--------|
        | 2025-04-27 | Memory Bank Initialization | Created initial Memory Bank structure | âœ… Complete |
        | 2025-04-27 | Systematic Codebase Analysis | Comprehensive analysis of redundancy and duplication | ðŸŸ¡ Planned |
        | 2025-04-27 | Establish Refactoring Strategy | Define iterative approach with clear stages | ðŸŸ¡ Planned |
        | 2025-04-28 | Core Component Library Design | Define shared component interfaces and patterns | ðŸŸ¡ Planned |
        | 2025-04-29 | First Refactoring Iteration | Implement initial component consolidation | ðŸŸ¡ Planned |
        | 2025-04-30 | Domain-Driven Structure Implementation | Begin incremental feature reorganization | ðŸŸ¡ Planned |
        | 2025-05-01 | Hook/Utility Consolidation | Refactor shared logic patterns | ðŸŸ¡ Planned |
        | 2025-05-02 | Dynamic Image System Implementation | Transform static image handling | ðŸŸ¡ Planned |
        | 2025-05-03 | Final Integration & Validation | Complete integration testing | ðŸŸ¡ Planned |

        ## Value Delivery Assessment

        | Value Proposition | Implementation Status | Evidence | Gaps |
        |-------------------|----------------------|----------|------|
        | **Modern, Responsive Website** | ðŸŸ¢ On Track | React/TypeScript stack, Mobile-first approach | Need to verify responsiveness |
        | **Authentic Owner Representation** | ðŸŸ¢ On Track | Content focus on personal approach | Implementation verification needed |
        | **Regional Focus (Hyperlocal SEO)** | ðŸŸ¢ On Track | Clear strategy documentation | Implementation verification needed |
        | **Service Showcase** | ðŸŸ¢ On Track | Service data structure established | Filtering implementation verification needed |
        | **Seasonal Adaptation** | ðŸŸ¡ Partial | Data structures support concept | Implementation mechanism unknown |
        | **Lead Generation** | ðŸŸ¡ Partial | Contact page routing exists | Form implementation details needed |

        ## Next Assessment Focus Areas

        1. **Redundancy and Duplication Analysis**
           - Create comprehensive inventory of code duplication
           - Map component redundancies across features
           - Identify patterns of repeated logic
           - Establish baseline metrics for current state

        2. **Refactoring Strategy Development**
           - Define clear boundaries for incremental refactoring
           - Establish validation criteria for each step
           - Create testing protocols for maintaining functionality
           - Design dependency-aware refactoring sequence

        3. **Component Library Design**
           - Analyze existing component patterns
           - Define unified component interfaces
           - Design composition patterns for extensibility
           - Create migration path from current components

        4. **Domain-Driven Architecture Planning**
           - Map business domains to code organization
           - Define module boundaries and interfaces
           - Design shared code strategy
           - Create incremental migration plan
    ```

    ---

    #### `memory-bank\7-tasks.md`

    ```markdown
        # Tasks: Ringerike Landskap Website

        ## Task Prioritization Framework

        Tasks are prioritized based on:
        1. **Value Alignment**: Contribution to the core purpose defined in `1-projectbrief.md`
        2. **Complexity Reduction**: Net reduction in system complexity
        3. **Implementation Difficulty**: Resource requirements and technical risk
        4. **Dependencies**: Prerequisite relationships between tasks

        Priority levels:
        - **P0**: Critical path - must be completed before other tasks
        - **P1**: High importance - significant value, should be prioritized
        - **P2**: Medium importance - valuable but can be deferred
        - **P3**: Low importance - consider if resources permit

        ## Analysis Tasks

        ### A1: Systematic Codebase Analysis for Redundancy and Duplication
        **Priority**: P0
        **Value Proposition**: Comprehensive analysis to identify all instances of redundancy and duplication across the codebase, establishing a clear roadmap for iterative refactoring.
        **Connection to Root Purpose**: Ensures the system can be maintained efficiently while continuing to deliver the authentic, personalized representation required by the project brief.

        **Steps**:
        1. Map all component exports and identify duplicate functionality
        2. Analyze component implementation patterns across the codebase
        3. Create dependency graphs to visualize relationships and identify redundancies
        4. Document all instances of code duplication with severity ratings
        5. Catalog UI component redundancies (buttons, cards, forms, etc.)
        6. Identify duplicate logic patterns across different features
        7. Create baseline metrics for code quality and complexity

        **Expected Yield**:
        - Complete inventory of redundancies and duplications
        - Severity classification of each issue
        - Dependency mapping showing interconnections
        - Prioritized refactoring roadmap
        - Baseline metrics for measuring improvement

        **Success Metrics**:
        - Coverage of > 95% of codebase in analysis
        - Clear documentation of all redundancies
        - Actionable prioritization for refactoring tasks
        - Measurable complexity/redundancy metrics

        ### A2: Establish Iterative Refactoring Strategy
        **Priority**: P0
        **Value Proposition**: Sequential, incremental approach to refactoring that minimizes risks while systematically eliminating redundancy.
        **Connection to Root Purpose**: Ensures maintenance of system integrity throughout the refactoring process, preserving the website's ability to showcase Ringerike Landskap effectively.

        **Steps**:
        1. Define clear boundaries for each refactoring iteration
        2. Establish validation criteria for each refactoring step
        3. Create rollback procedures for each planned change
        4. Design verification tests for pre/post refactoring comparison
        5. Schedule refactoring stages with explicit dependencies
        6. Define completion criteria for each refactoring phase

        **Expected Yield**:
        - Comprehensive refactoring roadmap with clear stages
        - Risk mitigation strategy for each refactoring step
        - Validation protocols for maintaining system integrity
        - Schedule with measurable milestones

        **Success Metrics**:
        - Clear sequence of refactoring steps with dependencies
        - Validation tests for each refactoring phase
        - Minimal risk profile through incremental approach
        - Measurable progress tracking mechanisms

        ### A2: Custom Hook Implementation Analysis
        **Priority**: P1
        **Value Proposition**: Understanding the central filtering system and state management approach to identify optimization opportunities.
        **Connection to Root Purpose**: The filtering system is core to the service and project showcase capabilities that connect customers with relevant services.

        **Steps**:
        1. Locate all custom hook implementations
        2. Analyze useFilteredData hook in detail
        3. Map hook usage patterns across components
        4. Evaluate memoization and performance techniques
        5. Document state management patterns

        **Expected Yield**:
        - Understanding of core filtering mechanism
        - Identification of optimization opportunities
        - Evaluation of state management effectiveness
        - Foundation for potential performance improvements

        **Success Metrics**:
        - Hook coverage completeness
        - Performance pattern identification
        - Usage pattern documentation

        ### A3: SEO Implementation Audit
        **Priority**: P1
        **Value Proposition**: Verify that the hyperlocal SEO strategy is properly implemented to ensure regional visibility.
        **Connection to Root Purpose**: Direct connection to regional focus constraint and visibility requirements.

        **Steps**:
        1. Analyze Meta component implementation
        2. Review structured data markup
        3. Verify local SEO optimizations
        4. Check keyword implementation in content
        5. Evaluate mobile optimization for SEO

        **Expected Yield**:
        - Assessment of SEO implementation completeness
        - Identification of SEO enhancement opportunities
        - Verification of hyperlocal optimization

        **Success Metrics**:
        - SEO best practices compliance
        - Local optimization implementation
        - Structured data completeness

        ### A4: Seasonal Adaptation Mechanism Analysis
        **Priority**: P2
        **Value Proposition**: Understanding how the seasonal content adaptation works to evaluate effectiveness and potential improvements.
        **Connection to Root Purpose**: Seasonal adaptation is a core business requirement that affects service prioritization and customer relevance.

        **Steps**:
        1. Locate seasonal detection mechanism
        2. Map seasonal influence on component rendering
        3. Analyze content prioritization logic
        4. Evaluate UI adaptation implementation
        5. Document seasonal data flow

        **Expected Yield**:
        - Understanding of seasonal implementation
        - Identification of adaptation opportunities
        - Documentation of seasonal logic

        **Success Metrics**:
        - Mechanism documentation completeness
        - Adaptation pattern identification
        - Season detection logic understanding

        ### A5: Accessibility Implementation Assessment
        **Priority**: P2
        **Value Proposition**: Verify that accessibility requirements are properly implemented to ensure WCAG compliance.
        **Connection to Root Purpose**: Supports the inclusive nature of the site and ensures the widest possible audience can access services.

        **Steps**:
        1. Audit semantic HTML usage
        2. Evaluate ARIA attribute implementation
        3. Test keyboard navigation
        4. Check color contrast compliance
        5. Assess screen reader compatibility

        **Expected Yield**:
        - Accessibility compliance assessment
        - Identification of improvement opportunities
        - Documentation of current accessibility patterns

        **Success Metrics**:
        - WCAG compliance level determination
        - Accessibility pattern documentation
        - Improvement opportunity identification

        ## Implementation Tasks

        ### I1: Route Path Refactoring
        **Priority**: P2
        **Value Proposition**: Simplify URL structure for improved usability and SEO by updating paths as noted in documentation.
        **Connection to Root Purpose**: Enhances the clarity of navigation and page URLs, supporting both user experience and SEO goals.

        **Steps**:
        1. Update route definitions in App.tsx
        2. Change `/hva-vi-gjor` to `/hva`
        3. Change `/hvem-er-vi` to `/hvem`
        4. Update any internal links to these routes
        5. Add redirects for backward compatibility

        **Expected Yield**:
        - Cleaner URL structure
        - More memorable URLs
        - Maintained backward compatibility

        **Success Metrics**:
        - All route references updated
        - No broken links after change
        - Redirect functioning properly

        **Technical Risk**: Low - straightforward path updates with redirects for compatibility

        ### I2: Implement Unified Component Library
        **Priority**: P1
        **Value Proposition**: Create a unified component library that eliminates duplication while maintaining clear boundaries between shared and feature-specific components.
        **Connection to Root Purpose**: Improves maintainability and developer experience, ensuring continued high-quality representation of the company while reducing technical debt.

        **Steps**:
        1. Create core UI component library structure
        2. Extract common patterns from duplicate components
        3. Design consistent component interfaces with TypeScript
        4. Implement unified styling approach using Tailwind CSS
        5. Create composition patterns for component extension
        6. Add comprehensive documentation with usage examples
        7. Implement automated tests for shared components
        8. Create migration guide for transitioning from duplicated components

        **Expected Yield**:
        - Significant reduction in code duplication
        - Consistent component API across the application
        - Improved maintainability through standardization
        - Enhanced developer experience with clear documentation
        - Reduced technical debt and cognitive load

        **Success Metrics**:
        - 80%+ reduction in component duplication
        - 100% type safety for component APIs
        - Comprehensive documentation for all components
        - Clear boundaries between shared and feature-specific code
        - Successful migration from duplicate implementations

        **Technical Risk**: Medium - requires careful interface design and migration strategy

        ### I3: Refactor to Domain-Driven Feature Organization
        **Priority**: P1
        **Value Proposition**: Transform the project structure into a domain-driven organization that improves cohesion while maintaining clear separation of concerns.
        **Connection to Root Purpose**: Enhances maintainability and authentic representation through logical organization that mirrors business domains.

        **Steps**:
        1. Design feature-based directory structure aligned with business domains
        2. Define modular boundaries between features with clear interfaces
        3. Refactor code incrementally, feature by feature
        4. Establish shared utilities and hooks as separate modules
        5. Migrate components to appropriate feature modules
        6. Update import paths while maintaining functionality
        7. Validate each step with comprehensive testing
        8. Document architectural decisions and patterns

        **Expected Yield**:
        - Improved code organization that mirrors business domains
        - Clear separation of concerns with explicit boundaries
        - Reduced complexity through logical grouping
        - Better scaling capabilities for future features
        - Enhanced developer experience through intuitive structure

        **Success Metrics**:
        - Successful incremental migration with no regressions
        - Clear feature boundaries with minimal cross-feature dependencies
        - Improved modularity scores in static analysis
        - Maintainable separation between shared and feature-specific code
        - Documentation of architectural patterns and decisions

        **Technical Risk**: Medium - requires careful incremental approach and validation

        **Expected Yield**:
        - Significantly reduced code maintenance for image handling
        - More scalable system for adding new project/service images
        - Improved error resilience for media assets
        - Consistent image loading pattern across features

        **Success Metrics**:
        - Elimination of hardcoded image paths
        - Successful dynamic loading of all image categories
        - Proper error handling for missing images
        - Maintained or improved performance metrics

        **Technical Risk**: Medium - requires careful transition without breaking existing image displays

        ### I4: Filter System Performance Optimization
        **Priority**: P2
        **Value Proposition**: Improve user experience with faster filtering response, especially for larger data sets.
        **Connection to Root Purpose**: Enhanced filtering experience helps users find relevant services and projects more efficiently.

        **Steps**:
        1. Analyze current filter hook implementation
        2. Identify performance bottlenecks
        3. Implement memoization optimizations
        4. Add virtualization for large lists
        5. Optimize filter calculation logic

        **Expected Yield**:
        - Improved filtering response time
        - Better handling of larger data sets
        - Smoother user experience

        **Success Metrics**:
        - 50%+ reduction in filtering computation time
        - Smooth scrolling with large data sets
        - Maintained type safety

        **Technical Risk**: Medium - requires careful optimization without breaking existing functionality

        ### I4: Accessibility Enhancement
        **Priority**: P2
        **Value Proposition**: Ensure WCAG AA compliance to provide inclusive access and meet legal requirements.
        **Connection to Root Purpose**: Makes the website accessible to all potential customers, supporting the inclusive nature of the business.

        **Steps**:
        1. Address semantic HTML issues
        2. Add missing ARIA attributes
        3. Enhance keyboard navigation
        4. Fix any color contrast issues
        5. Improve screen reader compatibility

        **Expected Yield**:
        - WCAG AA compliance
        - Improved accessibility for all users
        - Reduced legal risk

        **Success Metrics**:
        - WCAG AA compliance verification
        - Keyboard navigation for all interactive elements
        - Proper screen reader compatibility

        **Technical Risk**: Medium - may require UI component adjustments

        ### I5: SEO Enhancement Implementation
        **Priority**: P1
        **Value Proposition**: Strengthen hyperlocal SEO to improve visibility in the Ringerike region.
        **Connection to Root Purpose**: Directly supports the regional focus constraint by enhancing local search visibility.

        **Steps**:
        1. Enhance structured data implementation
        2. Optimize meta tag generation
        3. Improve local keyword implementation
        4. Add region-specific schema markup
        5. Implement JSON-LD for local business

        **Expected Yield**:
        - Improved local search visibility
        - Enhanced rich snippet opportunities
        - Better regional targeting

        **Success Metrics**:
        - Structured data validation passes
        - Local business markup completeness
        - Meta tag optimization for local terms

        **Technical Risk**: Low - primarily enhancement of existing SEO approach

        ## Future Consideration Tasks

        ### F1: Automated Accessibility Testing
        **Priority**: P3
        **Value Proposition**: Maintain accessibility compliance through automated testing.
        **Connection to Root Purpose**: Ensures continued inclusivity and quality of the digital showcase.

        **Steps**:
        1. Research accessibility testing tools
        2. Implement automated accessibility checks
        3. Integrate with build pipeline
        4. Create accessibility reporting
        5. Document remediation process

        **Expected Yield**:
        - Proactive accessibility monitoring
        - Consistent compliance over time
        - Early detection of issues

        **Success Metrics**:
        - Automated testing implementation
        - Integration with development workflow
        - Documentation of process

        **Technical Risk**: Medium - requires tooling selection and integration

        ### F2: Performance Monitoring Implementation
        **Priority**: P3
        **Value Proposition**: Track and maintain performance metrics to ensure optimal user experience.
        **Connection to Root Purpose**: Supports the technical performance requirements defined in the project brief.

        **Steps**:
        1. Implement Core Web Vitals monitoring
        2. Add performance budgeting
        3. Set up alerting for regressions
        4. Create performance dashboarding
        5. Document optimization process

        **Expected Yield**:
        - Proactive performance monitoring
        - Consistent performance over time
        - Early detection of regressions

        **Success Metrics**:
        - Monitoring system implementation
        - Baseline performance metrics
        - Documentation of process

        **Technical Risk**: Medium - requires tooling selection and integration

        ## Integration Opportunities

        ### O1: Seasonal Content Enhancement
        **Priority**: P3
        **Value Proposition**: Enhance the seasonal adaptation system to provide more targeted content.
        **Connection to Root Purpose**: Strengthens the seasonal relevance of services, which is a key business differentiator.

        **Concept**:
        - Expand seasonal tagging beyond current implementation
        - Create more granular seasonal recommendations
        - Enhance UI adaptation based on season
        - Implement predictive seasonal content

        **Potential Value**:
        - More relevant seasonal content presentation
        - Enhanced user experience across seasons
        - Stronger seasonal business targeting

        **Exploration Needed**:
        - Current seasonal implementation details
        - Seasonal data model extensions
        - UI adaptation capabilities

        ### O2: Hyperlocal Content Personalization
        **Priority**: P3
        **Value Proposition**: Personalize content based on user location within the Ringerike region.
        **Connection to Root Purpose**: Further enhances the regional focus by providing location-specific content.

        **Concept**:
        - Implement location-aware content prioritization
        - Create area-specific project showcases
        - Develop location-based service recommendations

        **Potential Value**:
        - Highly targeted content for local areas
        - Enhanced relevance for specific communities
        - Stronger local connection with customers

        **Exploration Needed**:
        - Location detection capabilities
        - Content regionalization strategy
        - Data model for location tagging
    ```

    ---

    #### `memory-bank\8-metaPhilosophy.md`

    ```markdown
        # Meta Philosophy: Ringerike Landskap Memory Bank

        ## Conceptual Foundation

        The Ringerike Landskap Memory Bank is built on the philosophical foundation of the **Primacy of Contextual Abstraction**—the principle that all understanding must flow from and reconnect to the most abstract, value-centric comprehension of a project. This approach transforms documentation from passive storage into an active cognitive framework that shapes how we perceive and interact with the codebase.

        ## Structure as Cognitive Tool

        ### The Dual Nature of Structure

        The Memory Bank structure functions simultaneously as both lens and ledger:

        1. **As Lens**: The structure shapes how we perceive the codebase, focusing attention on abstractions before details and systematically directing understanding from purpose toward implementation. This hierarchical filtering system prevents detail overwhelm and maintains connection to core value.

        2. **As Ledger**: The structure records knowledge in a value-aligned way, capturing insights at their appropriate abstraction level and enforcing explicit connections between concrete details and abstract purpose. This ensures no information exists in isolation from the project's fundamental mission.

        ### Abstraction Hierarchy as Value Filter

        ```mermaid
        flowchart TD
            A[New Information] --> B{Value Filter}
            B --> C{Abstraction Level?}
            C -->|Root Purpose| D[1-projectbrief.md]
            C -->|Context| E[2-productContext.md]
            C -->|Architecture| F[3-systemPatterns.md]
            C -->|Technology| G[4-techContext.md]
            C -->|Current Analysis| H[5-activeContext.md]
            C -->|Status & Progress| I[6-progress.md]
            C -->|Action| J[7-tasks.md]
            B --> K{Complexity Reduction?}
            K -->|Yes| L[Accept Information]
            K -->|No| M[Reject or Refactor]
        ```

        Information is admitted to the Memory Bank only when:
        1. Its abstraction level is clearly identified
        2. It provides demonstrable complexity reduction
        3. It connects explicitly to root purpose

        This filtering mechanism ensures the Memory Bank maintains high value density by trading information volume for insight clarity.

        ## Knowledge Persistence Through Change

        ### Self-Reinforcing Value Creation

        The Memory Bank creates value that persists and compounds through:

        1. **Abstraction-First Navigation**: By consistently beginning with the project's irreducible purpose (`1-projectbrief.md`), all understanding remains anchored to this invariant core, even as implementation details evolve.

        2. **Enforced Connection Paths**: Every insight must maintain traceable connections to the root purpose, creating a resilient web of understanding that can withstand changes to specific components.

        3. **Active Complexity Arbitrage**: The system continuously trades volume of information for clarity of insight, distilling raw information into valuable patterns that transcend implementation specifics.

        4. **Structure-Enforced Pruning**: The organization inherently exposes information that has become disconnected from purpose or lacks proper abstraction placement, naturally identifying content for removal or refactoring.

        5. **Quantum Essence Distillation**: The optional `0-distilledContext.md` provides rapid orientation to the irreducible core, enabling quick cognitive realignment even after memory resets.

        ### Maintaining Alignment Through Evolution

        As the codebase evolves, the Memory Bank maintains its value through:

        1. **Structure-First Updates**: Any update begins by validating that the structure itself remains optimal for the current state of understanding.

        2. **Root-Anchored Modification**: Changes to any file require explicit justification of how they enhance connection to the root purpose.

        3. **Abstraction Level Integrity**: Content modifications must respect established abstraction boundaries, with coherent organization favored over exhaustive detail.

        4. **Value-Density Monitoring**: Regular assessment of the Memory Bank's value density ensures it continues to provide maximum actionable insight with minimum cognitive overhead.

        ## Philosophical Underpinnings

        ### Knowledge as Connected Graph vs. Isolated Facts

        The Memory Bank rejects the notion of knowledge as a collection of isolated facts, instead treating understanding as a densely connected graph with the project's purpose at its center. This connected model means that:

        1. No insight has value in isolation
        2. Connection to purpose determines information value
        3. Patterns and principles outrank implementation details
        4. Abstraction serves as the primary tool for complexity management

        ### Complexity Reduction as Primary Value Driver

        The system is built on the philosophical stance that **complexity reduction is the primary driver of sustainable value** in software engineering. Complexity is actively managed through:

        1. **Abstraction Tiering**: Information organized by abstraction level
        2. **Pattern Extraction**: Identifying recurring patterns to reduce cognitive load
        3. **Explicit Connection**: Making implicit relationships explicit
        4. **Controlled Vocabulary**: Using consistent terminology across abstraction levels
        5. **Structured Simplification**: Removing details that don't contribute to essential understanding

        ### Anti-Entropy as Organizing Principle

        The Memory Bank operates as an anti-entropy system, continuously working against the natural tendency of information to become disordered over time:

        1. **Structure Validation**: Regular reassessment of structure fitness
        2. **Connection Enforcement**: Requiring explicit purpose connections
        3. **Value Justification**: Ongoing evaluation of information value
        4. **Complexity Monitoring**: Tracking and addressing rising complexity
        5. **Active Pruning**: Removing information that no longer provides net value

        ## Value Maintenance Mechanisms

        ### Structure Validation Protocol

        The structure validation process—executed as the mandatory first step in any Memory Bank interaction—embodies the system's core philosophy:

        1. Begin by questioning the structure itself, not its contents
        2. Assess the structure's effectiveness as both lens and ledger
        3. Determine if the current abstraction hierarchy optimally serves understanding
        4. Make structural adjustments before content modifications
        5. Document the rationale for structural decisions

        This validation creates a self-improving system that evolves toward increasing clarity and value density.

        ### Connection Density Optimization

        The Memory Bank optimizes for connection density—the number of meaningful relationships between pieces of information—rather than information volume:

        ```mermaid
        graph TD
            subgraph "Low Value Information"
                A[Isolated Fact 1]
                B[Isolated Fact 2]
                C[Isolated Fact 3]
                D[Isolated Fact 4]
            end

            subgraph "High Value Memory Bank"
                E[Purpose] --- F[Pattern 1]
                E --- G[Pattern 2]
                F --- H[Implementation 1]
                F --- I[Implementation 2]
                G --- J[Implementation 3]
                G --- K[Implementation 4]
                H --- I
                I --- J
            end
        ```

        This connection-centric approach:
        1. Facilitates insight discovery through relationship traversal
        2. Makes the system resilient to partial information loss
        3. Creates emergent understanding that exceeds the sum of individual facts
        4. Naturally identifies knowledge gaps as missing connections

        ### Value Density Maintenance

        The Memory Bank actively maintains high value density through:

        1. **Information Replacement**: New insights replace rather than supplement existing content when covering the same concept
        2. **Pattern Elevation**: Recurring details are abstracted into named patterns
        3. **Root Reconnection**: Periodic verification that all content maintains connection to purpose
        4. **Entropy Detection**: Regular scanning for disconnected or redundant information
        5. **Minimalist Optimization**: Continuous refinement toward the minimum information needed for maximum understanding

        ## Practical Application Philosophy

        ### Using the Memory Bank to Drive Understanding

        The Memory Bank is designed to be used in specific ways that maximize its value:

        1. **Sequential First Read**: Initial orientation through reading files in numerical order (0-7)
        2. **Purpose-First Reference**: Any subsequent reference begins with `1-projectbrief.md`
        3. **Targeted Depth Traversal**: Follow explicit connections to navigate from purpose to specific implementation details
        4. **Connection-Based Context**: Use the connection network to establish context for specific questions
        5. **Structure Maintenance First**: Always begin with structure validation before content updates

        ### Maintaining The Cognitive Framework

        The Memory Bank is not a static repository but a dynamic cognitive framework that requires active maintenance:

        1. **Regular Structure Validation**: Question and refine the structure regularly
        2. **Value-Justified Updates**: Every update must demonstrate complexity reduction
        3. **Connection Verification**: Ensure all information maintains clear purpose connections
        4. **Abstraction Level Integrity**: Place information at its appropriate abstraction level
        5. **Continuous Simplification**: Always seek further simplification without losing essential understanding

        ## Final Principles

        ### The Essential Nature of the Memory Bank

        The Memory Bank for the Ringerike Landskap website is built on three essential principles:

        1. **Persistent Maximum Actionable Value**: Every aspect of the Memory Bank serves to maximize actionable value that persists through changes and cognitive resets.

        2. **Root-First Understanding**: All knowledge flows from the irreducible purpose of creating a digital showcase that authentically represents Ringerike Landskap's personalized approach to landscaping services in the Ringerike region.

        3. **Complexity Reduction Above All**: The system relentlessly trades information volume for clarity of insight, creating a model that provides more understanding with less cognitive overhead.

        These principles transform the Memory Bank from mere documentation into a living cognitive framework that shapes how the codebase is perceived, understood, and evolved.
    ```

    ---

    #### `project\memory-bank\0-distilledContext.md`

    ```markdown
        # Distilled Context: Ringerike Landskap Website

        ## Core Purpose
        A modern, maintainable React TypeScript website for a landscaping company based in Ringerike, Norway that showcases services, projects, testimonials, and offers contact information.

        ## Primary Cleanup Goals
        1. Eliminate component duplication across directories
        2. Consolidate related functionality into feature modules
        3. Create a clear shared UI component library
        4. Standardize directory structure and naming patterns
        5. Optimize import paths with aliases
        6. Reduce overall complexity while preserving all functionality
    ```

    ---

    #### `project\memory-bank\1-projectbrief.md`

    ```markdown
        # Project Brief: Ringerike Landskap Website

        ## Project Overview
        The Ringerike Landskap website is a digital platform for a Norwegian landscaping and construction company based in the Ringerike region. It serves as both a marketing tool and a portfolio showcase for their landscaping services, completed projects, and customer testimonials.

        ## Business Goals
        1. Showcase the company's landscaping and construction services
        2. Display a portfolio of completed projects to demonstrate expertise
        3. Present customer testimonials to build trust
        4. Provide information about service areas in the Ringerike region
        5. Facilitate contact and inquiries from potential customers
        6. Highlight local expertise and knowledge of regional conditions

        ## Target Audience
        - Homeowners in the Ringerike region looking for landscaping services
        - Property developers requiring professional landscaping
        - Commercial property owners needing outdoor space design and implementation
        - Local customers familiar with Norwegian language (site appears to be in Norwegian)

        ## Key Features
        1. **Service Catalog**: Comprehensive listing of all offered services
        2. **Project Portfolio**: Gallery of completed projects with filtering capability
        3. **Testimonials Section**: Customer reviews and feedback
        4. **Service Area Map**: Visual representation of areas served
        5. **Seasonal Content**: Weather-adapted service recommendations
        6. **Local Expertise Highlights**: Content emphasizing regional knowledge
        7. **Contact Information**: Forms and details for reaching the company
        8. **Responsive Design**: Functions well on all device sizes

        ## Technical Requirements
        - Built with React 18 and TypeScript 5
        - Vite as build tool for fast development
        - Tailwind CSS for styling
        - React Router for navigation
        - SEO optimized with proper metadata
        - Image optimization for performance
        - Filtering system for projects and services

        ## Success Criteria
        - Clear presentation of services and expertise
        - Intuitive navigation and user experience
        - Fast loading times and performance
        - Mobile-friendly responsive design
        - Accurate representation of the company's work
        - Easy contact methods for potential customers
    ```

    ---

    #### `project\memory-bank\2-productContext.md`

    ```markdown
        # Product Context: Ringerike Landskap Website

        ## Key User Needs

        1. **Find Suitable Landscaping Services**
           - Easily browse available services
           - Filter services by type/need
           - Access detailed information about each service
           - See visual examples of work quality

        2. **Evaluate Company Credibility**
           - View portfolio of past projects
           - Read customer testimonials
           - Understand company experience and expertise
           - Verify service area coverage

        3. **Make Contact Decisions**
           - Find contact information easily
           - Submit inquiries through forms
           - Understand the process for engaging services
           - Access seasonal specials or promotions

        4. **Navigate Efficiently**
           - Intuitive menu and navigation
           - Responsive design for all devices
           - Quick loading and responsive interface
           - Clear calls to action

        ## Core User Journeys

        ### Journey 1: Service Discovery
        1. Land on homepage
        2. Browse featured services
        3. Filter services based on needs
        4. View service details
        5. See relevant projects using that service
        6. Contact for inquiry

        ### Journey 2: Project Portfolio Exploration
        1. Navigate to projects section
        2. Filter projects by category or location
        3. View project details and specifications
        4. See related services
        5. Read associated testimonials
        6. Contact for similar work

        ### Journey 3: Local Service Verification
        1. Check service area coverage
        2. View local expertise information
        3. See weather-adapted services
        4. Read about regional specialization
        5. Verify company details
        6. Make contact decision

        ## Feature Justification

        Each component in the system should serve one or more of these user needs:

        | Feature | User Need | Component Justification |
        |---------|-----------|-------------------------|
        | Service Catalog | Find suitable services | Service listings, filtering, detail views |
        | Project Portfolio | Evaluate credibility | Project galleries, filter mechanisms, detail views |
        | Testimonials | Evaluate credibility | Testimonial displays, rating visuals |
        | Service Area Map | Verify local service | Map visualization, area listings |
        | Seasonal Content | Relevant service timing | Seasonal CTAs, weather-adapted displays |
        | Contact Forms | Make contact decisions | Form components, validation, submission |
        | Navigation | Navigate efficiently | Header, footer, menu components |
        | Responsive Design | Access on any device | Layout components, responsive containers |

        ## Critical Functionality to Preserve

        1. **Filtering System**
           - The `useFilteredData` hook provides essential functionality across the application
           - Filter UI components must maintain their current behavior
           - Filter state and interactions must remain consistent

        2. **Image Display**
           - Image galleries and project showcases are central to demonstrating quality
           - Image optimization and lazy loading should be preserved
           - Responsive image behavior must be maintained

        3. **SEO Optimization**
           - Metadata components and schema.org markup
           - Semantic HTML structure
           - Page titles and descriptions

        4. **Seasonal Adaptations**
           - Components that display weather-relevant or seasonal content
           - Service recommendations based on time of year
           - CTAs that change with seasons

        5. **Local Expertise Presentation**
           - Service area visualization
           - Local knowledge highlighting
           - Regional specialization content
    ```

    ---

    #### `project\memory-bank\3-systemPatterns.md`

    ```markdown
        # System Patterns: Ringerike Landskap Website

        ## Target Architecture Overview

        The target architecture follows a feature-based organization with clear separation between shared UI components and feature-specific components. The system will use clear boundaries and consistent patterns throughout.

        ```mermaid
        flowchart TD
            A[Entry Point] --> B[App/Routes]
            B --> C[Pages]
            C --> D[Features]
            D --> E[UI Components]
            D --> F[Feature-specific Components]
            E --> G[UI Primitives]
            F --> E
        ```

        ## Directory Structure

        ```
        src/
        ├── app/                    # Application setup and routes
        │   ├── App.tsx             # Main app component with routing
        │   └── main.tsx            # Entry point
        ├── assets/                 # Static assets
        ├── components/             # Non-primitive UI components (composite)
        │   ├── layout/             # Layout components
        │   │   ├── Header/         # Header component
        │   │   └── Footer/         # Footer component
        │   └── common/             # Shared composite components
        ├── features/               # Feature modules
        │   ├── services/           # Service-related feature
        │   │   ├── components/     # Feature-specific components
        │   │   ├── hooks/          # Feature-specific hooks
        │   │   ├── types/          # Feature-specific types
        │   │   └── index.ts        # Public API
        │   ├── projects/           # Project-related feature
        │   ├── testimonials/       # Testimonial-related feature
        │   └── contact/            # Contact-related feature
        ├── hooks/                  # Shared hooks
        ├── pages/                  # Page components
        │   ├── home/               # Home page
        │   ├── services/           # Services page
        │   ├── projects/           # Projects page
        │   └── contact/            # Contact page
        ├── ui/                     # UI primitive components
        │   ├── button/             # Button component
        │   ├── container/          # Container component
        │   ├── hero/               # Hero component
        │   └── index.ts            # UI component exports
        ├── utils/                  # Utility functions
        ├── lib/                    # Shared library code
        │   ├── api/                # API-related functions
        │   ├── types/              # Shared type definitions
        │   └── utils/              # Utility functions
        ├── styles/                 # Global styles
        └── config/                 # Configuration
        ```

        ## Component Organization Patterns

        ### UI Primitive Components

        UI primitive components follow this structure:

        ```
        component-name/
        ├── ComponentName.tsx       # Component implementation
        ├── ComponentName.test.tsx  # Component tests (future)
        └── index.ts                # Export file
        ```

        Example `index.ts`:
        ```typescript
        export { default } from './ComponentName';
        export type { ComponentNameProps } from './ComponentName';
        ```

        ### Feature Modules

        Feature modules follow this structure:

        ```
        feature-name/
        ├── components/             # Feature-specific components
        │   ├── FeatureComponent/   # Component organized like UI components
        │   └── index.ts            # Export file
        ├── hooks/                  # Feature-specific hooks
        │   ├── useFeatureHook.ts   # Custom hook for feature
        │   └── index.ts            # Export file
        ├── types/                  # Feature-specific types
        ├── utils/                  # Feature-specific utilities
        └── index.ts                # Public API
        ```

        Example `index.ts`:
        ```typescript
        export * from './components';
        export * from './hooks';
        export { featureFunction } from './utils';
        ```

        ### Pages

        Page components follow this structure:

        ```
        page-name/
        ├── PageName.tsx            # Page component
        ├── sections/               # Page-specific sections
        │   ├── SectionName.tsx     # Section component
        │   └── index.ts            # Export file
        └── index.ts                # Export file
        ```

        Example `index.ts`:
        ```typescript
        export { default } from './PageName';
        ```

        ## Import Path Patterns

        The project will use path aliases for cleaner imports:

        ```typescript
        // Current approach (relative paths)
        import Button from '../../ui/button/Button';
        import { useFilteredData } from '../../../hooks/useFilteredData';

        // Target approach (aliased paths)
        import { Button } from '@ui';
        import { useFilteredData } from '@hooks';
        ```

        ### Path Alias Structure

        ```typescript
        // tsconfig.json and vite.config.ts
        {
          '@': './src',
          '@ui': './src/ui',
          '@components': './src/components',
          '@features': './src/features',
          '@hooks': './src/hooks',
          '@utils': './src/utils',
          '@lib': './src/lib',
          '@pages': './src/pages',
          '@assets': './src/assets',
          '@config': './src/config',
          '@types': './src/types'
        }
        ```

        ## Component Composition Strategy

        Components will be composed following a clear hierarchy:

        ```mermaid
        flowchart TD
            A[Primitives] --> B[Composite Components]
            B --> C[Feature Components]
            C --> D[Page Sections]
            D --> E[Pages]
        ```

        1. **UI Primitives**: Basic building blocks (buttons, inputs, containers)
        2. **Composite Components**: Combinations of primitives (forms, cards, etc.)
        3. **Feature Components**: Domain-specific components using composites and primitives
        4. **Page Sections**: Larger page areas composed of feature components
        5. **Pages**: Full pages composed of sections

        ## Data Flow Pattern

        Data flow will follow this pattern:

        ```mermaid
        flowchart TD
            A[API/Local Data] --> B[Hooks]
            B --> C[Feature Components]
            C --> D[Page Components]
            A --> E[Global State - future]
            E --> C
        ```

        1. **Data Source**: Data from API or local files
        2. **Data Loading**: Using hooks to fetch and manage data
        3. **Component Consumption**: Components receive data via props
        4. **User Interaction**: Components dispatch actions or update local state

        ## Naming Conventions

        - **Files**: PascalCase for components, camelCase for utilities/hooks
        - **Directories**: kebab-case for directories
        - **Component Props**: Suffixed with `Props` (e.g., `ButtonProps`)
        - **Hooks**: Prefixed with `use` (e.g., `useFilteredData`)
        - **Feature Exports**: Named exports for clarity
        - **Component Directories**: Singular form (e.g., `button/` not `buttons/`)

        ## SVG and Asset Handling

        - SVG components will be placed in `@ui/icons/`
        - Static assets will be organized by type in `@assets/`
        - Consistent use of Tailwind for styling

        ## SEO Pattern

        - Metadata components will be centralized in `@components/seo/`
        - Page components will compose SEO metadata
        - Schema.org markup will be organized by entity type
    ```

    ---

    #### `project\memory-bank\4-techContext.md`

    ```markdown
        # Technical Context: Ringerike Landskap Website

        ## Tech Stack Details

        | Technology | Version | Purpose | Implementation Notes |
        |------------|---------|---------|---------------------|
        | React | 18.3.1 | UI framework | Using functional components with hooks |
        | TypeScript | 5.5.3 | Type safety | Strict mode enabled |
        | Vite | 5.4.2 | Build tool | Fast HMR for development |
        | Tailwind CSS | 3.4.1 | Styling | Utility-first CSS approach |
        | React Router | 6.22.3 | Routing | Client-side routing |
        | React Helmet | 6.1.0 | SEO | Metadata management |
        | Framer Motion | 12.5.0 | Animations | Used for transitions |
        | Lucide React | 0.344.0 | Icons | SVG icon library |

        ## Current Directory Structure Issues

        The current project structure has several issues:

        1. **Duplication across directories**:
           - Components like `Logo` appear in both `components/ui` and `components/common`
           - Service-related components exist in multiple locations
           - Layout components have unclear boundaries

        2. **Inconsistent component organization**:
           - Some components are in directories with index files
           - Others are direct .tsx files in parent directories
           - No clear pattern for where components should go

        3. **Unclear feature boundaries**:
           - Feature files are mixed with generic components
           - No clear separation between features and shared components
           - Features directory structure is inconsistent

        4. **Import complexity**:
           - Deeply nested relative imports (../../..)
           - No path aliases for simplified imports
           - Inconsistent import patterns

        5. **Mixed component granularity**:
           - UI primitives and complex components mixed together
           - No clear hierarchy of component composition
           - Similar components with different APIs

        ## Technical Constraints

        1. **TypeScript Configuration**:
           - Must maintain strict type checking
           - Need to set up path aliases in tsconfig.json
           - Should preserve existing type definitions

        2. **Vite Configuration**:
           - Must update path aliases in vite.config.ts to match tsconfig.json
           - Should optimize build process
           - Need to preserve fast HMR

        3. **Tailwind Integration**:
           - Must maintain consistent use of Tailwind utilities
           - Should centralize theme customization
           - Need to preserve responsive design utilities

        4. **React Router Setup**:
           - Must preserve existing routes
           - Should improve route organization
           - Need to maintain dynamic routing for detail pages

        5. **SEO Considerations**:
           - Must preserve React Helmet implementation
           - Should centralize metadata management
           - Need to maintain schema.org markup

        ## Existing Patterns to Preserve

        1. **Hooks Pattern**:
           - The `useFilteredData` hook is central to filtering functionality
           - The `useData` hook provides data fetching abstraction
           - Custom hooks follow React naming conventions

        2. **Component Props Pattern**:
           - Components use explicit prop interfaces
           - Props use consistent naming conventions
           - Proper use of default props and optional values

        3. **Utility Function Pattern**:
           - The `cn` utility for class name merging
           - Type-safe utility functions
           - Pure functions for data manipulation

        4. **Data Structure Pattern**:
           - Clear data types for services, projects, testimonials
           - Consistent data fetching and transformation
           - Type-safe data handling

        ## Performance Considerations

        1. **Image Optimization**:
           - Currently using WebP format for optimized images
           - Should preserve responsive image handling
           - Need to maintain optimized loading strategies

        2. **Bundle Size**:
           - Should implement code splitting for routes
           - Need to optimize dependency imports
           - Consider lazy loading for larger components

        3. **Render Performance**:
           - Should use memoization where appropriate
           - Need to optimize re-renders
           - Consider performance-sensitive component refactoring

        ## Developer Experience Goals

        1. **File Location Predictability**:
           - Clear, consistent patterns for where code lives
           - Intuitive directory structure
           - Reduced search time for finding components

        2. **Import Simplification**:
           - Path aliases for major directories
           - Shorter, more maintainable imports
           - Consistent import patterns

        3. **Component Discovery**:
           - Clear component API documentation
           - Consistent component directory structure
           - Easy-to-find component exports

        4. **Refactoring Safety**:
           - Type-safe component props
           - Centralized type definitions
           - Consistent naming patterns

        ## Testing Considerations

        1. **Component Testing**:
           - Structure should facilitate component tests
           - Pure components should be easily testable
           - Props should be designed for testability

        2. **Visual Regression Testing**:
           - Screenshots directory suggests visual testing
           - Structure should support visual regression tests
           - Component isolation should make testing easier

        3. **Integration Testing**:
           - Page components should be testable
           - Hooks should have clear testing strategies
           - Data flow should be testable
    ```

    ---

    #### `project\memory-bank\5-activeContext.md`

    ```markdown
        # Active Context: Component Duplication Analysis & Structure Issues

        ## Component Duplication Analysis

        ### UI Components

        | Component | Locations | Description | Consolidation Strategy |
        |-----------|-----------|-------------|------------------------|
        | `Logo` | `components/ui/Logo.tsx`<br>`components/common/Logo.tsx` | Company logo component | Move to `ui/logo/` as a primitive component |
        | `ServiceCard` | `components/ServiceCard.tsx`<br>`components/services/ServiceCard.tsx` | Card displaying service information | Move to `features/services/components/ServiceCard/` |
        | `Navbar` | `components/Navbar.tsx`<br>`components/layout/Navbar.tsx` | Navigation component | Move to `components/layout/Navbar/` |
        | `ServiceAreaList` | `components/common/ServiceAreaList.tsx`<br>`components/ui/ServiceAreaList.tsx` | List of service areas | Move to `features/services/components/ServiceAreaList/` |
        | `SeasonalCTA` | `components/common/SeasonalCTA.tsx`<br>`components/ui/SeasonalCTA.tsx` | Seasonal call-to-action | Move to `features/home/<USER>/SeasonalCTA/` |

        ### Feature Functionality

        | Feature | Current Locations | Issues | Consolidation Strategy |
        |---------|------------------|--------|------------------------|
        | Services | `components/services/`<br>`features/services.tsx`<br>`pages/Services.tsx` | Scattered across multiple directories | Consolidate in `features/services/` |
        | Projects | `components/projects/`<br>`features/projects.tsx`<br>`pages/Projects.tsx` | Inconsistent organization | Consolidate in `features/projects/` |
        | Testimonials | `components/seo/TestimonialsSchema.tsx`<br>`features/testimonials.tsx`<br>`pages/TestimonialsPage.tsx` | Mixed with SEO components | Consolidate in `features/testimonials/` |
        | Contact | `components/contact/`<br>`pages/contact/` | Missing feature organization | Create `features/contact/` |

        ## Directory Structure Assessment

        ### Inconsistent Component Organization

        1. **Mixed Component Styles**:
           - Some UI components are in dedicated directories (`ui/button/Button.tsx`)
           - Others are direct files (`components/ui/Logo.tsx`)
           - Different export patterns across components

        2. **Unclear Boundaries**:
           - `components/common/` vs `components/ui/` vs `ui/` - purpose overlap
           - `components/layout/` vs `layout/` - duplication
           - `shared/` directory with minimal usage

        3. **Multiple Layer Issues**:
           - Components in root `components/` directory
           - Also in feature-specific subdirectories
           - And in both `ui/` and `components/ui/`

        ### Import Path Complexity

        Current import patterns show excessive nesting and inconsistency:

        ```typescript
        // From src/components/ui/index.ts
        export { default as Button } from '../../ui/button';
        export { default as Container } from '../../ui/container';
        export { default as Hero } from '../../ui/hero';
        export { default as Logo } from './Logo';
        ```

        This mixing of relative imports from different depths creates maintenance challenges.

        ## Features Module Assessment

        ### Current Feature Organization

        The `features/` directory has an inconsistent structure:

        1. **Direct Feature Files**:
           - `features/home.tsx`
           - `features/projects.tsx`
           - `features/services.tsx`

        2. **Feature Directories**:
           - `features/home/<USER>
           - `features/projects/`
           - `features/services/`

        This creates confusion about where feature code should reside.

        ### Feature Component Organization

        Feature-specific components are scattered:

        1. Some in feature directories:
           - `features/home/<USER>

        2. Others in component directories:
           - `components/services/Gallery.tsx`
           - `components/projects/ProjectGrid.tsx`

        3. Some in page directories:
           - `pages/services/ServiceList.tsx` (inferred)

        ## Page Structure Assessment

        Pages have inconsistent organization:

        1. **Direct Page Files**:
           - `pages/Projects.tsx`
           - `pages/Services.tsx`

        2. **Directory-Based Pages**:
           - `pages/home/<USER>
           - `pages/contact/`

        ## Data and API Organization

        1. **Data Sources**:
           - `data/` directory with static data
           - `content/` directory with overlapping purpose
           - `lib/api/` for API functions

        2. **Type Definitions**:
           - Types in `types/` directory
           - Also in `lib/types.ts`
           - Feature-specific types mixed in components

        ## Key Issues to Address

        1. **Component Consolidation**:
           - Eliminate duplicate component implementations
           - Create consistent component organization pattern
           - Clear separation between UI primitives and feature components

        2. **Feature Organization**:
           - Consolidate feature-related code into feature modules
           - Move feature-specific components from general directories
           - Establish consistent feature module structure

        3. **Directory Structure**:
           - Clear boundaries between directories
           - Consistent patterns for component organization
           - Eliminate redundant directories

        4. **Import Path Optimization**:
           - Implement path aliases
           - Reduce relative import complexity
           - Create consistent import patterns

        5. **Type Organization**:
           - Consolidate type definitions
           - Feature-specific types in feature modules
           - Shared types in central location

        ## Current Focus

        The current cleanup focus is on:

        1. Analyzing component duplication to create a comprehensive migration plan
        2. Establishing clear directory structure
        3. Creating patterns for component organization
        4. Setting up path aliases for import simplification
        5. Planning the feature module migration sequence
    ```

    ---

    #### `project\memory-bank\6-progress.md`

    ```markdown
        # Progress Tracking: Ringerike Landskap Website Cleanup

        ## Structure Entropy Metrics

        | Metric | Initial State | Current State | Target State | Progress |
        |--------|--------------|--------------|--------------|----------|
        | Component Duplication Ratio | 1.4 | 1.4 | 1.0 | ğŸ”´ Not Started |
        | Directory Depth | 5+ | 5+ | 3-4 | ğŸ”´ Not Started |
        | Import Complexity | 3+ segments | 3+ segments | 1-2 | ğŸ”´ Not Started |
        | Type Coverage | Partial | Partial | 100% | ğŸ”´ Not Started |
        | Component API Consistency | Mixed | Mixed | Consistent | ğŸ”´ Not Started |

        ## Component Consolidation Progress

        | Component Type | Initial Count | Current Count | Target Count | Status |
        |----------------|--------------|---------------|-------------|--------|
        | Button/CTA | 2+ | 2+ | 1 | ğŸ”´ Not Started |
        | Logo | 2 | 2 | 1 | ğŸ”´ Not Started |
        | ServiceCard | 2 | 2 | 1 | ğŸ”´ Not Started |
        | Navbar/Header | 2 | 2 | 1 | ğŸ”´ Not Started |
        | ServiceAreaList | 2 | 2 | 1 | ğŸ”´ Not Started |
        | Layout Components | 3+ | 3+ | 2 | ğŸ”´ Not Started |

        ## Feature Module Migration Progress

        | Feature | Components Migrated | Hooks Migrated | Types Migrated | Utils Migrated | Status |
        |---------|---------------------|----------------|----------------|----------------|--------|
        | Services | 0% | 0% | 0% | 0% | ğŸ”´ Not Started |
        | Projects | 0% | 0% | 0% | 0% | ğŸ”´ Not Started |
        | Testimonials | 0% | 0% | 0% | 0% | ğŸ”´ Not Started |
        | Home | 0% | 0% | 0% | 0% | ğŸ”´ Not Started |
        | Contact | 0% | 0% | 0% | 0% | ğŸ”´ Not Started |

        ## Directory Structure Progress

        | Directory | Initial State | Target State | Status |
        |-----------|--------------|--------------|--------|
        | `/ui` | Mixed organization | Consistent primitives | ğŸ”´ Not Started |
        | `/components` | Inconsistent | Clear composite components | ğŸ”´ Not Started |
        | `/features` | Mixed files and directories | Feature modules | ğŸ”´ Not Started |
        | `/pages` | Mixed organization | Consistent page components | ğŸ”´ Not Started |
        | `/hooks` | Basic structure | Complete shared hooks | ğŸ”´ Not Started |
        | `/lib` | Mixed organization | Utility organization | ğŸ”´ Not Started |
        | `/types` | Mixed locations | Centralized types | ğŸ”´ Not Started |

        ## Path Alias Configuration Progress

        | Configuration | Status | Notes |
        |---------------|--------|-------|
        | tsconfig.json | ğŸ”´ Not Started | Path aliases need to be added |
        | vite.config.ts | ğŸ”´ Not Started | Path resolution needs to be configured |
        | Import updates | ğŸ”´ Not Started | All imports need to be updated to use aliases |

        ## Testing and Validation Progress

        | Validation Step | Status | Notes |
        |-----------------|--------|-------|
        | TypeScript Checking | ğŸ”´ Not Started | Ensure no type errors after restructuring |
        | Build Verification | ğŸ”´ Not Started | Ensure build succeeds after changes |
        | Visual Validation | ğŸ”´ Not Started | Compare screenshots before/after |
        | Functionality Testing | ğŸ”´ Not Started | Verify all features still work |

        ## Milestone Progress

        | Milestone | Status | Completion |
        |-----------|--------|------------|
        | Project Analysis | ğŸŸ¡ In Progress | 50% |
        | Configuration Setup | ğŸ”´ Not Started | 0% |
        | UI Component Consolidation | ğŸ”´ Not Started | 0% |
        | Feature Module Migration | ğŸ”´ Not Started | 0% |
        | Page Component Updates | ğŸ”´ Not Started | 0% |
        | Import Path Optimization | ğŸ”´ Not Started | 0% |
        | Final Testing and Validation | ğŸ”´ Not Started | 0% |

        ## Challenges and Solutions

        | Challenge | Solution | Status |
        |-----------|----------|--------|
        | Component duplication with different props | Create unified API | ğŸ”´ Not Started |
        | Deep relative imports | Implement path aliases | ğŸ”´ Not Started |
        | Mixed component patterns | Standardize on consistent pattern | ğŸ”´ Not Started |
        | Feature boundaries | Clear module organization | ğŸ”´ Not Started |
        | Import update effort | Batch updates by directory | ğŸ”´ Not Started |

        ## Next Steps

        1. Complete project analysis documentation
        2. Set up path aliases in configuration
        3. Start UI component consolidation
        4. Begin feature module migration
        5. Update page components to use new structure
    ```

    ---

    #### `project\memory-bank\7-tasks.md`

    ```markdown
        # Task Sequence: Ringerike Landskap Website Cleanup

        ## Phase 0: Preparation & Safety (1-2 hours)

        ### Task 0.1: Repository Preparation
        - [x] Create memory bank documents for project analysis
        - [ ] Create a new git branch for refactoring (`git checkout -b structure-cleanup`)
        - [ ] Ensure the project builds in its current state
        - [ ] Take baseline screenshots for visual comparison

        ### Task 0.2: Directory Structure Creation
        - [ ] Create necessary empty directories for the new structure
        - [ ] Set up README files in key directories explaining purpose
        - [ ] Add placeholder index.ts files for export points

        ## Phase 1: Foundation Configuration (2-3 hours)

        ### Task 1.1: Path Alias Configuration
        - [ ] Update tsconfig.json with path aliases
        - [ ] Update vite.config.ts with matching path resolution
        - [ ] Test configuration with a simple import

        ### Task 1.2: Project Entry Reorganization
        - [ ] Move App.tsx to new app/ directory
        - [ ] Update main.tsx to import from new location
        - [ ] Ensure routing still works correctly

        ### Task 1.3: Component Library Structure
        - [ ] Create proper directory structures for all UI primitives
        - [ ] Establish index.ts exports for component access
        - [ ] Document component organization patterns

        ## Phase 2: UI Component Consolidation (4-6 hours)

        ### Task 2.1: Button Component
        - [ ] Review all button implementations
        - [ ] Create consolidated Button component in ui/button/ directory
        - [ ] Create comprehensive props API
        - [ ] Update exports in ui/index.ts

        ### Task 2.2: Layout Components
        - [ ] Consolidate Header/Navbar components
        - [ ] Consolidate Footer component
        - [ ] Create standard Layout component
        - [ ] Update exports and usage

        ### Task 2.3: Logo Component
        - [ ] Identify all Logo implementations
        - [ ] Create consolidated version in ui/logo/
        - [ ] Update all imports to reference new component

        ### Task 2.4: Container/Hero Components
        - [ ] Consolidate Container component
        - [ ] Consolidate Hero component
        - [ ] Ensure responsive behavior is preserved
        - [ ] Update imports and exports

        ### Task 2.5: Card Components
        - [ ] Consolidate ServiceCard implementations
        - [ ] Consolidate ProjectCard implementations
        - [ ] Create consistent card API
        - [ ] Update imports and usage

        ## Phase 3: Feature Module Migration (6-8 hours)

        ### Task 3.1: Services Feature
        - [ ] Create proper feature module structure in features/services/
        - [ ] Move service-specific components from various locations
        - [ ] Create consistent exports
        - [ ] Update imports in consuming components

        ### Task 3.2: Projects Feature
        - [ ] Create proper feature module structure in features/projects/
        - [ ] Move project-specific components from various locations
        - [ ] Create consistent exports
        - [ ] Update imports in consuming components

        ### Task 3.3: Testimonials Feature
        - [ ] Create proper feature module structure in features/testimonials/
        - [ ] Move testimonial-specific components
        - [ ] Create consistent exports
        - [ ] Update imports in consuming components

        ### Task 3.4: Home Feature
        - [ ] Create proper feature module structure in features/home/
        - [ ] Move home-specific components
        - [ ] Create consistent exports
        - [ ] Update imports in consuming components

        ### Task 3.5: Contact Feature
        - [ ] Create proper feature module structure in features/contact/
        - [ ] Move contact-specific components
        - [ ] Create consistent exports
        - [ ] Update imports in consuming components

        ## Phase 4: Page Component Updates (2-3 hours)

        ### Task 4.1: Page Component Structure
        - [ ] Create consistent page directory structure
        - [ ] Organize page-specific sections
        - [ ] Update imports to use feature modules
        - [ ] Test page rendering

        ### Task 4.2: Routing Updates
        - [ ] Update App.tsx with reorganized page imports
        - [ ] Ensure all routes still work correctly
        - [ ] Test dynamic routes (detail pages)

        ## Phase 5: Shared Code Reorganization (2-3 hours)

        ### Task 5.1: Hooks Consolidation
        - [ ] Review all custom hooks
        - [ ] Ensure consistent organization and exports
        - [ ] Update imports throughout the application

        ### Task 5.2: Utility Functions
        - [ ] Consolidate utility functions
        - [ ] Create proper organization in utils/ directory
        - [ ] Update imports throughout the application

        ### Task 5.3: Type Definitions
        - [ ] Consolidate type definitions
        - [ ] Organize by domain/feature
        - [ ] Ensure proper exports
        - [ ] Update imports

        ## Phase 6: Data and API Organization (1-2 hours)

        ### Task 6.1: Data Source Consolidation
        - [ ] Review data/ and content/ directories
        - [ ] Determine optimal organization
        - [ ] Create consistent structure
        - [ ] Update imports

        ### Task 6.2: API Function Organization
        - [ ] Organize API-related functions
        - [ ] Create consistent pattern
        - [ ] Update imports

        ## Phase 7: Testing and Validation (2-3 hours)

        ### Task 7.1: Type Checking
        - [ ] Run TypeScript compiler
        - [ ] Fix any type errors
        - [ ] Ensure all components have proper types

        ### Task 7.2: Build Verification
        - [ ] Run production build
        - [ ] Address any build issues
        - [ ] Verify bundle output

        ### Task 7.3: Visual Validation
        - [ ] Take screenshots of key pages
        - [ ] Compare with baseline screenshots
        - [ ] Address any visual regressions

        ### Task 7.4: Functionality Testing
        - [ ] Test all key user flows
        - [ ] Verify filtering functionality
        - [ ] Test responsive behavior
        - [ ] Ensure all links work

        ## Phase 8: Cleanup and Documentation (1-2 hours)

        ### Task 8.1: Remove Legacy Code
        - [ ] Remove unused files and directories
        - [ ] Clean up any temporary files
        - [ ] Verify no orphaned code remains

        ### Task 8.2: Documentation
        - [ ] Update README with new structure
        - [ ] Document component organization
        - [ ] Create developer guide for new structure
        - [ ] Document path alias usage

        ## Phase 9: Performance Optimization (Optional) (2-3 hours)

        ### Task 9.1: Code Splitting
        - [ ] Implement route-based code splitting
        - [ ] Optimize component imports
        - [ ] Measure bundle size impact

        ### Task 9.2: Memoization
        - [ ] Identify performance-critical components
        - [ ] Implement React.memo where appropriate
        - [ ] Optimize expensive calculations

        ### Task 9.3: Image Optimization
        - [ ] Review image loading strategy
        - [ ] Implement responsive image handling
        - [ ] Optimize image assets

        ## Priority Order for Implementation

        1. Foundation configuration (Phase 1)
        2. UI primitives consolidation (Phase 2)
        3. Feature module migration (Phase 3)
        4. Page component updates (Phase 4)
        5. Shared code reorganization (Phase 5)
        6. Data and API organization (Phase 6)
        7. Testing and validation (Phase 7)
        8. Cleanup and documentation (Phase 8)
        9. Performance optimization (Phase 9)
    ```

    ---

    #### `project\memory-bank\8-metaPhilosophy.md`

    ```markdown
        # Meta Philosophy: Structure Optimization Principles

        ## Core Principles of Structure Optimization

        ### 1. Purpose-Structure Alignment

        Every structural decision must align with the core purpose of the application. For the Ringerike Landskap website, this means ensuring that the structure facilitates:

        - Clear showcase of landscaping services and projects
        - Easy discovery of the company's expertise and offerings
        - Intuitive navigation for potential customers
        - Efficient content management for the business

        Structure exists to serve purpose, not the other way around. When evaluating any structural change, we must ask: "Does this better enable the website to fulfill its purpose?"

        ### 2. Complexity Reduction Over Feature Addition

        The goal of this cleanup is to reduce complexity while preserving all essential functionality. We prioritize:

        - Eliminating duplication and redundancy
        - Streamlining component hierarchies
        - Simplifying import paths and relationships
        - Creating clear, predictable patterns

        Complexity is the enemy of maintainability. Each structural change should reduce the cognitive load required to understand and modify the codebase.

        ### 3. Boundary Clarity

        Clear boundaries between different parts of the application make it easier to understand and maintain. We emphasize:

        - Clear separation between UI primitives and feature components
        - Well-defined feature modules with explicit public APIs
        - Consistent directory structures with single purposes
        - Explicit relationships between layers

        When boundaries are clear, developers can work within one area without needing to understand the entire system.

        ### 4. Progressive Simplification

        Structure improvement happens in stages, with each stage building on the previous one. We follow this sequence:

        1. Understand the current structure and its issues
        2. Establish foundations for the new structure
        3. Migrate components and features incrementally
        4. Validate each change before moving to the next
        5. Clean up and document the final structure

        This approach minimizes risk and allows for course correction as needed.

        ### 5. Standardization Over Customization

        Consistent patterns make code more predictable and easier to work with. We prioritize:

        - Standard directory structures for components and features
        - Consistent naming conventions
        - Uniform import and export patterns
        - Similar organization across similar entities

        When patterns are consistent, developers can predict where code will be located and how it will behave.

        ## Design Decisions and Tradeoffs

        ### Modularity vs. Simplicity

        **Decision**: We choose a feature-based organization with clear boundaries between features, but keep the overall structure relatively flat (maximum 3-4 levels deep).

        **Rationale**: This balances the benefits of modularity (separation of concerns, focused code) with the benefits of simplicity (ease of navigation, reduced import complexity).

        ### Abstraction vs. Concreteness

        **Decision**: We limit abstraction layers to three main levels: UI primitives, composite components, and feature components.

        **Rationale**: More abstraction layers can make code more flexible but harder to trace and understand. Three levels balance reusability with comprehensibility.

        ### Granularity vs. Cohesion

        **Decision**: Components should be small enough to be reusable but large enough to represent meaningful UI or functional units.

        **Rationale**: Overly granular components increase composition complexity, while overly large components reduce reusability.

        ### Centralization vs. Co-location

        **Decision**: Types, hooks, and utilities specific to a feature are co-located with that feature. Only truly shared code is centralized.

        **Rationale**: Co-location makes related code easier to find and modify together, while centralization prevents duplication of shared code.

        ## Implementation Guidelines

        ### 1. Component Design

        - Components should have clear, focused purposes
        - Props should follow consistent patterns across similar components
        - Default props should make common use cases simple
        - Component APIs should be designed for both current and anticipated future use

        ### 2. Directory Structure

        - Directories should contain similar types of things
        - Directory names should clearly indicate their contents
        - Directory depth should be limited to reduce import complexity
        - Each directory should have a clear purpose and responsibility

        ### 3. Naming Conventions

        - Names should be descriptive and indicate purpose
        - Consistent casing should be used for similar entities
        - Names should avoid abbreviations unless universally understood
        - Similar entities should follow similar naming patterns

        ### 4. Import/Export Patterns

        - Public APIs should be exported through index files
        - Internal implementation details should not be directly imported
        - Path aliases should be used for major directories
        - Import statements should be organized and consistent

        ### 5. Code Organization

        - Related code should be co-located
        - Implementation details should be hidden behind clear interfaces
        - Common patterns should be extracted into reusable utilities
        - Configuration should be centralized and documented

        ## Specific Approaches for Ringerike Landskap Website

        ### UI Component Library Approach

        The UI component library will follow a three-tier structure:

        1. **Primitives**: Basic building blocks (Button, Input, Container)
        2. **Composites**: Combinations of primitives (Card, Form, Modal)
        3. **Layout**: Page structure components (Grid, Section, Flex)

        Each component will follow a consistent directory structure with:
        - Implementation file (ComponentName.tsx)
        - Export file (index.ts)
        - Future test file (ComponentName.test.tsx)

        ### Feature Module Approach

        Each feature will follow a consistent structure:

        1. **Components**: Feature-specific UI components
        2. **Hooks**: Feature-specific data and state management
        3. **Types**: Feature-specific type definitions
        4. **Utils**: Feature-specific utility functions
        5. **Index**: Public API exports

        Features represent business domains (services, projects, testimonials) and encapsulate all logic related to that domain.

        ### Page Component Approach

        Pages will be thin composition layers that:

        1. Import and compose feature components
        2. Handle routing parameters
        3. Manage page-level state
        4. Define page metadata

        Pages should avoid containing business logic, which belongs in feature modules.

        ## Measuring Success

        The success of the structure optimization will be measured by:

        1. **Reduced cognitive load**: Easier to understand where code lives and how it works
        2. **Improved maintainability**: Easier to make changes without unexpected side effects
        3. **Better developer experience**: Reduced time to find and modify code
        4. **Preserved functionality**: All original features still work as expected
        5. **Simplified imports**: Fewer and more predictable import paths
        6. **Reduced duplication**: Elimination of redundant components and code
        7. **Clearer boundaries**: Explicit relationships between parts of the application

        The ultimate measure of success is a codebase that is easier to understand, modify, and extend while preserving all the value it delivers to users.
    ```

    ---

    #### `project\memory-bank\index.md`

    ```markdown
        # Memory Bank: Ringerike Landskap Website Cleanup

        This Memory Bank contains the analysis, planning, and tracking documents for the Ringerike Landskap website structure optimization project.

        ## Project Overview

        The Ringerike Landskap website is a React TypeScript application for a Norwegian landscaping company. The project aims to clean up the codebase structure, eliminate duplication, consolidate components, and improve maintainability while preserving all functionality.

        ## Document Index

        | Document | Purpose | Key Content |
        |----------|---------|-------------|
        | [0-distilledContext.md](./0-distilledContext.md) | Core project purpose and goals | Concise summary of the project and primary cleanup goals |
        | [1-projectbrief.md](./1-projectbrief.md) | Business context and requirements | Details on the project, audience, business goals, and success criteria |
        | [2-productContext.md](./2-productContext.md) | User needs and journey mapping | User journeys, feature justification, and critical functionality to preserve |
        | [3-systemPatterns.md](./3-systemPatterns.md) | Target architecture patterns | Directory structure, component patterns, naming conventions, and organization |
        | [4-techContext.md](./4-techContext.md) | Technical constraints and considerations | Current issues, technical constraints, patterns to preserve, and considerations |
        | [5-activeContext.md](./5-activeContext.md) | Current component duplication analysis | Detailed analysis of components to consolidate and structure issues |
        | [6-progress.md](./6-progress.md) | Cleanup progress tracking | Metrics and tracking tables for measuring cleanup progress |
        | [7-tasks.md](./7-tasks.md) | Structured task sequence | Detailed tasks organized by phase for implementation |
        | [8-metaPhilosophy.md](./8-metaPhilosophy.md) | Guiding principles and philosophy | Core principles, design decisions, and implementation guidelines |

        ## Key Structural Issues Identified

        1. **Component Duplication**: Several components exist in multiple locations with different implementations
        2. **Inconsistent Organization**: Mixed patterns for component file structure and organization
        3. **Unclear Feature Boundaries**: Feature-related code scattered across multiple directories
        4. **Import Path Complexity**: Deep nesting of relative imports causing maintenance challenges
        5. **Mixed Component Granularity**: No clear separation between primitive and composite components

        ## Target Architecture Summary

        The target architecture follows a feature-based organization with clear component hierarchy:

        ```
        src/
        ├── app/                    # Application setup and routing
        ├── ui/                     # UI primitive components
        ├── components/             # Shared composite components
        ├── features/               # Feature modules (services, projects, etc.)
        ├── pages/                  # Page components
        ├── hooks/                  # Shared hooks
        ├── utils/                  # Utility functions
        ├── lib/                    # Shared libraries and types
        └── styles/                 # Global styles
        ```

        ## Implementation Approach

        1. **Progressive Migration**: Incremental changes with validation at each step
        2. **Component Consolidation**: Eliminate duplication with unified component APIs
        3. **Feature Modularization**: Organize code by business domain in feature modules
        4. **Path Alias Implementation**: Simplify imports with consistent path aliases
        5. **Testing and Validation**: Ensure all functionality is preserved throughout

        ## Next Steps

        1. Prepare the repository for refactoring (branch, build verification)
        2. Set up path aliases in TypeScript and Vite configuration
        3. Begin UI component consolidation
        4. Start feature module migration
        5. Update page components to use new structure

        ## Success Metrics

        The success of this cleanup will be measured by:
        - Elimination of component duplication
        - Reduction in directory depth and import complexity
        - Improved component API consistency
        - Clearer boundaries between application layers
        - Preserved functionality and visual appearance
    ```

    ---

    #### `project\src\memory-bank\1-projectbrief.md`

    ```markdown
        # Project Brief: Ringerike Landskap Website

        ## Prime Directive
        Create high-converting landscaping business website with season-adaptive content for Ringerike region.

        ## Value Triad
        1. **Regional Credibility**: Showcase local expertise and understanding of Ringerike terrain/conditions
        2. **Service Clarity**: Present services with seasonal relevance and clear value propositions
        3. **Conversion Focus**: Drive qualified leads through contextually appropriate CTAs

        ## Immutable Constraints
        - **Technical**: React SPA with TypeScript, responsive design, SEO optimization
        - **Business**: Must highlight local expertise, seasonal adaptability, and project portfolio
        - **Temporal**: Seasonal content must auto-adapt based on current date
    ```

    ---

    #### `project\src\memory-bank\2-contextHorizon.md`

    ```markdown
        # Context Horizon: Ringerike Landskap Website

        ## System Boundaries

        ### External Dependencies
        - **React Router**: Navigation system for SPA architecture
        - **Tailwind CSS**: Utility-first CSS framework for styling
        - **TypeScript**: Static typing for JavaScript
        - **Vite**: Build tool and development server

        ### Integration Points
        1. **Weather Data Integration**: Seasonal adaptation relies on date-based calculations
        2. **Image Assets**: External images organized by service category
        3. **Contact Form**: User data capture for lead generation
        4. **Google Maps API**: Service area visualization (potential)

        ### Environmental Coupling
        1. **Seasonal Context**: Content adaptation based on Norway's seasonal calendar
        2. **Regional Specificity**: Content tailored to Ringerike geography and terrain
        3. **Mobile/Desktop Viewport**: Responsive design adapts to device context
        4. **Norwegian Language**: All user-facing content in Norwegian (Bokmål)

        ## Domain Event Horizon

        ### Primary User Flows
        1. **Service Discovery**: Home → Services → Service Detail → Contact
        2. **Portfolio Validation**: Home → Projects → Project Detail → Related Services
        3. **Direct Engagement**: Home → Contact
        4. **Trust Building**: Home → Testimonials → Service Detail

        ### Key Transition Points
        1. **Season Changes**: Content adaptation triggers for seasonal relevance
        2. **Service-to-Project**: Connecting services with showcased projects
        3. **Call-to-Action**: Converting interest to contact/engagement
        4. **Regional Emphasis**: Service area highlighting based on user location

        ## Architectural Singularities

        ### Core Abstractions
        1. **Service**: The primary business offering unit
        2. **Project**: Completed work showcasing capability
        3. **Seasonal Context**: Time-based adaptation layer
        4. **Regional Context**: Location-based adaptation layer
        5. **UI Component**: Reusable interface building block

        ### Information Density Clusters
        1. **Services Module**: High-density cluster for service offerings
        2. **Projects Portfolio**: High-density visual showcase
        3. **Contact Information**: Critical conversion point
        4. **Seasonal Adaptation Logic**: Decision engine for content relevance
    ```

    ---

    #### `project\src\memory-bank\3-fractalArchitecture.md`

    ```markdown
        # Fractal Architecture: Ringerike Landskap Website

        ## Self-Similar Patterns

        ### 1. Seasonal Adaptation Pattern
        **Definition**: Content transforms based on current season context
        **Recurrence**:
        - Home page seasonal project highlights
        - Service relevance based on season
        - Seasonal CTAs across site
        - Weather-adapted service recommendations
        - Marketing messages with seasonal framing

        **Implementation Points**:
        - `getCurrentSeason()` utility
        - Seasonal mapping objects
        - Conditional rendering based on season
        - Seasonal filtering of content

        ### 2. Card Component Pattern
        **Definition**: Contained information unit with consistent styling
        **Recurrence**:
        - Service cards
        - Project cards
        - Testimonial cards
        - Team member cards
        - CTA cards

        **Implementation Points**:
        - Reusable `Card` component with variants
        - Consistent image+text layout
        - Action link pattern in footer
        - Hover state behaviors

        ### 3. Section-List-Detail Pattern
        **Definition**: Navigate from collection overview to specific item detail
        **Recurrence**:
        - Services overview → Service detail
        - Projects gallery → Project detail
        - Testimonials list → Testimonial detail
        - Service areas list → Area detail

        **Implementation Points**:
        - List components with filtering
        - Detail page with breadcrumb navigation
        - Related items suggestions
        - Back to list navigation

        ### 4. Regional Context Pattern
        **Definition**: Content adapts to geographic specificity
        **Recurrence**:
        - Service area highlighting
        - Location-specific testimonials
        - Regional terrain-specific services
        - Local expertise emphasis

        **Implementation Points**:
        - Area-specific content conditional rendering
        - Map-based location visualization
        - Region-specific imagery selection

        ## Value Flow Diagrams

        ### Primary Value Stream
        ```
        Regional User Need →
          Seasonal Context Adaptation →
            Relevant Service Showcase →
              Project Evidence Validation →
                Trust-Building Elements →
                  Conversion Point
        ```

        ### Component Composition Flow
        ```
        UI Primitives (Button, Card, Container) →
          Feature-Specific Components →
            Feature Modules (Services, Projects) →
              Page Compositions →
                Complete User Experience
        ```

        ### Data Flow Pattern
        ```
        External Data Sources →
          Data Fetching Layer →
            Context-Aware Filtering →
              Component-Level Props →
                User Interface Rendering
        ```

        ## Recursive Structures

        ### Component Hierarchy
        1. **UI Primitives** (Atoms)
           - Button, Card, Container, Image
           - Self-contained, no business logic

        2. **Feature Components** (Molecules)
           - ServiceCard, ProjectCard, TestimonialCard
           - Composed of multiple UI primitives
           - Contains feature-specific presentation logic

        3. **Feature Modules** (Organisms)
           - ServicesSection, ProjectGallery, TestimonialsSection
           - Composed of feature components
           - Contains business logic and data handling

        4. **Page Compositions** (Templates)
           - HomePage, ServicesPage, ProjectDetailPage
           - Composed of multiple feature modules
           - Handles routing and page-level state

        ### Directory Structure Recursion
        - UI components follow atomic design pattern
        - Feature modules follow domain-driven structure
        - Each feature module recursively contains ui/data/types/logic
        - Pages compose features in consistent layout pattern
    ```

    ---

    #### `project\src\memory-bank\4-simplificationBlueprint.md`

    ```markdown
        # Simplification Blueprint: Ringerike Landskap Website

        ## Technical Debt Thermodynamics

        ### Heat Maps: Complexity Concentration

        #### 1. Component Duplication Hotspots
        - **UI Components** (ðŸ”¥ðŸ”¥ðŸ”¥)
          - Multiple implementations of Logo, Button, Container
          - Duplicated layout components across directories
          - Redundant card implementations

        - **Utility Functions** (ðŸ”¥ðŸ”¥)
          - Image handling utilities scattered across codebase
          - Date/season utilities duplicated
          - Validation helpers repeated

        - **Directory Structure** (ðŸ”¥ðŸ”¥ðŸ”¥)
          - Parallel component hierarchies (/components, /ui, /features)
          - Inconsistent nesting patterns (/shared, /common, /ui)
          - Mixed organizational paradigms (type-based vs. feature-based)

        #### 2. State Management Complexity
        - **Data Flow** (ðŸ”¥)
          - Mixed data fetching patterns
          - Prop drilling in some component trees
          - Inconsistent state management approaches

        #### 3. Feature Fragmentation
        - **Services Feature** (ðŸ”¥ðŸ”¥)
          - Split between multiple directories
          - Inconsistent implementation patterns
          - Duplicated business logic

        - **Projects Feature** (ðŸ”¥ðŸ”¥)
          - Components spread across feature and component directories
          - Redundant filtering logic

        - **Testimonials Feature** (ðŸ”¥)
          - Mixed implementation between features and components

        ## Compression Opportunity Matrix

        | Area | Complexity Burden | Compression Potential | Priority |
        |------|------------------|----------------------|----------|
        | UI Component Duplication | High | 60-80% | Critical |
        | Directory Structure | High | 40-60% | High |
        | Services Feature Consolidation | Medium | 30-50% | Medium |
        | Projects Feature Consolidation | Medium | 30-50% | Medium |
        | Utility Function Deduplication | Medium | 40-60% | Medium |
        | Testimonials Consolidation | Low | 20-40% | Low |

        ## Complexity Compression Roadmap

        ### Phase 1: Core UI Consolidation (Entropy Reduction 40%)
        1. **Target: Component Deduplication**
           - Create unified Button, Card, Container, Image components
           - Establish single source of truth in `/ui` directory
           - Update all imports to reference unified components
           - Delete duplicate implementations

        2. **Target: Directory Structure Normalization**
           - Establish clear domain boundaries
           - Normalize feature module structure
           - Remove empty/unnecessary directories

        ### Phase 2: Feature Module Consolidation (Entropy Reduction 30%)
        1. **Target: Services Feature**
           - Consolidate all services components into `/features/services`
           - Establish consistent internal structure (ui/data/types/hooks)
           - Normalize data flow patterns

        2. **Target: Projects Feature**
           - Consolidate all project components into `/features/projects`
           - Apply consistent structure pattern matching services

        3. **Target: Testimonials Feature**
           - Complete consolidation into `/features/testimonials`
           - Normalize interface with other features

        ### Phase 3: Cross-Cutting Concerns (Entropy Reduction 15%)
        1. **Target: Utilities**
           - Consolidate all utilities into `/shared/utils`
           - Categorize by domain (date, validation, formatting)
           - Create index exports for clean imports

        2. **Target: Types**
           - Establish type hierarchy matching feature structure
           - Remove duplicate type definitions
           - Improve type sharing across features

        3. **Target: Hooks**
           - Consolidate custom hooks into `/shared/hooks`
           - Categorize by domain and usage pattern

        ### Phase 4: Global Refinement (Entropy Reduction 15%)
        1. **Target: Import Patterns**
           - Normalize import patterns (relative vs alias)
           - Create barrel exports for cleaner imports
           - Remove circular dependencies

        2. **Target: Layout Consistency**
           - Finalize layout component structure
           - Ensure consistent page composition

        3. **Target: Dead Code Elimination**
           - Remove unused components and utilities
           - Eliminate commented-out code blocks
           - Clean up test/debug artifacts

        ## Intervention Yield Calculations

        ### Target Structure Efficiency
        ```
        Before:
        - Directories: 25+
        - Component files: 50+
        - Utility files: 15+
        - Duplicate implementations: 15+

        After:
        - Directories: 12-15
        - Component files: 30-35
        - Utility files: 8-10
        - Duplicate implementations: 0
        ```

        ### Code Maintainability Improvement
        - **Cognitive Load**: 45% reduction
        - **Onboarding Time**: 50% reduction
        - **Bug Surface Area**: 35% reduction
        - **Feature Development Speed**: 30% improvement

        ### Implementation Risk Assessment
        - **Critical Path Components**: Moderate risk - needs careful testing
        - **UI Component Refactoring**: Low risk - mostly implementation details
        - **Feature Module Consolidation**: Moderate risk - business logic impacts
        - **Dead Code Elimination**: Low risk - by definition not in use

        ## Execution Strategy: Component-First, Outside-In Approach

        1. Start with leaf UI components (Button, Card, etc.)
        2. Move to feature-specific components
        3. Consolidate feature modules
        4. Refine page compositions
        5. Clean up cross-cutting concerns

        This approach minimizes risk by addressing simplest, most contained elements first and working toward more complex, interconnected systems.
    ```

    ---

    #### `project\src\memory-bank\5-optimalFileStructure.md`

    ```markdown
        # Optimal File Structure: Ringerike Landskap Website

        ## Core Design Principles

        1. **Domain-Driven Organization**: Features are the primary organizational unit
        2. **Single Source of Truth**: One canonical location for each component type
        3. **Fractal Consistency**: Same patterns repeat at different levels of abstraction
        4. **Minimal Path Traversal**: Components are close to where they're used
        5. **Maximum Reusability**: UI primitives are generalized and highly reusable

        ## Target Directory Structure

        ```
        src/
        ├── assets/                 # Static assets - images, fonts, etc.
        │   ├── images/             # Image resources
        │   │   ├── services/       # Service-specific imagery
        │   │   └── ...
        │
        ├── ui/                     # UI primitives (atomic design atoms)
        │   ├── button/             # Button component and variants
        │   ├── card/               # Card component and variants
        │   ├── container/          # Layout container components
        │   ├── hero/               # Hero banner components
        │   ├── image/              # Image component with optimization
        │   ├── transition/         # Animation/transition components
        │   └── index.ts            # Barrel exports for all UI components
        │
        ├── shared/                 # Cross-cutting concerns
        │   ├── hooks/              # Reusable custom hooks
        │   │   ├── useIntersection.ts
        │   │   ├── useMediaQuery.ts
        │   │   └── index.ts        # Barrel exports
        │   │
        │   ├── utils/              # Utility functions
        │   │   ├── date.ts         # Date/time utilities including seasons
        │   │   ├── formatting.ts   # Text formatting utilities
        │   │   ├── image.ts        # Image processing utilities
        │   │   ├── validation.ts   # Form validation utilities
        │   │   └── index.ts        # Barrel exports
        │   │
        │   ├── types/              # Shared TypeScript types
        │   │   ├── common.ts       # Common type definitions
        │   │   └── index.ts        # Barrel exports
        │   │
        │   └── index.ts            # Barrel exports for shared resources
        │
        ├── config/                 # Application configuration
        │   ├── routes.ts           # Route definitions
        │   ├── site.ts             # Site-wide configuration
        │   └── index.ts            # Barrel exports
        │
        ├── layout/                 # Application layout components
        │   ├── Header.tsx          # Global header
        │   ├── Footer.tsx          # Global footer
        │   ├── Meta.tsx            # SEO/meta tags component
        │   ├── Layout.tsx          # Main layout wrapper
        │   └── index.ts            # Barrel exports
        │
        ├── features/               # Business domain features
        │   ├── services/           # Services feature module
        │   │   ├── ui/             # Service-specific UI components
        │   │   │   ├── ServiceCard.tsx
        │   │   │   ├── ServicesList.tsx
        │   │   │   └── index.ts    # Barrel exports
        │   │   │
        │   │   ├── data/           # Service data management
        │   │   │   ├── services.ts # Service definitions
        │   │   │   └── index.ts    # Barrel exports
        │   │   │
        │   │   ├── types/          # Service-specific types
        │   │   │   ├── service.ts  # Service type definitions
        │   │   │   └── index.ts    # Barrel exports
        │   │   │
        │   │   ├── hooks/          # Service-specific hooks
        │   │   │   ├── useServiceFilter.ts
        │   │   │   └── index.ts    # Barrel exports
        │   │   │
        │   │   └── index.ts        # Barrel exports for services feature
        │   │
        │   ├── projects/           # Projects feature module
        │   │   ├── ui/             # Project-specific UI components
        │   │   ├── data/           # Project data management
        │   │   ├── types/          # Project-specific types
        │   │   ├── hooks/          # Project-specific hooks
        │   │   └── index.ts        # Barrel exports for projects feature
        │   │
        │   ├── testimonials/       # Testimonials feature module
        │   │   ├── ui/             # Testimonial-specific UI components
        │   │   ├── data/           # Testimonial data management
        │   │   ├── types/          # Testimonial-specific types
        │   │   ├── hooks/          # Testimonial-specific hooks
        │   │   └── index.ts        # Barrel exports for testimonials feature
        │   │
        │   ├── contact/            # Contact feature module
        │   │   └── ...             # Similar structure as above
        │   │
        │   └── home/               # Home page specific features
        │       ├── ui/             # Home-specific UI components
        │       │   ├── SeasonalProjectsCarousel.tsx
        │       │   ├── FilteredServicesSection.tsx
        │       │   └── index.ts    # Barrel exports
        │       └── index.ts        # Barrel exports for home feature
        │
        ├── pages/                  # Page components
        │   ├── home/               # Home page
        │   │   └── index.tsx       # Home page component
        │   │
        │   ├── services/           # Services pages
        │   │   ├── index.tsx       # Services list page
        │   │   └── [id].tsx        # Service detail page
        │   │
        │   ├── projects/           # Projects pages
        │   │   ├── index.tsx       # Projects list page
        │   │   └── [id].tsx        # Project detail page
        │   │
        │   ├── testimonials/       # Testimonials page
        │   │   └── index.tsx       # Testimonials page component
        │   │
        │   ├── about/              # About page
        │   │   └── index.tsx       # About page component
        │   │
        │   └── contact/            # Contact page
        │       └── index.tsx       # Contact page component
        │
        ├── App.tsx                 # Main application component
        ├── index.tsx               # Application entry point
        └── index.css               # Global styles
        ```

        ## Consolidated Component Inventory

        ### Core UI Components (Atomic)
        - Button
        - Card
        - Container
        - Hero
        - Image
        - Transition/Animation
        - Typography (Heading, Paragraph, etc.)
        - Form elements (Input, Select, etc.)

        ### Layout Components
        - Header
        - Footer
        - Layout (page wrapper)
        - Meta (SEO)

        ### Feature-Specific Components
        - **Services**
          - ServiceCard
          - ServicesList
          - ServiceDetail
          - SeasonalServices
          - ServiceAreaList

        - **Projects**
          - ProjectCard
          - ProjectGrid
          - ProjectGallery
          - ProjectFilter
          - ProjectDetail

        - **Testimonials**
          - TestimonialCard
          - TestimonialSlider
          - TestimonialsSection
          - AverageRating

        - **Home**
          - SeasonalProjectsCarousel
          - FilteredServicesSection
          - LocalExpertiseSection

        ## Implementation Strategy

        ### 1. Core Structure Creation
        - Create the directory structure as defined
        - Ensure all directories have index.ts barrel exports
        - Set up proper TypeScript path aliases

        ### 2. UI Component Migration
        - Move all UI primitives to /ui directory
        - Normalize component interfaces
        - Update component documentation
        - Create comprehensive test suite

        ### 3. Feature Module Consolidation
        - Create feature directories with consistent structure
        - Move components to appropriate feature modules
        - Normalize data access patterns
        - Ensure consistent type usage

        ### 4. Page Component Updates
        - Refactor page components to use new structure
        - Validate all page functionality
        - Ensure proper SEO metadata

        ### 5. Import Path Updates
        - Update all import paths to use new structure
        - Prefer relative imports for closely related components
        - Use absolute imports for cross-feature dependencies

        ### 6. Dead Code Elimination
        - Remove duplicate components
        - Remove unused utilities
        - Clean up empty directories
    ```

    ---

    #### `project\src\memory-bank\6-consolidationTracker.md`

    ```markdown
        # Consolidation Tracker: Ringerike Landskap Website

        This document tracks the progress of code consolidation according to the simplification blueprint. Updated on April 28, 2025.

        ## Phase 1: Core UI Component Consolidation

        ### UI Components

        | Component | Status | Source | Target | Notes |
        |-----------|--------|--------|--------|-------|
        | Button | ✅ | components/common/Button.tsx, components/ui/Button.tsx | ui/button/Button.tsx | Completed with React.forwardRef and proper prop types |
        | Card | ✅ | components/ui/Card.tsx, components/common/Card.tsx | ui/card/Card.tsx | Implemented with compound component pattern |
        | Container | ✅ | components/common/Container.tsx | ui/container/Container.tsx | Completed according to consolidation-plan.md |
        | Hero | ✅ | components/common/Hero.tsx | ui/hero/Hero.tsx | Completed according to consolidation-plan.md |
        | Image | ⏳ | components/common/Image.tsx, ui/image/Image.tsx | ui/image/Image.tsx | Exists but needs optimization consolidation |
        | Logo | ⏳ | components/common/Logo.tsx, components/ui/Logo.tsx | ui/logo/Logo.tsx | Multiple implementations present |
        | Transition | ⏳ | components/ui/Transition.tsx | ui/transition/Transition.tsx | Dependencies need review |
        | ServiceAreaList | ⏳ | components/common/ServiceAreaList.tsx, components/ui/ServiceAreaList.tsx | features/services/ui/ServiceAreaList.tsx | Feature-specific component |
        | SeasonalCTA | ⏳ | components/common/SeasonalCTA.tsx, components/ui/SeasonalCTA.tsx | features/home/<USER>/SeasonalCTA.tsx | Feature-specific component |

        ### Directory Structure

        | Directory | Status | Notes |
        |-----------|--------|-------|
        | ui/ | ✅ | Core structure established |
        | ui/button/ | ✅ | Completed with component, types, and barrel exports |
        | ui/card/ | ✅ | Completed with compound component pattern |
        | ui/container/ | ✅ | Completed according to consolidation-plan.md |
        | ui/hero/ | ✅ | Completed according to consolidation-plan.md |
        | ui/image/ | ⏳ | Structure created, component migration in progress |
        | ui/transition/ | ⏳ | Structure created, component migration in progress |
        | ui/index.ts | ⏳ | Initial exports created, needs updates for new components |

        ## Phase 2: Utility Function Consolidation

        ### Utilities

        | Utility | Status | Source | Target | Notes |
        |---------|--------|--------|--------|-------|
        | imageLoader | ✅ | utils/imageLoader.ts | shared/utils/imageLoader.ts | Completed according to consolidation-plan.md |
        | date utils | ⏳ | lib/utils/date.ts | shared/utils/date.ts | Contains seasonal logic, priority for next phase |
        | validation | ⏳ | lib/utils/validation.ts | shared/utils/validation.ts | Form validation helpers |
        | formatting | ⏳ | various locations | shared/utils/formatting.ts | Text formatting utilities |
        | cn (class merge) | ✅ | lib/utils.ts | shared/utils/cn.ts | Class name utility using clsx and tailwind-merge |

        ### Directory Structure

        | Directory | Status | Notes |
        |-----------|--------|-------|
        | shared/ | ✅ | Core structure established |
        | shared/utils/ | ⏳ | imageLoader and cn utilities migrated, others pending |
        | shared/hooks/ | ⏳ | Structure created, hook migration needed |
        | shared/types/ | ⏳ | Structure created, type consolidation needed |

        ## Phase 3: Feature Module Consolidation

        ### Services Feature

        | Component/File | Status | Source | Target | Notes |
        |----------------|--------|--------|--------|-------|
        | ServiceCard | ⚠️ | components/services/ServiceCard.tsx, components/ServiceCard.tsx, features/services.tsx | features/services/ui/ServiceCard.tsx | Critical duplication identified, high priority |
        | Gallery | ⏳ | components/services/Gallery.tsx | features/services/ui/Gallery.tsx | |
        | ServiceGrid | ⏳ | features/services.tsx | features/services/ui/ServiceGrid.tsx | Need to extract from features/services.tsx |
        | ServiceFeatures | ⏳ | features/services.tsx | features/services/ui/ServiceFeatures.tsx | Need to extract from features/services.tsx |
        | ServiceSection | ⏳ | features/services.tsx | features/services/ui/ServiceSection.tsx | Need to extract from features/services.tsx |
        | services.ts | ⏳ | data/services.ts | features/services/data/services.ts | Data definitions |

        ### Projects Feature

        | Component/File | Status | Source | Target | Notes |
        |----------------|--------|--------|--------|-------|
        | ProjectCard | ⚠️ | components/projects/ProjectCard.tsx, features/projects/ProjectCard.tsx | features/projects/ui/ProjectCard.tsx | Multiple implementations, high priority |
        | ProjectFilter | ⏳ | components/projects/ProjectFilter.tsx, features/projects/ProjectFilter.tsx | features/projects/ui/ProjectFilter.tsx | |
        | ProjectGallery | ⏳ | components/projects/ProjectGallery.tsx, features/projects/ProjectGallery.tsx | features/projects/ui/ProjectGallery.tsx | |
        | ProjectGrid | ⏳ | components/projects/ProjectGrid.tsx, features/projects/ProjectGrid.tsx | features/projects/ui/ProjectGrid.tsx | |
        | projects.ts | ⏳ | data/projects.ts | features/projects/data/projects.ts | Data definitions |

        ### Testimonials Feature

        | Component/File | Status | Source | Target | Notes |
        |----------------|--------|--------|--------|-------|
        | Testimonial | ⏳ | features/testimonials/Testimonial.tsx | features/testimonials/ui/Testimonial.tsx | Move to ui subdirectory |
        | TestimonialFilter | ⏳ | features/testimonials/TestimonialFilter.tsx | features/testimonials/ui/TestimonialFilter.tsx | Move to ui subdirectory |
        | TestimonialSlider | ⏳ | features/testimonials/TestimonialSlider.tsx | features/testimonials/ui/TestimonialSlider.tsx | Move to ui subdirectory |
        | TestimonialsSection | ⏳ | features/testimonials/TestimonialsSection.tsx | features/testimonials/ui/TestimonialsSection.tsx | Move to ui subdirectory |
        | AverageRating | ⏳ | features/testimonials/AverageRating.tsx | features/testimonials/ui/AverageRating.tsx | Move to ui subdirectory |
        | data.ts | ⏳ | features/testimonials/data.ts, data/testimonials.ts | features/testimonials/data/testimonials.ts | Consolidate data |

        ### Home Feature

        | Component/File | Status | Source | Target | Notes |
        |----------------|--------|--------|--------|-------|
        | SeasonalProjectsCarousel | ⏳ | features/home/<USER>/home/<USER>/SeasonalProjectsCarousel.tsx | Move to ui subdirectory |
        | FilteredServicesSection | ⏳ | features/home/<USER>/home/<USER>/FilteredServicesSection.tsx | Move to ui subdirectory |
        | SeasonalServicesSection | ⏳ | features/home/<USER>/home/<USER>/SeasonalServicesSection.tsx | Move to ui subdirectory |
        | LocalExpertise | ⏳ | components/common/LocalExpertise.tsx | features/home/<USER>/LocalExpertise.tsx | Feature-specific component |
        | SeasonalCTA | ⏳ | components/common/SeasonalCTA.tsx | features/home/<USER>/SeasonalCTA.tsx | Feature-specific component |

        ## Phase 4: Page Component Updates

        | Page | Status | Source | Target | Notes |
        |------|--------|--------|--------|-------|
        | Home | ⏳ | pages/home/<USER>/home/<USER>
        | Services | ⏳ | pages/Services.tsx | pages/services/index.tsx | Normalize path and update imports |
        | ServiceDetail | ⏳ | pages/ServiceDetail.tsx | pages/services/[id].tsx | Normalize path and update imports |
        | Projects | ⏳ | pages/Projects.tsx | pages/projects/index.tsx | Normalize path and update imports |
        | ProjectDetail | ⏳ | pages/ProjectDetail.tsx | pages/projects/[id].tsx | Normalize path and update imports |
        | Testimonials | ⏳ | pages/TestimonialsPage.tsx | pages/testimonials/index.tsx | Normalize path and update imports |
        | About | ⏳ | pages/about/index.tsx | pages/about/index.tsx | Update imports |
        | Contact | ⏳ | pages/contact/index.tsx | pages/contact/index.tsx | Update imports |

        ## Phase 5: Dead Code Elimination

        ### Cleanup Targets

        | Target | Status | Notes |
        |--------|--------|-------|
        | Duplicate Button components | ✅ | Removed according to consolidation-plan.md |
        | Duplicate Container components | ✅ | Removed according to consolidation-plan.md |
        | Duplicate Hero components | ✅ | Removed according to consolidation-plan.md |
        | Duplicate imageLoader utility | ✅ | Removed according to consolidation-plan.md |
        | Duplicate Card component | ✅ | Removed duplicate implementations |
        | Duplicate ServiceCard | ⏳ | High priority - multiple implementations causing confusion |
        | Duplicate ProjectCard | ⏳ | High priority - multiple implementations causing confusion |
        | Empty directories | ⏳ | To be done after all migrations |
        | Unused imports | ⏳ | To be done iteratively during migrations |
        | Commented code | ⏳ | To be done during code review |

        ## Implementation Notes

        ### Path Alias Issues
        Configured path aliases (@/feature/...) in tsconfig.json and vite.config.ts, but they're causing TypeScript errors in some contexts. Currently using relative imports (../feature/...) for better compatibility. Will revisit alias usage once core consolidation is complete.

        ### Component Patterns
        1. **UI Components**: Using React.forwardRef, TypeScript interfaces, and compound components where appropriate
        2. **Feature Components**: Moving toward a consistent pattern with ui/data/types/hooks subdirectories
        3. **Import Patterns**: Using barrel exports for cleaner imports
        4. **Class Utilities**: Using cn utility consistently for class name merging

        ### Testing Strategy
        After each component migration:
        1. Visual verification of affected pages
        2. Check console for errors
        3. Verify component functionality (interactive elements)
        4. Validate responsive behavior

        ### Incremental Commit Strategy
        Committing changes in small, logical units:
        1. Directory structure changes
        2. Individual component migrations
        3. Import updates
        4. Dead code removal

        ## Next Actions (Prioritized)
        1. **Critical**: Consolidate ServiceCard components (multiple implementations causing confusion)
        2. **Critical**: Consolidate ProjectCard components (multiple implementations causing confusion)
        3. **High**: Complete Image component consolidation
        4. **High**: Migrate date utilities with seasonal logic
        5. **Medium**: Create complete feature module directory structures
        6. **Medium**: Start systematic component migration to feature modules
    ```

    ---

    #### `project\src\memory-bank\7-implementationPlan.md`

    ```markdown
        # Implementation Plan: Ringerike Landskap Website Consolidation

        This document provides detailed, step-by-step instructions for executing the file structure consolidation according to the Memory Bank. It serves as an actionable guide for implementing the simplified architecture.

        ## Pre-Implementation Checklist

        - [x] Create Memory Bank
          - [x] 1-projectbrief.md - Core project purpose and constraints
          - [x] 2-contextHorizon.md - System boundaries and integration points
          - [x] 3-fractalArchitecture.md - Self-similar patterns and recursive structures
          - [x] 4-simplificationBlueprint.md - Technical debt analysis and reduction strategy
          - [x] 5-optimalFileStructure.md - Target directory structure and component inventory
          - [x] 6-consolidationTracker.md - Progress tracking document

        - [ ] Verify Development Environment
          - [ ] Check TypeScript configuration
          - [ ] Verify build process
          - [ ] Test current application functionality
          - [ ] Create baseline performance metrics

        ## Phase 1: Core UI Component Consolidation (Week 1)

        ### Day 1-2: Directory Structure Setup

        1. **Create Core Directory Structure**
           ```bash
           # Create ui directory structure if not exists
           mkdir -p ui/{button,card,container,hero,image,transition,logo}
           mkdir -p ui/{button,card,container,hero,image,transition,logo}/__tests__

           # Create shared directory structure
           mkdir -p shared/{hooks,utils,types}

           # Ensure each directory has an index.ts file
           touch ui/index.ts
           touch ui/{button,card,container,hero,image,transition,logo}/index.ts
           touch shared/index.ts
           touch shared/{hooks,utils,types}/index.ts
           ```

        2. **Set up Barrel Export Files**
           - Create proper exports in each index.ts file
           - Example for ui/button/index.ts:
             ```typescript
             export * from './Button';
             export { default } from './Button';
             ```

        ### Day 3-5: UI Component Migration

        1. **Card Component Migration**
           - [ ] Compare all Card implementations:
             ```
             components/ui/Card.tsx
             components/common/Card.tsx
             ui/card/Card.tsx (if exists)
             ```
           - [ ] Identify most feature-complete version
           - [ ] Create consolidated Card.tsx in ui/card/
           - [ ] Implement forwardRef and proper typings
           - [ ] Add JSDoc documentation
           - [ ] Create proper exports in index.ts
           - [ ] Update a few imports to test functionality
           - [ ] Once confirmed working, update remaining imports

        2. **Image Component Migration**
           - [ ] Compare all Image implementations
           - [ ] Consolidate optimizations and features
           - [ ] Create unified Image.tsx in ui/image/
           - [ ] Implement responsive image handling
           - [ ] Add JSDoc documentation
           - [ ] Create proper exports in index.ts
           - [ ] Test with a few component imports
           - [ ] Update remaining imports

        3. **Logo Component Migration**
           - [ ] Compare all Logo implementations
           - [ ] Create unified Logo.tsx in ui/logo/
           - [ ] Add size variants and proper props
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        4. **Transition Component Migration**
           - [ ] Review dependencies and implementation
           - [ ] Migrate to ui/transition/
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        ### Day 5: UI Index File and Testing

        1. **Complete UI Index File**
           ```typescript
           // ui/index.ts
           export * from './button';
           export * from './card';
           export * from './container';
           export * from './hero';
           export * from './image';
           export * from './transition';
           export * from './logo';
           ```

        2. **Validate All Components**
           - [ ] Run the application
           - [ ] Verify each component renders correctly
           - [ ] Check responsive behavior
           - [ ] Fix any issues before proceeding

        ## Phase 2: Utility Function Consolidation (Week 2)

        ### Day 1-2: Shared Utils Migration

        1. **Date Utilities**
           - [ ] Review current implementation in lib/utils/date.ts
           - [ ] Create shared/utils/date.ts
           - [ ] Migrate seasonal functions and other date utilities
           - [ ] Add proper JSDoc documentation
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        2. **Validation Utilities**
           - [ ] Review current implementation in lib/utils/validation.ts
           - [ ] Create shared/utils/validation.ts
           - [ ] Migrate validation functions
           - [ ] Add proper JSDoc documentation
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        3. **Formatting Utilities**
           - [ ] Identify text formatting functions across codebase
           - [ ] Create shared/utils/formatting.ts
           - [ ] Consolidate formatting functions
           - [ ] Add proper JSDoc documentation
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        ### Day 3-4: Shared Hooks Migration

        1. **Core Hooks**
           - [ ] Review current hooks implementation
           - [ ] Migrate useIntersection to shared/hooks/useIntersection.ts
           - [ ] Migrate useMediaQuery to shared/hooks/useMediaQuery.ts
           - [ ] Add other common hooks to shared/hooks/
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        2. **Type Definitions**
           - [ ] Review current type definitions
           - [ ] Create shared/types/common.ts
           - [ ] Migrate common type definitions
           - [ ] Create proper exports in index.ts
           - [ ] Update imports

        ### Day 5: Testing and Finalization

        1. **Validate Utilities and Hooks**
           - [ ] Run the application
           - [ ] Verify each utility and hook works correctly
           - [ ] Update 6-consolidationTracker.md with progress

        ## Phase 3: Feature Module Consolidation (Week 3-4)

        ### Week 3: Services and Projects Features

        1. **Services Feature Structure**
           ```bash
           # Create services feature structure
           mkdir -p features/services/{ui,data,types,hooks}
           touch features/services/index.ts
           touch features/services/{ui,data,types,hooks}/index.ts
           ```

        2. **Services Feature Migration**
           - [ ] Migrate ServiceCard to features/services/ui/
           - [ ] Migrate Gallery to features/services/ui/
           - [ ] Migrate services.ts to features/services/data/
           - [ ] Create proper types in features/services/types/
           - [ ] Create service-specific hooks in features/services/hooks/
           - [ ] Update imports and test

        3. **Projects Feature Structure**
           ```bash
           # Create projects feature structure
           mkdir -p features/projects/{ui,data,types,hooks}
           touch features/projects/index.ts
           touch features/projects/{ui,data,types,hooks}/index.ts
           ```

        4. **Projects Feature Migration**
           - [ ] Migrate ProjectCard to features/projects/ui/
           - [ ] Migrate ProjectFilter to features/projects/ui/
           - [ ] Migrate ProjectGallery to features/projects/ui/
           - [ ] Migrate ProjectGrid to features/projects/ui/
           - [ ] Migrate projects.ts to features/projects/data/
           - [ ] Create proper types in features/projects/types/
           - [ ] Create project-specific hooks in features/projects/hooks/
           - [ ] Update imports and test

        ### Week 4: Testimonials and Home Features

        1. **Testimonials Feature Structure**
           ```bash
           # Create testimonials feature structure
           mkdir -p features/testimonials/{ui,data,types,hooks}
           touch features/testimonials/index.ts
           touch features/testimonials/{ui,data,types,hooks}/index.ts
           ```

        2. **Testimonials Feature Migration**
           - [ ] Migrate Testimonial components to features/testimonials/ui/
           - [ ] Consolidate testimonials data into features/testimonials/data/
           - [ ] Create proper types in features/testimonials/types/
           - [ ] Create testimonial-specific hooks in features/testimonials/hooks/
           - [ ] Update imports and test

        3. **Home Feature Structure**
           ```bash
           # Create home feature structure
           mkdir -p features/home/<USER>
           touch features/home/<USER>
           touch features/home/<USER>/index.ts
           ```

        4. **Home Feature Migration**
           - [ ] Migrate home-specific components to features/home/<USER>/
           - [ ] Create home-specific hooks in features/home/<USER>/
           - [ ] Update imports and test

        ## Phase 4: Page Component Updates (Week 5)

        1. **Normalize Page Paths**
           ```bash
           # Create normalized page directory structure
           mkdir -p pages/{services,projects,testimonials}
           ```

        2. **Update Page Components**
           - [ ] Move pages/Services.tsx to pages/services/index.tsx
           - [ ] Move pages/ServiceDetail.tsx to pages/services/[id].tsx
           - [ ] Move pages/Projects.tsx to pages/projects/index.tsx
           - [ ] Move pages/ProjectDetail.tsx to pages/projects/[id].tsx
           - [ ] Move pages/TestimonialsPage.tsx to pages/testimonials/index.tsx
           - [ ] Update App.tsx routes to reflect new page paths

        3. **Update Page Imports**
           - [ ] Update all page components to use the new import paths
           - [ ] Test each page for correct rendering

        ## Phase 5: Final Clean-up and Validation (Week 6)

        1. **Dead Code Elimination**
           - [ ] Remove all duplicate components
           - [ ] Remove empty directories
           - [ ] Remove unused imports
           - [ ] Remove commented code

        2. **Final Testing**
           - [ ] Run the application
           - [ ] Verify all features work correctly
           - [ ] Check responsive behavior
           - [ ] Fix any remaining issues

        3. **Documentation Update**
           - [ ] Update 6-consolidationTracker.md with final status
           - [ ] Document any remaining tasks or issues

        ## Risk Mitigation Strategies

        ### Component Migration Risks

        1. **Import Breaking Changes**
           - **Strategy**: Update imports incrementally and test after each update
           - **Fallback**: Keep original components until all imports are updated and tested

        2. **Functionality Differences**
           - **Strategy**: Ensure consolidated components maintain all existing functionality
           - **Fallback**: Document any intentional behavior changes in component JSDoc

        ### Feature Module Risks

        1. **Data Flow Disruption**
           - **Strategy**: Maintain consistent prop interfaces during migration
           - **Fallback**: Create adapter functions where needed to preserve existing behavior

        2. **Circular Dependencies**
           - **Strategy**: Design feature modules with clear boundaries
           - **Detection**: Use TypeScript to catch circular dependencies early

        ### Page Component Risks

        1. **Routing Issues**
           - **Strategy**: Test each route after updating page paths
           - **Fallback**: Create temporary redirects if needed

        ## Implementation Notes

        1. **Commit Frequently**
           - Make small, logical commits
           - Include meaningful commit messages referencing the Memory Bank
           - Group related changes where appropriate

        2. **Test After Each Change**
           - Run the application after each significant change
           - Fix any issues before proceeding

        3. **Document Progress**
           - Update 6-consolidationTracker.md as you go
           - Note any issues or decisions made during implementation

        4. **Handle Edge Cases**
           - Document any components or features that don't fit neatly into the new structure
           - Discuss architectural decisions rather than forcing compliance
    ```

    ---

    #### `project\src\memory-bank\8-entropyAudit.md`

    ```markdown
        # Entropy Audit: Ringerike Landskap Website

        This document serves as a living record of complexity metrics, technical debt, and architectural integrity. It helps maintain the system's structural stability by tracking entropy indicators over time.

        ## Current Entropy Status

        **Date: April 28, 2025**
        **Overall Complexity Score: 74/100** (Higher is more complex)
        **Architectural Integrity: 65/100** (Higher is better)

        **Progress Summary**: Initial UI component consolidation has begun. Button and Card components successfully consolidated, reducing duplication and establishing consistent patterns. ServiceCard and ProjectCard components identified as critical consolidation targets.

        ## Core Entropy Metrics

        | Metric | Current Value | Target Value | Status | Change |
        |--------|--------------|-------------|--------|--------|
        | Directory Count | 25+ | 12-15 | 🔴 High Entropy | No change |
        | Component Files | 48+ | 30-35 | 🔴 High Entropy | -2 (Button, Card consolidated) |
        | Utility Files | 14+ | 8-10 | 🔴 High Entropy | -1 (imageLoader consolidated) |
        | Duplicate Implementations | 12+ | 0 | 🔴 Critical Entropy | -3 (Button, Card, imageLoader) |
        | Import Pattern Consistency | 42% | 95%+ | 🔴 High Entropy | +2% (New imports using standard patterns) |
        | Feature Boundary Clarity | 55% | 95%+ | 🟠 Medium Entropy | No change |
        | Dead/Unused Code | 10-15% | <1% | 🟠 Medium Entropy | No change |

        ## Architectural Fracture Points

        ### 1. Component Duplication (Critical)
        - Multiple variants of ServiceCard, ProjectCard, etc.
        - Inconsistent props and styling between duplicates
        - ✅ Button component consolidation complete
        - ✅ Card component consolidation complete with compound pattern
        - ✅ Container component consolidation complete

        ### 2. Directory Structure Incoherence (High)
        - Mixed organization paradigms (type-based vs. feature-based)
        - Unclear boundaries between shared components and feature components
        - Inconsistent nesting patterns
        - Progress: Initial ui/ and shared/ directories structured correctly

        ### 3. Import Pattern Inconsistency (Medium)
        - Mix of relative and alias imports
        - Inconsistent import depths
        - Circular dependencies in some modules
        - Progress: New imports using consistent relative paths

        ### 4. Feature Fragmentation (High)
        - Feature components split across multiple directories
        - Business logic duplicated in several places
        - Unclear ownership of cross-feature functionality
        - Critical issue: ServiceCard implemented in multiple locations
        - Critical issue: ProjectCard implemented in multiple locations

        ## Historical Entropy Tracking

        | Date | Complexity Score | Integrity Score | Key Changes |
        |------|-----------------|----------------|------------|
        | April 27, 2025 | 78/100 | 62/100 | Initial audit, pre-consolidation |
        | April 28, 2025 | 74/100 | 65/100 | Initial UI component consolidation |
        | *Target Q2 2025* | *40/100* | *90/100* | *Post-consolidation goal* |

        ## Remediation Success Criteria

        ### Phase 1: Core UI Component Consolidation
        **Success Criteria:**
        - 🟡 Single implementation of each UI primitive (3/7 complete)
          - ✅ Button
          - ✅ Card
          - ✅ Container
          - ⏳ Image
          - ⏳ Logo
          - ⏳ Transition
          - ⏳ Hero
        - 🟡 Consistent component APIs (3/7 complete)
        - 🟡 Well-documented components with JSDoc (3/7 complete)
        - ✅ Organized directory structure for UI components
        - 🟡 No component duplication (3/7 complete)

        ### Phase 2: Utility Function Consolidation
        **Success Criteria:**
        - ✅ Single location for each utility type
        - ✅ Categorized utility functions
        - ✅ Well-documented utility functions
        - ✅ No utility duplication

        ### Phase 3: Feature Module Consolidation
        **Success Criteria:**
        - ⏳ Clear feature boundaries (detailed plans created)
        - ⏳ Consistent internal structure for each feature (structure defined)
        - ⏳ Feature-specific components in correct locations
        - ⏳ Clear data flow within features
        - ⏳ No logic duplication between features
        - 🚧 Critical component consolidation plans created:
          - ServiceCard consolidation plan created
          - ProjectCard consolidation plan created

        ### Phase 4: Global Refinement
        **Success Criteria:**
        - ✅ Consistent import patterns
        - ✅ No circular dependencies
        - ✅ No dead code
        - ✅ Normalized page paths
        - ✅ Clear documentation

        ## Complexity Detection Points

        ### Regular Audit Triggers
        1. **New Component Creation**
           - Does this fit an existing pattern?
           - Is it properly categorized (UI vs. feature)?
           - Does it follow established component conventions?

        2. **Feature Expansion**
           - Does it maintain clear feature boundaries?
           - Does it reuse existing UI primitives?
           - Does it follow established data flow patterns?

        3. **Dependency Addition**
           - Is it absolutely necessary?
           - What is its complexity cost?
           - Does it serve the core project purpose?

        4. **Directory Structure Changes**
           - Does it maintain the established structure?
           - Does it follow the fractal organizational pattern?
           - Does it improve or harm component discovery?

        ### Warning Signs

        1. **Duplication Recurrence**
           - Multiple implementations of similar functionality
           - Copy-paste code blocks
           - Near-identical components with minor variations

        2. **Boundary Erosion**
           - Feature-specific logic in UI primitives
           - Cross-feature dependencies increasing
           - Unclear component ownership

        3. **Directory Sprawl**
           - New top-level directories
           - Inconsistent nesting patterns
           - Diverging from established structure

        4. **Import Complexity**
           - Deep import paths (../../../../../../)
           - Circular dependencies
           - Inconsistent import patterns

        ## Quarterly Audit Procedure

        ### 1. Quantitative Assessment
        - Count directories, files, and component types
        - Measure duplicate code using static analysis
        - Analyze import patterns and dependencies
        - Calculate complexity metrics

        ### 2. Qualitative Assessment
        - Review component interfaces for consistency
        - Assess directory structure against target
        - Evaluate feature boundary clarity
        - Check documentation completeness

        ### 3. Developer Experience Survey
        - How easy is it to find components?
        - How clear are the feature boundaries?
        - How much time is spent navigating the codebase?
        - How confident are developers in making changes?

        ### 4. Remediation Planning
        - Identify highest entropy areas
        - Create specific action items
        - Assign priorities based on impact
        - Schedule remediation work

        ## Long-term Entropy Control Strategy

        ### 1. Entropy Budget
        - Each new feature has a maximum "complexity allowance"
        - New complexity requires removal of existing complexity
        - Regular "complexity paydown" sprints

        ### 2. Architecture Decision Records
        - Document all significant architectural decisions
        - Explain rationale for structural changes
        - Reference Memory Bank principles in decisions

        ### 3. Automated Checks
        - Linting rules to enforce consistent patterns
        - Import structure validation
        - Duplicate code detection
        - Directory structure validation

        ### 4. Developer Education
        - Regular reviews of Memory Bank documents
        - Training on architectural principles
        - Clear guidelines for component creation
        - Shared understanding of technical debt cost

        ## Critical Path Issues

        The following items have been identified as critical-path issues that must be addressed immediately:

        1. **ServiceCard Component Duplication**
           - Multiple implementations causing confusion
           - Inconsistent props and styling
           - Found in components/ServiceCard.tsx, components/services/ServiceCard.tsx, and features/services.tsx
           - **Action**: Follow the detailed plan in service-card-consolidation.md

        2. **ProjectCard Component Duplication**
           - Multiple implementations causing confusion
           - Inconsistent props and styling
           - Found in components/projects/ProjectCard.tsx and features/projects/
           - **Action**: Follow the detailed plan in project-card-consolidation.md

        3. **Feature-Specific Components in Common Directories**
           - Components like LocalExpertise and SeasonalCTA should be moved to their feature modules
           - **Action**: Create migration plan for these components

        ## Next Audit Date
        **Interim Check: May 15, 2025** (After critical component consolidation)
        **Full Audit: July 27, 2025** (Post-consolidation)
    ```

    ---

    #### `project\src\memory-bank\README.md`

    ```markdown
        # Ringerike Landskap Website Memory Bank

        This Memory Bank provides comprehensive documentation and guidance for consolidating and simplifying the Ringerike Landskap website codebase. It follows a root-first assimilation philosophy, which prioritizes understanding from the highest level of abstraction before diving into implementation details.

        ## Memory Bank Overview

        The Memory Bank is structured as a series of documents with increasing specificity, each building on the previous:

        1. **[1-projectbrief.md](./1-projectbrief.md)**: Core purpose and constraints of the project
        2. **[2-contextHorizon.md](./2-contextHorizon.md)**: System boundaries and integration points
        3. **[3-fractalArchitecture.md](./3-fractalArchitecture.md)**: Self-similar patterns and recursive structures
        4. **[4-simplificationBlueprint.md](./4-simplificationBlueprint.md)**: Technical debt analysis and reduction strategy
        5. **[5-optimalFileStructure.md](./5-optimalFileStructure.md)**: Target directory structure and component inventory
        6. **[6-consolidationTracker.md](./6-consolidationTracker.md)**: Progress tracking document
        7. **[7-implementationPlan.md](./7-implementationPlan.md)**: Step-by-step execution guide
        8. **[8-entropyAudit.md](./8-entropyAudit.md)**: Complexity metrics and architectural integrity tracking

        ## How to Use This Memory Bank

        ### For Initial Understanding
        1. Start with `1-projectbrief.md` to grasp the core purpose of the project
        2. Read `2-contextHorizon.md` to understand system boundaries
        3. Study `3-fractalArchitecture.md` to recognize recurring patterns
        4. Review `4-simplificationBlueprint.md` to understand the consolidation strategy

        ### For Implementation
        1. Use `5-optimalFileStructure.md` as your architectural reference
        2. Follow `7-implementationPlan.md` for step-by-step implementation guidance
        3. Track progress in `6-consolidationTracker.md`
        4. Monitor complexity using metrics in `8-entropyAudit.md`

        ### For Code Review
        1. Reference `3-fractalArchitecture.md` to ensure new code follows established patterns
        2. Check `5-optimalFileStructure.md` to verify proper component placement
        3. Use `8-entropyAudit.md` to evaluate if changes increase or decrease entropy

        ## Implementation Principles

        1. **Component-First Approach**: Start with leaf UI components and work upward
        2. **Incremental Migration**: Migrate and test one component at a time
        3. **Clear Feature Boundaries**: Maintain strict separation between features
        4. **Single Source of Truth**: Eliminate duplication at all levels
        5. **Outside-In Testing**: Validate from user experience to code internals

        ## Complexity Management

        The current codebase has significant duplication and structural issues. The Memory Bank focuses on:

        1. **Eliminating Component Duplication**: Consolidating multiple implementations
        2. **Normalizing Directory Structure**: Creating a clear, consistent organization
        3. **Feature-Based Organization**: Grouping related components by business domain
        4. **Utility Consolidation**: Creating single sources of truth for shared functions
        5. **Dead Code Elimination**: Removing unused and redundant code

        ## Getting Started

        1. Read through the Memory Bank documents in numerical order
        2. Verify the development environment as outlined in `7-implementationPlan.md`
        3. Begin with Phase 1: Core UI Component Consolidation
        4. Update `6-consolidationTracker.md` as you make progress

        ## Long-Term Maintenance

        Use `8-entropyAudit.md` to schedule regular audits of the codebase to ensure that:

        1. Architectural integrity is maintained
        2. New features follow established patterns
        3. Technical debt is regularly addressed
        4. Component duplication doesn't resurface

        ---

        By following the principles outlined in this Memory Bank, the Ringerike Landskap website codebase will achieve maximal simplicity and elegance while maintaining all essential functionality and adaptability.
    ```

    ---

    #### `project\src\memory-bank\consolidation-plan.md`

    ```markdown
        # File Structure Consolidation Plan

        ## Objective
        Systematically consolidate the project's file structure to follow the unified feature-based architecture described in the Memory Bank, while removing duplication and preserving existing functionality.

        ## Current Structure Issues
        1. **Component Duplication**: Multiple implementations of the same components (Button, Card, etc.)
        2. **Utility Duplication**: Overlapping utilities (imageLoader.ts in both utils/ and shared/utils/)
        3. **Inconsistent Directory Structure**: Mixture of different organizational patterns
        4. **Import Path Inconsistency**: Varied import styles (relative vs. alias paths)

        ## Target Directory Structure
        ```
        src/
        ├── features/      # Business domain modules
        │   ├── services/  # Services feature
        │   │   ├── ui/    # Service-specific UI components
        │   │   ├── hooks/ # Service-specific custom hooks
        │   │   ├── types/ # Service-specific type definitions
        │   │   └── data/  # Service-specific data management
        │   ├── projects/  # Projects feature
        │   │   ├── ui/    # Same structure as services
        │   │   └── ...
        │   └── testimonials/ # Testimonials feature
        │       └── ...
        ├── ui/            # Shared UI primitives
        ├── layout/        # Global layout components
        ├── shared/        # Cross-cutting concerns
        │   ├── hooks/     # Shared hooks
        │   ├── utils/     # Shared utilities
        │   └── types/     # Shared type definitions
        ├── pages/         # Page components that compose features
        └── assets/        # Static assets
        ```

        ## Implementation Strategy: Incremental Component-First Approach
        This strategy minimizes disruption while systematically addressing the most critical issues first.

        ### Phase 1: Core UI Component Consolidation
        **Priority: High** - Reduces most duplication with minimal risk

        #### Components to Consolidate:
        1. **Button Component**
           - ✅ Keep: `src/ui/button/Button.tsx` (most feature-complete)
           - 🗑️ Remove: `src/components/common/Button.tsx` and `src/components/ui/Button.tsx`
           - ✅ Update imports to use `@/ui/button`

        2. **Card Component**
           - Keep: `src/ui/card/Card.tsx`
           - Remove: Any duplicates in components/ directories
           - Update imports to use `@/ui/card`

        3. **Image Component**
           - Keep: `src/ui/image/Image.tsx`
           - Consolidate any duplicates
           - Update imports to use `@/ui/image`

        4. **Container Component**
           - Move from `src/components/common/Container.tsx` to `src/ui/container/Container.tsx`
           - Create index.ts export file
           - Update imports to use `@/ui/container`

        5. **Hero Component**
           - Move from `src/components/common/Hero.tsx` to `src/ui/hero/Hero.tsx`
           - Create index.ts export file
           - Update imports to use `@/ui/hero`

        ### Phase 2: Utility Consolidation
        **Priority: Medium-High** - Addresses functional duplication

        1. **imageLoader Utility**
           - ✅ Keep: `src/shared/utils/imageLoader.ts` (more comprehensive)
           - 🗑️ Remove: `src/utils/imageLoader.ts`
           - ✅ Update imports to use `@/shared/utils/imageLoader`
           - ✅ Add backward compatibility for IMAGE_CATEGORIES

        2. **Other Utilities**
           - Move essential utilities from `/utils` to `/shared/utils`
           - Update imports across the codebase

        ### Phase 3: Feature-Based Organization
        **Priority: Medium** - Establishes proper domain boundaries

        1. **Services Feature**
           - ✅ Already has proper organization in `features/services/`
           - Validate all imports use the proper paths

        2. **Projects Feature**
           - Create `features/projects/` with ui/, hooks/, types/, and data/ subdirectories
           - Move relevant components from components/projects/ into features/projects/ui/
           - Update imports across the codebase

        3. **Testimonials Feature**
           - Create feature directory structure
           - Migrate components, hooks, and data

        ### Phase 4: Page Component Updates
        **Priority: Medium-Low** - Completes the implementation

        1. Update all page components to use the consolidated components and features
        2. Ensure consistent import patterns using aliases
        3. Validate all functionality works as expected

        ### Phase 5: Legacy Cleanup
        **Priority: Low** - Final cleanup after validation

        1. Remove any remaining duplicate components
        2. Remove empty directories
        3. Final validation of all functionality

        ## Implementation Order and Verification Steps

        ### For Each Component/Utility:
        1. **Analyze**: Compare all versions to identify the most complete
        2. **Consolidate**: Keep the best version, update as needed
        3. **Update Imports**: Update a few strategic files first
        4. **Test**: Verify functionality before proceeding
        5. **Scale**: Apply the same process to more files

        ### Verification:
        After each step:
        1. Verify the app still runs
        2. Check for console errors
        3. Test affected functionality
        4. Validate visual appearance

        ## Already Completed Steps
        1. ✅ Button component import updated in ProjectDetail.tsx
        2. ✅ imageLoader utility reference updated in ProjectDetail.tsx
        3. ✅ Created Container component in ui/container/ directory
        4. ✅ Created Hero component in ui/hero/ directory
        5. ✅ Updated ProjectDetail.tsx to use the new components
        6. ✅ Fixed path references to use relative imports for better compatibility
        7. ✅ Removed duplicate Button components from components/common and components/ui
        8. ✅ Removed duplicate Container components from components/common and components/ui
        9. ✅ Removed duplicate Hero components from components/common, components/ui, and components/layout
        10. ✅ Removed duplicate imageLoader.ts utility from utils/ directory

        ## Implementation Notes
        1. **Path Alias Issues**: While path aliases (@/ui/...) are configured in both tsconfig.json and vite.config.ts, they're causing TypeScript errors. We've temporarily switched to relative imports (../ui/...) for immediate compatibility. Once the consolidation is complete, we can revisit alias usage.

        2. **Component Merging Strategy**: For each consolidated component, we're:
           - Taking the most feature-complete version as the base
           - Enhancing with additional features from other implementations
           - Adding proper JSDoc documentation
           - Using consistent patterns (cn utility, React.forwardRef)
           - Creating index.tsx files for clean exports

        ## Next Actions
        1. Consolidate other UI components (Image, Card, etc.)
        2. Begin feature-specific component organization
        3. Update other page components to use the consolidated components
        4. Continue systematically updating imports throughout the codebase
    ```

    ---

    #### `project\src\memory-bank\consolidation-summary.md`

    ```markdown
        # Ringerike Landskap Website Consolidation Summary

        This document provides a high-level summary of the consolidation plan for the Ringerike Landskap website, highlighting key insights, priorities, and next steps. It serves as an executive summary of the work documented in the memory bank.

        ## Consolidation Philosophy

        The consolidation effort follows these core principles:

        1. **Single Source of Truth**: Eliminate duplication to create a single, authoritative implementation for each component and utility
        2. **Feature-Based Organization**: Group related components by business domain to create clear boundaries
        3. **Clear Component Hierarchy**: Distinguish between reusable UI primitives and feature-specific components
        4. **Consistent Patterns**: Apply consistent patterns for component structure, typing, and exports
        5. **Progressive Implementation**: Address the most critical issues first, working iteratively to maintain stability

        ## Current State Assessment

        After analyzing the codebase, we've identified several key issues:

        - **Component Duplication**: Multiple implementations of the same components (Button, Card, ServiceCard, etc.)
        - **Mixed Organization**: Inconsistent directory structure with mixed organizational paradigms
        - **Unclear Boundaries**: Feature-specific components spread across multiple directories
        - **Import Inconsistency**: Varied import patterns leading to maintenance challenges

        Current metrics show high complexity (74/100) and moderate architectural integrity (65/100), with significant opportunities for improvement.

        ## Critical Priorities

        The following items have been identified as critical priorities for immediate action:

        1. **ServiceCard Consolidation**: Multiple implementations causing confusion in the service feature
        2. **ProjectCard Consolidation**: Multiple implementations impacting the project feature
        3. **Image Component Consolidation**: Foundation for many other components, needs optimization

        Detailed implementation plans have been created for each of these components.

        ## Target Architecture

        The target architecture follows a clean, domain-driven structure:

        ```
        src/
        ├── ui/             # Reusable UI primitives
        ├── features/       # Business domain features
        │   ├── services/   # Services feature (ui/data/types/hooks)
        │   ├── projects/   # Projects feature
        │   └── ...         # Other features
        ├── shared/         # Cross-cutting concerns
        ├── pages/          # Page compositions
        └── assets/         # Static assets
        ```

        This structure creates clear boundaries between concerns, makes component discovery intuitive, and follows established React project patterns.

        ## Implementation Strategy

        The implementation follows a phased approach:

        1. **Critical Component Consolidation**: ServiceCard, ProjectCard, Image (Week 1)
        2. **Feature Module Structure**: Services, Projects, Testimonials features (Week 2)
        3. **Remaining UI Components**: Logo, Transition, Hero (Week 3)
        4. **Utility Function Consolidation**: Date, validation, formatting utilities (Week 3-4)
        5. **Page Component Updates**: Normalize structure and update imports (Week 4-5)
        6. **Final Cleanup**: Dead code elimination and documentation (Week 6)

        Each phase builds on the previous, ensuring stable incremental progress.

        ## Expected Outcomes

        Upon completion of the consolidation effort, we expect:

        - **Reduced Complexity**: Complexity score reduced from 74/100 to under 50/100
        - **Improved Architecture**: Architectural integrity increased from 65/100 to over 85/100
        - **Enhanced Developer Experience**: Clearer structure, easier component discovery
        - **Better Performance**: Optimized components and reduced bundle size
        - **Easier Maintenance**: Clear organization reduces cognitive load for future development

        ## Resources & Documentation

        The following documents provide detailed guidance for the consolidation effort:

        - **[6-consolidationTracker.md](./6-consolidationTracker.md)**: Current status of component consolidation
        - **[service-card-consolidation.md](./service-card-consolidation.md)**: Detailed plan for ServiceCard
        - **[project-card-consolidation.md](./project-card-consolidation.md)**: Detailed plan for ProjectCard
        - **[image-component-consolidation.md](./image-component-consolidation.md)**: Detailed plan for Image component
        - **[8-entropyAudit.md](./8-entropyAudit.md)**: Complexity metrics and architectural integrity
        - **[master-implementation-plan.md](./master-implementation-plan.md)**: Comprehensive implementation roadmap

        ## Next Steps

        Immediate next steps for beginning the consolidation process:

        1. Review the master implementation plan and familiarize with the approach
        2. Begin with ServiceCard consolidation (highest priority)
        3. Update the consolidation tracker as items are completed
        4. Schedule a midpoint review after critical components are consolidated
        5. Adjust the plan based on findings during implementation

        ## Long-term Maintenance

        To maintain the simplified structure after completion:

        1. Conduct regular entropy audits (quarterly)
        2. Enforce architectural decisions through code reviews
        3. Keep documentation up-to-date when adding new components
        4. Periodically review for emerging patterns that might need standardization

        ---

        This consolidation effort represents a significant investment in code quality and maintainability. By systematically addressing the identified issues, the Ringerike Landskap website codebase will achieve maximal simplicity and elegance while maintaining all essential functionality and adaptability.
    ```

    ---

    #### `project\src\memory-bank\image-component-consolidation.md`

    ```markdown
        # Image Component Consolidation Plan

        This document provides a detailed plan for consolidating the Image component implementations found in the codebase. This task is identified as a high-priority item in the consolidation tracker, as the current Image component is partially migrated but needs optimization consolidation.

        ## Current Status

        According to the consolidation tracker, the Image component has the following status:

        - Multiple implementations exist: `components/common/Image.tsx` and `ui/image/Image.tsx`
        - A structure has been created at `ui/image/` but the component needs optimization consolidation
        - The component is already exported in `ui/index.ts`

        This partial migration state needs to be resolved to complete the core UI component consolidation phase.

        ## Component Requirements

        The consolidated Image component should:

        1. Handle responsive images effectively
        2. Support image optimization techniques
        3. Include proper loading strategies (lazy loading)
        4. Handle fallback/error states gracefully
        5. Support various aspect ratios
        6. Follow established patterns (React.forwardRef, proper TypeScript interfaces)
        7. Provide clean, well-documented code with JSDoc comments

        ## Implementation Steps

        ### Step 1: Analyze Existing Implementations

        Compare both implementations to:
        - Identify features unique to each implementation
        - Determine the most complete implementation
        - Understand optimization techniques used
        - Document the props interface of each

        ### Step 2: Define Component Interface

        File: `src/ui/image/Image.tsx` (updated type definitions)

        ```typescript
        export interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
          src: string;
          alt: string;
          className?: string;
          aspectRatio?: 'square' | '16:9' | '4:3' | 'auto';
          objectFit?: 'cover' | 'contain' | 'fill' | 'none';
          fallback?: string;
          loading?: 'lazy' | 'eager';
          onLoad?: () => void;
          onError?: () => void;
        }
        ```

        ### Step 3: Implement Consolidated Component

        File: `src/ui/image/Image.tsx`

        ```typescript
        import React, { useState } from 'react';
        import { cn } from '@/shared/utils';

        export interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
          src: string;
          alt: string;
          className?: string;
          aspectRatio?: 'square' | '16:9' | '4:3' | 'auto';
          objectFit?: 'cover' | 'contain' | 'fill' | 'none';
          fallback?: string;
          loading?: 'lazy' | 'eager';
          onLoad?: () => void;
          onError?: () => void;
        }

        /**
         * Image component with optimization and responsive handling
         *
         * @param src - The source URL of the image
         * @param alt - Alternative text for the image
         * @param className - Additional CSS classes
         * @param aspectRatio - Control the aspect ratio of the image container
         * @param objectFit - How the image should be resized to fit its container
         * @param fallback - URL to fallback image if main image fails to load
         * @param loading - Control loading behavior ('lazy' or 'eager')
         * @param onLoad - Callback when image successfully loads
         * @param onError - Callback when image fails to load
         */
        const Image = React.forwardRef<HTMLImageElement, ImageProps>(
          ({
            src,
            alt,
            className = '',
            aspectRatio = 'auto',
            objectFit = 'cover',
            fallback,
            loading = 'lazy',
            onLoad,
            onError,
            ...props
          }, ref) => {
            const [error, setError] = useState(false);

            // Handle aspect ratio classes
            const aspectRatioClasses = {
              'square': 'aspect-square',
              '16:9': 'aspect-video',
              '4:3': 'aspect-[4/3]',
              'auto': ''
            };

            // Handle object fit classes
            const objectFitClasses = {
              'cover': 'object-cover',
              'contain': 'object-contain',
              'fill': 'object-fill',
              'none': 'object-none'
            };

            // Handle image error
            const handleError = () => {
              setError(true);
              onError?.();
            };

            // Handle image load
            const handleLoad = () => {
              onLoad?.();
            };

            return (
              <div className={cn(
                aspectRatio !== 'auto' ? aspectRatioClasses[aspectRatio] : '',
                className
              )}>
                <img
                  ref={ref}
                  src={error && fallback ? fallback : src}
                  alt={alt}
                  loading={loading}
                  className={cn(
                    'w-full h-full',
                    objectFitClasses[objectFit]
                  )}
                  onError={handleError}
                  onLoad={handleLoad}
                  {...props}
                />
              </div>
            );
          }
        );

        Image.displayName = 'Image';

        export default Image;
        ```

        ### Step 4: Update Index Files

        File: `src/ui/image/index.ts`
        ```typescript
        export { default } from './Image';
        export type { ImageProps } from './Image';
        ```

        Ensure the component is properly exported in the main UI index file:

        File: `src/ui/index.ts` (check current exports and update if needed)
        ```typescript
        // Ensure the Image component is exported
        export { default as Image } from './image';
        export type { ImageProps } from './image';
        ```

        ### Step 5: Update Imports

        1. Identify all files importing any of the Image component implementations
        2. Update imports to use the new consolidated component (`import { Image } from '@/ui';` or `import Image from '@/ui/image';`)
        3. Test each update to ensure functionality is maintained
        4. Make any necessary adjustments to handle prop differences

        ### Step 6: Remove Duplicates

        Once all imports have been updated and tested:
        1. Remove `src/components/common/Image.tsx`
        2. Update `src/components/common/index.ts` to remove the Image export
        3. Clean up any other references to the old component

        ## Testing Strategy

        1. **Component Testing**:
           - Test with various image sizes and formats
           - Verify aspect ratio behavior works as expected
           - Test fallback functionality by intentionally using a non-existent image source
           - Test lazy loading behavior

        2. **Integration Testing**:
           - Test in each context where Image is used
           - Verify appearance is consistent with previous implementation
           - Check responsive behavior on different screen sizes

        3. **Performance Testing**:
           - Verify that optimization techniques are working
           - Check network tab to confirm proper image loading
           - Test with Lighthouse or similar tool to verify performance impact

        ## Backward Compatibility

        To maintain backward compatibility:
        - Support all props used in existing implementations
        - Ensure default behavior matches the most used component
        - Handle any prop name differences with appropriate fallbacks or aliasing
        - Document any breaking changes that couldn't be avoided

        ## Component-Specific Considerations

        The Image component has some unique requirements:
        - Handling proper image optimization is critical for site performance
        - Supporting various aspect ratios correctly is important for layout stability
        - Error states need to be handled gracefully
        - Accessibility requirements (proper alt text) must be enforced

        ## Implementation Timeline

        - Analysis of existing implementations: 1 hour
        - Component implementation: 2 hours
        - Update index files and exports: 30 minutes
        - Import updates and testing: 2 hours
        - Cleanup: 30 minutes

        Total estimated time: 6 hours

        ## Dependencies

        This work should be coordinated with:
        - ServiceCard consolidation (which likely uses the Image component)
        - ProjectCard consolidation (which likely uses the Image component)
        - Any optimization utilities used by the current implementations
    ```

    ---

    #### `project\src\memory-bank\master-implementation-plan.md`

    ```markdown
        # Master Implementation Plan: Ringerike Landskap Website Consolidation

        This document serves as the primary guide for implementing the file structure consolidation described in the Memory Bank. It integrates all individual component plans into a cohesive roadmap with clear priorities, timelines, and dependencies.

        ## Current Status Summary

        As of April 28, 2025, the project has:

        - Successfully consolidated some core UI components (Button, Card, Container)
        - Partially consolidated utilities (imageLoader, cn)
        - Established the target directory structure in the memory bank
        - Identified critical consolidation targets (ServiceCard, ProjectCard)
        - Created detailed plans for high-priority components (ServiceCard, ProjectCard, Image)

        Current metrics:
        - Complexity Score: 74/100 (down from 78/100)
        - Architectural Integrity: 65/100 (up from 62/100)

        ## Prioritized Consolidation Roadmap

        ### Phase 1: Critical Component Consolidation (Week 1)

        #### Priority 1: ServiceCard Component
        - **Timeline**: 2 days
        - **Effort**: 6 hours
        - **Plan**: Follow [service-card-consolidation.md](./service-card-consolidation.md)
        - **Dependencies**: None (already has detailed plan)
        - **Impact**: Critical for feature boundary clarity

        #### Priority 2: ProjectCard Component
        - **Timeline**: 2 days
        - **Effort**: 6 hours
        - **Plan**: Follow [project-card-consolidation.md](./project-card-consolidation.md)
        - **Dependencies**: None (already has detailed plan)
        - **Impact**: Critical for feature boundary clarity

        #### Priority 3: Image Component
        - **Timeline**: 1 day
        - **Effort**: 6 hours
        - **Plan**: Follow [image-component-consolidation.md](./image-component-consolidation.md)
        - **Dependencies**: None (already has detailed plan)
        - **Impact**: Used by many other components, improves performance

        ### Phase 2: Feature Module Structure (Week 2)

        #### Priority 4: Services Feature Module
        - **Timeline**: 2 days
        - **Effort**: 8 hours
        - **Plan**: Create complete directory structure and migrate components
        - **Dependencies**: ServiceCard consolidation
        - **Impact**: Establishes pattern for other features

        ```bash
        # Create complete directory structure
        mkdir -p src/features/services/ui
        mkdir -p src/features/services/data
        mkdir -p src/features/services/types
        mkdir -p src/features/services/hooks
        ```

        #### Priority 5: Projects Feature Module
        - **Timeline**: 2 days
        - **Effort**: 8 hours
        - **Plan**: Create complete directory structure and migrate components
        - **Dependencies**: ProjectCard consolidation
        - **Impact**: Reinforces feature-based organization

        ```bash
        # Create complete directory structure
        mkdir -p src/features/projects/ui
        mkdir -p src/features/projects/data
        mkdir -p src/features/projects/types
        mkdir -p src/features/projects/hooks
        ```

        #### Priority 6: Testimonials Feature Module
        - **Timeline**: 1 day
        - **Effort**: 6 hours
        - **Plan**: Create complete directory structure and migrate components
        - **Dependencies**: None
        - **Impact**: Maintains consistency across features

        ### Phase 3: Remaining UI Components (Week 3)

        #### Priority 7: Logo Component
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Consolidate Logo implementations
        - **Dependencies**: None
        - **Impact**: Medium, used in header/footer

        #### Priority 8: Transition Component
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Consolidate animation/transition components
        - **Dependencies**: None
        - **Impact**: Medium, enhances UX

        #### Priority 9: Hero Component
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Finish Hero component consolidation
        - **Dependencies**: Image component
        - **Impact**: High visual impact on multiple pages

        ### Phase 4: Utility Function Consolidation (Week 3-4)

        #### Priority 10: Date Utilities
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Consolidate date-related functions, especially seasonal logic
        - **Dependencies**: None
        - **Impact**: Critical for seasonal content adaptation

        #### Priority 11: Validation Utilities
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Consolidate form validation helpers
        - **Dependencies**: None
        - **Impact**: Important for forms/contact functionality

        #### Priority 12: Formatting Utilities
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Consolidate text formatting functions
        - **Dependencies**: None
        - **Impact**: Consistent text presentation

        ### Phase 5: Page Component Updates (Week 4-5)

        #### Priority 13: Page Component Structure
        - **Timeline**: 2 days
        - **Effort**: 8 hours
        - **Plan**: Normalize page paths and directory structure
        - **Dependencies**: Feature modules completed
        - **Impact**: Architectural consistency

        ```bash
        # Create normalized page structure
        mkdir -p src/pages/services
        mkdir -p src/pages/projects
        mkdir -p src/pages/testimonials
        ```

        #### Priority 14: Page Component Imports
        - **Timeline**: 3 days
        - **Effort**: 12 hours
        - **Plan**: Update all imports in page components
        - **Dependencies**: All components consolidated
        - **Impact**: Functional integrity of application

        ### Phase 6: Final Cleanup (Week 6)

        #### Priority 15: Dead Code Elimination
        - **Timeline**: 2 days
        - **Effort**: 8 hours
        - **Plan**: Remove unused components, imports, and files
        - **Dependencies**: All consolidation complete
        - **Impact**: Codebase cleanliness and maintenance

        #### Priority 16: Documentation Update
        - **Timeline**: 1 day
        - **Effort**: 4 hours
        - **Plan**: Update all Memory Bank documents with final status
        - **Dependencies**: All work complete
        - **Impact**: Knowledge preservation

        ## Weekly Implementation Schedule

        ### Week 1: Critical Components
        - Monday-Tuesday: ServiceCard consolidation
        - Wednesday-Thursday: ProjectCard consolidation
        - Friday: Image component consolidation

        ### Week 2: Feature Module Structure
        - Monday-Tuesday: Services feature module
        - Wednesday-Thursday: Projects feature module
        - Friday: Testimonials feature module

        ### Week 3: UI Components and Utilities
        - Monday: Logo component
        - Tuesday: Transition component
        - Wednesday: Hero component
        - Thursday-Friday: Date and validation utilities

        ### Week 4: Utilities and Pages
        - Monday: Formatting utilities
        - Tuesday-Wednesday: Page component structure
        - Thursday-Friday: Begin page component imports

        ### Week 5: Page Components and Testing
        - Monday-Tuesday: Complete page component imports
        - Wednesday-Friday: Testing and fixes

        ### Week 6: Cleanup and Documentation
        - Monday-Tuesday: Dead code elimination
        - Wednesday: Documentation update
        - Thursday-Friday: Final testing and validation

        ## Validation Strategy

        ### Incremental Testing
        After each component or feature is consolidated:
        1. Verify the application runs without errors
        2. Check console for warnings
        3. Visual validation of affected components
        4. Functional testing of interactive elements
        5. Responsive behavior testing

        ### Comprehensive Testing
        After each phase is complete:
        1. Run the full application
        2. Test all main user flows
        3. Verify all pages render correctly
        4. Check for regressions

        ### Final Validation
        At project completion:
        1. Full application testing
        2. Performance testing (Lighthouse)
        3. Cross-browser compatibility
        4. Responsive design validation

        ## Risk Mitigation

        ### Component Consolidation Risks
        1. **Breaking Changes**: Maintain backward compatibility with existing props
        2. **Visual Regression**: Compare before/after renders visually
        3. **Performance Impact**: Monitor bundle size and rendering performance

        ### Feature Module Risks
        1. **Circular Dependencies**: Enforce strict boundaries between features
        2. **Import Failures**: Update imports incrementally and test frequently
        3. **Data Flow Disruption**: Maintain consistent data interfaces

        ### General Risks
        1. **Time Overruns**: Build in buffer time for complex components
        2. **Knowledge Gaps**: Document findings and decisions as you go
        3. **Scope Creep**: Stick to consolidation, defer enhancements

        ## Success Criteria

        The consolidation will be considered successful when:

        1. **Component Metrics**:
           - Zero component duplications
           - All UI primitives in ui/ directory
           - All feature components in their respective feature modules
           - All utilities in shared/utils directory

        2. **Structure Metrics**:
           - Directory count reduced to target (12-15)
           - Component files reduced to target (30-35)
           - Utility files reduced to target (8-10)

        3. **Quality Metrics**:
           - Import pattern consistency > 90%
           - Feature boundary clarity > 90%
           - Dead/unused code < 1%

        4. **Audit Results**:
           - Complexity score < 50/100
           - Architectural integrity > 85/100

        ## Ongoing Entropy Control

        To maintain the simplified structure after completion:

        1. Update the Memory Bank with any new findings or decisions
        2. Conduct regular entropy audits (quarterly)
        3. Enforce architectural decisions through code reviews
        4. Keep documentation up-to-date when adding new components

        ## Next Steps

        1. Begin with ServiceCard consolidation
        2. Update consolidation tracker as items are completed
        3. Perform interim entropy audit after critical components are consolidated
        4. Adjust timeline if needed based on findings during implementation
    ```

    ---

    #### `project\src\memory-bank\project-card-consolidation.md`

    ```markdown
        # ProjectCard Component Consolidation Plan

        This document provides a detailed plan for consolidating the multiple ProjectCard component implementations found in the codebase. This task is identified as a high-priority item in the consolidation tracker.

        ## Current Implementations

        There are multiple implementations of the ProjectCard component:

        1. **`src/components/projects/ProjectCard.tsx`** - Likely a basic implementation
        2. **`src/features/projects/ProjectCard.tsx`** - Potentially a more feature-rich implementation

        This duplication creates:
        - Confusion for developers about which component to use
        - Inconsistent styling and behavior across the application
        - Increased maintenance overhead
        - Risk of bugs when one version is updated but not others

        ## Consolidation Target

        We'll create a unified ProjectCard component at:

        ```
        src/features/projects/ui/ProjectCard.tsx
        ```

        This follows our target architecture where feature-specific components live within their feature module.

        ## Component Requirements

        The consolidated ProjectCard should:

        1. Support all variants and features from the current implementations
        2. Maintain backward compatibility with existing usage
        3. Follow established patterns (React.forwardRef, proper TypeScript interfaces)
        4. Use our consolidated UI components where appropriate (Card, Image, etc.)
        5. Provide clean, well-documented code with JSDoc comments
        6. Handle responsive behavior consistently

        ## Implementation Steps

        ### Step 1: Create Directory Structure

        ```bash
        # Create the necessary directory structure
        mkdir -p src/features/projects/ui
        mkdir -p src/features/projects/data
        mkdir -p src/features/projects/types
        mkdir -p src/features/projects/hooks
        touch src/features/projects/index.ts
        touch src/features/projects/ui/index.ts
        touch src/features/projects/data/index.ts
        touch src/features/projects/types/index.ts
        touch src/features/projects/hooks/index.ts
        ```

        ### Step 2: Create Types

        Create a types file for project-specific types:

        File: `src/features/projects/types/project.ts`
        ```typescript
        // Import any shared types needed
        import { ProjectType } from '@/types';

        export interface ProjectCardProps {
          project: ProjectType;
          className?: string;
          variant?: 'default' | 'compact' | 'featured';
          showCategory?: boolean;
          onClick?: () => void;
        }

        // Add more project-specific types as needed
        ```

        File: `src/features/projects/types/index.ts`
        ```typescript
        export * from './project';
        ```

        ### Step 3: Analyze Existing Implementations

        Before consolidation, compare all versions to:
        - Identify the most complete and flexible implementation
        - Note unique features from each implementation
        - Document all props and their use cases
        - Understand the styling approach used in each

        ### Step 4: Create Consolidated Component

        File: `src/features/projects/ui/ProjectCard.tsx`
        ```typescript
        import React from 'react';
        import { Link } from 'react-router-dom';
        import { ProjectCardProps } from '../types';
        import { cn } from '@/shared/utils';
        import { Card } from '@/ui/card';
        import { Image } from '@/ui/image';

        /**
         * ProjectCard component displays a project with various layout options
         *
         * @param project - The project data to display
         * @param className - Optional CSS class to apply to the component
         * @param variant - Layout variant: "default", "compact", or "featured"
         * @param showCategory - Whether to display the project category
         */
        const ProjectCard = React.forwardRef<HTMLDivElement, ProjectCardProps>(
          ({ project, className = '', variant = 'default', showCategory = true, onClick }, ref) => {
            const { id, title, description, image, category, date } = project;

            // Implementation will be based on the combined features of all existing implementations
            // We'll use the Card component from our UI library where appropriate

            // Implementation details will go here, with conditional rendering based on variant

            // Return the appropriate JSX based on the variant
          }
        );

        ProjectCard.displayName = 'ProjectCard';

        export default ProjectCard;
        ```

        File: `src/features/projects/ui/index.ts`
        ```typescript
        export { default as ProjectCard } from './ProjectCard';
        export * from './ProjectCard';
        ```

        File: `src/features/projects/index.ts`
        ```typescript
        export * from './ui';
        export * from './types';
        ```

        ### Step 5: Update Imports

        1. Identify all files importing any of the ProjectCard components using search functionality
        2. Update imports to use the new consolidated component
        3. Test each update to ensure functionality is maintained
        4. Make any necessary adjustments to handle prop differences

        ### Step 6: Remove Duplicates

        Once all imports have been updated and tested:
        1. Remove `src/components/projects/ProjectCard.tsx`
        2. Remove `src/features/projects/ProjectCard.tsx` (if it exists outside the ui directory)
        3. Clean up any remaining references to the old components

        ## Testing Strategy

        1. **Component Testing**:
           - Test rendering of all variants
           - Verify all props work as expected
           - Check for responsive behavior on different screen sizes

        2. **Integration Testing**:
           - Test in each context where ProjectCard is used (project lists, featured projects, etc.)
           - Verify navigation to project detail pages works
           - Check that all styling is applied correctly

        3. **Visual Verification**:
           - Compare before/after screenshots
           - Ensure consistent styling across variants
           - Verify responsive behavior

        ## Backward Compatibility

        To maintain backward compatibility:
        - Support the same props interface as existing components
        - Ensure the default styling matches current components
        - Maintain identical behavior for interactive elements
        - Handle any legacy data formats that might be passed to the component

        ## Component-Specific Considerations

        The ProjectCard likely has some unique requirements:
        - Handling different image aspect ratios consistently
        - Displaying categories/tags in a consistent way
        - Showing dates or other metadata
        - Link behavior to project detail pages

        ## Resources

        - [Project Card design specifications](link-to-design-doc)
        - [Current usages in the codebase](link-to-findings)

        ## Implementation Timeline

        - Analysis of existing implementations: 1 hour
        - Directory structure and type creation: 30 minutes
        - Component implementation: 2 hours
        - Import updates and testing: 2 hours
        - Cleanup: 30 minutes

        Total estimated time: 6 hours

        ## Dependencies

        This work should be coordinated with:
        - ServiceCard consolidation (similar patterns)
        - Card UI component (used as a base)
        - Image component (for consistent image handling)
    ```

    ---

    #### `project\src\memory-bank\service-card-consolidation.md`

    ```markdown
        # ServiceCard Component Consolidation Plan

        This document provides a detailed plan for consolidating the multiple ServiceCard component implementations found in the codebase. This is identified as a high-priority task in the consolidation tracker.

        ## Current Implementations

        Currently, there are three implementations of the ServiceCard component:

        1. **`src/components/ServiceCard.tsx`** - Basic implementation with title, description, and image
        2. **`src/components/services/ServiceCard.tsx`** - Possibly a variant with different features
        3. **`src/features/services.tsx`** - Contains a `ServiceCard` component with multiple variants ("default", "compact", "featured")

        This duplication creates several issues:
        - Confusion about which component to use
        - Inconsistent styling and behavior
        - Maintenance overhead
        - Potential for bugs when one version is updated but not others

        ## Consolidation Target

        We'll create a unified ServiceCard component at:

        ```
        src/features/services/ui/ServiceCard.tsx
        ```

        This follows our target architecture where feature-specific components live within their feature module.

        ## Component Requirements

        The consolidated ServiceCard should:

        1. Support all variants from the current implementations
        2. Maintain backward compatibility with existing usage
        3. Follow established patterns (React.forwardRef, proper TypeScript interfaces)
        4. Use our consolidated UI components where appropriate
        5. Provide clean, well-documented code with JSDoc comments

        ## Implementation Steps

        ### Step 1: Create Directory Structure

        ```bash
        # Create the necessary directory structure
        mkdir -p src/features/services/ui
        mkdir -p src/features/services/data
        mkdir -p src/features/services/types
        mkdir -p src/features/services/hooks
        touch src/features/services/index.ts
        touch src/features/services/ui/index.ts
        touch src/features/services/data/index.ts
        touch src/features/services/types/index.ts
        touch src/features/services/hooks/index.ts
        ```

        ### Step 2: Create Types

        Create a types file for service-specific types:

        File: `src/features/services/types/service.ts`
        ```typescript
        // Import any shared types needed
        import { ServiceType } from '@/types';

        export interface ServiceCardProps {
          service: ServiceType;
          className?: string;
          variant?: 'default' | 'compact' | 'featured';
          onClick?: () => void;
        }

        // Add more service-specific types as needed
        ```

        File: `src/features/services/types/index.ts`
        ```typescript
        export * from './service';
        ```

        ### Step 3: Analyze Existing Implementations

        Before consolidation, compare all versions to:
        - Identify the most complete and flexible implementation
        - Note unique features from each implementation
        - Understand all use cases and props

        ### Step 4: Create Consolidated Component

        File: `src/features/services/ui/ServiceCard.tsx`
        ```typescript
        import React from 'react';
        import { Link } from 'react-router-dom';
        import { ServiceCardProps } from '../types';
        import { cn } from '@/shared/utils';

        /**
         * ServiceCard component displays a service with various layout options
         *
         * @param service - The service data to display
         * @param className - Optional CSS class to apply to the component
         * @param variant - Layout variant: "default" (with image background), "compact" (horizontal layout), or "featured" (card with image on top)
         */
        const ServiceCard = React.forwardRef<HTMLDivElement, ServiceCardProps>(
          ({ service, className = '', variant = 'default', onClick }, ref) => {
            const { id, title, description, image } = service;

            // Implementation will be based on the combined features of all existing implementations
            // The most complete version appears to be in src/features/services.tsx with its variant support

            // Implementation details will go here...

            // Return the appropriate JSX based on the variant
          }
        );

        ServiceCard.displayName = 'ServiceCard';

        export default ServiceCard;
        ```

        File: `src/features/services/ui/index.ts`
        ```typescript
        export { default as ServiceCard } from './ServiceCard';
        export * from './ServiceCard';
        ```

        File: `src/features/services/index.ts`
        ```typescript
        export * from './ui';
        export * from './types';
        ```

        ### Step 5: Update Imports

        1. Identify all files importing any of the ServiceCard components
        2. Update imports to use the new consolidated component
        3. Test each update to ensure functionality is maintained

        ### Step 6: Remove Duplicates

        Once all imports have been updated and tested:
        1. Remove `src/components/ServiceCard.tsx`
        2. Remove `src/components/services/ServiceCard.tsx`
        3. Refactor `src/features/services.tsx` to remove the ServiceCard implementation, or migrate its other components to individual files

        ## Testing Strategy

        1. **Component Testing**:
           - Test rendering of all variants
           - Verify all props work as expected
           - Check for responsive behavior

        2. **Integration Testing**:
           - Test in each context where ServiceCard is used
           - Verify navigation to service detail pages works
           - Check that all styling is applied correctly

        3. **Visual Verification**:
           - Compare before/after screenshots
           - Ensure consistent styling across variants

        ## Backward Compatibility

        To maintain backward compatibility:
        - Support the same props interface as existing components
        - Ensure the default styling matches current components
        - Maintain identical behavior for interactive elements

        ## Resources

        - [Material for ServiceCard design](link-to-design-doc)
        - [Current usages in the codebase](link-to-findings)

        ## Implementation Timeline

        - Analysis of existing implementations: 1 hour
        - Directory structure and type creation: 30 minutes
        - Component implementation: 2 hours
        - Import updates and testing: 2 hours
        - Cleanup: 30 minutes

        Total estimated time: 6 hours
    ```

