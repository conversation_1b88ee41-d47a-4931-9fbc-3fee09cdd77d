# Website Environment Configuration

This directory contains environment configuration files for the website runtime.

## Files

- `.env.development` - Development environment configuration
- `.env.staging` - Staging environment configuration
- `.env.production` - Production environment configuration

## Variable Naming Conventions

- `VITE_*` - Variables that are accessible in the website code
- `VITE_WEBSITE_*` - Website-specific variables

## Critical Variables

The following variables must be consistent between the root and website environment files:

- `VITE_BASE_URL`
- `VITE_API_URL`
- `VITE_ENABLE_ANALYTICS`
- `VITE_ANALYTICS_ID`
- `VITE_DEBUG`
- `VITE_FEATURE_*`

## Validation

Run `npm run validate-env` to check for inconsistencies between the root and website environment files.

## Notes

- These files should not be committed to version control (they are in .gitignore)
- Use `.env.example` files for documentation and templates
- The website can function independently with these environment files
