# Action Plan for Restructuring Ringerike Landskap's Project Architecture

This action plan outlines a pragmatic approach to reorganizing the current project structure for Ringerike Landskap AS. The goal is to create a more maintainable, scalable architecture that eliminates duplication and improves developer experience.

## Current Structure Issues

After analyzing the codebase, I've identified several structural challenges:

1. Component duplication (multiple `Button`, `Hero`, and `ServiceCard` components)
2. Inconsistent organization (mixed feature-based, type-based, and domain-based structures)
3. Scattered configuration and data management
4. Unclear separation of concerns between UI and business logic
5. Excessive nesting in component directories

## Proposed Structure Implementation Plan

### Phase 1: Core Architecture Reorganization (Week 1)

```
src/
├── assets/                     # Static assets (images, fonts)
├── components/                 # Shared UI components
│   ├── ui/                     # Pure UI components
│   └── layout/                 # Layout components
├── features/                   # Feature modules
├── hooks/                      # Custom hooks
├── lib/                        # Utilities and configuration
├── pages/                      # Route components
└── App.tsx                     # Root component
```

**Tasks:**
1. Create the base folder structure
2. Establish clear organization principles in README.md
3. Set up path aliases in tsconfig.json for cleaner imports

### Phase 2: Component Consolidation and Cleanup (Week 2)

**Tasks:**
1. Audit and deduplicate UI components:
   - Consolidate duplicated components (`Button`, `Hero`, `ServiceCard`)
   - Move shared components to `components/ui/`
   - Document component props and usage

2. Restructure layout components:
   - Move `Header`, `Footer`, `Navbar` to `components/layout/`
   - Create consistent layout wrappers

3. Extract domain-specific components to features:
   - Move service-specific components to `features/services/components/`
   - Move project-specific components to `features/projects/components/`

### Phase 3: Data Management Simplification (Week 3)

**Tasks:**
1. Consolidate data management:
   - Merge `data/`, `content/`, and `config/` into `lib/`
   - Organize by domain (`lib/services/data.ts`, `lib/projects/data.ts`)

2. Create data access hooks:
   - Move data fetching logic to custom hooks
   - Place hooks in respective feature directories

3. Implement centralized site configuration:
   - Create `lib/config/site.ts` for global site settings
   - Ensure environment-specific configurations work correctly

### Phase 4: Feature Module Organization (Week 4)

**Tasks:**
1. Create feature modules with consistent structure:
   ```
   features/
   ├── services/                # Services feature
   │   ├── components/          # Feature-specific components
   │   ├── hooks/               # Feature-specific hooks
   │   ├── types.ts             # Feature-specific types
   │   ├── utils.ts             # Feature-specific utilities
   │   └── index.ts             # Public API exports
   ```

2. Implement the same structure for other features:
   - projects
   - testimonials
   - contact
   - about

3. Ensure each feature exports a clean public API through its index.ts

## Implementation Details

### Component Organization Guidelines

1. **UI Components** (`components/ui/`):
   - Pure presentational components
   - No business logic or data fetching
   - Example: `Button`, `Card`, `Form` inputs

2. **Layout Components** (`components/layout/`):
   - Page structure components
   - Example: `Header`, `Footer`, `MainLayout`

3. **Feature Components** (`features/*/components/`):
   - Domain-specific components
   - Can contain business logic
   - Example: `ServiceCard`, `ProjectGallery`

### Feature Module Guidelines

Each feature module should:
1. Handle one business domain completely
2. Export components through a public API
3. Contain its own types, hooks, and utilities
4. Be organized consistently

### File Naming Conventions

- React components: PascalCase (e.g., `Button.tsx`)
- Hooks, utils: camelCase (e.g., `useProject.ts`, `formatDate.ts`)
- Type definitions: PascalCase for types, camelCase for files
- Indexes: `index.ts` for exports

## Migration Strategy

To minimize disruption during refactoring:

1. **Incremental approach**:
   - Create new structure alongside existing one
   - Move components one by one
   - Update imports incrementally

2. **Feature-by-feature migration**:
   - Complete one feature before moving to the next
   - Start with a smaller feature to establish patterns

3. **Testing strategy**:
   - Test each component after migration
   - Ensure all functionality works before removing old components

## Benefits of New Structure

1. **Eliminates duplication**: Single source of truth for components
2. **Improves discoverability**: Components are where you expect them to be
3. **Enhances maintainability**: Clear separation of concerns
4. **Facilitates collaboration**: Team members can work on different features
5. **Supports scalability**: Easy to add new features without disrupting existing ones

This plan provides a clear, stepwise approach to transforming the codebase into a more maintainable structure while ensuring the site continues to function throughout the process.

Citations:
[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/14677857/3e449bb0-ef5c-4c3b-a6c8-54730fa4334a/paste.txt
[2] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/14677857/244beb6c-4967-428d-b47d-25a7c66b52fc/paste-2.txt