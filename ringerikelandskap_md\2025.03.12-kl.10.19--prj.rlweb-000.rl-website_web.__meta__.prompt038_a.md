

Develop a comprehensive, step-by-step approach that addresses the following aspects in a detailed and actionable manner. The response should cater to an expert audience, maintain technical rigor and clarity, and emphasize adaptability and iterative improvements over time. Where appropriate, include concrete examples or sample excerpts to illustrate requirements and expected outcomes.

I. Company Narrative
──────────────────────
1. Rewrite the description for Ringerike Landskap AS to highlight that it is a young, growing, and experienced landscaping and machine-contracting organization based in Hole, Buskerud. The revised narrative must underscore:
   • Its strong local expertise in areas such as Hole, Hønefoss, Jevnaker, Sundvollen, and Vik.
   • Its ability to create outdoor spaces that seamlessly blend functionality, durability, and personalized aesthetics.
   • Maintain a tone that is both professional and approachable, ensuring the description retains conversational momentum alongside technical detail.

2. Recast the company’s selling point by shifting focus away from high-quality materials, seasonal relevance, and transparent communication, and instead centering on the two owners. The new narrative should emphasize:
   • Their ability to deliver precise, customer-tailored solutions.
   • Their exceptional interpersonal skills and genuine personal investment in every client relationship.
   • A proven track record of providing unexpected added value.
   • A diverse skill set—including professional welding and innovative use of corten steel.
   • Their creativity, flexibility, and extensive experience in designing spaces for both private and commercial needs optimized for the Norwegian climate.

II. Cursor AI Agent Guidelines
─────────────────────────────
Develop a set of generalized, non-code-specific guidelines for a Cursor AI agent that include:
   • A clear outline of the core project context and the unique characteristics derived from initial notes and documentation.
   • A defined list of key actions that the AI must execute to remain fully aligned with project objectives.
   • Critical guardrails to enforce process integrity and prevent deviations—including detailed methods for enforcing rules and for updating documentation concurrently with code changes.
   • Explicit instructions focusing on eliminating duplicate code and ensuring every alteration is accompanied by up-to-date, accurate documentation.
   • Include checkpoints and feedback loops to facilitate ongoing adaptation as the project evolves.

III. Code Workflow and Website Rendering Protocols
─────────────────────────────────────────────────────────────
Propose detailed workflow instructions for iterative code improvements and website rendering that ensure:
   • Systematic consolidation of code to maintain an intuitive, efficient workflow.
   • Strict adherence to best software practices specific to the project’s tech stack.
   • Automated triggers that update documentation immediately after code modifications.
   • Strategies to detect and prevent duplicate code, thereby sustaining a single unified source of truth.
   • A modular process that supports iterative refinements and continuous process documentation.

IV. React Code Dependency Visualization Guidelines
─────────────────────────────────────────────────────────────
Establish detailed guidelines for visualizing code dependencies in React projects, which should include:
   • The integration of interactive visualization tools (e.g., an extension similar to the React Component Tree VS Code extension) that automatically depict component interdependencies, available props, and conditional rendering logic.
   • An automated process to generate and update detailed dependency maps whenever files are modified.
   • Strategies for robust cycle detection and hierarchical mapping applicable to both small-scale and large-scale applications.
   • Optionally leveraging popular Python libraries and interactive node editor systems to provide a dynamic visual representation.
   • Provide sample excerpts or a flow diagram mapping key dependency analysis checkpoints where possible.

V. Data Duplication Analysis and Migration Strategy
─────────────────────────────────────────────────────────────
Define a comprehensive process for analyzing the codebase for data duplication and implementing a migration strategy. This process should include:
   • A thorough scan for multiple definitions of location data across specified modules (e.g., src/content/locations/index.ts, src/lib/constants.ts, src/lib/constants/index.ts, and the src/ts.md documentation).
   • Identification and reconciliation of discrepancies between the Location and ServiceArea type structures.
   • Enforcement of uniform import practices (e.g., ensuring SERVICE_AREAS is consistently imported from '@/lib/constants').
   • A root cause analysis to determine factors like fragmented data management and overlapping directory structures.
   • The development of a migration strategy that consolidates data to a single source of truth, harmonizes type definitions, implements disciplined re-export patterns, and revises documentation systematically—all while safeguarding backward compatibility and minimizing potential refactoring risks.
   • Include clear checkpoints, validation methods, and examples for verifying the integrity of the consolidation process.

Final Requirements:
──────────────────────
• Ensure that every action is clearly documented and aligned with project requirements.
• Structure the guidance modularly with clear subheadings for easy reference.
• Incorporate concrete examples, sample excerpts, or diagrams/flow charts where appropriate to illustrate expectations.
• Emphasize iterative review and adaptability, ensuring that documentation and processes evolve seamlessly with project changes.
