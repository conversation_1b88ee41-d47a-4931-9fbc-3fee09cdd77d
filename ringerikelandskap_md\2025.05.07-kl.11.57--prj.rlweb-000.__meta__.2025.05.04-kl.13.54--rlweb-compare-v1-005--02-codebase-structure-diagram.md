# Ringerike Landskap Codebase Structure Visualization

This document provides visual representations of the Ringerike Landskap website's codebase architecture to complement the written analysis.

## 1. High-Level Directory Structure

```mermaid
graph TD
    root[Ringerike Landskap Website] --> config[config/]
    root --> memorybank[memorybank/]
    root --> public[public/]
    root --> scripts[scripts/]
    root --> src[src/]
    
    %% Config branch
    config --> env[env/]
    
    %% Public branch
    public --> images[images/]
    public --> htaccess[.htaccess]  
    public --> redirects[_redirects]
    images --> categorized[categorized/]
    images --> hero[hero/]
    images --> icons[icons/]
    images --> site[site/]
    images --> team[team/]
    
    %% Scripts branch
    scripts --> analysis[analysis/]
    
    %% Source code branch
    src --> app[app/]
    src --> components[components/]
    src --> content[content/]
    src --> data[data/]
    src --> docs[docs/]
    src --> layout[layout/]
    src --> lib[lib/]
    src --> sections[sections/]
    src --> styles[styles/]
    src --> ui[ui/]
    
    %% Library subdirectories
    lib --> api[api/]
    lib --> config_lib[config/]
    lib --> constants[constants/]
    lib --> context[context/]
    lib --> hooks[hooks/]
    lib --> types[types/]
    lib --> utils[utils/]
    
    %% Sections subdirectories (chronological order)
    sections --> home[10-home/]
    sections --> about[20-about/]
    sections --> services[30-services/]
    sections --> projects[40-projects/]
    sections --> testimonials[50-testimonials/]
    sections --> contact[60-contact/]

    %% Style for directories vs files
    classDef directory fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef file fill:#fff,stroke:#bbb,stroke-width:1px,stroke-dasharray: 5 5;
    
    class config,memorybank,public,scripts,src,env,images,analysis,app,components,content,data,docs,layout,lib,sections,styles,ui,api,config_lib,constants,context,hooks,types,utils,home,about,services,projects,testimonials,contact,categorized,hero,icons,site,team directory;
    class htaccess,redirects file;
```

## 2. Architectural Layers

```mermaid
graph TD
    root[Architectural Layers] --> presentation[Presentation Layer]
    root --> business[Business Logic Layer]
    root --> data[Data Layer]
    root --> config[Configuration Layer]
    
    %% Presentation Layer
    presentation --> ui[UI Components<br/><i>src/ui/</i>]
    presentation --> sections[Section Components<br/><i>src/sections/</i>]
    presentation --> layout[Layout Components<br/><i>src/layout/</i>]
    
    %% Business Logic Layer
    business --> api[API Layer<br/><i>src/lib/api/</i>]
    business --> hooks[React Hooks<br/><i>src/lib/hooks/</i>]
    business --> context[Context Providers<br/><i>src/lib/context/</i>]
    business --> utils[Utility Functions<br/><i>src/lib/utils/</i>]
    
    %% Data Layer
    data --> constants[Constants<br/><i>src/lib/constants/</i>]
    data --> types[TypeScript Types<br/><i>src/lib/types/</i>]
    data --> staticData[Static Data<br/><i>src/data/</i>]
    data --> contentData[Content Data<br/><i>src/content/</i>]
    
    %% Configuration Layer
    config --> appConfig[App Config<br/><i>src/lib/config/</i>]
    config --> envConfig[Environment Config<br/><i>config/env/</i>]
    
    %% Style nodes
    classDef layer fill:#e6f7ff,stroke:#333,stroke-width:2px;
    classDef component fill:#f9f9f9,stroke:#333,stroke-width:1px;
    
    class presentation,business,data,config layer;
    class ui,sections,layout,api,hooks,context,utils,constants,types,staticData,contentData,appConfig,envConfig component;
```

## 3. Key System Relationships

```mermaid
graph TD
    %% Core systems
    seasonal[Seasonal Content System] --> seasonalConstants[Seasonal Constants<br/><i>constants/seasonal.ts</i>]
    seasonal --> seasonalUtils[Seasonal Utilities<br/><i>utils/filtering.ts</i>]
    seasonal --> seasonalHook[Seasonal Hook<br/><i>hooks/useSeasonalData.ts</i>]
    seasonal --> seasonalComponents[Seasonal Components<br/><i>sections/10-home/</i>]
    
    filtering[Filtering System] --> filterUtils[Filter Utilities<br/><i>utils/filtering.ts</i>]
    filtering --> projectFilter[Project Filter<br/><i>sections/40-projects/ProjectFilter.tsx</i>]
    filtering --> serviceFilter[Service Filter<br/><i>sections/30-services/</i>]
    
    api[API & Data System] --> apiDef[API Definition<br/><i>api/index.ts</i>]
    api --> apiImpl[API Implementation<br/><i>api/enhanced.ts</i>]
    api --> dataHook[Data Hook<br/><i>hooks/useData.ts</i>]
    api --> dataConstants[Data Constants<br/><i>constants/data.ts</i>]
    
    %% Relationships between systems
    seasonalUtils -.-> filterUtils
    apiImpl -.-> filterUtils
    dataHook -.-> apiImpl
    
    %% Style nodes
    classDef system fill:#e6f7ff,stroke:#333,stroke-width:2px;
    classDef component fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef relationship stroke:#999,stroke-width:1px,stroke-dasharray: 5 5;
    
    class seasonal,filtering,api system;
    class seasonalConstants,seasonalUtils,seasonalHook,seasonalComponents,filterUtils,projectFilter,serviceFilter,apiDef,apiImpl,dataHook,dataConstants component;
```

## 4. Component Architecture

```mermaid
graph TD
    %% Component tiers
    root[Component Architecture] --> atomicUI[Atomic UI Components<br/><i>src/ui/</i>]
    root --> sectionComponents[Section Components<br/><i>src/sections/</i>]
    root --> layoutComponents[Layout Components<br/><i>src/layout/</i>]
    
    %% Examples of each tier
    atomicUI --> button[Button]
    atomicUI --> card[Card]
    atomicUI --> container[Container]
    atomicUI --> hero[Hero]
    
    sectionComponents --> homeSection[Home Components]
    sectionComponents --> projectsSection[Project Components]
    sectionComponents --> servicesSection[Services Components]
    
    homeSection --> filteredServices[FilteredServicesSection]
    homeSection --> seasonalProjects[SeasonalProjectsCarousel]
    
    projectsSection --> projectCard[ProjectCard]
    projectsSection --> projectFilter[ProjectFilter]
    projectsSection --> projectGrid[ProjectGrid]
    
    layoutComponents --> header[Header]
    layoutComponents --> footer[Footer]
    layoutComponents --> meta[Meta]
    
    %% Component relationships
    filteredServices -.-> button
    filteredServices -.-> card
    projectCard -.-> card
    header -.-> button
    header -.-> container
    
    %% Style nodes
    classDef tier fill:#e6f7ff,stroke:#333,stroke-width:2px;
    classDef component fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef sectionComponent fill:#f2f2f2,stroke:#333,stroke-width:1px;
    classDef relationship stroke:#999,stroke-width:1px,stroke-dasharray: 5 5;
    
    class atomicUI,sectionComponents,layoutComponents tier;
    class button,card,container,hero,header,footer,meta component;
    class homeSection,projectsSection,servicesSection,filteredServices,seasonalProjects,projectCard,projectFilter,projectGrid sectionComponent;
```

## 5. Data & API Flow

```mermaid
flowchart TD
    %% Data sources
    dataFiles[Data Files<br/><i>src/data/</i>] --> constants[Constants<br/><i>src/lib/constants/</i>]
    constants --> apiLayer[API Layer<br/><i>src/lib/api/</i>]
    
    %% API consumption path
    apiLayer --> cache{Cache}
    cache -- "Hit" --> cachedData[Cached Data]
    cache -- "Miss" --> fetchData[Fetch Data Function]
    fetchData --> filterData[Filter & Process]
    filterData --> cachedData
    cachedData --> components[React Components]
    
    %% Hooks system
    hooks[Hooks<br/><i>src/lib/hooks/</i>] --> useData[useData]
    hooks --> useSeasonalData[useSeasonalData]
    useData --> apiLayer
    useSeasonalData --> filterUtils[Filter Utilities<br/><i>src/lib/utils/filtering.ts</i>]
    
    %% Component rendering
    components --> ui[UI Rendering]
    
    %% Style nodes
    classDef data fill:#e6f7ff,stroke:#333,stroke-width:1px;
    classDef process fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef decision fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef hook fill:#d5e8d4,stroke:#333,stroke-width:1px;
    
    class dataFiles,constants,cachedData data;
    class apiLayer,fetchData,filterData,components,ui process;
    class cache decision;
    class hooks,useData,useSeasonalData,filterUtils hook;
```

These diagrams provide a visual representation of the codebase architecture, helping to understand the structure, relationships, and data flow in the Ringerike Landskap website.
