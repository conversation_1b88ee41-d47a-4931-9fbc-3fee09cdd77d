# Tech Context: Ringerike Landskap Website

## Technologies Used

### Core Framework and Libraries
- **React** (v18+): JavaScript library for building user interfaces
- **TypeScript**: Statically typed superset of JavaScript
- **Vite**: Modern build tool and development server
- **React Router DOM**: For client-side routing
- **Tailwind CSS**: Utility-first CSS framework

### Development Tools
- **ESLint**: For code linting and enforcing coding standards
- **PostCSS**: For CSS processing and transformations
- **Node.js**: JavaScript runtime for build processes
- **npm/yarn**: Package management
- **Git**: Version control

### Optimization Tools
- **Image optimization**: For WebP conversion and compression
- **Dependency visualization**: For codebase analysis and refactoring

## Development Setup

### Local Development Environment
```
1. Clone repository
2. Install dependencies with npm/yarn
3. Start development server with `npm run dev`
4. Access site at http://localhost:5173/
```

### Project Structure
The project follows a feature-based organization:

```
project/
├── public/             # Static assets
│   ├── images/         # Image files
│   │   ├── site/       # Site-wide images (hero, etc.)
│   │   ├── projects/   # Project images
│   │   └── team/       # Team member images
├── src/                # Source code
│   ├── components/     # Reusable components
│   │   ├── ui/         # General UI components
│   │   ├── layout/     # Layout components
│   │   └── feature/    # Feature-specific components
│   ├── data/           # Static data (projects, services)
│   ├── hooks/          # Custom React hooks
│   ├── pages/          # Page components
│   ├── styles/         # Global styles
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── App.tsx         # Main application component
│   └── main.tsx        # Entry point
```

### Build Process
- Development: `npm run dev` - Vite dev server with hot module replacement
- Production: `npm run build` - Optimized production build
- Preview: `npm run preview` - Preview production build locally

## Technical Constraints

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Limited support for Internet Explorer (basic functionality)

### Performance Targets
- Lighthouse score targets:
  - Performance: 90+
  - Accessibility: 90+
  - Best Practices: 90+
  - SEO: 90+
- Load time under 2 seconds for initial page load on 4G connection
- First Contentful Paint under 1.5 seconds

### Content Management
- Static content managed through code repository
- Content updates require code changes and redeployment
- No CMS integration in the current implementation

### SEO Requirements
- Optimized metadata for each page
- Proper heading structure
- Semantic HTML
- Descriptive image alt attributes
- Sitemap.xml and robots.txt

## Dependencies

### Production Dependencies
- Core React ecosystem (react, react-dom, react-router-dom)
- Styling libraries (tailwindcss, postcss, autoprefixer)
- Type definitions (@types/*)
- Utility libraries (as needed)

### Development Dependencies
- Vite and related plugins
- TypeScript configuration
- ESLint and configuration
- Testing libraries (if implemented)

## Tool Usage Patterns

### Development Workflow
1. Feature implementation follows branch-based workflow
2. Changes are tested in local development environment
3. Code reviews for quality assurance
4. Production builds for deployment verification

### CSS Management
- Tailwind utility classes for most styling needs
- Custom CSS only when needed for specific components
- Mobile-first responsive approach

### Type Safety
- TypeScript interfaces for all component props
- Type definitions for data structures
- Strict type checking enabled

### Asset Management
- Images stored in public directory by category
- WebP format preferred for better performance
- Responsive image sizing based on usage context

### Testing Strategy
- Component testing with React Testing Library (if implemented)
- Accessibility testing with axe-core (if implemented)
- Manual testing across devices and browsers

### Deployment
- Static site hosting
- CI/CD workflow for automated builds and testing
- Environment-specific configuration with .env files
